import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import qs from 'querystring';

import logo from '../assets/img/fc-logo.png';

import ProfileForm from '../components/ProfileForm';
import ProfileData from '../components/ProfileData';

const Setup = ({ location }) => {
	const [loaded, setLoaded] = useState(false);
	const [queryString, setQueryString] = useState('');

	const payslip = useSelector((state) => state.user.payslip);
	const remitaDatum = useSelector((state) => state.user.remitaDatum);
	const lender = useSelector((state) => state.lender.profile);

	const query = qs.parse(location.search.replace('?', ''));

	useEffect(() => {
		if (!loaded) {
			document.body.className = 'pages';
			if (query.fcl) {
				const string = `?fcl=${query.fcl}`;
				setQueryString(string);
			}
			setLoaded(true);
		}
	}, [loaded, query.fcl]);

	return (
		<div className="all-wrapper menu-side">
			<div className="layout-w">
				<div className="menu-mobile menu-activated-on-click color-scheme-dark">
					<div className="mm-logo-buttons-w">
						<Link className="mm-logo" to="/">
							<img alt="logo" src={logo} />
							<span>FastCash</span>
						</Link>
					</div>
				</div>
				<div className="content-w">
					<div className="top-menu-secondary">
						<ul>
							<li>
								<Link to={`/${queryString}`}>HOME</Link>
							</li>
							<li className="active">
								<Link to={`/setup${queryString}`}>SETUP</Link>
							</li>
						</ul>
					</div>
					<div className="content-i">
						<div className="content-box">
							<div className="row">
								{payslip && (
									<div className="col-sm-4">
										<ProfileData user={payslip} lender={lender} />
									</div>
								)}
								<div className={payslip ? 'col-sm-8' : 'col-sm-12'}>
									<ProfileForm
										user={payslip}
										remitaDatum={remitaDatum}
										lenderId={query.fcl || ''}
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default Setup;
