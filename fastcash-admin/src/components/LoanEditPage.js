import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';

import { calculateLoan, setTenure, setEarnings, doRefresh, calculateSMELoan } from '../actions/loan';
import { dateLimit } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import LoanForm from './LoanForm';
import LoanDetails from './LoanDetails';
import CurrentLoanDetails from './CurrentLoanDetails';
import loader from '../assets/img/loader.gif';

class LoanEditPage extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
            current: null,
            type: '',
            title: '',
			message: '',
			error_msg: '',
			platform: '',
        };
	}

	async componentDidMount() {
		this.setState({ error_msg: '' });
		const { tenure, payslip, current, earnings, lender } = this.props;
        const ir = lender.interest_rate;
		this.setState({ current });

		let dates = [];
		for (let d=1; d<=parseInt(dateLimit, 10); d++) {
			dates = [ ...dates, { date: moment().subtract(d, 'month').format('M-YYYY') } ];
		}
		
		let ippisNewEarnings = [];
		for (let i = 0; i < earnings.length; i++) {
			const earning = earnings[i];
			const found = ippisNewEarnings.find(e => e.month === earning.month && e.year === earning.year && parseFloat(e.net_earning) === parseFloat(earning.net_earning));
			if(!found){
				ippisNewEarnings = [...ippisNewEarnings, earning];
			}
		}
		const ippisFilteredEarnings = ippisNewEarnings.filter(e => {
			return dates.find(d => d.date === `${e.month}-${e.year}`) && parseInt(e.net_earning, 10) > 0;
		});
		ippisFilteredEarnings.sort((a, b) => b.year - a.year || b.month - a.month);
		const newEarnings = ippisFilteredEarnings.slice(0, 3);

		this.props.startBlock();
		this.props.doRefresh(true);

		if(current.platform === 'sme') {
			this.props.setEarnings([]);
			this.setState({ error_msg: '', platform: 'sme' });
			this.props.stopBlock();
			
			this.props.calculateSMELoan(payslip.eligible_amount || 0);
			this.props.doRefresh(false);
		} else {
			if(newEarnings.length >= 1){
				this.props.setEarnings(newEarnings);
				this.setState({ error_msg: '', platform: current.platform });
				this.props.stopBlock();

				if(current.platform === 'ippis'){
					const map_earnings = newEarnings.map(r => r.net_earning);
					const net_earning = Math.min( ...map_earnings );
					this.props.calculateLoan(net_earning, tenure, ir, 0.33);
				} else {
					const map_earnings = newEarnings.map(r => r.net_earning);
					const total_net_earning = map_earnings.reduce((total, amount) => parseFloat(amount) + total, 0);
					const net_earning = (total_net_earning / 3).toFixed(2);
					this.props.calculateLoan(net_earning, tenure, ir, 0.4);
				}

				this.props.doRefresh(false);
			} else {
				this.setState({ error_msg: 'net earnings not available' });
				this.props.stopBlock();
				this.props.doRefresh(false);
			}
		}
    }

    componentWillUnmount = () => {
		this.props.setTenure(1);
    };
	
	render() {
		const { user, myAmount, myMonthlyDeductions, monthlyDeductions, netEarning, eligibleAmt, tenure, bankAccounts, refresh } = this.props;
		const { current, error_msg, platform } = this.state;
		const accountNumbers = bankAccounts.length > 0 ? bankAccounts.map(a => a.bank_name ? ({ id: a.id, value: `${a.account_number} - ${a.bank_name}` }) : ({ id: a.id, value: a.account_number })) : [];
		return refresh ? (
			<div className="content-i"><div className="content-box" style={{textAlign:'center'}}><img src={loader} alt=""/></div></div>
		) : (
			<div className="content-i">
				<div className="content-box p-0">
					{
						error_msg !== '' ? (
							<div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>{error_msg}</div>
						) : (
							<div>
								{current && (
									<Fragment>
										<div className="row">
											<div className="col-sm-12">
												<CurrentLoanDetails
													amount={current.amount}
													deduction={current.total_deduction}
													netEarning={current.net_earning}
													tenure={current.tenure}
													platform={current.platform}
													is_topup={current.is_topup === 1}
													myDisburseAmount={current.disburse_amount}
													interestRate={current.interest_rate}
													insurance={current.insurance}
													processingFee={current.processing_fee}
												/>
											</div>
										</div>
										<div className="row">
											<div className="col-sm-6">
												<LoanDetails
													amount={myAmount}
													myMonthlyDeductions={myMonthlyDeductions}
													monthlyDeductions={monthlyDeductions}
													netEarning={netEarning}
													eligibleAmt={eligibleAmt}
													tenure={tenure}
													platform={platform}
													is_topup={current.is_topup === 1}
													myDisburseAmount={0}
												/>
											</div>
											<div className="col-sm-6">
												<div className="element-wrapper">
													<div className="element-wrapper">
														<LoanForm
															user={user}
															tenure={tenure}
															netEarning={netEarning}
															eligibleAmt={eligibleAmt}
															accountNumbers={accountNumbers}
															bankAccounts={bankAccounts}
															platform={platform}
															topup={current.topup}
															old_loan={null}
															loanID={current.id}
															insurance={current.insurance}
															processingFee={current.processing_fee}
															section='edit'
														/>
													</div>
												</div>
											</div>
										</div>
									</Fragment>
								)}
							</div>
						)
					}
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
    return {
        user: ownProps.user,
        myAmount: state.loan.myAmount,
        myMonthlyDeductions: state.loan.myMonthlyDeductions,
        monthlyDeductions: state.loan.monthlyDeductions,
        netEarning: state.loan.netEarning,
        eligibleAmt: state.loan.eligibleAmt,
        tenure: state.loan.tenure,
		refresh: state.loan.refresh,
		stopLoanDate: state.settings.stop_loan_date,
		eligibilityGap: state.settings.eligibility_gap,
		earnings: ownProps.earnings,
		bankAccounts: ownProps.bank_accounts,
        payslip: ownProps.payslip,
        current: ownProps.loan,
        lender: state.lender.profile,
    }
};

export default connect(mapStateToProps, { calculateLoan, setTenure, setEarnings, startBlock, stopBlock, doRefresh, calculateSMELoan })(LoanEditPage);
