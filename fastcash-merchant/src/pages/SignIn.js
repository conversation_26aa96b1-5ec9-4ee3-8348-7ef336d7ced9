/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import { withRouter } from 'react-router-dom';
import { baseURL, httpRequest, loginAPI, authKey } from '../config/constants';
import { changeUserData } from '../actions/user';
import { setLoans } from '../actions/loan';
import loading from '../assets/img/loading.gif';
import logoBig from '../assets/img/fc-logo.png';
import { putSettings } from '../actions/settings';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {}
    if (!values.username) {
        errors.username = 'Enter your username';
    }
    if (!values.password) {
        errors.password = 'Enter your password';
    }
    return errors;
};

const renderField = ({input, id, icon, label, hideclass, type, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')} ${hideclass}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" />
        <div className={`pre-icon os-icon ${icon}`}></div>
        {touched &&
            ((error && <span className="help-block">{error}</span>) ||
                (warning && <span className="help-block">{warning}</span>))}
    </div>
);

class SignIn extends Component {
	authAdmin = async data => {
		try {
            const response = await httpRequest(`${baseURL}${loginAPI}`, 'POST', false, data);
            const user = response.user;
            this.props.putSettings(response.settings);
            this.props.changeUserData(user, true);
            this.props.setLoans(response.loans);
            localStorage.setItem(authKey, response.token);
            this.props.reset('signinform');
            this.notify('', 'authentication successful!', 'success');
            this.props.history.push({ pathname: `/dashboard/${user.merchant_code}` });
        }
        catch (error) {
            const message = error.message || 'Error, check your connection and try again';
            throw new SubmissionError({
                _error: message,
            });
        }
	};

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

    componentDidMount() {
        document.body.className="auth-wrapper pages";
    }

    render() {
        const { handleSubmit, error, submitting, pristine } = this.props;
        return (
            <div className="all-wrapper menu-side with-pattern">
                <div className="auth-box-wh">
                    <div className="logo-w" style={{padding: '10%'}}>
                        <Link to="/"><img alt="logo" src={logoBig} style={{width: '50%'}}/></Link>
                    </div>
                    <h4 className="auth-header">FastCash Merchant</h4>
                    {error && <div className="alert alert-danger" dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                    <form onSubmit={handleSubmit(this.authAdmin)} autoComplete="off">
                        <Field
                            name="username"
                            id="username"
                            type="text"
                            component={renderField}
                            label="Username"
                            icon="os-icon-user-male-circle"
                        />
                        <Field
                            name="password"
                            id="password"
                            type="password"
                            component={renderField}
                            label="Password"
                            icon="os-icon-fingerprint"
                        />
                        <div>
                            <Link className="btn btn-link" to="/forgot-password">Forgot Password? Click Here</Link>
                        </div>
                        <div className="buttons-w">
                            <button className="btn btn-primary" type="submit" disabled={pristine || submitting}>
                                {submitting? <img src={loading} alt=""/> : 'Log In'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    }
}

SignIn = reduxForm({
    form: 'signinform',
    validate
})(SignIn);

export default withRouter(connect(null, { putSettings, changeUserData, setLoans, reset, doNotify })(SignIn));