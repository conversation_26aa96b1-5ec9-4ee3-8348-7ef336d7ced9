import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';

import { baseURL, httpRequest, userAPI } from '../config/constants';
import loading from '../assets/img/loading.gif';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {}
    if (!values.user) {
        errors.user = 'Select user';
    }
    if (!values.lender_id) {
        errors.lender_id = 'Select lender';
    }
    return errors;
};

const renderField = ({input, id, label, type, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" disabled="disabled" />
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

const renderSelectField = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label>{label}</label>
        <select {...input} className="form-control">
            <option value="">{`Select ${label}`}</option>
            {data.map((c, i) => <option value={c.id} key={i}>{c.name}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class TransferToLender extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
            offices: [],
        };
    }
    
    transferLender = async data => {
		try {
            const { user, customer } = this.props;
            const details = { ...data, staff_id: user.id };
            const url = `${baseURL}${userAPI}/change-lender/${customer.id}`;
            await httpRequest(url, 'POST', true, details);
            this.props.reset('switchlender');
            this.notify('', 'user has been transferred to another lender!', 'success');
            this.props.closeModal();
        }
        catch (error) {
            const message = error.message || 'could not user transfer to another lender';
            this.notify('', message, 'error');
            throw new SubmissionError({
                _error: message,
            });
        }
    };
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
    };
    
    render() {
        const { closeModal, handleSubmit, error, submitting, pristine, lenders, customer, user } = this.props;
        return (
            <div>
                <div className="modal-header faded smaller">
                    <div className="modal-title">
                        <span>Transfer to another Lender</span>
                    </div>
                    <button aria-label="Close" className="close" onClick={closeModal} type="button"><span aria-hidden="true"> ×</span></button>
                </div>
                <form onSubmit={handleSubmit(this.transferLender)}>
                    <div className="modal-body">
                        {error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}/>}
                        <div className="row">
                            <div className="col-sm-8">
								<Field
									name="user"
									id="user"
									type="text"
									component={renderField}
									label="User"
								/>
                            </div>
                            <div className="col-sm-8">
                                <div className="form-group ">
                                    <label>Current Lender</label>
                                    <input type="text" className="form-control" disabled="disabled" value={customer ? customer.lender.name : ''} />
                                </div>
                            </div>
                            <div className="col-sm-8">
								<Field
									name="lender_id"
									component={renderSelectField}
									label="Lenders"
									data={lenders.filter(l => l.id !== parseInt(user.lender_id, 10))}
								/>
                            </div>
                        </div>
                    </div>
                    <div className="modal-footer buttons-on-left">
                        <button className="btn btn-teal" disabled={pristine || submitting} type="submit">
                            {submitting ? <img src={loading} alt=""/> : 'Submit'}
                        </button>
                        <button className="btn btn-link cursor" onClick={closeModal} type="button"> Cancel</button>
                    </div>
                </form>
            </div>
        );
    }
}

TransferToLender = reduxForm({
    form: 'switchlender',
    validate,
})(TransferToLender);

const mapStateToProps = (state, ownProps) => {
    return {
        initialValues: {
            user: ownProps.customer ? ownProps.customer.name : '',
            lender_id: "",
        },
        user: state.user.user,
        lenders: state.lender.list,
    }
}

export default connect(mapStateToProps, { reset, doNotify })(TransferToLender);
