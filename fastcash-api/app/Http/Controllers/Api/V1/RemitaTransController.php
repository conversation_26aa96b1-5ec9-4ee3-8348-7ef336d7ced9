<?php

namespace App\Http\Controllers\Api\V1;

use App\Exports\ExportLoan;
use App\Models\Loan;
use App\Models\PaymentDate;
use App\Models\RemitaTransaction;
use App\Models\Transaction;
use App\Models\Wallet;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use DB;
use Log;

class RemitaTransController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Upload files.
     *
     * @param Request $request
     * @param string $type
     * @return \Illuminate\Http\Response
     */
    public function upload(Request $request, $type)
    {
        DB::beginTransaction();
        try {
            $file_type = $request->get('file_type');
            if ($type == 'bank' && $file_type == '') {
                return response()->json(['error' => 'select transaction type'], 500);
            }

            $staff = $request->get('staff');

            $data = [];
            $heading_slug = [];

            $remitafile = Excel::toArray(null, request()->file('file'));

            $remita_file = $remitafile;

            // get heading
            $needle = $type == 'remita' ? 'S/N' : 'Transaction Date';
            $heading_array = getHeading($remita_file, $needle);
            $headings = array_values($heading_array);
            info('headings ' . count($headings));

            if (count($headings) > 0) {
                foreach ($headings[0] as $key => $item) {
                    $heading_slug[] = $item == null ? 'NULL' . $key : Str::slug($item, '_');
                }

                // parse file
                foreach ($remita_file as $sheet) {
                    foreach ($sheet as $key => $row) {
                        $data[] = array_combine($heading_slug, $row);
                    }
                }

                $uploaded = [];
                $approved_transactions = [];

                if ($type == 'remita') {
                    // create transaction
                    foreach ($data as $key => $value) {
                        if ($value['repayment_ref'] != null) {
                            $check = RemitaTransaction::where('transaction_ref', $value['repayment_ref'])->first();
                            if (!$check) {
                                $loan = getLoan($value);
                                if ($loan) {
                                    $remitaTrans = RemitaTransaction::create([
                                        'user_id' => $loan->user_id,
                                        'customer_id' => $value['customer_id'],
                                        'first_name' => $value['first_name'],
                                        'last_name' => $value['last_name'],
                                        'phone_number' => $value['phone_number'],
                                        'employer_name' => $value['employer_name'],
                                        'salary_account' => $value['salary_account'],
                                        'loan_merchant' => $value['loan_merchant'],
                                        'disbursement_account' => $value['disbursement_account'],
                                        'repayment_credit_account' => $value['repayment_credit_account'],
                                        'bvn' => $value['bvn'],
                                        'salary_bank_code' => $value['salary_bank_code'],
                                        'disbursement_account_bank_code' => $value['disbursement_account_bank_code'],
                                        'repayment_credit_bank_code' => $value['repayment_credit_bank_code'],
                                        'repayment_credit_bank_name' => $value['repayment_credit_bank_name'],
                                        'collection_start_date' => $value['collection_start_date'],
                                        'disbursement_date' => $value['disbursement_date'],
                                        'salary_payment_date' => $value['salary_payment_date'],
                                        'mandate_reference' => $value['mandate_reference'],
                                        'last_salary_payment_date' => $value['last_salary_payment_date'],
                                        'transaction_ref' => $value['transaction_ref'],
                                        'salary_payment_ref' => $value['salary_payment_ref'],
                                        'repayment_ref' => $value['repayment_ref'],
                                        'repayment_status' => $value['repayment_status'],
                                        'repayment_date' => $value['repayment_date'],
                                        'status_reason' => $value['status_reason'],
                                        'loan_amount' => $value['loan_amount_n'],
                                        'interest_amount' => $value['interest_amount_n'],
                                        'repayment_amount' => $value['repayment_amount_n'],
                                        'repayment_collection_fee' => $value['repayment_collection_fee_n'],
                                        'last_salary_credit_amount' => $value['last_salary_credit_amount_n'],
                                    ]);

                                    $uploaded[] = 'loan: ' . $loan->id . ', rid: ' . $remitaTrans->id;

                                    $item = Transaction::create([
                                        'user_id' => $loan->user_id,
                                        'loan_id' => $loan->id,
                                        'office_id' => $loan->office_id,
                                        'lender_id' => $loan->lender_id,
                                        'source' => strtoupper($loan->platform),
                                        'amount' => $value['repayment_amount_n'],
                                        'status' => 'Pending',
                                        'reference' => null,
                                        'channel' => 'remita-repayments',
                                        'approved' => 0,
                                        'repayment_source' => 'remita',
                                        'repayment_reference' => $value['repayment_ref'],
                                        'remita_transaction_id' => $remitaTrans->id,
                                        'uploaded_by' => $staff,
                                        'transaction_flag' => 'deduction',
                                        'repayment_date' => null,
                                        'principal' => $value['repayment_amount_n'] - $loan->monthly_interest,
                                        'interest' => $loan->monthly_interest,
                                        'outst_amount' => calcOutstRemita($loan->id, $loan->total_deduction, 0)
                                    ]);
                                    //'repayment_date' => $value['repayment_date'],

                                    $notify = doNotify($item->user_id, 'transactions', $item->id, 'remita_loan_repayment', 'success', null, $loan->lender_id, $staff);
                                }
                            }
                        }
                    }

                    info('remita upload: ' . $type . ', count ' . count($data) . ', uploaded: ' . json_encode($uploaded));
                } else {
                    if ($file_type == 'bank_one') {
                        foreach ($data as $key => $value) {
                            $details = explode('/', $value['narration']);
                            $details = count($details) == 0 ? $details : explode('-', $details[0]);
                            $repayment_ref = count($details) > 1 ? $details[1] : join('', $details);
                            $amount = $value['cr'];
                            try {
                                $unix_date = ($value['transaction_date'] - 25569) * 86400;
                                $transaction_date = gmdate("Y-m-d", $unix_date);
                            } catch (\Exception $e){
                                $transaction_date = date('Y-m-d');
                            }

                            $transaction = Transaction::where('repayment_reference', $repayment_ref)->where('approved', 0)->first();
                            $approved_transactions = parseTransaction($transaction, $repayment_ref, $amount, $transaction_date, $staff);
                        }
                    } else if ($file_type == 'cmfb') {
                        foreach ($data as $key => $value) {
                            if ($value['transaction_remarks'] != null) {
                                $transaction_amount = str_replace(',', '', $value['deposits']);

                                $trans_dates = explode('/', $value['transaction_date']);
                                $transaction_date = count($trans_dates) > 2 ? $trans_dates[2] . '-' . $trans_dates[1] . '-' . $trans_dates[0] : null;

                                $explode = explode('/', $value['transaction_remarks']);
                                if (count($explode) > 0) {
                                    $repayment_ref = preg_replace("/[^0-9]/", "", $explode[0]);

                                    $transaction = Transaction::where('repayment_reference', $repayment_ref)->where('approved', 0)->first();
                                    $approved_transactions = parseTransaction($transaction, $value['transaction_remarks'], $transaction_amount, $transaction_date, $staff);
                                }
                            }
                        }
                    }
                }

                DB::commit();
                $report = 'okay:' . count($approved_transactions);
                info('transactions uploaded: ' . json_encode($approved_transactions));

                return response()->json(compact('report'), 200);
            }

            DB::rollBack();
            return response()->json(['error' => 'uploading failed'], 500);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'File could not be uploaded'], 500);
        }
    }

    /**
     * Download remita report.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function download(Request $request)
    {
        try {
            // $loans = RemitaTransaction::whereNull('bank_repayment_ref')->get(['id', 'first_name', 'last_name', 'customer_id', 'phone_number', 'employer_name', 'salary_account', 'loan_merchant', 'disbursement_account', 'repayment_credit_account', 'bvn', 'salary_bank_code', 'disbursement_account_bank_code', 'repayment_credit_bank_code', 'repayment_credit_bank_name', 'collection_start_date', 'disbursement_date', 'salary_payment_date', 'mandate_reference', 'last_salary_payment_date', 'transaction_ref', 'salary_payment_ref', 'repayment_ref', 'repayment_status', 'repayment_date', 'status_reason', 'loan_amount', 'interest_amount', 'repayment_amount', 'repayment_collection_fee', 'last_salary_credit_amount']);

            $loans = [];

            $transactions = Transaction::where('approved', '<', 1)->whereNull('deleted_at')->whereNull('description')->get(['remita_transaction_id']);

            foreach($transactions as $item){
                if($item->remita_transaction_id !== null){
                    $remitaTrans = RemitaTransaction::where('id', $item->remita_transaction_id)->whereNull('bank_repayment_ref')->first(['id', 'first_name', 'last_name', 'customer_id', 'phone_number', 'employer_name', 'salary_account', 'loan_merchant', 'disbursement_account', 'repayment_credit_account', 'bvn', 'salary_bank_code', 'disbursement_account_bank_code', 'repayment_credit_bank_code', 'repayment_credit_bank_name', 'collection_start_date', 'disbursement_date', 'salary_payment_date', 'mandate_reference', 'last_salary_payment_date', 'transaction_ref', 'salary_payment_ref', 'repayment_ref', 'repayment_status', 'repayment_date', 'status_reason', 'loan_amount', 'interest_amount', 'repayment_amount', 'repayment_collection_fee', 'last_salary_credit_amount']);
                    if($remitaTrans !== null){
                        $loans[] = $remitaTrans;
                    }
                }
            }

            $allLoans = json_decode(json_encode($loans), true);

            $report = [];
            foreach ($allLoans as $item) {
                $transaction = Transaction::where('remita_transaction_id', $item['id'])->first();
                if ($transaction) {
                    $loan = Loan::where('id', $transaction->loan_id)->first();
                    if ($loan) {
                        $new_report = [];
                        foreach ($item as $key => $value) {
                            $new_report[ucwords(str_replace("_", " ", $key))] = $value;
                            unset($new_report[$key]);
                        }
                        $report[] = $new_report;
                    }
                }
            }

            if (count($report) == 0) {
                return response()->json(['message' => 'no loans available'], 200);
            }

            $headings = getHeadings($report[0]);

            $filename = 'remita-rpt-' . time();
            $filepath = 'repayments/' . $filename . '.xlsx';

            Excel::store(new ExportLoan($report, $headings), $filepath, 'public');

            $file = env('APP_URL') . '/repayments/' . $filename . '.xlsx';
            return response()->json(compact('file'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not download remita report'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
