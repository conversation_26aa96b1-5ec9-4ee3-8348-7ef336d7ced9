<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddCommissionLoan extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->integer('lender_interest_rate')->nullable()->after('interest_rate');
            $table->decimal('commission', 20,2)->nullable()->after('monthly_interest');
            $table->decimal('monthly_commission', 20,2)->nullable()->after('commission');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropColumn(['lender_interest_rate', 'commission', 'monthly_commission']);
        });
    }
}
