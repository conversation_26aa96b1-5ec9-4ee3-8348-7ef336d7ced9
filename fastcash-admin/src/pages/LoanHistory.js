import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, change, SubmissionError } from 'redux-form';
import moment from 'moment';

import { doNotify } from '../actions/general';
import { baseURL, httpRequest, remitaLoanHistoryAPI, currency } from '../config/constants';

import loading from '../assets/img/loading.gif';

const validate = values => {
    const errors = {}
    if (!values.phone) {
        errors.phone = 'Enter phone number';
    }
    return errors;
};

const renderField = ({input, id, label, type, disabled, meta: {touched, error, warning}}) => (
    <div className={`form-group mb-0 ${(touched && error && 'has-error')}`}>
        <input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

const renderSelectField = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group${(touched && error ? ' has-error':'')}`}>
        <select {...input} className="form-control" placeholder={label}>
            <option value="">Select Bank</option>
            {data.map(t => <option value={t.id} key={t.id}>{t.name}</option>)}
        </select>
        {touched && error && <span className="help-block">{error}</span>}
    </div>
);

class LoanHistory extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			submitting: false,
			data: null,
			phone: '',
			failed: false,
			method: 'phone',
		};
	}
	
    fetchHistory = async data => {
		const { method } = this.state;
		const { user, banks } = this.props;

		this.setState({ submitting: true, data: null, phone: data.phone });
		const bank = banks.find(b => Number(b.id) === Number(data.bank))

		try {
			const url = `${baseURL}${remitaLoanHistoryAPI}?fetch=search&role_category=${user.role_name}&search_method=${method}`;
			const rs = await httpRequest(url, 'POST', true, { ...data, bank_code: bank?.code || '' });
			this.setState({ submitting: false, data: rs.result });
			this.notify('', 'data found!', 'success');
		}
		catch (error) {
			this.setState({ submitting: false, failed: true, method: 'account_number' });
			const message = error.message || 'could not fetch history';
			throw new SubmissionError({
				_error: message,
			});
		}
    };
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};
	
	render() {
		const { handleSubmit, error, pristine, banks } = this.props;
		const { submitting, data, phone, failed } = this.state;
		const salaryPaymentDetails = data && data.salaryPaymentDetails ? data.salaryPaymentDetails : [];
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="element-wrapper">
						<div className="element-box">
							<form onSubmit={handleSubmit(this.fetchHistory)}>
								{error && <div className="alert alert-danger mb-2" dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}/>}
								<div className="row">
									<div className="text-right col-sm-2">
										<label className="mb-0" style={{lineHeight: '35px'}} htmlFor="phone">Phone number:</label>
									</div>
									<div className="col-sm-4">
										<Field
											name="phone"
											id="phone"
											type="text"
											component={renderField}
											label="Phone"
											onChange={() => {
												this.props.change('account_number', '')
												this.setState({ failed: false, method: 'phone' })
											}}
										/>
									</div>
									{!failed && <div className="col-sm-4">
										<button className="btn btn-primary" disabled={pristine || submitting} type="submit">
											{submitting? <img src={loading} alt=""/> : 'Fetch'}
										</button>
									</div>}
								</div>
								{failed && <div className="row" style={{ marginTop: '8px' }}>
									<div className="text-right col-sm-2">
										<label className="mb-0" style={{lineHeight: '35px'}} htmlFor="account_number">Account number:</label>
									</div>
									<div className="col-sm-4">
										<Field
											name="account_number"
											id="account_number"
											type="text"
											component={renderField}
											label="Account number"
										/>
									</div>
									<div className="col-sm-3">
										<Field
											name="bank"
											data={banks}
											component={renderSelectField}
											label="Select Bank"
										/>
									</div>
									<div className="col-sm-3">
										<button className="btn btn-primary" disabled={pristine || submitting} type="submit">
											{submitting? <img src={loading} alt=""/> : 'Fetch'}
										</button>
									</div>
								</div>}
							</form>
						</div>
					</div>
					{!submitting && data && (
						<div className="element-wrapper">
							<h6 className="element-header">Loan History</h6>
							<div className="element-box">
								<div className="row">
									<div className="col-sm-7">
										<div className="table-responsive" style={{overflowX: 'scroll'}}>
											<table className="table table-lightborder">
												<tbody>
													<tr>
														<td>Customer id</td>
														<td className="text-left">{data.customerId}</td>
													</tr>
													<tr>
														<td>Customer phone</td>
														<td className="text-left">{phone}</td>
													</tr>
													<tr>
														<td>Customer name</td>
														<td className="text-left">{data.customerName}</td>
													</tr>
												</tbody>
											</table>
										</div>
									</div>
									<div className="col-sm-12">
										<div className="table-responsive" style={{overflowX: 'scroll'}}>
											<table className="table table-lightborder">
												<tbody>
													<tr>
														<th>Loan Provider</th>
														<th>Amount</th>
														<th>Outstanding Amt.</th>
														<th>Repayment Amt.</th>
														<th>Status</th>
														<th>Date</th>
													</tr>
													{data.loanHistoryDetails && data.loanHistoryDetails.map((info, i) => {
														return (
															<tr key={i}>
																<td>{info.loanProvider}</td>
																<td>{currency(info.loanAmount)}</td>
																<td>{currency(info.outstandingAmount)}</td>
																<td>{currency(info.repaymentAmount)}</td>
																<td>{info.status === 'STOP' ? 'ENDED' : info.status}</td>
																<td>{info.loanDisbursementDate.split(' ')[0]}</td>
															</tr>
														);
													})}
													{!data.loanHistoryDetails || (data.loanHistoryDetails && data.loanHistoryDetails.length === 0 && <tr><td colSpan="6">No loan found</td></tr>)}
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
							<h6 className="element-header mt-5">Net Earnings</h6>
							<div className="element-box">
								<div className="table-responsive" style={{overflowX: 'scroll'}}>
									<table className="table table-lightborder">
										<tbody>
											<tr>
												<th>Date</th>
												<th>Net Earning</th>
											</tr>
											{salaryPaymentDetails.map((info, i) => {
												const date = info.paymentDate.split('+');
												return (
													<tr key={i}>
														<td>{moment(date[0], "DD-MM-YYYY HH:mm:ss").format('MMM-YYYY')}</td>
														<td>{currency(info.amount)}</td>
													</tr>
												);
											})}
											{salaryPaymentDetails.length === 0 && (
												<tr>
													<td colSpan="2" className="text-center">No loan found</td>
												</tr>
											)}
										</tbody>
									</table>
								</div>
							</div>
						</div>
					)}
				</div>
			</div>
		);
	}
}

LoanHistory = reduxForm({
    form: 'createadmin',
    validate,
})(LoanHistory);

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
		banks: state.general.banks,
	}
};

export default connect(mapStateToProps, { doNotify, change })(LoanHistory);
