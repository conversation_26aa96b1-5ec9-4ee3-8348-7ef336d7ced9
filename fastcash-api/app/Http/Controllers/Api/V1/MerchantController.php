<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Lender;
use App\Models\Loan;
use App\Models\Merchant;
use App\Models\MerchantPay;
use App\Models\Office;
use App\Models\Role;
use App\Models\User;
use App\Services\MailHandler;
use Auth;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\Wallet;
use Illuminate\Support\Facades\Validator;
use DB;
use Log;

class MerchantController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $lender = $request->has('lender') ? $request->input('lender') : '';

        if ($lender != '') {
            $merchants = Merchant::where('lender_id', $lender)->get();
            $merchants = $merchants->map(function ($item, $key) {
                $user = User::find($item->user_id);
                $office = Office::find($item->office_id);

                $item->user = $user->name;
                $item->office = ($office ? $office->name : null);
                $item->count = MerchantPay::where('merchant_id', $item->id)->where('paid', 0)->count();

                $item->enabled = $user->enabled;

                $loans = Loan::where('merchant_code', $item->merchant_code)->get();
                $loans = $loans->map(function ($item, $key) {
                    $item->user = User::where('id', $item->user_id)->first(['id', 'name', 'phone', 'ippis']);
                    $pay = MerchantPay::where('loan_id', $item->id)->first(['paid']);
                    $item->paid = $pay->paid;
                    return $item;
                });

                $item->loans = $loans;
                $item->wallet_balance = Wallet::where('user_id', $item->user_id)->where('is_lender', 1)->where('approved', 1)->where('paid', 1)->sum('amount');
                $item->commission_balance = Wallet::where('user_id', $item->user_id)->where('category', 'loan-referral-commission')->where('is_lender', 1)->where('approved', 1)->where('paid', 1)->sum('amount');

                $lender = Lender::find($item->lender_id);
                $item->approved_count = Loan::where('merchant_code', $item->merchant_code)->where('approved', 1)->count();

                $paid_loans = MerchantPay::where('merchant_id', $item->id)->where('paid', 1)->get();
                $item->paid_count = count($paid_loans);

                return $item;
            });

            return response()->json(compact('merchants'), 200);
        }

        return response()->json(['error' => 'could not fetch merchants'], 500);
    }

    /**
     * Find user for merchant.
     *
     * @return \Illuminate\Http\Response
     */
    public function findUser(Request $request)
    {
        $input = $request->all();

        try {
            $found = User::with('office')
                ->where('lender_id', $input['lender_id'])
                ->where(function ($query) use ($input) {
                    $query->where('ippis', 'LIKE', '%'.$input['phone'].'%')->orWhere('phone', 'LIKE', '%'.$input['phone'].'%')->orWhere('phone', 'LIKE', '%'.$input['phone'].'%');
                })
                ->where('enabled', 1)
                ->get(['id', 'name', 'office_id', 'phone', 'created_at']);

            $data = collect($found)->filter(function ($item, $key) {
                return $item->role == 'user';
            })->values();

            return response()->json(['users' => $data], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'network error, please try again'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $isUser = $request->has('q') && $request->input('q') == 'user' ? $request->input('q') : '';

        if ($isUser == '') {
            $validator = Validator::make($request->all(), [
                'office' => 'required',
                'firstname' => 'required',
                'lastname' => 'required',
                'email' => 'required',
                'gender' => 'required',
                'phone' => 'required',
                'lender_id' => 'required',
            ]);

            if ($validator->fails()) {
                $error = validatorMessage($validator->errors());
                return response()->json(['error' => $error], 500);
            }
        }

        DB::beginTransaction();
        try {
            $data = $request->all();

            $password = generatePassword(6);
            $merchant_code = generateMerchantCode(5);

            if ($isUser == '') {
                $found = User::where('lender_id', $data['lender_id'])->where(function ($query) use ($data) {
                    $query->where('email', $data['email'])->orWhere('phone', $data['phone']);
                })->first();

                if ($found) {
                    if ($found->enabled == 0) {
                        return response()->json(['error' => 'account was banned'], 500);
                    }

                    if ($found->role == 'user') {
                        return response()->json(['error' => 'user account already exists, search for the account and make account a merchant user'], 500);
                    } else if ($found->role == 'merchant' || $found->role == 'u-merchant') {
                        return response()->json(['error' => 'merchant account already exists'], 500);
                    }
                }

                $username = generateUsername($data['firstname'], $data['lastname']);

                $user = User::create([
                    'name' => $data['firstname'] . ' ' . $data['lastname'],
                    'username' => $username,
                    'merchant_password' => bcrypt($password),
                    'email' => $data['email'],
                    'phone' => $data['phone'],
                    'gender' => $data['gender'],
                    'office_id' => $data['office'],
                    'enabled' => 1,
                    'lender_id' => $data['lender_id'],
                ]);
                $user->roles()->attach(Role::where('name', 'merchant')->first());

                $merchant = Merchant::create([
                    'user_id' => $user->id,
                    'merchant_code' => $merchant_code,
                    'office_id' => $data['office'],
                    'lender_id' => $data['lender_id'],
                ]);

            } else {
                $user = User::where('id', $data['user_id'])->first();

                $name = explode(',', $user->name);
                if(count($name) > 1){
                    $others = explode(' ', $name[1]);

                    $username = generateUsername($name[0], $others[0]);
                } else {
                    $name = explode(' ', $user->name);

                    if(count($name) > 1){
                        $username = generateUsername($name[0], $name[1]);
                    } else {
                        $email = explode('@', $user->email);

                        $username = generateUsername($name[0], $email[0]);
                    }
                }

                $user->username = $username;
                $user->merchant_password = bcrypt($password);
                $user->save();

                $role = Role::where('name', 'u-merchant')->first();

                DB::table('user_roles')
                    ->where('user_id', $user->id)
                    ->update(['role_id' => $role->id]);

                $merchant = Merchant::create([
                    'user_id' => $user->id,
                    'merchant_code' => $merchant_code,
                    'office_id' => $user->office_id,
                    'lender_id' => $data['lender_id'],
                ]);
            }

            $notify = doNotify($user->id, 'merchants', $merchant->id, 'new_merchant', 'success', NULL, $user->lender_id, $data['staff_id']);

            DB::commit();

            try {
                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = $user->name;
                $mail->to_email = $user->email;
                $mail->template = 'email.new_merchant';
                $mail->subject = 'Account Created';
                $mail->login = env('MERCHANT_URL');

                $mail->username = $user->username;
                $mail->securepwd = $password;
                $mail->merchant_code = $merchant->merchant_code;

                $send = (new MailHandler())->sendMail($mail);
            } catch (\Exception $e) {
                Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            }

            return response()->json(compact('merchant'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not create merchant, try again later'], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $merchant = Merchant::find($id);
        $office = Office::find($merchant->office_id);
        $user = User::find($merchant->user_id);

        $merchant->lender = Lender::where('id', $user->lender_id)->first();
        $merchant->user = $user->name;
        $merchant->office = ($office ? $office->name : null);
        $merchant->count = MerchantPay::where('merchant_id', $merchant->id)->where('paid', 0)->count();
        $merchant->enabled = $user->enabled;

        $loans = Loan::where('merchant_code', $merchant->merchant_code)->get(['id', 'user_id', 'amount', 'created_at']);
        $loans = $loans->map(function ($item, $key) {
            $item->user = User::where('id', $item->user_id)->first(['id', 'name', 'email']);
            return $item;
        });

        $merchant->loans = $loans;

        return response()->json(compact('merchant'), 200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $merchant = Merchant::find($id);
            $userID = $merchant->user_id;
            $user = User::destroy($userID);

            $merchant->deleted_by = Auth::user()->id;
            $merchant->save();
            $merchant->delete();

            return response()->json(['data' => $userID], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'network error, try again'], 500);
        }
    }

    public function payMerchant(Request $request, $id)
    {
        $data = $request->all();

        try {
            $merchant = Merchant::find($id);
            $merchant_pays = MerchantPay::where('merchant_id', $merchant->id)->where('paid', 0)->take(20)->get();
            $lender = Lender::where('id', $merchant->lender_id)->first();
            $noWalletDeposit = [];

            foreach ($merchant_pays as $pay) { // loans with no earlier merchant commisions are retrieved
                $loan = Loan::find($pay->loan_id);
                $walletTrans = Wallet::where('loan_id', $loan->id)->where('category', 'loan-referral-commission')->where('type', 'credit')->first();
                if($walletTrans == null){
                    $noWalletDeposit[] = $loan;
                }
            }

            // Merchant wallet is credited for loans with no wallet deposits
            foreach ($noWalletDeposit as $key => $value) {
                $transaction = Transaction::create([
                    'user_id' => $merchant->user_id,
                    'loan_id' => $value->id,
                    'office_id' => $value->office_id,
                    'source' => strtoupper($value->platform),
                    'amount' => $lender->merchant_commission,
                    'interest' => 0,
                    'principal' => 0,
                    'outst_amount' => 0,
                    'status' => 'Completed',
                    'description' => 'Loan Referral Commission',
                    'transaction_flag' => 'commission',
                    'lender_id' => $value->lender_id,
                    'approved' => 1,
                    'approved_by' => $data['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                ]);

                // credit merchant wallet
                $w2 = Wallet::create([
                    'user_id' => $merchant->user_id,
                    'category' => 'loan-referral-commission',
                    'loan_id' => $value->id,
                    'type' => 'credit',
                    'amount' => $lender->merchant_commission,
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $data['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $value->lender_id,
                    'is_lender' => 1,
                    'paid' => 1,
                ]);

                $notify_tc = doNotify($transaction->user_id, 'transactions', $transaction->id, 'loan_referral_commission', 'success', null, $transaction->lender_id, $data['staff']);
            }

            foreach ($merchant_pays as $pay) {
                $transaction = Transaction::create([
                    'user_id' => $merchant->user_id,
                    'loan_id' => $pay->id,
                    'office_id' => $pay->office_id,
                    'source' => strtoupper($pay->platform),
                    'amount' => $lender->merchant_commission,
                    'interest' => 0,
                    'principal' => 0,
                    'outst_amount' => 0,
                    'status' => 'Completed',
                    'description' => 'Loan Referral Commission',
                    'transaction_flag' => 'commission',
                    'lender_id' => $pay->lender_id,
                    'approved' => 1,
                    'approved_by' => $data['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                ]);

                // debit merchant wallet
                $w1 = Wallet::create([
                    'user_id' => $merchant->user_id,
                    'category' => 'loan-referral-commission',
                    'type' => 'debit',
                    'loan_id' => $pay->id,
                    'amount' => (-1 * $lender->merchant_commission),
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $data['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $pay->lender_id,
                    'is_lender' => 1,
                    'paid' => 1,
                ]);

                $notify_tc = doNotify($transaction->user_id, 'transactions', $transaction->id, 'loan_referral_commission', 'success', null, $transaction->lender_id, $data['staff']);

                $mpay = MerchantPay::find($pay->id);
                $mpay->paid = 1;
                $mpay->paid_at = date('Y-m-d');
                $mpay->save();
            }

            $loans = Loan::where('merchant_code', $merchant->merchant_code)->get(['id']);
            $approved_count = Loan::where('merchant_code', $merchant->merchant_code)->where('approved', 1)->count();
            $paid_count = MerchantPay::where('merchant_id', $merchant->id)->where('paid', 1)->count();

            $merch = (object)null;
            $merch->id = $merchant->id;
            $merch->approved_count = $approved_count;
            $merch->paid_count = $paid_count;
            $merch->count = MerchantPay::where('merchant_id', $merchant->id)->where('paid', 0)->count();

            return response()->json(compact('merch'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'network error, try again'], 500);
        }
    }

    public function singlePayMerchant(Request $request)
    {
        $data = $request->all();

        $loan = Loan::find($data['loan_id']);
        $merchant = Merchant::find($data['merchant_id']);
        $mpay = MerchantPay::where('loan_id', $loan->id)->where('merchant_id', $merchant->id)->where('paid', 0)->first();
        $lender = Lender::where('id', $merchant->lender_id)->first();

        if($loan){
            $walletTrans = Wallet::where('loan_id', $loan->id)->where('category', 'loan-referral-commission')->where('type', 'credit')->first();

            if($walletTrans == null){
                try {
                    $transaction = Transaction::create([
                        'user_id' => $merchant->user_id,
                        'loan_id' => $loan->id,
                        'office_id' => $loan->office_id,
                        'source' => strtoupper($loan->platform),
                        'amount' => $lender->merchant_commission,
                        'interest' => 0,
                        'principal' => 0,
                        'outst_amount' => 0,
                        'status' => 'Completed',
                        'description' => 'Loan Referral Commission',
                        'transaction_flag' => 'commission',
                        'lender_id' => $loan->lender_id,
                        'approved' => 1,
                        'approved_by' => $data['staff_id'],
                        'approved_at' => date('Y-m-d H:i:s'),
                    ]);

                    // credit merchant wallet
                    $w2 = Wallet::create([
                        'user_id' => $merchant->user_id,
                        'category' => 'loan-referral-commission',
                        'loan_id' => $loan->id,
                        'type' => 'credit',
                        'amount' => $lender->merchant_commission,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $data['staff_id'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 1,
                        'paid' => 1,
                    ]);

                    $notify_tc = doNotify($transaction->user_id, 'transactions', $transaction->id, 'loan_referral_commission', 'success', null, $transaction->lender_id, $data['staff']);


                }catch (\Exception $e) {
                    Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
                    return response()->json(['error' => 'network error, try again'], 500);
                }
            }

            DB::beginTransaction();
            try {
                $transaction = Transaction::create([
                    'user_id' => $merchant->user_id,
                    'loan_id' => $loan->id,
                    'office_id' => $loan->office_id,
                    'source' => strtoupper($loan->platform),
                    'amount' => $lender->merchant_commission,
                    'interest' => 0,
                    'principal' => 0,
                    'outst_amount' => 0,
                    'status' => 'Completed',
                    'description' => 'Loan Referral Commission',
                    'transaction_flag' => 'commission',
                    'lender_id' => $loan->lender_id,
                    'approved' => 1,
                    'approved_by' => $data['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                ]);

                // debit merchant wallet
                $w1 = Wallet::create([
                    'user_id' => $merchant->user_id,
                    'category' => 'loan-referral-commission',
                    'type' => 'debit',
                    'loan_id' => $loan->id,
                    'amount' => (-1 * $lender->merchant_commission),
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $data['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $loan->lender_id,
                    'is_lender' => 1,
                    'paid' => 1,
                ]);

                $notify_tc = doNotify($transaction->user_id, 'transactions', $transaction->id, 'loan_referral_commission', 'success', null, $transaction->lender_id, $data['staff']);

                $mpay->paid = 1;
                $mpay->paid_at = date('Y-m-d');
                $mpay->save();

                $loans = Loan::where('merchant_code', $merchant->merchant_code)->get(['id']);
                $approved_count = Loan::where('merchant_code', $merchant->merchant_code)->where('approved', 1)->count();
                $paid_count = MerchantPay::where('merchant_id', $merchant->id)->where('paid', 1)->count();

                DB::commit();

                $merch = (object)null;
                $merch->id = $merchant->id;
                $merch->approved_count = $approved_count;
                $merch->paid_count = $paid_count;
                $merch->count = MerchantPay::where('merchant_id', $merchant->id)->where('paid', 0)->count();

                return response()->json(compact('merch'), 200);
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
                return response()->json(['error' => 'network error, try again'], 500);
            }
        }else{
            return response()->json(['error' => 'Loan does not exist'], 500);
        }

    }
}
