/*!
 * Bootstrap v4.0.0-alpha.6 (https://getbootstrap.com)
 * Copyright 2011-2017 The Bootstrap Authors
 * Copyright 2011-2017 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */


/*! normalize.css v5.0.0 | MIT License | github.com/necolas/normalize.css */

html {
    font-family: sans-serif;
    line-height: 1.15;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%
}

body {
    margin: 0
}

article,
aside,
footer,
header,
nav,
section {
    display: block
}

h1 {
    font-size: 2em;
    margin: 0.67em 0
}

figcaption,
figure,
main {
    display: block
}

figure {
    margin: 1em 40px
}

hr {
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    height: 0;
    overflow: visible
}

pre {
    font-family: monospace, monospace;
    font-size: 1em
}

a {
    background-color: transparent;
    -webkit-text-decoration-skip: objects
}

a:active,
a:hover {
    outline-width: 0
}

abbr[title] {
    border-bottom: none;
    text-decoration: underline;
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

b,
strong {
    font-weight: inherit
}

b,
strong {
    font-weight: bolder
}

code,
kbd,
samp {
    font-family: monospace, monospace;
    font-size: 1em
}

dfn {
    font-style: italic
}

mark {
    background-color: #ff0;
    color: #000
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -0.25em
}

sup {
    top: -0.5em
}

audio,
video {
    display: inline-block
}

audio:not([controls]) {
    display: none;
    height: 0
}

img {
    border-style: none
}

svg:not(:root) {
    overflow: hidden
}

button,
input,
optgroup,
select,
textarea {
    font-family: sans-serif;
    font-size: 100%;
    line-height: 1.15;
    margin: 0
}

button,
input {
    overflow: visible
}

button,
select {
    text-transform: none
}

button,
html [type="button"],
[type="reset"],
[type="submit"] {
    -webkit-appearance: button
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
    border-style: none;
    padding: 0
}

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
    outline: 1px dotted ButtonText
}

fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em
}

legend {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: inherit;
    display: table;
    max-width: 100%;
    padding: 0;
    white-space: normal
}

progress {
    display: inline-block;
    vertical-align: baseline
}

textarea {
    overflow: auto
}

[type="checkbox"],
[type="radio"] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0
}

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
    height: auto
}

[type="search"] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

details,
menu {
    display: block
}

summary {
    display: list-item
}

canvas {
    display: inline-block
}

template {
    display: none
}

[hidden] {
    display: none
}

@media print {
    *,
    *::before,
    *::after,
    p::first-letter,
    div::first-letter,
    blockquote::first-letter,
    li::first-letter,
    p::first-line,
    div::first-line,
    blockquote::first-line,
    li::first-line {
        text-shadow: none !important;
        -webkit-box-shadow: none !important;
        box-shadow: none !important
    }
    a,
    a:visited {
        text-decoration: underline
    }
    abbr[title]::after {
        content: " (" attr(title) ")"
    }
    pre {
        white-space: pre-wrap !important
    }
    pre,
    blockquote {
        border: 1px solid #999;
        page-break-inside: avoid
    }
    thead {
        display: table-header-group
    }
    tr,
    img {
        page-break-inside: avoid
    }
    p,
    h2,
    h3 {
        orphans: 3;
        widows: 3
    }
    h2,
    h3 {
        page-break-after: avoid
    }
    .navbar {
        display: none
    }
    .badge {
        border: 1px solid #000
    }
    .table {
        border-collapse: collapse !important
    }
    .table td,
    .table th {
        background-color: #fff !important
    }
    .table-bordered th,
    .table-bordered td {
        border: 1px solid #ddd !important
    }
}

html {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

*,
*::before,
*::after {
    -webkit-box-sizing: inherit;
    box-sizing: inherit
}

@-ms-viewport {
    width: device-width
}

html {
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: transparent
}

body {
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #3E4B5B;
    background-color: #f8faff
}

[tabindex="-1"]:focus {
    outline: none !important
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin-top: 0;
    margin-bottom: .5rem
}

p {
    margin-top: 0;
    margin-bottom: 1rem
}

abbr[title],
abbr[data-original-title] {
    cursor: help
}

address {
    margin-bottom: 1rem;
    font-style: normal;
    line-height: inherit
}

ol,
ul,
dl {
    margin-top: 0;
    margin-bottom: 1rem
}

ol ol,
ul ul,
ol ul,
ul ol {
    margin-bottom: 0
}

dt {
    font-weight: 500
}

dd {
    margin-bottom: .5rem;
    margin-left: 0
}

blockquote {
    margin: 0 0 1rem
}

a {
    color: #3b75e3;
    text-decoration: none
}

a:focus,
a:hover {
    color: #1a50b7;
    text-decoration: underline
}

a:not([href]):not([tabindex]) {
    color: inherit;
    text-decoration: none
}

a:not([href]):not([tabindex]):focus,
a:not([href]):not([tabindex]):hover {
    color: inherit;
    text-decoration: none
}

a:not([href]):not([tabindex]):focus {
    outline: 0
}

pre {
    margin-top: 0;
    margin-bottom: 1rem;
    overflow: auto
}

figure {
    margin: 0 0 1rem
}

img {
    vertical-align: middle
}

[role="button"] {
    cursor: pointer
}

a,
area,
button,
[role="button"],
input,
label,
select,
summary,
textarea {
    -ms-touch-action: manipulation;
    touch-action: manipulation
}

table {
    border-collapse: collapse;
    background-color: transparent
}

caption {
    padding-top: 0.8rem 0.9rem;
    padding-bottom: 0.8rem 0.9rem;
    color: #aaa;
    text-align: left;
    caption-side: bottom
}

th {
    text-align: left
}

label {
    display: inline-block;
    margin-bottom: .5rem
}

button:focus {
    outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color
}

input,
button,
select,
textarea {
    line-height: inherit
}

input[type="radio"]:disabled,
input[type="checkbox"]:disabled {
    cursor: not-allowed
}

input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
    -webkit-appearance: listbox
}

textarea {
    resize: vertical
}

fieldset {
    min-width: 0;
    padding: 0;
    margin: 0;
    border: 0
}

legend {
    display: block;
    width: 100%;
    padding: 0;
    margin-bottom: .5rem;
    font-size: 1.5rem;
    line-height: inherit
}

input[type="search"] {
    -webkit-appearance: none
}

output {
    display: inline-block
}

[hidden] {
    display: none !important
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
    margin-bottom: .5rem;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500;
    line-height: 1.1;
    color: #334152
}

h1,
.h1 {
    font-size: 2.3rem
}

h2,
.h2 {
    font-size: 1.8rem
}

h3,
.h3 {
    font-size: 1.75rem
}

h4,
.h4 {
    font-size: 1.5rem
}

h5,
.h5 {
    font-size: 1.25rem
}

h6,
.h6 {
    font-size: 1rem
}

.lead {
    font-size: 1.25rem;
    font-weight: 300
}

.display-1 {
    font-size: 6rem;
    font-weight: 300;
    line-height: 1.1
}

.display-2 {
    font-size: 5.5rem;
    font-weight: 300;
    line-height: 1.1
}

.display-3 {
    font-size: 4.5rem;
    font-weight: 300;
    line-height: 1.1
}

.display-4 {
    font-size: 3.5rem;
    font-weight: 300;
    line-height: 1.1
}

hr {
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

small,
.small {
    font-size: 80%;
    font-weight: 400
}

mark,
.mark {
    padding: .2em;
    background-color: #fcf8e3
}

.list-unstyled {
    padding-left: 0;
    list-style: none
}

.list-inline {
    padding-left: 0;
    list-style: none
}

.list-inline-item {
    display: inline-block
}

.list-inline-item:not(:last-child) {
    margin-right: 5px
}

.initialism {
    font-size: 90%;
    text-transform: uppercase
}

.blockquote {
    padding: .5rem 1rem;
    margin-bottom: 1rem;
    font-size: 1.125rem;
    border-left: .25rem solid #eceeef
}

.blockquote-footer {
    display: block;
    font-size: 80%;
    color: #636c72
}

.blockquote-footer::before {
    content: "\2014 \00A0"
}

.blockquote-reverse {
    padding-right: 1rem;
    padding-left: 0;
    text-align: right;
    border-right: .25rem solid #eceeef;
    border-left: 0
}

.blockquote-reverse .blockquote-footer::before {
    content: ""
}

.blockquote-reverse .blockquote-footer::after {
    content: "\00A0 \2014"
}

.img-fluid {
    max-width: 100%;
    height: auto
}

.img-thumbnail {
    padding: .25rem;
    background-color: #f8faff;
    border: 1px solid #ddd;
    border-radius: .25rem;
    -webkit-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    max-width: 100%;
    height: auto
}

.figure {
    display: inline-block
}

.figure-img {
    margin-bottom: .5rem;
    line-height: 1
}

.figure-caption {
    font-size: 90%;
    color: #636c72
}

code,
kbd,
pre,
samp {
    font-family: Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace
}

code {
    padding: .2rem .4rem;
    font-size: 90%;
    color: #5782e4;
    background-color: #f2f7ff;
    border-radius: .25rem
}

a>code {
    padding: 0;
    color: inherit;
    background-color: inherit
}

kbd {
    padding: .2rem .4rem;
    font-size: 90%;
    color: #fff;
    background-color: #292b2c;
    border-radius: .2rem
}

kbd kbd {
    padding: 0;
    font-size: 100%;
    font-weight: 500
}

pre {
    display: block;
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 90%;
    color: #292b2c
}

pre code {
    padding: 0;
    font-size: inherit;
    color: inherit;
    background-color: transparent;
    border-radius: 0
}

.pre-scrollable {
    max-height: 340px;
    overflow-y: scroll
}

.container {
    position: relative;
    margin-left: auto;
    margin-right: auto;
    padding-right: 15px;
    padding-left: 15px
}

@media (min-width: 576px) {
    .container {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 768px) {
    .container {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 992px) {
    .container {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 1200px) {
    .container {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 1450px) {
    .container {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 576px) {
    .container {
        width: 540px;
        max-width: 100%
    }
}

@media (min-width: 768px) {
    .container {
        width: 720px;
        max-width: 100%
    }
}

@media (min-width: 992px) {
    .container {
        width: 960px;
        max-width: 100%
    }
}

@media (min-width: 1200px) {
    .container {
        width: 1140px;
        max-width: 100%
    }
}

@media (min-width: 1450px) {
    .container {
        width: 1380px;
        max-width: 100%
    }
}

.container-fluid {
    position: relative;
    margin-left: auto;
    margin-right: auto;
    padding-right: 15px;
    padding-left: 15px
}

@media (min-width: 576px) {
    .container-fluid {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 768px) {
    .container-fluid {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 992px) {
    .container-fluid {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 1200px) {
    .container-fluid {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 1450px) {
    .container-fluid {
        padding-right: 15px;
        padding-left: 15px
    }
}

.row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px
}

@media (min-width: 576px) {
    .row {
        margin-right: -15px;
        margin-left: -15px
    }
}

@media (min-width: 768px) {
    .row {
        margin-right: -15px;
        margin-left: -15px
    }
}

@media (min-width: 992px) {
    .row {
        margin-right: -15px;
        margin-left: -15px
    }
}

@media (min-width: 1200px) {
    .row {
        margin-right: -15px;
        margin-left: -15px
    }
}

@media (min-width: 1450px) {
    .row {
        margin-right: -15px;
        margin-left: -15px
    }
}

.no-gutters {
    margin-right: 0;
    margin-left: 0
}

.no-gutters>.col,
.no-gutters>[class*="col-"] {
    padding-right: 0;
    padding-left: 0
}

.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12,
.col,
.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm,
.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md,
.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg,
.col-xl-1,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl,
.col-xxl-1,
.col-xxl-2,
.col-xxl-3,
.col-xxl-4,
.col-xxl-5,
.col-xxl-6,
.col-xxl-7,
.col-xxl-8,
.col-xxl-9,
.col-xxl-10,
.col-xxl-11,
.col-xxl-12,
.col-xxl {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px
}

@media (min-width: 576px) {
    .col-1,
    .col-2,
    .col-3,
    .col-4,
    .col-5,
    .col-6,
    .col-7,
    .col-8,
    .col-9,
    .col-10,
    .col-11,
    .col-12,
    .col,
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12,
    .col-sm,
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12,
    .col-md,
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12,
    .col-lg,
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12,
    .col-xl,
    .col-xxl-1,
    .col-xxl-2,
    .col-xxl-3,
    .col-xxl-4,
    .col-xxl-5,
    .col-xxl-6,
    .col-xxl-7,
    .col-xxl-8,
    .col-xxl-9,
    .col-xxl-10,
    .col-xxl-11,
    .col-xxl-12,
    .col-xxl {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 768px) {
    .col-1,
    .col-2,
    .col-3,
    .col-4,
    .col-5,
    .col-6,
    .col-7,
    .col-8,
    .col-9,
    .col-10,
    .col-11,
    .col-12,
    .col,
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12,
    .col-sm,
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12,
    .col-md,
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12,
    .col-lg,
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12,
    .col-xl,
    .col-xxl-1,
    .col-xxl-2,
    .col-xxl-3,
    .col-xxl-4,
    .col-xxl-5,
    .col-xxl-6,
    .col-xxl-7,
    .col-xxl-8,
    .col-xxl-9,
    .col-xxl-10,
    .col-xxl-11,
    .col-xxl-12,
    .col-xxl {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 992px) {
    .col-1,
    .col-2,
    .col-3,
    .col-4,
    .col-5,
    .col-6,
    .col-7,
    .col-8,
    .col-9,
    .col-10,
    .col-11,
    .col-12,
    .col,
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12,
    .col-sm,
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12,
    .col-md,
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12,
    .col-lg,
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12,
    .col-xl,
    .col-xxl-1,
    .col-xxl-2,
    .col-xxl-3,
    .col-xxl-4,
    .col-xxl-5,
    .col-xxl-6,
    .col-xxl-7,
    .col-xxl-8,
    .col-xxl-9,
    .col-xxl-10,
    .col-xxl-11,
    .col-xxl-12,
    .col-xxl {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 1200px) {
    .col-1,
    .col-2,
    .col-3,
    .col-4,
    .col-5,
    .col-6,
    .col-7,
    .col-8,
    .col-9,
    .col-10,
    .col-11,
    .col-12,
    .col,
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12,
    .col-sm,
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12,
    .col-md,
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12,
    .col-lg,
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12,
    .col-xl,
    .col-xxl-1,
    .col-xxl-2,
    .col-xxl-3,
    .col-xxl-4,
    .col-xxl-5,
    .col-xxl-6,
    .col-xxl-7,
    .col-xxl-8,
    .col-xxl-9,
    .col-xxl-10,
    .col-xxl-11,
    .col-xxl-12,
    .col-xxl {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 1450px) {
    .col-1,
    .col-2,
    .col-3,
    .col-4,
    .col-5,
    .col-6,
    .col-7,
    .col-8,
    .col-9,
    .col-10,
    .col-11,
    .col-12,
    .col,
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12,
    .col-sm,
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12,
    .col-md,
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12,
    .col-lg,
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12,
    .col-xl,
    .col-xxl-1,
    .col-xxl-2,
    .col-xxl-3,
    .col-xxl-4,
    .col-xxl-5,
    .col-xxl-6,
    .col-xxl-7,
    .col-xxl-8,
    .col-xxl-9,
    .col-xxl-10,
    .col-xxl-11,
    .col-xxl-12,
    .col-xxl {
        padding-right: 15px;
        padding-left: 15px
    }
}

.col {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%
}

.col-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto
}

.col-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 8.3333333333%;
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%
}

.col-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 16.6666666667%;
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%
}

.col-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%
}

.col-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.3333333333%;
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%
}

.col-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 41.6666666667%;
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%
}

.col-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%
}

.col-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 58.3333333333%;
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%
}

.col-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.6666666667%;
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%
}

.col-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%
}

.col-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 83.3333333333%;
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%
}

.col-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 91.6666666667%;
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%
}

.col-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%
}

.pull-0 {
    right: auto
}

.pull-1 {
    right: 8.3333333333%
}

.pull-2 {
    right: 16.6666666667%
}

.pull-3 {
    right: 25%
}

.pull-4 {
    right: 33.3333333333%
}

.pull-5 {
    right: 41.6666666667%
}

.pull-6 {
    right: 50%
}

.pull-7 {
    right: 58.3333333333%
}

.pull-8 {
    right: 66.6666666667%
}

.pull-9 {
    right: 75%
}

.pull-10 {
    right: 83.3333333333%
}

.pull-11 {
    right: 91.6666666667%
}

.pull-12 {
    right: 100%
}

.push-0 {
    left: auto
}

.push-1 {
    left: 8.3333333333%
}

.push-2 {
    left: 16.6666666667%
}

.push-3 {
    left: 25%
}

.push-4 {
    left: 33.3333333333%
}

.push-5 {
    left: 41.6666666667%
}

.push-6 {
    left: 50%
}

.push-7 {
    left: 58.3333333333%
}

.push-8 {
    left: 66.6666666667%
}

.push-9 {
    left: 75%
}

.push-10 {
    left: 83.3333333333%
}

.push-11 {
    left: 91.6666666667%
}

.push-12 {
    left: 100%
}

.offset-1 {
    margin-left: 8.3333333333%
}

.offset-2 {
    margin-left: 16.6666666667%
}

.offset-3 {
    margin-left: 25%
}

.offset-4 {
    margin-left: 33.3333333333%
}

.offset-5 {
    margin-left: 41.6666666667%
}

.offset-6 {
    margin-left: 50%
}

.offset-7 {
    margin-left: 58.3333333333%
}

.offset-8 {
    margin-left: 66.6666666667%
}

.offset-9 {
    margin-left: 75%
}

.offset-10 {
    margin-left: 83.3333333333%
}

.offset-11 {
    margin-left: 91.6666666667%
}

@media (min-width: 576px) {
    .col-sm {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }
    .col-sm-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }
    .col-sm-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.3333333333%;
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }
    .col-sm-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.6666666667%;
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }
    .col-sm-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }
    .col-sm-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3333333333%;
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }
    .col-sm-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.6666666667%;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }
    .col-sm-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }
    .col-sm-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.3333333333%;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }
    .col-sm-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.6666666667%;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }
    .col-sm-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }
    .col-sm-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.3333333333%;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }
    .col-sm-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.6666666667%;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }
    .col-sm-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }
    .pull-sm-0 {
        right: auto
    }
    .pull-sm-1 {
        right: 8.3333333333%
    }
    .pull-sm-2 {
        right: 16.6666666667%
    }
    .pull-sm-3 {
        right: 25%
    }
    .pull-sm-4 {
        right: 33.3333333333%
    }
    .pull-sm-5 {
        right: 41.6666666667%
    }
    .pull-sm-6 {
        right: 50%
    }
    .pull-sm-7 {
        right: 58.3333333333%
    }
    .pull-sm-8 {
        right: 66.6666666667%
    }
    .pull-sm-9 {
        right: 75%
    }
    .pull-sm-10 {
        right: 83.3333333333%
    }
    .pull-sm-11 {
        right: 91.6666666667%
    }
    .pull-sm-12 {
        right: 100%
    }
    .push-sm-0 {
        left: auto
    }
    .push-sm-1 {
        left: 8.3333333333%
    }
    .push-sm-2 {
        left: 16.6666666667%
    }
    .push-sm-3 {
        left: 25%
    }
    .push-sm-4 {
        left: 33.3333333333%
    }
    .push-sm-5 {
        left: 41.6666666667%
    }
    .push-sm-6 {
        left: 50%
    }
    .push-sm-7 {
        left: 58.3333333333%
    }
    .push-sm-8 {
        left: 66.6666666667%
    }
    .push-sm-9 {
        left: 75%
    }
    .push-sm-10 {
        left: 83.3333333333%
    }
    .push-sm-11 {
        left: 91.6666666667%
    }
    .push-sm-12 {
        left: 100%
    }
    .offset-sm-0 {
        margin-left: 0%
    }
    .offset-sm-1 {
        margin-left: 8.3333333333%
    }
    .offset-sm-2 {
        margin-left: 16.6666666667%
    }
    .offset-sm-3 {
        margin-left: 25%
    }
    .offset-sm-4 {
        margin-left: 33.3333333333%
    }
    .offset-sm-5 {
        margin-left: 41.6666666667%
    }
    .offset-sm-6 {
        margin-left: 50%
    }
    .offset-sm-7 {
        margin-left: 58.3333333333%
    }
    .offset-sm-8 {
        margin-left: 66.6666666667%
    }
    .offset-sm-9 {
        margin-left: 75%
    }
    .offset-sm-10 {
        margin-left: 83.3333333333%
    }
    .offset-sm-11 {
        margin-left: 91.6666666667%
    }
}

@media (min-width: 768px) {
    .col-md {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }
    .col-md-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }
    .col-md-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.3333333333%;
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }
    .col-md-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.6666666667%;
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }
    .col-md-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }
    .col-md-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3333333333%;
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }
    .col-md-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.6666666667%;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }
    .col-md-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }
    .col-md-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.3333333333%;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }
    .col-md-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.6666666667%;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }
    .col-md-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }
    .col-md-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.3333333333%;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }
    .col-md-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.6666666667%;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }
    .col-md-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }
    .pull-md-0 {
        right: auto
    }
    .pull-md-1 {
        right: 8.3333333333%
    }
    .pull-md-2 {
        right: 16.6666666667%
    }
    .pull-md-3 {
        right: 25%
    }
    .pull-md-4 {
        right: 33.3333333333%
    }
    .pull-md-5 {
        right: 41.6666666667%
    }
    .pull-md-6 {
        right: 50%
    }
    .pull-md-7 {
        right: 58.3333333333%
    }
    .pull-md-8 {
        right: 66.6666666667%
    }
    .pull-md-9 {
        right: 75%
    }
    .pull-md-10 {
        right: 83.3333333333%
    }
    .pull-md-11 {
        right: 91.6666666667%
    }
    .pull-md-12 {
        right: 100%
    }
    .push-md-0 {
        left: auto
    }
    .push-md-1 {
        left: 8.3333333333%
    }
    .push-md-2 {
        left: 16.6666666667%
    }
    .push-md-3 {
        left: 25%
    }
    .push-md-4 {
        left: 33.3333333333%
    }
    .push-md-5 {
        left: 41.6666666667%
    }
    .push-md-6 {
        left: 50%
    }
    .push-md-7 {
        left: 58.3333333333%
    }
    .push-md-8 {
        left: 66.6666666667%
    }
    .push-md-9 {
        left: 75%
    }
    .push-md-10 {
        left: 83.3333333333%
    }
    .push-md-11 {
        left: 91.6666666667%
    }
    .push-md-12 {
        left: 100%
    }
    .offset-md-0 {
        margin-left: 0%
    }
    .offset-md-1 {
        margin-left: 8.3333333333%
    }
    .offset-md-2 {
        margin-left: 16.6666666667%
    }
    .offset-md-3 {
        margin-left: 25%
    }
    .offset-md-4 {
        margin-left: 33.3333333333%
    }
    .offset-md-5 {
        margin-left: 41.6666666667%
    }
    .offset-md-6 {
        margin-left: 50%
    }
    .offset-md-7 {
        margin-left: 58.3333333333%
    }
    .offset-md-8 {
        margin-left: 66.6666666667%
    }
    .offset-md-9 {
        margin-left: 75%
    }
    .offset-md-10 {
        margin-left: 83.3333333333%
    }
    .offset-md-11 {
        margin-left: 91.6666666667%
    }
}

@media (min-width: 992px) {
    .col-lg {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }
    .col-lg-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }
    .col-lg-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.3333333333%;
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }
    .col-lg-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.6666666667%;
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }
    .col-lg-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }
    .col-lg-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3333333333%;
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }
    .col-lg-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.6666666667%;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }
    .col-lg-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }
    .col-lg-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.3333333333%;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }
    .col-lg-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.6666666667%;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }
    .col-lg-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }
    .col-lg-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.3333333333%;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }
    .col-lg-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.6666666667%;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }
    .col-lg-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }
    .pull-lg-0 {
        right: auto
    }
    .pull-lg-1 {
        right: 8.3333333333%
    }
    .pull-lg-2 {
        right: 16.6666666667%
    }
    .pull-lg-3 {
        right: 25%
    }
    .pull-lg-4 {
        right: 33.3333333333%
    }
    .pull-lg-5 {
        right: 41.6666666667%
    }
    .pull-lg-6 {
        right: 50%
    }
    .pull-lg-7 {
        right: 58.3333333333%
    }
    .pull-lg-8 {
        right: 66.6666666667%
    }
    .pull-lg-9 {
        right: 75%
    }
    .pull-lg-10 {
        right: 83.3333333333%
    }
    .pull-lg-11 {
        right: 91.6666666667%
    }
    .pull-lg-12 {
        right: 100%
    }
    .push-lg-0 {
        left: auto
    }
    .push-lg-1 {
        left: 8.3333333333%
    }
    .push-lg-2 {
        left: 16.6666666667%
    }
    .push-lg-3 {
        left: 25%
    }
    .push-lg-4 {
        left: 33.3333333333%
    }
    .push-lg-5 {
        left: 41.6666666667%
    }
    .push-lg-6 {
        left: 50%
    }
    .push-lg-7 {
        left: 58.3333333333%
    }
    .push-lg-8 {
        left: 66.6666666667%
    }
    .push-lg-9 {
        left: 75%
    }
    .push-lg-10 {
        left: 83.3333333333%
    }
    .push-lg-11 {
        left: 91.6666666667%
    }
    .push-lg-12 {
        left: 100%
    }
    .offset-lg-0 {
        margin-left: 0%
    }
    .offset-lg-1 {
        margin-left: 8.3333333333%
    }
    .offset-lg-2 {
        margin-left: 16.6666666667%
    }
    .offset-lg-3 {
        margin-left: 25%
    }
    .offset-lg-4 {
        margin-left: 33.3333333333%
    }
    .offset-lg-5 {
        margin-left: 41.6666666667%
    }
    .offset-lg-6 {
        margin-left: 50%
    }
    .offset-lg-7 {
        margin-left: 58.3333333333%
    }
    .offset-lg-8 {
        margin-left: 66.6666666667%
    }
    .offset-lg-9 {
        margin-left: 75%
    }
    .offset-lg-10 {
        margin-left: 83.3333333333%
    }
    .offset-lg-11 {
        margin-left: 91.6666666667%
    }
}

@media (min-width: 1200px) {
    .col-xl {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }
    .col-xl-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }
    .col-xl-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.3333333333%;
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }
    .col-xl-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.6666666667%;
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }
    .col-xl-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }
    .col-xl-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3333333333%;
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }
    .col-xl-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.6666666667%;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }
    .col-xl-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }
    .col-xl-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.3333333333%;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }
    .col-xl-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.6666666667%;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }
    .col-xl-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }
    .col-xl-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.3333333333%;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }
    .col-xl-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.6666666667%;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }
    .col-xl-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }
    .pull-xl-0 {
        right: auto
    }
    .pull-xl-1 {
        right: 8.3333333333%
    }
    .pull-xl-2 {
        right: 16.6666666667%
    }
    .pull-xl-3 {
        right: 25%
    }
    .pull-xl-4 {
        right: 33.3333333333%
    }
    .pull-xl-5 {
        right: 41.6666666667%
    }
    .pull-xl-6 {
        right: 50%
    }
    .pull-xl-7 {
        right: 58.3333333333%
    }
    .pull-xl-8 {
        right: 66.6666666667%
    }
    .pull-xl-9 {
        right: 75%
    }
    .pull-xl-10 {
        right: 83.3333333333%
    }
    .pull-xl-11 {
        right: 91.6666666667%
    }
    .pull-xl-12 {
        right: 100%
    }
    .push-xl-0 {
        left: auto
    }
    .push-xl-1 {
        left: 8.3333333333%
    }
    .push-xl-2 {
        left: 16.6666666667%
    }
    .push-xl-3 {
        left: 25%
    }
    .push-xl-4 {
        left: 33.3333333333%
    }
    .push-xl-5 {
        left: 41.6666666667%
    }
    .push-xl-6 {
        left: 50%
    }
    .push-xl-7 {
        left: 58.3333333333%
    }
    .push-xl-8 {
        left: 66.6666666667%
    }
    .push-xl-9 {
        left: 75%
    }
    .push-xl-10 {
        left: 83.3333333333%
    }
    .push-xl-11 {
        left: 91.6666666667%
    }
    .push-xl-12 {
        left: 100%
    }
    .offset-xl-0 {
        margin-left: 0%
    }
    .offset-xl-1 {
        margin-left: 8.3333333333%
    }
    .offset-xl-2 {
        margin-left: 16.6666666667%
    }
    .offset-xl-3 {
        margin-left: 25%
    }
    .offset-xl-4 {
        margin-left: 33.3333333333%
    }
    .offset-xl-5 {
        margin-left: 41.6666666667%
    }
    .offset-xl-6 {
        margin-left: 50%
    }
    .offset-xl-7 {
        margin-left: 58.3333333333%
    }
    .offset-xl-8 {
        margin-left: 66.6666666667%
    }
    .offset-xl-9 {
        margin-left: 75%
    }
    .offset-xl-10 {
        margin-left: 83.3333333333%
    }
    .offset-xl-11 {
        margin-left: 91.6666666667%
    }
}

@media (min-width: 1450px) {
    .col-xxl {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }
    .col-xxl-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }
    .col-xxl-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.3333333333%;
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }
    .col-xxl-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.6666666667%;
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }
    .col-xxl-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }
    .col-xxl-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3333333333%;
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }
    .col-xxl-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.6666666667%;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }
    .col-xxl-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }
    .col-xxl-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.3333333333%;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }
    .col-xxl-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.6666666667%;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }
    .col-xxl-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }
    .col-xxl-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.3333333333%;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }
    .col-xxl-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.6666666667%;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }
    .col-xxl-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }
    .pull-xxl-0 {
        right: auto
    }
    .pull-xxl-1 {
        right: 8.3333333333%
    }
    .pull-xxl-2 {
        right: 16.6666666667%
    }
    .pull-xxl-3 {
        right: 25%
    }
    .pull-xxl-4 {
        right: 33.3333333333%
    }
    .pull-xxl-5 {
        right: 41.6666666667%
    }
    .pull-xxl-6 {
        right: 50%
    }
    .pull-xxl-7 {
        right: 58.3333333333%
    }
    .pull-xxl-8 {
        right: 66.6666666667%
    }
    .pull-xxl-9 {
        right: 75%
    }
    .pull-xxl-10 {
        right: 83.3333333333%
    }
    .pull-xxl-11 {
        right: 91.6666666667%
    }
    .pull-xxl-12 {
        right: 100%
    }
    .push-xxl-0 {
        left: auto
    }
    .push-xxl-1 {
        left: 8.3333333333%
    }
    .push-xxl-2 {
        left: 16.6666666667%
    }
    .push-xxl-3 {
        left: 25%
    }
    .push-xxl-4 {
        left: 33.3333333333%
    }
    .push-xxl-5 {
        left: 41.6666666667%
    }
    .push-xxl-6 {
        left: 50%
    }
    .push-xxl-7 {
        left: 58.3333333333%
    }
    .push-xxl-8 {
        left: 66.6666666667%
    }
    .push-xxl-9 {
        left: 75%
    }
    .push-xxl-10 {
        left: 83.3333333333%
    }
    .push-xxl-11 {
        left: 91.6666666667%
    }
    .push-xxl-12 {
        left: 100%
    }
    .offset-xxl-0 {
        margin-left: 0%
    }
    .offset-xxl-1 {
        margin-left: 8.3333333333%
    }
    .offset-xxl-2 {
        margin-left: 16.6666666667%
    }
    .offset-xxl-3 {
        margin-left: 25%
    }
    .offset-xxl-4 {
        margin-left: 33.3333333333%
    }
    .offset-xxl-5 {
        margin-left: 41.6666666667%
    }
    .offset-xxl-6 {
        margin-left: 50%
    }
    .offset-xxl-7 {
        margin-left: 58.3333333333%
    }
    .offset-xxl-8 {
        margin-left: 66.6666666667%
    }
    .offset-xxl-9 {
        margin-left: 75%
    }
    .offset-xxl-10 {
        margin-left: 83.3333333333%
    }
    .offset-xxl-11 {
        margin-left: 91.6666666667%
    }
}

.table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 1rem
}

.table th,
.table td {
    padding: 0.8rem 0.9rem;
    vertical-align: top;
    border-top: 1px solid rgba(83, 101, 140, 0.33)
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid rgba(83, 101, 140, 0.33)
}

.table tbody+tbody {
    border-top: 2px solid rgba(83, 101, 140, 0.33)
}

.table .table {
    background-color: #f8faff
}

.table-sm th,
.table-sm td {
    padding: .3rem
}

.table-bordered {
    border: 1px solid rgba(83, 101, 140, 0.33)
}

.table-bordered th,
.table-bordered td {
    border: 1px solid rgba(83, 101, 140, 0.33)
}

.table-bordered thead th,
.table-bordered thead td {
    border-bottom-width: 2px
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(94, 130, 152, 0.05)
}

.table-hover tbody tr:hover {
    background-color: rgba(18, 95, 142, 0.075)
}

.table-active,
.table-active>th,
.table-active>td {
    background-color: rgba(18, 95, 142, 0.075)
}

.table-hover .table-active:hover {
    background-color: rgba(15, 80, 119, 0.075)
}

.table-hover .table-active:hover>td,
.table-hover .table-active:hover>th {
    background-color: rgba(15, 80, 119, 0.075)
}

.table-success,
.table-success>th,
.table-success>td {
    background-color: #dff0d8
}

.table-hover .table-success:hover {
    background-color: #d0e9c6
}

.table-hover .table-success:hover>td,
.table-hover .table-success:hover>th {
    background-color: #d0e9c6
}

.table-info,
.table-info>th,
.table-info>td {
    background-color: #d9edf7
}

.table-hover .table-info:hover {
    background-color: #c4e3f3
}

.table-hover .table-info:hover>td,
.table-hover .table-info:hover>th {
    background-color: #c4e3f3
}

.table-warning,
.table-warning>th,
.table-warning>td {
    background-color: #fcf8e3
}

.table-hover .table-warning:hover {
    background-color: #faf2cc
}

.table-hover .table-warning:hover>td,
.table-hover .table-warning:hover>th {
    background-color: #faf2cc
}

.table-danger,
.table-danger>th,
.table-danger>td {
    background-color: #f2dede
}

.table-hover .table-danger:hover {
    background-color: #ebcccc
}

.table-hover .table-danger:hover>td,
.table-hover .table-danger:hover>th {
    background-color: #ebcccc
}

.thead-inverse th {
    color: #f8faff;
    background-color: #292b2c
}

.thead-default th {
    color: #464a4c;
    background-color: #eceeef
}

.table-inverse {
    color: #f8faff;
    background-color: #292b2c
}

.table-inverse th,
.table-inverse td,
.table-inverse thead th {
    border-color: #f8faff
}

.table-inverse.table-bordered {
    border: 0
}

.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -ms-overflow-style: -ms-autohiding-scrollbar
}

.table-responsive.table-bordered {
    border: 0
}

.form-control {
    display: block;
    width: 100%;
    padding: .5rem .7rem;
    font-size: .9rem;
    line-height: 1.25;
    color: #464a4c;
    background-color: #fff;
    background-image: none;
    background-clip: padding-box;
    border: 1px solid #cecece;
    border-radius: .25rem;
    -webkit-transition: border-color ease-in-out 0.15s, -webkit-box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, -webkit-box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s, -webkit-box-shadow ease-in-out 0.15s
}

.form-control::-ms-expand {
    background-color: transparent;
    border: 0
}

.form-control:focus {
    color: #464a4c;
    background-color: #fff;
    border-color: #023d7b;
    outline: none
}

.form-control::-webkit-input-placeholder {
    color: #636c72;
    opacity: 1
}

.form-control:-ms-input-placeholder {
    color: #636c72;
    opacity: 1
}

.form-control::placeholder {
    color: #636c72;
    opacity: 1
}

.form-control:disabled,
.form-control[readonly] {
    background-color: #eceeef;
    opacity: 1
}

.form-control:disabled {
    cursor: not-allowed
}

select.form-control:not([size]):not([multiple]) {
    height: calc(2.125rem + 2px)
}

select.form-control:focus::-ms-value {
    color: #464a4c;
    background-color: #fff
}

.form-control-file,
.form-control-range {
    display: block
}

.col-form-label {
    padding-top: calc(.5rem - 1px * 2);
    padding-bottom: calc(.5rem - 1px * 2);
    margin-bottom: 0
}

.col-form-label-lg {
    padding-top: calc(.75rem - 1px * 2);
    padding-bottom: calc(.75rem - 1px * 2);
    font-size: 1.25rem
}

.col-form-label-sm {
    padding-top: calc(.25rem - 1px * 2);
    padding-bottom: calc(.25rem - 1px * 2);
    font-size: .7rem
}

.col-form-legend {
    padding-top: .5rem;
    padding-bottom: .5rem;
    margin-bottom: 0;
    font-size: .9rem
}

.form-control-static {
    padding-top: .5rem;
    padding-bottom: .5rem;
    margin-bottom: 0;
    line-height: 1.25;
    border: solid transparent;
    border-width: 1px 0
}

.form-control-static.form-control-sm,
.input-group-sm>.form-control-static.form-control,
.input-group-sm>.form-control-static.input-group-addon,
.input-group-sm>.input-group-btn>.form-control-static.btn,
.wrapper-front .input-group-sm>.input-group-btn>.form-control-static.fc-button,
.all-wrapper .input-group-sm>.input-group-btn>.form-control-static.fc-button,
.form-control-static.form-control-lg,
.input-group-lg>.form-control-static.form-control,
.input-group-lg>.form-control-static.input-group-addon,
.input-group-lg>.input-group-btn>.form-control-static.btn,
.wrapper-front .input-group-lg>.input-group-btn>.form-control-static.fc-button,
.all-wrapper .input-group-lg>.input-group-btn>.form-control-static.fc-button {
    padding-right: 0;
    padding-left: 0
}

.form-control-sm,
.input-group-sm>.form-control,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.btn,
.wrapper-front .input-group-sm>.input-group-btn>.fc-button,
.all-wrapper .input-group-sm>.input-group-btn>.fc-button {
    padding: .25rem .7rem;
    font-size: .8rem;
    border-radius: .2rem
}

select.form-control-sm:not([size]):not([multiple]),
.input-group-sm>select.form-control:not([size]):not([multiple]),
.input-group-sm>select.input-group-addon:not([size]):not([multiple]),
.input-group-sm>.input-group-btn>select.btn:not([size]):not([multiple]),
.wrapper-front .input-group-sm>.input-group-btn>select.fc-button:not([size]):not([multiple]),
.all-wrapper .input-group-sm>.input-group-btn>select.fc-button:not([size]):not([multiple]) {
    height: 1.644rem
}

.form-control-lg,
.input-group-lg>.form-control,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.btn,
.wrapper-front .input-group-lg>.input-group-btn>.fc-button,
.all-wrapper .input-group-lg>.input-group-btn>.fc-button {
    padding: .75rem 1.5rem;
    font-size: 1.25rem;
    border-radius: .3rem
}

select.form-control-lg:not([size]):not([multiple]),
.input-group-lg>select.form-control:not([size]):not([multiple]),
.input-group-lg>select.input-group-addon:not([size]):not([multiple]),
.input-group-lg>.input-group-btn>select.btn:not([size]):not([multiple]),
.wrapper-front .input-group-lg>.input-group-btn>select.fc-button:not([size]):not([multiple]),
.all-wrapper .input-group-lg>.input-group-btn>select.fc-button:not([size]):not([multiple]) {
    height: 3.1666666667rem
}

.form-group {
    margin-bottom: 1rem
}

.form-text {
    display: block;
    margin-top: .25rem
}

.form-check {
    position: relative;
    display: block;
    margin-bottom: .5rem
}

.form-check.disabled .form-check-label {
    color: #aaa;
    cursor: not-allowed
}

.form-check-label {
    padding-left: 1.25rem;
    margin-bottom: 0;
    cursor: pointer
}

.form-check-input {
    position: absolute;
    margin-top: .25rem;
    margin-left: -1.25rem
}

.form-check-input:only-child {
    position: static
}

.form-check-inline {
    display: inline-block
}

.form-check-inline .form-check-label {
    vertical-align: middle
}

.form-check-inline+.form-check-inline {
    margin-left: .75rem
}

.form-control-feedback {
    margin-top: .25rem
}

.form-control-success,
.form-control-warning,
.form-control-danger {
    padding-right: 2.1rem;
    background-repeat: no-repeat;
    background-position: center right .53125rem;
    background-size: 1.0625rem 1.0625rem
}

.has-success .form-control-feedback,
.has-success .form-control-label,
.has-success .col-form-label,
.has-success .form-check-label,
.has-success .custom-control {
    color: #90be2e
}

.has-success .form-control {
    border-color: #90be2e
}

.has-success .input-group-addon {
    color: #90be2e;
    border-color: #90be2e;
    background-color: #e4f1c7
}

.has-success .form-control-success {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2390be2e' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3E%3C/svg%3E")
}

.has-warning .form-control-feedback,
.has-warning .form-control-label,
.has-warning .col-form-label,
.has-warning .form-check-label,
.has-warning .custom-control {
    color: #f0ad4e
}

.has-warning .form-control {
    border-color: #f0ad4e
}

.has-warning .input-group-addon {
    color: #f0ad4e;
    border-color: #f0ad4e;
    background-color: #fff
}

.has-warning .form-control-warning {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23f0ad4e' d='M4.4 5.324h-.8v-2.46h.8zm0 1.42h-.8V5.89h.8zM3.76.63L.04 7.075c-.115.2.016.425.26.426h7.397c.242 0 .372-.226.258-.426C6.726 4.924 5.47 2.79 4.253.63c-.113-.174-.39-.174-.494 0z'/%3E%3C/svg%3E")
}

.has-danger .form-control-feedback,
.has-danger .form-control-label,
.has-danger .col-form-label,
.has-danger .form-check-label,
.has-danger .custom-control {
    color: #e65252
}

.has-danger .form-control {
    border-color: #e65252
}

.has-danger .input-group-addon {
    color: #e65252;
    border-color: #e65252;
    background-color: #fff
}

.has-danger .form-control-danger {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23e65252' viewBox='-2 -2 7 7'%3E%3Cpath stroke='%23d9534f' d='M0 0l3 3m0-3L0 3'/%3E%3Ccircle r='.5'/%3E%3Ccircle cx='3' r='.5'/%3E%3Ccircle cy='3' r='.5'/%3E%3Ccircle cx='3' cy='3' r='.5'/%3E%3C/svg%3E")
}

.form-inline {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.form-inline .form-check {
    width: 100%
}

@media (min-width: 576px) {
    .form-inline label {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        margin-bottom: 0
    }
    .form-inline .form-group {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        margin-bottom: 0
    }
    .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }
    .form-inline .form-control-static {
        display: inline-block
    }
    .form-inline .input-group {
        width: auto
    }
    .form-inline .form-control-label {
        margin-bottom: 0;
        vertical-align: middle
    }
    .form-inline .form-check {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        width: auto;
        margin-top: 0;
        margin-bottom: 0
    }
    .form-inline .form-check-label {
        padding-left: 0
    }
    .form-inline .form-check-input {
        position: relative;
        margin-top: 0;
        margin-right: .25rem;
        margin-left: 0
    }
    .form-inline .custom-control {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        padding-left: 0
    }
    .form-inline .custom-control-indicator {
        position: static;
        display: inline-block;
        margin-right: .25rem;
        vertical-align: text-bottom
    }
    .form-inline .has-feedback .form-control-feedback {
        top: 0
    }
}

.btn,
.wrapper-front .fc-button {
    display: inline-block;
    font-weight: 400;
    line-height: 1.25;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid transparent;
    padding: .5rem 1.2rem;
    font-size: 1rem;
    border-radius: .25rem;
    -webkit-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out
}

.btn:focus,
.wrapper-front .fc-button:focus,
.all-wrapper .fc-button:focus,
.btn:hover,
.wrapper-front .fc-button:hover,
.all-wrapper .fc-button:hover {
    text-decoration: none
}

.btn:focus,
.wrapper-front .fc-button:focus,
.all-wrapper .fc-button:focus,
.btn.focus,
.wrapper-front .focus.fc-button,
.all-wrapper .focus.fc-button {
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.25);
    box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.25)
}

.btn.disabled,
.wrapper-front .disabled.fc-button,
.all-wrapper .disabled.fc-button,
.btn:disabled,
.wrapper-front .fc-button:disabled,
.all-wrapper .fc-button:disabled {
    cursor: not-allowed;
    opacity: .65
}

.btn:active,
.wrapper-front .fc-button:active,
.all-wrapper .fc-button:active,
.btn.active,
.wrapper-front .active.fc-button,
.all-wrapper .active.fc-button {
    background-image: none
}

a.btn.disabled,
.wrapper-front a.disabled.fc-button,
.all-wrapper a.disabled.fc-button,
fieldset[disabled] a.btn,
fieldset[disabled] .wrapper-front a.fc-button,
.wrapper-front fieldset[disabled] a.fc-button,
fieldset[disabled] .all-wrapper a.fc-button,
.all-wrapper fieldset[disabled] a.fc-button {
    pointer-events: none
}

.btn-primary,
.wrapper-front .fc-button.fc-state-active,
.all-wrapper .fc-button.fc-state-active {
    color: #fff;
    background-color: #047bf8;
    border-color: #047bf8
}

.btn-primary:hover,
.wrapper-front .fc-button.fc-state-active:hover,
.all-wrapper .fc-button.fc-state-active:hover {
    color: #fff;
    background-color: #0362c6;
    border-color: #035dbc
}

.btn-primary:focus,
.wrapper-front .fc-button.fc-state-active:focus,
.btn-primary.focus,
.wrapper-front .focus.fc-button.fc-state-active {
    -webkit-box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.5);
    box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.5)
}

.btn-primary.disabled,
.wrapper-front .disabled.fc-button.fc-state-active,
.btn-primary:disabled,
.wrapper-front .fc-button.fc-state-active:disabled {
    background-color: #047bf8;
    border-color: #047bf8
}

.btn-primary:active,
.wrapper-front .fc-button.fc-state-active:active,
.btn-primary.active,
.wrapper-front .active.fc-button.fc-state-active,
.show>.btn-primary.dropdown-toggle,
.wrapper-front .show>.dropdown-toggle.fc-button.fc-state-active {
    color: #fff;
    background-color: #0362c6;
    background-image: none;
    border-color: #035dbc
}

.btn-secondary {
    color: #fff;
    background-color: #383d4d;
    border-color: #383d4d
}

.btn-secondary:hover {
    color: #fff;
    background-color: #23262f;
    border-color: #1e212a
}

.btn-secondary:focus,
.btn-secondary.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(56, 61, 77, 0.5);
    box-shadow: 0 0 0 2px rgba(56, 61, 77, 0.5)
}

.btn-secondary.disabled,
.btn-secondary:disabled {
    background-color: #383d4d;
    border-color: #383d4d
}

.btn-secondary:active,
.btn-secondary.active,
.show>.btn-secondary.dropdown-toggle {
    color: #fff;
    background-color: #23262f;
    background-image: none;
    border-color: #1e212a
}

.btn-info {
    color: #fff;
    background-color: #5bc0de;
    border-color: #5bc0de
}

.btn-info:hover {
    color: #fff;
    background-color: #31b0d5;
    border-color: #2aabd2
}

.btn-info:focus,
.btn-info.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(91, 192, 222, 0.5);
    box-shadow: 0 0 0 2px rgba(91, 192, 222, 0.5)
}

.btn-info.disabled,
.btn-info:disabled {
    background-color: #5bc0de;
    border-color: #5bc0de
}

.btn-info:active,
.btn-info.active,
.show>.btn-info.dropdown-toggle {
    color: #fff;
    background-color: #31b0d5;
    background-image: none;
    border-color: #2aabd2
}

.btn-success {
    color: #fff;
    background-color: #90be2e;
    border-color: #90be2e
}

.btn-success:hover {
    color: #fff;
    background-color: #719524;
    border-color: #6b8d22
}

.btn-success:focus,
.btn-success.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(144, 190, 46, 0.5);
    box-shadow: 0 0 0 2px rgba(144, 190, 46, 0.5)
}

.btn-success.disabled,
.btn-success:disabled {
    background-color: #90be2e;
    border-color: #90be2e
}

.btn-success:active,
.btn-success.active,
.show>.btn-success.dropdown-toggle {
    color: #fff;
    background-color: #719524;
    background-image: none;
    border-color: #6b8d22
}

.btn-warning {
    color: #fff;
    background-color: #f0ad4e;
    border-color: #f0ad4e
}

.btn-warning:hover {
    color: #fff;
    background-color: #ec971f;
    border-color: #eb9316
}

.btn-warning:focus,
.btn-warning.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(240, 173, 78, 0.5);
    box-shadow: 0 0 0 2px rgba(240, 173, 78, 0.5)
}

.btn-warning.disabled,
.btn-warning:disabled {
    background-color: #f0ad4e;
    border-color: #f0ad4e
}

.btn-warning:active,
.btn-warning.active,
.show>.btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #ec971f;
    background-image: none;
    border-color: #eb9316
}

.btn-danger {
    color: #fff;
    background-color: #e65252;
    border-color: #e65252
}

.btn-danger:hover {
    color: #fff;
    background-color: #e02525;
    border-color: #db2020
}

.btn-danger:focus,
.btn-danger.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(230, 82, 82, 0.5);
    box-shadow: 0 0 0 2px rgba(230, 82, 82, 0.5)
}

.btn-danger.disabled,
.btn-danger:disabled {
    background-color: #e65252;
    border-color: #e65252
}

.btn-danger:active,
.btn-danger.active,
.show>.btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #e02525;
    background-image: none;
    border-color: #db2020
}

.btn-outline-primary {
    color: #047bf8;
    background-image: none;
    background-color: transparent;
    border-color: #047bf8
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: #047bf8;
    border-color: #047bf8
}

.btn-outline-primary:focus,
.btn-outline-primary.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.5);
    box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.5)
}

.btn-outline-primary.disabled,
.btn-outline-primary:disabled {
    color: #047bf8;
    background-color: transparent
}

.btn-outline-primary:active,
.btn-outline-primary.active,
.show>.btn-outline-primary.dropdown-toggle {
    color: #fff;
    background-color: #047bf8;
    border-color: #047bf8
}

.btn-outline-secondary {
    color: #383d4d;
    background-image: none;
    background-color: transparent;
    border-color: #383d4d
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #383d4d;
    border-color: #383d4d
}

.btn-outline-secondary:focus,
.btn-outline-secondary.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(56, 61, 77, 0.5);
    box-shadow: 0 0 0 2px rgba(56, 61, 77, 0.5)
}

.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
    color: #383d4d;
    background-color: transparent
}

.btn-outline-secondary:active,
.btn-outline-secondary.active,
.show>.btn-outline-secondary.dropdown-toggle {
    color: #fff;
    background-color: #383d4d;
    border-color: #383d4d
}

.btn-outline-info {
    color: #5bc0de;
    background-image: none;
    background-color: transparent;
    border-color: #5bc0de
}

.btn-outline-info:hover {
    color: #fff;
    background-color: #5bc0de;
    border-color: #5bc0de
}

.btn-outline-info:focus,
.btn-outline-info.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(91, 192, 222, 0.5);
    box-shadow: 0 0 0 2px rgba(91, 192, 222, 0.5)
}

.btn-outline-info.disabled,
.btn-outline-info:disabled {
    color: #5bc0de;
    background-color: transparent
}

.btn-outline-info:active,
.btn-outline-info.active,
.show>.btn-outline-info.dropdown-toggle {
    color: #fff;
    background-color: #5bc0de;
    border-color: #5bc0de
}

.btn-outline-success {
    color: #90be2e;
    background-image: none;
    background-color: transparent;
    border-color: #90be2e
}

.btn-outline-success:hover {
    color: #fff;
    background-color: #90be2e;
    border-color: #90be2e
}

.btn-outline-success:focus,
.btn-outline-success.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(144, 190, 46, 0.5);
    box-shadow: 0 0 0 2px rgba(144, 190, 46, 0.5)
}

.btn-outline-success.disabled,
.btn-outline-success:disabled {
    color: #90be2e;
    background-color: transparent
}

.btn-outline-success:active,
.btn-outline-success.active,
.show>.btn-outline-success.dropdown-toggle {
    color: #fff;
    background-color: #90be2e;
    border-color: #90be2e
}

.btn-outline-warning {
    color: #f0ad4e;
    background-image: none;
    background-color: transparent;
    border-color: #f0ad4e
}

.btn-outline-warning:hover {
    color: #fff;
    background-color: #f0ad4e;
    border-color: #f0ad4e
}

.btn-outline-warning:focus,
.btn-outline-warning.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(240, 173, 78, 0.5);
    box-shadow: 0 0 0 2px rgba(240, 173, 78, 0.5)
}

.btn-outline-warning.disabled,
.btn-outline-warning:disabled {
    color: #f0ad4e;
    background-color: transparent
}

.btn-outline-warning:active,
.btn-outline-warning.active,
.show>.btn-outline-warning.dropdown-toggle {
    color: #fff;
    background-color: #f0ad4e;
    border-color: #f0ad4e
}

.btn-outline-danger {
    color: #e65252;
    background-image: none;
    background-color: transparent;
    border-color: #e65252
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #e65252;
    border-color: #e65252
}

.btn-outline-danger:focus,
.btn-outline-danger.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(230, 82, 82, 0.5);
    box-shadow: 0 0 0 2px rgba(230, 82, 82, 0.5)
}

.btn-outline-danger.disabled,
.btn-outline-danger:disabled {
    color: #e65252;
    background-color: transparent
}

.btn-outline-danger:active,
.btn-outline-danger.active,
.show>.btn-outline-danger.dropdown-toggle {
    color: #fff;
    background-color: #e65252;
    border-color: #e65252
}

.btn-link {
    font-weight: 400;
    color: #3b75e3;
    border-radius: 0
}

.btn-link,
.btn-link:active,
.btn-link.active,
.btn-link:disabled {
    background-color: transparent
}

.btn-link,
.btn-link:focus,
.btn-link:active {
    border-color: transparent
}

.btn-link:hover {
    border-color: transparent
}

.btn-link:focus,
.btn-link:hover {
    color: #1a50b7;
    text-decoration: underline;
    background-color: transparent
}

.btn-link:disabled {
    color: #636c72
}

.btn-link:disabled:focus,
.btn-link:disabled:hover {
    text-decoration: none
}

.btn-lg,
.btn-group-lg>.btn,
.wrapper-front .btn-group-lg>.fc-button {
    padding: .75rem 1.5rem;
    font-size: 1.25rem;
    border-radius: .3rem
}

.btn-sm,
.btn-group-sm>.btn,
.wrapper-front .btn-group-sm>.fc-button {
    padding: .2rem .5rem;
    font-size: .7rem;
    border-radius: .2rem
}

.btn-block {
    display: block;
    width: 100%
}

.btn-block+.btn-block {
    margin-top: .5rem
}

input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
    width: 100%
}

.dropup,
.dropdown {
    position: relative
}

.dropdown-toggle::after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: .3em;
    vertical-align: middle;
    content: "";
    border-top: .3em solid;
    border-right: .3em solid transparent;
    border-left: .3em solid transparent
}

.dropdown-toggle:focus {
    outline: 0
}

.dropup .dropdown-toggle::after {
    border-top: 0;
    border-bottom: .3em solid
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 10rem;
    padding: .5rem 0;
    margin: .125rem 0 0;
    font-size: .9rem;
    color: #3E4B5B;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: .25rem
}

.dropdown-divider {
    height: 1px;
    margin: .5rem 0;
    overflow: hidden;
    background-color: #eceeef
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 3px 1.5rem;
    clear: both;
    font-weight: 400;
    color: #292b2c;
    text-align: inherit;
    white-space: nowrap;
    background: none;
    border: 0
}

.dropdown-item:focus,
.dropdown-item:hover {
    color: #1d1e1f;
    text-decoration: none;
    background-color: #f7f7f9
}

.dropdown-item.active,
.dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #047bf8
}

.dropdown-item.disabled,
.dropdown-item:disabled {
    color: #636c72;
    cursor: not-allowed;
    background-color: transparent
}

.show>.dropdown-menu {
    display: block
}

.show>a {
    outline: 0
}

.dropdown-menu-right {
    right: 0;
    left: auto
}

.dropdown-menu-left {
    right: auto;
    left: 0
}

.dropdown-header {
    display: block;
    padding: .5rem 1.5rem;
    margin-bottom: 0;
    font-size: .7rem;
    color: #636c72;
    white-space: nowrap
}

.dropdown-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 990
}

.dropup .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-bottom: .125rem
}

.btn-group,
.btn-group-vertical {
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    vertical-align: middle
}

.btn-group>.btn,
.wrapper-front .btn-group>.fc-button,
.btn-group-vertical>.btn,
.wrapper-front .btn-group-vertical>.fc-button {
    position: relative;
    -webkit-box-flex: 0;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto
}

.btn-group>.btn:hover,
.wrapper-front .btn-group>.fc-button:hover,
.btn-group-vertical>.btn:hover,
.wrapper-front .btn-group-vertical>.fc-button:hover {
    z-index: 2
}

.btn-group>.btn:focus,
.wrapper-front .btn-group>.fc-button:focus,
.btn-group>.btn:active,
.wrapper-front .btn-group>.fc-button:active,
.btn-group>.btn.active,
.wrapper-front .btn-group>.active.fc-button,
.btn-group-vertical>.btn:focus,
.wrapper-front .btn-group-vertical>.fc-button:focus,
.btn-group-vertical>.btn:active,
.wrapper-front .btn-group-vertical>.fc-button:active,
.btn-group-vertical>.btn.active,
.wrapper-front .btn-group-vertical>.active.fc-button {
    z-index: 2
}

.btn-group .btn+.btn,
.btn-group .wrapper-front .fc-button+.btn,
.wrapper-front .btn-group .fc-button+.btn,
.btn-group .wrapper-front .btn+.fc-button,
.wrapper-front .btn-group .btn+.fc-button,
.btn-group .wrapper-front .fc-button+.fc-button,
.wrapper-front .btn-group .fc-button+.fc-button,
.btn-group .btn+.btn-group,
.btn-group .wrapper-front .fc-button+.btn-group,
.wrapper-front .btn-group .fc-button+.btn-group,
.btn-group .btn-group+.btn,
.btn-group .wrapper-front .btn-group+.fc-button,
.wrapper-front .btn-group .btn-group+.fc-button,
.btn-group .btn-group+.btn-group,
.btn-group-vertical .btn+.btn,
.btn-group-vertical .wrapper-front .fc-button+.btn,
.wrapper-front .btn-group-vertical .fc-button+.btn,
.btn-group-vertical .wrapper-front .btn+.fc-button,
.wrapper-front .btn-group-vertical .btn+.fc-button,
.btn-group-vertical .wrapper-front .fc-button+.fc-button,
.wrapper-front .btn-group-vertical .fc-button+.fc-button,
.btn-group-vertical .btn+.btn-group,
.btn-group-vertical .wrapper-front .fc-button+.btn-group,
.wrapper-front .btn-group-vertical .fc-button+.btn-group,
.btn-group-vertical .btn-group+.btn,
.btn-group-vertical .wrapper-front .btn-group+.fc-button,
.wrapper-front .btn-group-vertical .btn-group+.fc-button,
.btn-group-vertical .btn-group+.btn-group {
    margin-left: -1px
}

.btn-toolbar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.btn-toolbar .input-group {
    width: auto
}

.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle),
.wrapper-front .btn-group>.fc-button:not(:first-child):not(:last-child):not(.dropdown-toggle) {
    border-radius: 0
}

.btn-group>.btn:first-child,
.wrapper-front .btn-group>.fc-button:first-child {
    margin-left: 0
}

.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle),
.wrapper-front .btn-group>.fc-button:first-child:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.btn-group>.btn:last-child:not(:first-child),
.wrapper-front .btn-group>.fc-button:last-child:not(:first-child),
.btn-group>.dropdown-toggle:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.btn-group>.btn-group {
    float: left
}

.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn,
.wrapper-front .btn-group>.btn-group:not(:first-child):not(:last-child)>.fc-button {
    border-radius: 0
}

.btn-group>.btn-group:first-child:not(:last-child)>.btn:last-child,
.wrapper-front .btn-group>.btn-group:first-child:not(:last-child)>.fc-button:last-child,
.btn-group>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.btn-group>.btn-group:last-child:not(:first-child)>.btn:first-child,
.wrapper-front .btn-group>.btn-group:last-child:not(:first-child)>.fc-button:first-child {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
    outline: 0
}

.btn+.dropdown-toggle-split,
.wrapper-front .fc-button+.dropdown-toggle-split {
    padding-right: .9rem;
    padding-left: .9rem
}

.btn+.dropdown-toggle-split::after,
.wrapper-front .fc-button+.dropdown-toggle-split::after {
    margin-left: 0
}

.btn-sm+.dropdown-toggle-split,
.btn-group-sm>.btn+.dropdown-toggle-split,
.wrapper-front .btn-group-sm>.fc-button+.dropdown-toggle-split {
    padding-right: .375rem;
    padding-left: .375rem
}

.btn-lg+.dropdown-toggle-split,
.btn-group-lg>.btn+.dropdown-toggle-split,
.wrapper-front .btn-group-lg>.fc-button+.dropdown-toggle-split {
    padding-right: 1.125rem;
    padding-left: 1.125rem
}

.btn-group-vertical {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.btn-group-vertical .btn,
.btn-group-vertical .wrapper-front .fc-button,
.wrapper-front .btn-group-vertical .fc-button,
.btn-group-vertical .btn-group {
    width: 100%
}

.btn-group-vertical>.btn+.btn,
.wrapper-front .btn-group-vertical>.fc-button+.btn,
.wrapper-front .btn-group-vertical>.btn+.fc-button,
.wrapper-front .btn-group-vertical>.fc-button+.fc-button,
.btn-group-vertical>.btn+.btn-group,
.wrapper-front .btn-group-vertical>.fc-button+.btn-group,
.btn-group-vertical>.btn-group+.btn,
.wrapper-front .btn-group-vertical>.btn-group+.fc-button,
.btn-group-vertical>.btn-group+.btn-group {
    margin-top: -1px;
    margin-left: 0
}

.btn-group-vertical>.btn:not(:first-child):not(:last-child),
.wrapper-front .btn-group-vertical>.fc-button:not(:first-child):not(:last-child) {
    border-radius: 0
}

.btn-group-vertical>.btn:first-child:not(:last-child),
.wrapper-front .btn-group-vertical>.fc-button:first-child:not(:last-child) {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn:last-child:not(:first-child),
.wrapper-front .btn-group-vertical>.fc-button:last-child:not(:first-child) {
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn,
.wrapper-front .btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.fc-button {
    border-radius: 0
}

.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child,
.wrapper-front .btn-group-vertical>.btn-group:first-child:not(:last-child)>.fc-button:last-child,
.btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child,
.wrapper-front .btn-group-vertical>.btn-group:last-child:not(:first-child)>.fc-button:first-child {
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

[data-toggle="buttons"]>.btn input[type="radio"],
.wrapper-front [data-toggle="buttons"]>.fc-button input[type="radio"],
[data-toggle="buttons"]>.btn input[type="checkbox"],
.wrapper-front [data-toggle="buttons"]>.fc-button input[type="checkbox"],
[data-toggle="buttons"]>.btn-group>.btn input[type="radio"],
.wrapper-front [data-toggle="buttons"]>.btn-group>.fc-button input[type="radio"],
[data-toggle="buttons"]>.btn-group>.btn input[type="checkbox"],
.wrapper-front [data-toggle="buttons"]>.btn-group>.fc-button input[type="checkbox"] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none
}

.input-group {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%
}

.input-group .form-control {
    position: relative;
    z-index: 2;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    width: 1%;
    margin-bottom: 0
}

.input-group .form-control:focus,
.input-group .form-control:active,
.input-group .form-control:hover {
    z-index: 3
}

.input-group-addon,
.input-group-btn,
.input-group .form-control {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child),
.input-group .form-control:not(:first-child):not(:last-child) {
    border-radius: 0
}

.input-group-addon,
.input-group-btn {
    white-space: nowrap;
    vertical-align: middle
}

.input-group-addon {
    padding: .5rem .7rem;
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.25;
    color: #464a4c;
    text-align: center;
    background-color: #eceeef;
    border: 1px solid #cecece;
    border-radius: .25rem
}

.input-group-addon.form-control-sm,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.input-group-addon.btn,
.wrapper-front .input-group-sm>.input-group-btn>.input-group-addon.fc-button {
    padding: .25rem .7rem;
    font-size: .7rem;
    border-radius: .2rem
}

.input-group-addon.form-control-lg,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.input-group-addon.btn,
.wrapper-front .input-group-lg>.input-group-btn>.input-group-addon.fc-button {
    padding: .75rem 1.5rem;
    font-size: 1.25rem;
    border-radius: .3rem
}

.input-group-addon input[type="radio"],
.input-group-addon input[type="checkbox"] {
    margin-top: 0
}

.input-group .form-control:not(:last-child),
.input-group-addon:not(:last-child),
.input-group-btn:not(:last-child)>.btn,
.wrapper-front .input-group-btn:not(:last-child)>.fc-button,
.input-group-btn:not(:last-child)>.btn-group>.btn,
.wrapper-front .input-group-btn:not(:last-child)>.btn-group>.fc-button,
.input-group-btn:not(:last-child)>.dropdown-toggle,
.input-group-btn:not(:first-child)>.btn:not(:last-child):not(.dropdown-toggle),
.wrapper-front .input-group-btn:not(:first-child)>.fc-button:not(:last-child):not(.dropdown-toggle),
.input-group-btn:not(:first-child)>.btn-group:not(:last-child)>.btn,
.wrapper-front .input-group-btn:not(:first-child)>.btn-group:not(:last-child)>.fc-button {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.input-group-addon:not(:last-child) {
    border-right: 0
}

.input-group .form-control:not(:first-child),
.input-group-addon:not(:first-child),
.input-group-btn:not(:first-child)>.btn,
.wrapper-front .input-group-btn:not(:first-child)>.fc-button,
.input-group-btn:not(:first-child)>.btn-group>.btn,
.wrapper-front .input-group-btn:not(:first-child)>.btn-group>.fc-button,
.input-group-btn:not(:first-child)>.dropdown-toggle,
.input-group-btn:not(:last-child)>.btn:not(:first-child),
.wrapper-front .input-group-btn:not(:last-child)>.fc-button:not(:first-child),
.input-group-btn:not(:last-child)>.btn-group:not(:first-child)>.btn,
.wrapper-front .input-group-btn:not(:last-child)>.btn-group:not(:first-child)>.fc-button {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.form-control+.input-group-addon:not(:first-child) {
    border-left: 0
}

.input-group-btn {
    position: relative;
    font-size: 0;
    white-space: nowrap
}

.input-group-btn>.btn,
.wrapper-front .input-group-btn>.fc-button {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.input-group-btn>.btn+.btn,
.wrapper-front .input-group-btn>.fc-button+.btn,
.wrapper-front .input-group-btn>.btn+.fc-button,
.wrapper-front .input-group-btn>.fc-button+.fc-button {
    margin-left: -1px
}

.input-group-btn>.btn:focus,
.wrapper-front .input-group-btn>.fc-button:focus,
.input-group-btn>.btn:active,
.wrapper-front .input-group-btn>.fc-button:active,
.input-group-btn>.btn:hover,
.wrapper-front .input-group-btn>.fc-button:hover {
    z-index: 3
}

.input-group-btn:not(:last-child)>.btn,
.wrapper-front .input-group-btn:not(:last-child)>.fc-button,
.input-group-btn:not(:last-child)>.btn-group {
    margin-right: -1px
}

.input-group-btn:not(:first-child)>.btn,
.wrapper-front .input-group-btn:not(:first-child)>.fc-button,
.input-group-btn:not(:first-child)>.btn-group {
    z-index: 2;
    margin-left: -1px
}

.input-group-btn:not(:first-child)>.btn:focus,
.wrapper-front .input-group-btn:not(:first-child)>.fc-button:focus,
.input-group-btn:not(:first-child)>.btn:active,
.wrapper-front .input-group-btn:not(:first-child)>.fc-button:active,
.input-group-btn:not(:first-child)>.btn:hover,
.wrapper-front .input-group-btn:not(:first-child)>.fc-button:hover,
.input-group-btn:not(:first-child)>.btn-group:focus,
.input-group-btn:not(:first-child)>.btn-group:active,
.input-group-btn:not(:first-child)>.btn-group:hover {
    z-index: 3
}

.custom-control {
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    min-height: 1.5rem;
    padding-left: 1.5rem;
    margin-right: 1rem;
    cursor: pointer
}

.custom-control-input {
    position: absolute;
    z-index: -1;
    opacity: 0
}

.custom-control-input:checked~.custom-control-indicator {
    color: #fff;
    background-color: #047bf8
}

.custom-control-input:focus~.custom-control-indicator {
    -webkit-box-shadow: 0 0 0 1px #f8faff, 0 0 0 3px #047bf8;
    box-shadow: 0 0 0 1px #f8faff, 0 0 0 3px #047bf8
}

.custom-control-input:active~.custom-control-indicator {
    color: #fff;
    background-color: #b1d6fe
}

.custom-control-input:disabled~.custom-control-indicator {
    cursor: not-allowed;
    background-color: #eceeef
}

.custom-control-input:disabled~.custom-control-description {
    color: #636c72;
    cursor: not-allowed
}

.custom-control-indicator {
    position: absolute;
    top: .25rem;
    left: 0;
    display: block;
    width: 1rem;
    height: 1rem;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: #ddd;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 50% 50%
}

.custom-checkbox .custom-control-indicator {
    border-radius: .25rem
}

.custom-checkbox .custom-control-input:checked~.custom-control-indicator {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E")
}

.custom-checkbox .custom-control-input:indeterminate~.custom-control-indicator {
    background-color: #047bf8;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='%23fff' d='M0 2h4'/%3E%3C/svg%3E")
}

.custom-radio .custom-control-indicator {
    border-radius: 50%
}

.custom-radio .custom-control-input:checked~.custom-control-indicator {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fff'/%3E%3C/svg%3E")
}

.custom-controls-stacked {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.custom-controls-stacked .custom-control {
    margin-bottom: .25rem
}

.custom-controls-stacked .custom-control+.custom-control {
    margin-left: 0
}

.custom-select {
    display: inline-block;
    max-width: 100%;
    height: calc(2.25rem + 2px);
    padding: .375rem 1.75rem .375rem .75rem;
    line-height: 1.25;
    color: #464a4c;
    vertical-align: middle;
    background: #fff url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right .75rem center;
    background-size: 8px 10px;
    border: 1px solid #cecece;
    border-radius: .25rem;
    -moz-appearance: none;
    -webkit-appearance: none
}

.custom-select:focus {
    border-color: #7fbcfd;
    outline: none
}

.custom-select:focus::-ms-value {
    color: #464a4c;
    background-color: #fff
}

.custom-select:disabled {
    color: #636c72;
    cursor: not-allowed;
    background-color: #eceeef
}

.custom-select::-ms-expand {
    opacity: 0
}

.custom-select-sm {
    padding-top: .375rem;
    padding-bottom: .375rem;
    font-size: 75%
}

.custom-file {
    position: relative;
    display: inline-block;
    max-width: 100%;
    height: 2.5rem;
    margin-bottom: 0;
    cursor: pointer
}

.custom-file-input {
    min-width: 14rem;
    max-width: 100%;
    height: 2.5rem;
    margin: 0;
    filter: alpha(opacity=0);
    opacity: 0
}

.custom-file-control {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 5;
    height: 2.5rem;
    padding: .5rem 1rem;
    line-height: 1.5;
    color: #464a4c;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: #fff;
    border: 1px solid #cecece;
    border-radius: .25rem
}

.custom-file-control:lang(en)::after {
    content: "Choose file..."
}

.custom-file-control::before {
    position: absolute;
    top: -1px;
    right: -1px;
    bottom: -1px;
    z-index: 6;
    display: block;
    height: 2.5rem;
    padding: .5rem 1rem;
    line-height: 1.5;
    color: #464a4c;
    background-color: #eceeef;
    border: 1px solid #cecece;
    border-radius: 0 .25rem .25rem 0
}

.custom-file-control:lang(en)::before {
    content: "Browse"
}

.nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}

.nav-link {
    display: block;
    padding: 0.5em 1em
}

.nav-link:focus,
.nav-link:hover {
    text-decoration: none
}

.nav-link.disabled {
    color: #636c72;
    cursor: not-allowed
}

.nav-tabs {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.nav-tabs .nav-item {
    margin-bottom: -1px
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-right-radius: .25rem;
    border-top-left-radius: .25rem
}

.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
    border-color: #eceeef #eceeef rgba(0, 0, 0, 0.1)
}

.nav-tabs .nav-link.disabled {
    color: #636c72;
    background-color: transparent;
    border-color: transparent
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
    color: #464a4c;
    background-color: transparent;
    border-color: #ddd #ddd transparent
}

.nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.nav-pills .nav-link {
    border-radius: 30px
}

.nav-pills .nav-link.active,
.nav-pills .nav-item.show .nav-link {
    color: #fff;
    cursor: default;
    background-color: #047bf8
}

.nav-fill .nav-item {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    text-align: center
}

.nav-justified .nav-item {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 100%;
    flex: 1 1 100%;
    text-align: center
}

.tab-content>.tab-pane {
    display: none
}

.tab-content>.active {
    display: block
}

.breadcrumb {
    padding: .75rem 1rem;
    margin-bottom: 1rem;
    list-style: none;
    background-color: #eceeef;
    border-radius: .25rem
}

.breadcrumb::after {
    display: block;
    content: "";
    clear: both
}

.breadcrumb-item {
    float: left
}

.breadcrumb-item+.breadcrumb-item::before {
    display: inline-block;
    padding-right: .5rem;
    padding-left: .5rem;
    color: #636c72;
    content: "/"
}

.breadcrumb-item+.breadcrumb-item:hover::before {
    text-decoration: underline
}

.breadcrumb-item+.breadcrumb-item:hover::before {
    text-decoration: none
}

.breadcrumb-item.active {
    color: #636c72
}

.pagination {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: .25rem
}

.page-item:first-child .page-link {
    margin-left: 0;
    border-bottom-left-radius: .25rem;
    border-top-left-radius: .25rem
}

.page-item:last-child .page-link {
    border-bottom-right-radius: .25rem;
    border-top-right-radius: .25rem
}

.page-item.active .page-link {
    z-index: 2;
    color: #fff;
    background-color: #047bf8;
    border-color: #047bf8
}

.page-item.disabled .page-link {
    color: #636c72;
    pointer-events: none;
    cursor: not-allowed;
    background-color: #fff;
    border-color: #ddd
}

.page-link {
    position: relative;
    display: block;
    padding: .5rem .75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #3b75e3;
    background-color: #fff;
    border: 1px solid #ddd
}

.page-link:focus,
.page-link:hover {
    color: #1a50b7;
    text-decoration: none;
    background-color: #eceeef;
    border-color: #ddd
}

.pagination-lg .page-link {
    padding: .75rem 1.5rem;
    font-size: 1.25rem
}

.pagination-lg .page-item:first-child .page-link {
    border-bottom-left-radius: .3rem;
    border-top-left-radius: .3rem
}

.pagination-lg .page-item:last-child .page-link {
    border-bottom-right-radius: .3rem;
    border-top-right-radius: .3rem
}

.pagination-sm .page-link {
    padding: .25rem .5rem;
    font-size: .7rem
}

.pagination-sm .page-item:first-child .page-link {
    border-bottom-left-radius: .2rem;
    border-top-left-radius: .2rem
}

.pagination-sm .page-item:last-child .page-link {
    border-bottom-right-radius: .2rem;
    border-top-right-radius: .2rem
}

.badge {
    display: inline-block;
    padding: .25em .4em;
    font-size: 75%;
    font-weight: 500;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25rem
}

.badge:empty {
    display: none
}

.btn .badge,
.wrapper-front .fc-button .badge {
    position: relative;
    top: -1px
}

a.badge:focus,
a.badge:hover {
    color: #fff;
    text-decoration: none;
    cursor: pointer
}

.badge-pill {
    padding-right: .6em;
    padding-left: .6em;
    border-radius: 10rem
}

.badge-default {
    background-color: #636c72
}

.badge-default[href]:focus,
.badge-default[href]:hover {
    background-color: #4b5257
}

.badge-primary {
    background-color: #047bf8
}

.badge-primary[href]:focus,
.badge-primary[href]:hover {
    background-color: #0362c6
}

.badge-success {
    background-color: #90be2e
}

.badge-success[href]:focus,
.badge-success[href]:hover {
    background-color: #719524
}

.badge-info {
    background-color: #5bc0de
}

.badge-info[href]:focus,
.badge-info[href]:hover {
    background-color: #31b0d5
}

.badge-warning {
    background-color: #f0ad4e
}

.badge-warning[href]:focus,
.badge-warning[href]:hover {
    background-color: #ec971f
}

.badge-danger {
    background-color: #e65252
}

.badge-danger[href]:focus,
.badge-danger[href]:hover {
    background-color: #e02525
}

.align-baseline {
    vertical-align: baseline !important
}

.align-top {
    vertical-align: top !important
}

.align-middle {
    vertical-align: middle !important
}

.align-bottom {
    vertical-align: bottom !important
}

.align-text-bottom {
    vertical-align: text-bottom !important
}

.align-text-top {
    vertical-align: text-top !important
}

.bg-faded {
    background-color: #e9efff
}

.bg-primary {
    background-color: #047bf8 !important
}

a.bg-primary:focus,
a.bg-primary:hover {
    background-color: #0362c6 !important
}

.bg-success {
    background-color: #90be2e !important
}

a.bg-success:focus,
a.bg-success:hover {
    background-color: #719524 !important
}

.bg-info {
    background-color: #5bc0de !important
}

a.bg-info:focus,
a.bg-info:hover {
    background-color: #31b0d5 !important
}

.bg-warning {
    background-color: #f0ad4e !important
}

a.bg-warning:focus,
a.bg-warning:hover {
    background-color: #ec971f !important
}

.bg-danger {
    background-color: #e65252 !important
}

a.bg-danger:focus,
a.bg-danger:hover {
    background-color: #e02525 !important
}

.bg-inverse {
    background-color: #292b2c !important
}

a.bg-inverse:focus,
a.bg-inverse:hover {
    background-color: #101112 !important
}

.border-0 {
    border: 0 !important
}

.border-top-0 {
    border-top: 0 !important
}

.border-right-0 {
    border-right: 0 !important
}

.border-bottom-0 {
    border-bottom: 0 !important
}

.border-left-0 {
    border-left: 0 !important
}

.rounded {
    border-radius: .25rem
}

.rounded-top {
    border-top-right-radius: .25rem;
    border-top-left-radius: .25rem
}

.rounded-right {
    border-bottom-right-radius: .25rem;
    border-top-right-radius: .25rem
}

.rounded-bottom {
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem
}

.rounded-left {
    border-bottom-left-radius: .25rem;
    border-top-left-radius: .25rem
}

.rounded-circle {
    border-radius: 50%
}

.rounded-0 {
    border-radius: 0
}

.clearfix::after {
    display: block;
    content: "";
    clear: both
}

.d-none {
    display: none !important
}

.d-inline {
    display: inline !important
}

.d-inline-block {
    display: inline-block !important
}

.d-block {
    display: block !important
}

.d-table {
    display: table !important
}

.d-table-cell {
    display: table-cell !important
}

.d-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important
}

.d-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important
}

@media (min-width: 576px) {
    .d-sm-none {
        display: none !important
    }
    .d-sm-inline {
        display: inline !important
    }
    .d-sm-inline-block {
        display: inline-block !important
    }
    .d-sm-block {
        display: block !important
    }
    .d-sm-table {
        display: table !important
    }
    .d-sm-table-cell {
        display: table-cell !important
    }
    .d-sm-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }
    .d-sm-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media (min-width: 768px) {
    .d-md-none {
        display: none !important
    }
    .d-md-inline {
        display: inline !important
    }
    .d-md-inline-block {
        display: inline-block !important
    }
    .d-md-block {
        display: block !important
    }
    .d-md-table {
        display: table !important
    }
    .d-md-table-cell {
        display: table-cell !important
    }
    .d-md-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }
    .d-md-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media (min-width: 992px) {
    .d-lg-none {
        display: none !important
    }
    .d-lg-inline {
        display: inline !important
    }
    .d-lg-inline-block {
        display: inline-block !important
    }
    .d-lg-block {
        display: block !important
    }
    .d-lg-table {
        display: table !important
    }
    .d-lg-table-cell {
        display: table-cell !important
    }
    .d-lg-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }
    .d-lg-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media (min-width: 1200px) {
    .d-xl-none {
        display: none !important
    }
    .d-xl-inline {
        display: inline !important
    }
    .d-xl-inline-block {
        display: inline-block !important
    }
    .d-xl-block {
        display: block !important
    }
    .d-xl-table {
        display: table !important
    }
    .d-xl-table-cell {
        display: table-cell !important
    }
    .d-xl-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }
    .d-xl-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

.flex-first {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1
}

.flex-last {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
}

.flex-unordered {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0
}

.flex-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important
}

.flex-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important
}

.flex-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important
}

.flex-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important
}

.flex-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important
}

.flex-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important
}

.flex-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important
}

.justify-content-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important
}

.justify-content-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important
}

.justify-content-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important
}

.justify-content-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important
}

.justify-content-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important
}

.align-items-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important
}

.align-items-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important
}

.align-items-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important
}

.align-items-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important
}

.align-items-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important
}

.align-content-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important
}

.align-content-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important
}

.align-content-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important
}

.align-content-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important
}

.align-content-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important
}

.align-content-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important
}

.align-self-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important
}

.align-self-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important
}

.align-self-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important
}

.align-self-center {
    -ms-flex-item-align: center !important;
    align-self: center !important
}

.align-self-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important
}

.align-self-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important
}

@media (min-width: 576px) {
    .flex-sm-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1
    }
    .flex-sm-last {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }
    .flex-sm-unordered {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
    .flex-sm-row {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }
    .flex-sm-column {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }
    .flex-sm-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }
    .flex-sm-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }
    .flex-sm-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }
    .flex-sm-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }
    .flex-sm-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }
    .justify-content-sm-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }
    .justify-content-sm-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }
    .justify-content-sm-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important
    }
    .justify-content-sm-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }
    .justify-content-sm-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }
    .align-items-sm-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }
    .align-items-sm-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }
    .align-items-sm-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important
    }
    .align-items-sm-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }
    .align-items-sm-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }
    .align-content-sm-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }
    .align-content-sm-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }
    .align-content-sm-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }
    .align-content-sm-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }
    .align-content-sm-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }
    .align-content-sm-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }
    .align-self-sm-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important
    }
    .align-self-sm-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }
    .align-self-sm-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }
    .align-self-sm-center {
        -ms-flex-item-align: center !important;
        align-self: center !important
    }
    .align-self-sm-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }
    .align-self-sm-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important
    }
}

@media (min-width: 768px) {
    .flex-md-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1
    }
    .flex-md-last {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }
    .flex-md-unordered {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
    .flex-md-row {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }
    .flex-md-column {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }
    .flex-md-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }
    .flex-md-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }
    .flex-md-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }
    .flex-md-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }
    .flex-md-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }
    .justify-content-md-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }
    .justify-content-md-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }
    .justify-content-md-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important
    }
    .justify-content-md-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }
    .justify-content-md-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }
    .align-items-md-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }
    .align-items-md-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }
    .align-items-md-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important
    }
    .align-items-md-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }
    .align-items-md-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }
    .align-content-md-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }
    .align-content-md-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }
    .align-content-md-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }
    .align-content-md-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }
    .align-content-md-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }
    .align-content-md-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }
    .align-self-md-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important
    }
    .align-self-md-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }
    .align-self-md-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }
    .align-self-md-center {
        -ms-flex-item-align: center !important;
        align-self: center !important
    }
    .align-self-md-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }
    .align-self-md-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important
    }
}

@media (min-width: 992px) {
    .flex-lg-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1
    }
    .flex-lg-last {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }
    .flex-lg-unordered {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
    .flex-lg-row {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }
    .flex-lg-column {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }
    .flex-lg-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }
    .flex-lg-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }
    .flex-lg-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }
    .flex-lg-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }
    .flex-lg-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }
    .justify-content-lg-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }
    .justify-content-lg-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }
    .justify-content-lg-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important
    }
    .justify-content-lg-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }
    .justify-content-lg-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }
    .align-items-lg-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }
    .align-items-lg-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }
    .align-items-lg-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important
    }
    .align-items-lg-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }
    .align-items-lg-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }
    .align-content-lg-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }
    .align-content-lg-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }
    .align-content-lg-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }
    .align-content-lg-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }
    .align-content-lg-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }
    .align-content-lg-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }
    .align-self-lg-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important
    }
    .align-self-lg-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }
    .align-self-lg-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }
    .align-self-lg-center {
        -ms-flex-item-align: center !important;
        align-self: center !important
    }
    .align-self-lg-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }
    .align-self-lg-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important
    }
}

@media (min-width: 1200px) {
    .flex-xl-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1
    }
    .flex-xl-last {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }
    .flex-xl-unordered {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
    .flex-xl-row {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }
    .flex-xl-column {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }
    .flex-xl-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }
    .flex-xl-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }
    .flex-xl-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }
    .flex-xl-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }
    .flex-xl-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }
    .justify-content-xl-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }
    .justify-content-xl-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }
    .justify-content-xl-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important
    }
    .justify-content-xl-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }
    .justify-content-xl-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }
    .align-items-xl-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }
    .align-items-xl-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }
    .align-items-xl-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important
    }
    .align-items-xl-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }
    .align-items-xl-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }
    .align-content-xl-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }
    .align-content-xl-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }
    .align-content-xl-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }
    .align-content-xl-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }
    .align-content-xl-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }
    .align-content-xl-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }
    .align-self-xl-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important
    }
    .align-self-xl-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }
    .align-self-xl-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }
    .align-self-xl-center {
        -ms-flex-item-align: center !important;
        align-self: center !important
    }
    .align-self-xl-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }
    .align-self-xl-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important
    }
}

.float-left {
    float: left !important
}

.float-right {
    float: right !important
}

.float-none {
    float: none !important
}

@media (min-width: 576px) {
    .float-sm-left {
        float: left !important
    }
    .float-sm-right {
        float: right !important
    }
    .float-sm-none {
        float: none !important
    }
}

@media (min-width: 768px) {
    .float-md-left {
        float: left !important
    }
    .float-md-right {
        float: right !important
    }
    .float-md-none {
        float: none !important
    }
}

@media (min-width: 992px) {
    .float-lg-left {
        float: left !important
    }
    .float-lg-right {
        float: right !important
    }
    .float-lg-none {
        float: none !important
    }
}

@media (min-width: 1200px) {
    .float-xl-left {
        float: left !important
    }
    .float-xl-right {
        float: right !important
    }
    .float-xl-none {
        float: none !important
    }
}

.fixed-top {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030
}

.fixed-bottom {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1030
}

.sticky-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1030
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto
}

.w-25 {
    width: 25% !important
}

.w-50 {
    width: 50% !important
}

.w-75 {
    width: 75% !important
}

.w-100 {
    width: 100% !important
}

.h-25 {
    height: 25% !important
}

.h-50 {
    height: 50% !important
}

.h-75 {
    height: 75% !important
}

.h-100 {
    height: 100% !important
}

.mw-100 {
    max-width: 100% !important
}

.mh-100 {
    max-height: 100% !important
}

.m-0 {
    margin: 0 0 !important
}

.mt-0 {
    margin-top: 0 !important
}

.mr-0 {
    margin-right: 0 !important
}

.mb-0 {
    margin-bottom: 0 !important
}

.ml-0 {
    margin-left: 0 !important
}

.mx-0 {
    margin-right: 0 !important;
    margin-left: 0 !important
}

.my-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important
}

.m-1 {
    margin: .25rem .25rem !important
}

.mt-1 {
    margin-top: .25rem !important
}

.mr-1 {
    margin-right: .25rem !important
}

.mb-1 {
    margin-bottom: .25rem !important
}

.ml-1 {
    margin-left: .25rem !important
}

.mx-1 {
    margin-right: .25rem !important;
    margin-left: .25rem !important
}

.my-1 {
    margin-top: .25rem !important;
    margin-bottom: .25rem !important
}

.m-2 {
    margin: .5rem .5rem !important
}

.mt-2 {
    margin-top: .5rem !important
}

.mr-2 {
    margin-right: .5rem !important
}

.mb-2 {
    margin-bottom: .5rem !important
}

.ml-2 {
    margin-left: .5rem !important
}

.mx-2 {
    margin-right: .5rem !important;
    margin-left: .5rem !important
}

.my-2 {
    margin-top: .5rem !important;
    margin-bottom: .5rem !important
}

.m-3 {
    margin: 1rem 1rem !important
}

.mt-3 {
    margin-top: 1rem !important
}

.mr-3 {
    margin-right: 1rem !important
}

.mb-3 {
    margin-bottom: 1rem !important
}

.ml-3 {
    margin-left: 1rem !important
}

.mx-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important
}

.my-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important
}

.m-4 {
    margin: 1.5rem 1.5rem !important
}

.mt-4 {
    margin-top: 1.5rem !important
}

.mr-4 {
    margin-right: 1.5rem !important
}

.mb-4 {
    margin-bottom: 1.5rem !important
}

.ml-4 {
    margin-left: 1.5rem !important
}

.mx-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important
}

.my-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important
}

.m-5 {
    margin: 3rem 3rem !important
}

.mt-5 {
    margin-top: 3rem !important
}

.mr-5 {
    margin-right: 3rem !important
}

.mb-5 {
    margin-bottom: 3rem !important
}

.ml-5 {
    margin-left: 3rem !important
}

.mx-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important
}

.my-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important
}

.p-0 {
    padding: 0 0 !important
}

.pt-0 {
    padding-top: 0 !important
}

.pr-0 {
    padding-right: 0 !important
}

.pb-0 {
    padding-bottom: 0 !important
}

.pl-0 {
    padding-left: 0 !important
}

.px-0 {
    padding-right: 0 !important;
    padding-left: 0 !important
}

.py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important
}

.p-1 {
    padding: .25rem .25rem !important
}

.pt-1 {
    padding-top: .25rem !important
}

.pr-1 {
    padding-right: .25rem !important
}

.pb-1 {
    padding-bottom: .25rem !important
}

.pl-1 {
    padding-left: .25rem !important
}

.px-1 {
    padding-right: .25rem !important;
    padding-left: .25rem !important
}

.py-1 {
    padding-top: .25rem !important;
    padding-bottom: .25rem !important
}

.p-2 {
    padding: .5rem .5rem !important
}

.pt-2 {
    padding-top: .5rem !important
}

.pr-2 {
    padding-right: .5rem !important
}

.pb-2 {
    padding-bottom: .5rem !important
}

.pl-2 {
    padding-left: .5rem !important
}

.px-2 {
    padding-right: .5rem !important;
    padding-left: .5rem !important
}

.py-2 {
    padding-top: .5rem !important;
    padding-bottom: .5rem !important
}

.p-3 {
    padding: 1rem 1rem !important
}

.pt-3 {
    padding-top: 1rem !important
}

.pr-3 {
    padding-right: 1rem !important
}

.pb-3 {
    padding-bottom: 1rem !important
}

.pl-3 {
    padding-left: 1rem !important
}

.px-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important
}

.py-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important
}

.p-4 {
    padding: 1.5rem 1.5rem !important
}

.pt-4 {
    padding-top: 1.5rem !important
}

.pr-4 {
    padding-right: 1.5rem !important
}

.pb-4 {
    padding-bottom: 1.5rem !important
}

.pl-4 {
    padding-left: 1.5rem !important
}

.px-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important
}

.py-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important
}

.p-5 {
    padding: 3rem 3rem !important
}

.pt-5 {
    padding-top: 3rem !important
}

.pr-5 {
    padding-right: 3rem !important
}

.pb-5 {
    padding-bottom: 3rem !important
}

.pl-5 {
    padding-left: 3rem !important
}

.px-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important
}

.py-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important
}

.m-auto {
    margin: auto !important
}

.mt-auto {
    margin-top: auto !important
}

.mr-auto {
    margin-right: auto !important
}

.mb-auto {
    margin-bottom: auto !important
}

.ml-auto {
    margin-left: auto !important
}

.mx-auto {
    margin-right: auto !important;
    margin-left: auto !important
}

.my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important
}

@media (min-width: 576px) {
    .m-sm-0 {
        margin: 0 0 !important
    }
    .mt-sm-0 {
        margin-top: 0 !important
    }
    .mr-sm-0 {
        margin-right: 0 !important
    }
    .mb-sm-0 {
        margin-bottom: 0 !important
    }
    .ml-sm-0 {
        margin-left: 0 !important
    }
    .mx-sm-0 {
        margin-right: 0 !important;
        margin-left: 0 !important
    }
    .my-sm-0 {
        margin-top: 0 !important;
        margin-bottom: 0 !important
    }
    .m-sm-1 {
        margin: .25rem .25rem !important
    }
    .mt-sm-1 {
        margin-top: .25rem !important
    }
    .mr-sm-1 {
        margin-right: .25rem !important
    }
    .mb-sm-1 {
        margin-bottom: .25rem !important
    }
    .ml-sm-1 {
        margin-left: .25rem !important
    }
    .mx-sm-1 {
        margin-right: .25rem !important;
        margin-left: .25rem !important
    }
    .my-sm-1 {
        margin-top: .25rem !important;
        margin-bottom: .25rem !important
    }
    .m-sm-2 {
        margin: .5rem .5rem !important
    }
    .mt-sm-2 {
        margin-top: .5rem !important
    }
    .mr-sm-2 {
        margin-right: .5rem !important
    }
    .mb-sm-2 {
        margin-bottom: .5rem !important
    }
    .ml-sm-2 {
        margin-left: .5rem !important
    }
    .mx-sm-2 {
        margin-right: .5rem !important;
        margin-left: .5rem !important
    }
    .my-sm-2 {
        margin-top: .5rem !important;
        margin-bottom: .5rem !important
    }
    .m-sm-3 {
        margin: 1rem 1rem !important
    }
    .mt-sm-3 {
        margin-top: 1rem !important
    }
    .mr-sm-3 {
        margin-right: 1rem !important
    }
    .mb-sm-3 {
        margin-bottom: 1rem !important
    }
    .ml-sm-3 {
        margin-left: 1rem !important
    }
    .mx-sm-3 {
        margin-right: 1rem !important;
        margin-left: 1rem !important
    }
    .my-sm-3 {
        margin-top: 1rem !important;
        margin-bottom: 1rem !important
    }
    .m-sm-4 {
        margin: 1.5rem 1.5rem !important
    }
    .mt-sm-4 {
        margin-top: 1.5rem !important
    }
    .mr-sm-4 {
        margin-right: 1.5rem !important
    }
    .mb-sm-4 {
        margin-bottom: 1.5rem !important
    }
    .ml-sm-4 {
        margin-left: 1.5rem !important
    }
    .mx-sm-4 {
        margin-right: 1.5rem !important;
        margin-left: 1.5rem !important
    }
    .my-sm-4 {
        margin-top: 1.5rem !important;
        margin-bottom: 1.5rem !important
    }
    .m-sm-5 {
        margin: 3rem 3rem !important
    }
    .mt-sm-5 {
        margin-top: 3rem !important
    }
    .mr-sm-5 {
        margin-right: 3rem !important
    }
    .mb-sm-5 {
        margin-bottom: 3rem !important
    }
    .ml-sm-5 {
        margin-left: 3rem !important
    }
    .mx-sm-5 {
        margin-right: 3rem !important;
        margin-left: 3rem !important
    }
    .my-sm-5 {
        margin-top: 3rem !important;
        margin-bottom: 3rem !important
    }
    .p-sm-0 {
        padding: 0 0 !important
    }
    .pt-sm-0 {
        padding-top: 0 !important
    }
    .pr-sm-0 {
        padding-right: 0 !important
    }
    .pb-sm-0 {
        padding-bottom: 0 !important
    }
    .pl-sm-0 {
        padding-left: 0 !important
    }
    .px-sm-0 {
        padding-right: 0 !important;
        padding-left: 0 !important
    }
    .py-sm-0 {
        padding-top: 0 !important;
        padding-bottom: 0 !important
    }
    .p-sm-1 {
        padding: .25rem .25rem !important
    }
    .pt-sm-1 {
        padding-top: .25rem !important
    }
    .pr-sm-1 {
        padding-right: .25rem !important
    }
    .pb-sm-1 {
        padding-bottom: .25rem !important
    }
    .pl-sm-1 {
        padding-left: .25rem !important
    }
    .px-sm-1 {
        padding-right: .25rem !important;
        padding-left: .25rem !important
    }
    .py-sm-1 {
        padding-top: .25rem !important;
        padding-bottom: .25rem !important
    }
    .p-sm-2 {
        padding: .5rem .5rem !important
    }
    .pt-sm-2 {
        padding-top: .5rem !important
    }
    .pr-sm-2 {
        padding-right: .5rem !important
    }
    .pb-sm-2 {
        padding-bottom: .5rem !important
    }
    .pl-sm-2 {
        padding-left: .5rem !important
    }
    .px-sm-2 {
        padding-right: .5rem !important;
        padding-left: .5rem !important
    }
    .py-sm-2 {
        padding-top: .5rem !important;
        padding-bottom: .5rem !important
    }
    .p-sm-3 {
        padding: 1rem 1rem !important
    }
    .pt-sm-3 {
        padding-top: 1rem !important
    }
    .pr-sm-3 {
        padding-right: 1rem !important
    }
    .pb-sm-3 {
        padding-bottom: 1rem !important
    }
    .pl-sm-3 {
        padding-left: 1rem !important
    }
    .px-sm-3 {
        padding-right: 1rem !important;
        padding-left: 1rem !important
    }
    .py-sm-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important
    }
    .p-sm-4 {
        padding: 1.5rem 1.5rem !important
    }
    .pt-sm-4 {
        padding-top: 1.5rem !important
    }
    .pr-sm-4 {
        padding-right: 1.5rem !important
    }
    .pb-sm-4 {
        padding-bottom: 1.5rem !important
    }
    .pl-sm-4 {
        padding-left: 1.5rem !important
    }
    .px-sm-4 {
        padding-right: 1.5rem !important;
        padding-left: 1.5rem !important
    }
    .py-sm-4 {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important
    }
    .p-sm-5 {
        padding: 3rem 3rem !important
    }
    .pt-sm-5 {
        padding-top: 3rem !important
    }
    .pr-sm-5 {
        padding-right: 3rem !important
    }
    .pb-sm-5 {
        padding-bottom: 3rem !important
    }
    .pl-sm-5 {
        padding-left: 3rem !important
    }
    .px-sm-5 {
        padding-right: 3rem !important;
        padding-left: 3rem !important
    }
    .py-sm-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important
    }
    .m-sm-auto {
        margin: auto !important
    }
    .mt-sm-auto {
        margin-top: auto !important
    }
    .mr-sm-auto {
        margin-right: auto !important
    }
    .mb-sm-auto {
        margin-bottom: auto !important
    }
    .ml-sm-auto {
        margin-left: auto !important
    }
    .mx-sm-auto {
        margin-right: auto !important;
        margin-left: auto !important
    }
    .my-sm-auto {
        margin-top: auto !important;
        margin-bottom: auto !important
    }
}

@media (min-width: 768px) {
    .m-md-0 {
        margin: 0 0 !important
    }
    .mt-md-0 {
        margin-top: 0 !important
    }
    .mr-md-0 {
        margin-right: 0 !important
    }
    .mb-md-0 {
        margin-bottom: 0 !important
    }
    .ml-md-0 {
        margin-left: 0 !important
    }
    .mx-md-0 {
        margin-right: 0 !important;
        margin-left: 0 !important
    }
    .my-md-0 {
        margin-top: 0 !important;
        margin-bottom: 0 !important
    }
    .m-md-1 {
        margin: .25rem .25rem !important
    }
    .mt-md-1 {
        margin-top: .25rem !important
    }
    .mr-md-1 {
        margin-right: .25rem !important
    }
    .mb-md-1 {
        margin-bottom: .25rem !important
    }
    .ml-md-1 {
        margin-left: .25rem !important
    }
    .mx-md-1 {
        margin-right: .25rem !important;
        margin-left: .25rem !important
    }
    .my-md-1 {
        margin-top: .25rem !important;
        margin-bottom: .25rem !important
    }
    .m-md-2 {
        margin: .5rem .5rem !important
    }
    .mt-md-2 {
        margin-top: .5rem !important
    }
    .mr-md-2 {
        margin-right: .5rem !important
    }
    .mb-md-2 {
        margin-bottom: .5rem !important
    }
    .ml-md-2 {
        margin-left: .5rem !important
    }
    .mx-md-2 {
        margin-right: .5rem !important;
        margin-left: .5rem !important
    }
    .my-md-2 {
        margin-top: .5rem !important;
        margin-bottom: .5rem !important
    }
    .m-md-3 {
        margin: 1rem 1rem !important
    }
    .mt-md-3 {
        margin-top: 1rem !important
    }
    .mr-md-3 {
        margin-right: 1rem !important
    }
    .mb-md-3 {
        margin-bottom: 1rem !important
    }
    .ml-md-3 {
        margin-left: 1rem !important
    }
    .mx-md-3 {
        margin-right: 1rem !important;
        margin-left: 1rem !important
    }
    .my-md-3 {
        margin-top: 1rem !important;
        margin-bottom: 1rem !important
    }
    .m-md-4 {
        margin: 1.5rem 1.5rem !important
    }
    .mt-md-4 {
        margin-top: 1.5rem !important
    }
    .mr-md-4 {
        margin-right: 1.5rem !important
    }
    .mb-md-4 {
        margin-bottom: 1.5rem !important
    }
    .ml-md-4 {
        margin-left: 1.5rem !important
    }
    .mx-md-4 {
        margin-right: 1.5rem !important;
        margin-left: 1.5rem !important
    }
    .my-md-4 {
        margin-top: 1.5rem !important;
        margin-bottom: 1.5rem !important
    }
    .m-md-5 {
        margin: 3rem 3rem !important
    }
    .mt-md-5 {
        margin-top: 3rem !important
    }
    .mr-md-5 {
        margin-right: 3rem !important
    }
    .mb-md-5 {
        margin-bottom: 3rem !important
    }
    .ml-md-5 {
        margin-left: 3rem !important
    }
    .mx-md-5 {
        margin-right: 3rem !important;
        margin-left: 3rem !important
    }
    .my-md-5 {
        margin-top: 3rem !important;
        margin-bottom: 3rem !important
    }
    .p-md-0 {
        padding: 0 0 !important
    }
    .pt-md-0 {
        padding-top: 0 !important
    }
    .pr-md-0 {
        padding-right: 0 !important
    }
    .pb-md-0 {
        padding-bottom: 0 !important
    }
    .pl-md-0 {
        padding-left: 0 !important
    }
    .px-md-0 {
        padding-right: 0 !important;
        padding-left: 0 !important
    }
    .py-md-0 {
        padding-top: 0 !important;
        padding-bottom: 0 !important
    }
    .p-md-1 {
        padding: .25rem .25rem !important
    }
    .pt-md-1 {
        padding-top: .25rem !important
    }
    .pr-md-1 {
        padding-right: .25rem !important
    }
    .pb-md-1 {
        padding-bottom: .25rem !important
    }
    .pl-md-1 {
        padding-left: .25rem !important
    }
    .px-md-1 {
        padding-right: .25rem !important;
        padding-left: .25rem !important
    }
    .py-md-1 {
        padding-top: .25rem !important;
        padding-bottom: .25rem !important
    }
    .p-md-2 {
        padding: .5rem .5rem !important
    }
    .pt-md-2 {
        padding-top: .5rem !important
    }
    .pr-md-2 {
        padding-right: .5rem !important
    }
    .pb-md-2 {
        padding-bottom: .5rem !important
    }
    .pl-md-2 {
        padding-left: .5rem !important
    }
    .px-md-2 {
        padding-right: .5rem !important;
        padding-left: .5rem !important
    }
    .py-md-2 {
        padding-top: .5rem !important;
        padding-bottom: .5rem !important
    }
    .p-md-3 {
        padding: 1rem 1rem !important
    }
    .pt-md-3 {
        padding-top: 1rem !important
    }
    .pr-md-3 {
        padding-right: 1rem !important
    }
    .pb-md-3 {
        padding-bottom: 1rem !important
    }
    .pl-md-3 {
        padding-left: 1rem !important
    }
    .px-md-3 {
        padding-right: 1rem !important;
        padding-left: 1rem !important
    }
    .py-md-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important
    }
    .p-md-4 {
        padding: 1.5rem 1.5rem !important
    }
    .pt-md-4 {
        padding-top: 1.5rem !important
    }
    .pr-md-4 {
        padding-right: 1.5rem !important
    }
    .pb-md-4 {
        padding-bottom: 1.5rem !important
    }
    .pl-md-4 {
        padding-left: 1.5rem !important
    }
    .px-md-4 {
        padding-right: 1.5rem !important;
        padding-left: 1.5rem !important
    }
    .py-md-4 {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important
    }
    .p-md-5 {
        padding: 3rem 3rem !important
    }
    .pt-md-5 {
        padding-top: 3rem !important
    }
    .pr-md-5 {
        padding-right: 3rem !important
    }
    .pb-md-5 {
        padding-bottom: 3rem !important
    }
    .pl-md-5 {
        padding-left: 3rem !important
    }
    .px-md-5 {
        padding-right: 3rem !important;
        padding-left: 3rem !important
    }
    .py-md-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important
    }
    .m-md-auto {
        margin: auto !important
    }
    .mt-md-auto {
        margin-top: auto !important
    }
    .mr-md-auto {
        margin-right: auto !important
    }
    .mb-md-auto {
        margin-bottom: auto !important
    }
    .ml-md-auto {
        margin-left: auto !important
    }
    .mx-md-auto {
        margin-right: auto !important;
        margin-left: auto !important
    }
    .my-md-auto {
        margin-top: auto !important;
        margin-bottom: auto !important
    }
}

@media (min-width: 992px) {
    .m-lg-0 {
        margin: 0 0 !important
    }
    .mt-lg-0 {
        margin-top: 0 !important
    }
    .mr-lg-0 {
        margin-right: 0 !important
    }
    .mb-lg-0 {
        margin-bottom: 0 !important
    }
    .ml-lg-0 {
        margin-left: 0 !important
    }
    .mx-lg-0 {
        margin-right: 0 !important;
        margin-left: 0 !important
    }
    .my-lg-0 {
        margin-top: 0 !important;
        margin-bottom: 0 !important
    }
    .m-lg-1 {
        margin: .25rem .25rem !important
    }
    .mt-lg-1 {
        margin-top: .25rem !important
    }
    .mr-lg-1 {
        margin-right: .25rem !important
    }
    .mb-lg-1 {
        margin-bottom: .25rem !important
    }
    .ml-lg-1 {
        margin-left: .25rem !important
    }
    .mx-lg-1 {
        margin-right: .25rem !important;
        margin-left: .25rem !important
    }
    .my-lg-1 {
        margin-top: .25rem !important;
        margin-bottom: .25rem !important
    }
    .m-lg-2 {
        margin: .5rem .5rem !important
    }
    .mt-lg-2 {
        margin-top: .5rem !important
    }
    .mr-lg-2 {
        margin-right: .5rem !important
    }
    .mb-lg-2 {
        margin-bottom: .5rem !important
    }
    .ml-lg-2 {
        margin-left: .5rem !important
    }
    .mx-lg-2 {
        margin-right: .5rem !important;
        margin-left: .5rem !important
    }
    .my-lg-2 {
        margin-top: .5rem !important;
        margin-bottom: .5rem !important
    }
    .m-lg-3 {
        margin: 1rem 1rem !important
    }
    .mt-lg-3 {
        margin-top: 1rem !important
    }
    .mr-lg-3 {
        margin-right: 1rem !important
    }
    .mb-lg-3 {
        margin-bottom: 1rem !important
    }
    .ml-lg-3 {
        margin-left: 1rem !important
    }
    .mx-lg-3 {
        margin-right: 1rem !important;
        margin-left: 1rem !important
    }
    .my-lg-3 {
        margin-top: 1rem !important;
        margin-bottom: 1rem !important
    }
    .m-lg-4 {
        margin: 1.5rem 1.5rem !important
    }
    .mt-lg-4 {
        margin-top: 1.5rem !important
    }
    .mr-lg-4 {
        margin-right: 1.5rem !important
    }
    .mb-lg-4 {
        margin-bottom: 1.5rem !important
    }
    .ml-lg-4 {
        margin-left: 1.5rem !important
    }
    .mx-lg-4 {
        margin-right: 1.5rem !important;
        margin-left: 1.5rem !important
    }
    .my-lg-4 {
        margin-top: 1.5rem !important;
        margin-bottom: 1.5rem !important
    }
    .m-lg-5 {
        margin: 3rem 3rem !important
    }
    .mt-lg-5 {
        margin-top: 3rem !important
    }
    .mr-lg-5 {
        margin-right: 3rem !important
    }
    .mb-lg-5 {
        margin-bottom: 3rem !important
    }
    .ml-lg-5 {
        margin-left: 3rem !important
    }
    .mx-lg-5 {
        margin-right: 3rem !important;
        margin-left: 3rem !important
    }
    .my-lg-5 {
        margin-top: 3rem !important;
        margin-bottom: 3rem !important
    }
    .p-lg-0 {
        padding: 0 0 !important
    }
    .pt-lg-0 {
        padding-top: 0 !important
    }
    .pr-lg-0 {
        padding-right: 0 !important
    }
    .pb-lg-0 {
        padding-bottom: 0 !important
    }
    .pl-lg-0 {
        padding-left: 0 !important
    }
    .px-lg-0 {
        padding-right: 0 !important;
        padding-left: 0 !important
    }
    .py-lg-0 {
        padding-top: 0 !important;
        padding-bottom: 0 !important
    }
    .p-lg-1 {
        padding: .25rem .25rem !important
    }
    .pt-lg-1 {
        padding-top: .25rem !important
    }
    .pr-lg-1 {
        padding-right: .25rem !important
    }
    .pb-lg-1 {
        padding-bottom: .25rem !important
    }
    .pl-lg-1 {
        padding-left: .25rem !important
    }
    .px-lg-1 {
        padding-right: .25rem !important;
        padding-left: .25rem !important
    }
    .py-lg-1 {
        padding-top: .25rem !important;
        padding-bottom: .25rem !important
    }
    .p-lg-2 {
        padding: .5rem .5rem !important
    }
    .pt-lg-2 {
        padding-top: .5rem !important
    }
    .pr-lg-2 {
        padding-right: .5rem !important
    }
    .pb-lg-2 {
        padding-bottom: .5rem !important
    }
    .pl-lg-2 {
        padding-left: .5rem !important
    }
    .px-lg-2 {
        padding-right: .5rem !important;
        padding-left: .5rem !important
    }
    .py-lg-2 {
        padding-top: .5rem !important;
        padding-bottom: .5rem !important
    }
    .p-lg-3 {
        padding: 1rem 1rem !important
    }
    .pt-lg-3 {
        padding-top: 1rem !important
    }
    .pr-lg-3 {
        padding-right: 1rem !important
    }
    .pb-lg-3 {
        padding-bottom: 1rem !important
    }
    .pl-lg-3 {
        padding-left: 1rem !important
    }
    .px-lg-3 {
        padding-right: 1rem !important;
        padding-left: 1rem !important
    }
    .py-lg-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important
    }
    .p-lg-4 {
        padding: 1.5rem 1.5rem !important
    }
    .pt-lg-4 {
        padding-top: 1.5rem !important
    }
    .pr-lg-4 {
        padding-right: 1.5rem !important
    }
    .pb-lg-4 {
        padding-bottom: 1.5rem !important
    }
    .pl-lg-4 {
        padding-left: 1.5rem !important
    }
    .px-lg-4 {
        padding-right: 1.5rem !important;
        padding-left: 1.5rem !important
    }
    .py-lg-4 {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important
    }
    .p-lg-5 {
        padding: 3rem 3rem !important
    }
    .pt-lg-5 {
        padding-top: 3rem !important
    }
    .pr-lg-5 {
        padding-right: 3rem !important
    }
    .pb-lg-5 {
        padding-bottom: 3rem !important
    }
    .pl-lg-5 {
        padding-left: 3rem !important
    }
    .px-lg-5 {
        padding-right: 3rem !important;
        padding-left: 3rem !important
    }
    .py-lg-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important
    }
    .m-lg-auto {
        margin: auto !important
    }
    .mt-lg-auto {
        margin-top: auto !important
    }
    .mr-lg-auto {
        margin-right: auto !important
    }
    .mb-lg-auto {
        margin-bottom: auto !important
    }
    .ml-lg-auto {
        margin-left: auto !important
    }
    .mx-lg-auto {
        margin-right: auto !important;
        margin-left: auto !important
    }
    .my-lg-auto {
        margin-top: auto !important;
        margin-bottom: auto !important
    }
}

@media (min-width: 1200px) {
    .m-xl-0 {
        margin: 0 0 !important
    }
    .mt-xl-0 {
        margin-top: 0 !important
    }
    .mr-xl-0 {
        margin-right: 0 !important
    }
    .mb-xl-0 {
        margin-bottom: 0 !important
    }
    .ml-xl-0 {
        margin-left: 0 !important
    }
    .mx-xl-0 {
        margin-right: 0 !important;
        margin-left: 0 !important
    }
    .my-xl-0 {
        margin-top: 0 !important;
        margin-bottom: 0 !important
    }
    .m-xl-1 {
        margin: .25rem .25rem !important
    }
    .mt-xl-1 {
        margin-top: .25rem !important
    }
    .mr-xl-1 {
        margin-right: .25rem !important
    }
    .mb-xl-1 {
        margin-bottom: .25rem !important
    }
    .ml-xl-1 {
        margin-left: .25rem !important
    }
    .mx-xl-1 {
        margin-right: .25rem !important;
        margin-left: .25rem !important
    }
    .my-xl-1 {
        margin-top: .25rem !important;
        margin-bottom: .25rem !important
    }
    .m-xl-2 {
        margin: .5rem .5rem !important
    }
    .mt-xl-2 {
        margin-top: .5rem !important
    }
    .mr-xl-2 {
        margin-right: .5rem !important
    }
    .mb-xl-2 {
        margin-bottom: .5rem !important
    }
    .ml-xl-2 {
        margin-left: .5rem !important
    }
    .mx-xl-2 {
        margin-right: .5rem !important;
        margin-left: .5rem !important
    }
    .my-xl-2 {
        margin-top: .5rem !important;
        margin-bottom: .5rem !important
    }
    .m-xl-3 {
        margin: 1rem 1rem !important
    }
    .mt-xl-3 {
        margin-top: 1rem !important
    }
    .mr-xl-3 {
        margin-right: 1rem !important
    }
    .mb-xl-3 {
        margin-bottom: 1rem !important
    }
    .ml-xl-3 {
        margin-left: 1rem !important
    }
    .mx-xl-3 {
        margin-right: 1rem !important;
        margin-left: 1rem !important
    }
    .my-xl-3 {
        margin-top: 1rem !important;
        margin-bottom: 1rem !important
    }
    .m-xl-4 {
        margin: 1.5rem 1.5rem !important
    }
    .mt-xl-4 {
        margin-top: 1.5rem !important
    }
    .mr-xl-4 {
        margin-right: 1.5rem !important
    }
    .mb-xl-4 {
        margin-bottom: 1.5rem !important
    }
    .ml-xl-4 {
        margin-left: 1.5rem !important
    }
    .mx-xl-4 {
        margin-right: 1.5rem !important;
        margin-left: 1.5rem !important
    }
    .my-xl-4 {
        margin-top: 1.5rem !important;
        margin-bottom: 1.5rem !important
    }
    .m-xl-5 {
        margin: 3rem 3rem !important
    }
    .mt-xl-5 {
        margin-top: 3rem !important
    }
    .mr-xl-5 {
        margin-right: 3rem !important
    }
    .mb-xl-5 {
        margin-bottom: 3rem !important
    }
    .ml-xl-5 {
        margin-left: 3rem !important
    }
    .mx-xl-5 {
        margin-right: 3rem !important;
        margin-left: 3rem !important
    }
    .my-xl-5 {
        margin-top: 3rem !important;
        margin-bottom: 3rem !important
    }
    .p-xl-0 {
        padding: 0 0 !important
    }
    .pt-xl-0 {
        padding-top: 0 !important
    }
    .pr-xl-0 {
        padding-right: 0 !important
    }
    .pb-xl-0 {
        padding-bottom: 0 !important
    }
    .pl-xl-0 {
        padding-left: 0 !important
    }
    .px-xl-0 {
        padding-right: 0 !important;
        padding-left: 0 !important
    }
    .py-xl-0 {
        padding-top: 0 !important;
        padding-bottom: 0 !important
    }
    .p-xl-1 {
        padding: .25rem .25rem !important
    }
    .pt-xl-1 {
        padding-top: .25rem !important
    }
    .pr-xl-1 {
        padding-right: .25rem !important
    }
    .pb-xl-1 {
        padding-bottom: .25rem !important
    }
    .pl-xl-1 {
        padding-left: .25rem !important
    }
    .px-xl-1 {
        padding-right: .25rem !important;
        padding-left: .25rem !important
    }
    .py-xl-1 {
        padding-top: .25rem !important;
        padding-bottom: .25rem !important
    }
    .p-xl-2 {
        padding: .5rem .5rem !important
    }
    .pt-xl-2 {
        padding-top: .5rem !important
    }
    .pr-xl-2 {
        padding-right: .5rem !important
    }
    .pb-xl-2 {
        padding-bottom: .5rem !important
    }
    .pl-xl-2 {
        padding-left: .5rem !important
    }
    .px-xl-2 {
        padding-right: .5rem !important;
        padding-left: .5rem !important
    }
    .py-xl-2 {
        padding-top: .5rem !important;
        padding-bottom: .5rem !important
    }
    .p-xl-3 {
        padding: 1rem 1rem !important
    }
    .pt-xl-3 {
        padding-top: 1rem !important
    }
    .pr-xl-3 {
        padding-right: 1rem !important
    }
    .pb-xl-3 {
        padding-bottom: 1rem !important
    }
    .pl-xl-3 {
        padding-left: 1rem !important
    }
    .px-xl-3 {
        padding-right: 1rem !important;
        padding-left: 1rem !important
    }
    .py-xl-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important
    }
    .p-xl-4 {
        padding: 1.5rem 1.5rem !important
    }
    .pt-xl-4 {
        padding-top: 1.5rem !important
    }
    .pr-xl-4 {
        padding-right: 1.5rem !important
    }
    .pb-xl-4 {
        padding-bottom: 1.5rem !important
    }
    .pl-xl-4 {
        padding-left: 1.5rem !important
    }
    .px-xl-4 {
        padding-right: 1.5rem !important;
        padding-left: 1.5rem !important
    }
    .py-xl-4 {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important
    }
    .p-xl-5 {
        padding: 3rem 3rem !important
    }
    .pt-xl-5 {
        padding-top: 3rem !important
    }
    .pr-xl-5 {
        padding-right: 3rem !important
    }
    .pb-xl-5 {
        padding-bottom: 3rem !important
    }
    .pl-xl-5 {
        padding-left: 3rem !important
    }
    .px-xl-5 {
        padding-right: 3rem !important;
        padding-left: 3rem !important
    }
    .py-xl-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important
    }
    .m-xl-auto {
        margin: auto !important
    }
    .mt-xl-auto {
        margin-top: auto !important
    }
    .mr-xl-auto {
        margin-right: auto !important
    }
    .mb-xl-auto {
        margin-bottom: auto !important
    }
    .ml-xl-auto {
        margin-left: auto !important
    }
    .mx-xl-auto {
        margin-right: auto !important;
        margin-left: auto !important
    }
    .my-xl-auto {
        margin-top: auto !important;
        margin-bottom: auto !important
    }
}

.text-justify {
    text-align: justify !important
}

.text-nowrap {
    white-space: nowrap !important
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.text-left {
    text-align: left !important
}

.text-right {
    text-align: right !important
}

.text-center {
    text-align: center !important
}

@media (min-width: 576px) {
    .text-sm-left {
        text-align: left !important
    }
    .text-sm-right {
        text-align: right !important
    }
    .text-sm-center {
        text-align: center !important
    }
}

@media (min-width: 768px) {
    .text-md-left {
        text-align: left !important
    }
    .text-md-right {
        text-align: right !important
    }
    .text-md-center {
        text-align: center !important
    }
}

@media (min-width: 992px) {
    .text-lg-left {
        text-align: left !important
    }
    .text-lg-right {
        text-align: right !important
    }
    .text-lg-center {
        text-align: center !important
    }
}

@media (min-width: 1200px) {
    .text-xl-left {
        text-align: left !important
    }
    .text-xl-right {
        text-align: right !important
    }
    .text-xl-center {
        text-align: center !important
    }
}

.text-lowercase {
    text-transform: lowercase !important
}

.text-uppercase {
    text-transform: uppercase !important
}

.text-capitalize {
    text-transform: capitalize !important
}

.font-weight-normal {
    font-weight: 400
}

.font-weight-bold {
    font-weight: 500
}

.font-italic {
    font-style: italic
}

.text-white {
    color: #fff !important
}

.text-muted {
    color: #aaa !important
}

a.text-muted:focus,
a.text-muted:hover {
    color: #919090 !important
}

.text-primary {
    color: #047bf8 !important
}

a.text-primary:focus,
a.text-primary:hover {
    color: #0362c6 !important
}

.text-success {
    color: #90be2e !important
}

a.text-success:focus,
a.text-success:hover {
    color: #719524 !important
}

.text-info {
    color: #5bc0de !important
}

a.text-info:focus,
a.text-info:hover {
    color: #31b0d5 !important
}

.text-warning {
    color: #f0ad4e !important
}

a.text-warning:focus,
a.text-warning:hover {
    color: #ec971f !important
}

.text-danger {
    color: #e65252 !important
}

a.text-danger:focus,
a.text-danger:hover {
    color: #e02525 !important
}

.text-gray-dark {
    color: #292b2c !important
}

a.text-gray-dark:focus,
a.text-gray-dark:hover {
    color: #101112 !important
}

.text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0
}

.invisible {
    visibility: hidden !important
}

.hidden-xs-up {
    display: none !important
}

@media (max-width: 575px) {
    .hidden-xs-down {
        display: none !important
    }
}

@media (min-width: 576px) {
    .hidden-sm-up {
        display: none !important
    }
}

@media (max-width: 767px) {
    .hidden-sm-down {
        display: none !important
    }
}

@media (min-width: 768px) {
    .hidden-md-up {
        display: none !important
    }
}

@media (max-width: 991px) {
    .hidden-md-down {
        display: none !important
    }
}

@media (min-width: 992px) {
    .hidden-lg-up {
        display: none !important
    }
}

@media (max-width: 1199px) {
    .hidden-lg-down {
        display: none !important
    }
}

@media (min-width: 1200px) {
    .hidden-xl-up {
        display: none !important
    }
}

.hidden-xl-down {
    display: none !important
}

.visible-print-block {
    display: none !important
}

@media print {
    .visible-print-block {
        display: block !important
    }
}

.visible-print-inline {
    display: none !important
}

@media print {
    .visible-print-inline {
        display: inline !important
    }
}

.visible-print-inline-block {
    display: none !important
}

@media print {
    .visible-print-inline-block {
        display: inline-block !important
    }
}

@media print {
    .hidden-print {
        display: none !important
    }
}

.table th {
    font-weight: 500
}

.table.table-editable td:hover {
    background-color: #fff;
    -webkit-box-shadow: inset 0px 0px 0px 2px #047bf8;
    box-shadow: inset 0px 0px 0px 2px #047bf8
}

.table.table-lightborder td {
    border-top-color: rgba(83, 101, 140, 0.08)
}

.table.table-lightfont td {
    font-weight: 300
}

.table th,
.table td {
    vertical-align: middle
}

.table th img,
.table td img {
    max-width: 100%
}

.table thead th {
    border-bottom: 1px solid #999
}

.table tfoot th {
    border-top: 1px solid #999
}

.table tfoot th,
.table thead th {
    font-size: .7rem;
    text-transform: uppercase;
    border-top: none
}

.table tbody+tbody {
    border-top: 1px solid rgba(83, 101, 140, 0.33)
}

.table td.nowrap {
    white-space: nowrap
}

.table .row-actions {
    text-align: center
}

.table .row-actions .os-icon {
    font-size: 16px
}

.table .row-actions a {
    margin-right: 0.8rem;
    color: #3E4B5B
}

.table .row-actions a.danger {
    color: #9d1818
}

.table .row-actions a:last-child {
    margin-right: 0px
}

.table .cell-image-list {
    position: relative;
    display: inline-block;
    white-space: nowrap
}

.table .cell-image-list .cell-img {
    display: inline-block;
    width: 30px;
    height: 30px;
    background-size: cover;
    background-position: center center;
    border-radius: 2px;
    -webkit-box-shadow: 0px 0px 0px 2px #fff, 1px 1px 5px rgba(0, 0, 0, 0.8);
    box-shadow: 0px 0px 0px 2px #fff, 1px 1px 5px rgba(0, 0, 0, 0.8);
    vertical-align: middle;
    -webkit-transition: all 0.1s ease;
    transition: all 0.1s ease;
    -webkit-transform: scale(1);
    transform: scale(1);
    position: relative
}

.table .cell-image-list .cell-img:nth-child(1) {
    z-index: 5
}

.table .cell-image-list .cell-img:nth-child(2) {
    z-index: 4
}

.table .cell-image-list .cell-img:nth-child(3) {
    z-index: 3
}

.table .cell-image-list .cell-img:nth-child(4) {
    z-index: 2
}

.table .cell-image-list .cell-img:nth-child(5) {
    z-index: 1
}

.table .cell-image-list .cell-img+.cell-img {
    margin-left: -15px
}

.table .cell-image-list .cell-img+.cell-img:hover {
    -webkit-transform: translateX(5px);
    transform: translateX(5px)
}

.table .cell-image-list .cell-img-more {
    font-size: .7rem;
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    right: -70%;
    background-color: #fff;
    padding: 3px 5px;
    border-radius: 4px;
    z-index: 7;
    white-space: nowrap
}

.table-lg td {
    padding: 1.2rem 1.5rem
}

.table.table-v2 thead tr th,
.table.table-v2 tfoot tr th {
    text-align: center;
    border-top: 1px solid #999;
    border-bottom: 1px solid #999;
    background-color: rgba(0, 0, 0, 0.05)
}

.table.table-v2 thead tr th:first-child,
.table.table-v2 tfoot tr th:first-child {
    border-left: 1px solid #999
}

.table.table-v2 thead tr th:last-child,
.table.table-v2 tfoot tr th:last-child {
    border-right: 1px solid #999
}

.table.table-v2 tbody tr td {
    border-color: #d1d8e6
}

.controls-above-table {
    margin-bottom: 1rem
}

.controls-above-table .btn,
.controls-above-table .wrapper-front .fc-button,
.wrapper-front .controls-above-table .fc-button {
    margin-right: 0.5rem
}

.controls-above-table .form-control {
    margin-right: 1rem
}

.controls-above-table .form-control:last-child {
    margin-right: 0px
}

.controls-below-table {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    font-size: .9rem
}

.controls-below-table .table-records-info {
    color: rgba(0, 0, 0, 0.5)
}

.controls-below-table .table-records-pages ul {
    list-style: none
}

.controls-below-table .table-records-pages ul li {
    display: inline-block;
    margin: 0px 10px
}

.controls-below-table .table-records-pages ul li a.current {
    color: #3E4B5B
}

.wrapper-front table.dataTable {
    border-collapse: collapse !important
}

button,
input,
optgroup,
select,
textarea {
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 400
}

.form-control.rounded {
    border-radius: 30px
}

select.form-control.rounded {
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 40px;
    padding-left: 15px;
    background-position: right 5px top 50%;
    background-repeat: no-repeat;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAMCAYAAABSgIzaAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBNYWNpbnRvc2giIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NDZFNDEwNjlGNzFEMTFFMkJEQ0VDRTM1N0RCMzMyMkIiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NDZFNDEwNkFGNzFEMTFFMkJEQ0VDRTM1N0RCMzMyMkIiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo0NkU0MTA2N0Y3MUQxMUUyQkRDRUNFMzU3REIzMzIyQiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo0NkU0MTA2OEY3MUQxMUUyQkRDRUNFMzU3REIzMzIyQiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PuGsgwQAAAA5SURBVHjaYvz//z8DOYCJgUxAf42MQIzTk0D/M+KzkRGPoQSdykiKJrBGpOhgJFYTWNEIiEeAAAMAzNENEOH+do8AAAAASUVORK5CYII=)
}

.form-text {
    font-size: .9rem
}

.has-danger .form-control-feedback.text-muted {
    color: #e65252 !important
}

.form-control {
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif
}

.form-control.bright {
    border-color: #334652
}

.form-control[type="checkbox"] {
    width: auto;
    display: inline-block
}

.form-control::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.4)
}

.form-control:-ms-input-placeholder {
    color: rgba(0, 0, 0, 0.4)
}

.form-control::placeholder {
    color: rgba(0, 0, 0, 0.4)
}

.form-check-input {
    margin-right: 0.5rem
}

.form-buttons-w {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

.form-buttons-w .btn+.btn,
.form-buttons-w .wrapper-front .fc-button+.btn,
.wrapper-front .form-buttons-w .fc-button+.btn,
.form-buttons-w .wrapper-front .btn+.fc-button,
.wrapper-front .form-buttons-w .btn+.fc-button,
.form-buttons-w .wrapper-front .fc-button+.fc-button,
.wrapper-front .form-buttons-w .fc-button+.fc-button {
    margin-left: 10px
}

label.bigger {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    margin-top: 1rem
}

fieldset {
    margin-top: 2rem
}

legend {
    font-size: 1.1rem;
    display: block;
    margin-bottom: 1.5rem;
    position: relative;
    color: #047bf8
}

legend span {
    padding: 0px 0.5rem 0 0;
    background-color: #fff;
    display: inline-block;
    z-index: 2;
    position: relative
}

legend:before {
    content: "";
    position: absolute;
    left: 0px;
    right: 0px;
    height: 1px;
    top: 50%;
    background-color: rgba(0, 0, 0, 0.1);
    z-index: 1
}

.form-header {
    margin-bottom: 1rem;
    padding-top: 0.5rem;
    display: block
}

.form-desc {
    color: #999;
    margin-bottom: 1.5rem;
    font-weight: 300;
    font-size: .9rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: block
}

.nav.smaller {
    font-size: .8rem
}

.nav.smaller.nav-tabs .nav-link {
    padding: 0.7em 1.1em
}

.nav.smaller.nav-pills .nav-link {
    padding: 0.2em 1.1em
}

.nav.bigger {
    font-size: 1.25rem;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500
}

.nav.bigger.nav-tabs .nav-link.active:after,
.nav.bigger.nav-tabs .nav-item.show .nav-link:after {
    height: 6px;
    bottom: -3px;
    border-radius: 2px
}

.nav.bigger.nav-tabs .nav-link {
    padding-left: 0px;
    padding-right: 0px;
    margin-right: 2rem
}

.nav.upper {
    font-size: 1rem;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 2px
}

.nav.upper.nav-tabs .nav-link.active:after,
.nav.upper.nav-tabs .nav-item.show .nav-link:after {
    height: 3px;
    bottom: -2px;
    border-radius: 2px
}

.nav.upper.nav-tabs .nav-link {
    padding-left: 0px;
    padding-right: 0px;
    padding-bottom: 15px;
    margin-right: 2rem
}

.nav.upper.centered.nav-tabs .nav-link {
    padding-left: 0px;
    padding-right: 0px;
    padding-bottom: 15px;
    margin-left: 1rem;
    margin-right: 1rem
}

.nav.upper.centered.nav-tabs .nav-item {
    margin-right: 0px
}

.nav.centered {
    text-align: center;
    -ms-flex-pack: distribute;
    justify-content: space-around
}

.nav-link i {
    display: inline-block;
    color: #b0c4f3;
    font-size: 26px;
    margin-bottom: 5px
}

.nav-link span {
    display: block;
    font-size: .8rem
}

.nav-link.active i {
    color: #047bf8
}

.nav-tabs .nav-item {
    margin-bottom: 0px;
    margin-right: 1rem
}

.nav-tabs .nav-link {
    border: none;
    color: rgba(0, 0, 0, 0.3)
}

.nav-tabs .nav-link.disabled {
    color: #636c72;
    background-color: transparent;
    border-color: transparent
}

.nav-tabs .nav-link,
.nav-tabs .nav-item .nav-link {
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    position: relative
}

.nav-tabs .nav-link:after,
.nav-tabs .nav-item .nav-link:after {
    content: "";
    width: 0%;
    height: 3px;
    background-color: #047bf8;
    position: absolute;
    bottom: -2px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link:hover,
.nav-tabs .nav-item:hover .nav-link {
    color: #464a4c;
    background-color: transparent;
    border-color: #ddd #ddd transparent;
    position: relative
}

.nav-tabs .nav-link.active:after,
.nav-tabs .nav-item.show .nav-link:after,
.nav-tabs .nav-link:hover:after,
.nav-tabs .nav-item:hover .nav-link:after {
    width: 100%
}

.nav-pills .nav-link {
    border-radius: 30px;
    color: rgba(0, 0, 0, 0.4)
}

.nav-pills .nav-link.active,
.nav-pills .nav-item.show .nav-link {
    color: #fff;
    cursor: default;
    background-color: #047bf8
}

.toggled-buttons .btn-toggled {
    border: 2px solid transparent;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 500;
    font-size: .8rem;
    padding: 4px 8px;
    color: rgba(0, 0, 0, 0.3);
    margin: 5px 0px
}

.toggled-buttons .btn-toggled.on,
.toggled-buttons .btn-toggled:hover {
    border-color: #047bf8;
    color: #047bf8
}

.toggled-buttons .btn-toggled+.btn-toggled {
    margin-left: 10px
}

.toggled-buttons.solid .btn-toggled {
    background-color: rgba(0, 0, 0, 0.07);
    color: rgba(0, 0, 0, 0.6);
    font-size: .9rem
}

.toggled-buttons.solid .btn-toggled.on,
.toggled-buttons.solid .btn-toggled:hover {
    background-color: #047bf8;
    color: #fff
}

.btn-sm,
.btn-group-sm>.btn,
.wrapper-front .btn-group-sm>.fc-button {
    padding: .2rem .5rem;
    font-size: .675rem;
    border-radius: .2rem;
    text-transform: uppercase
}

.btn-white,
.wrapper-front .fc-button {
    color: #333;
    background-color: #fff;
    border-color: rgba(0, 0, 0, 0.5)
}

.btn-white:hover,
.wrapper-front .fc-button:hover {
    color: #333;
    background-color: #e6e5e5;
    border-color: rgba(0, 0, 0, 0.5)
}

.btn-white:focus,
.wrapper-front .fc-button:focus,
.btn-white.focus,
.wrapper-front .focus.fc-button {
    -webkit-box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.5)
}

.btn-white.disabled,
.wrapper-front .disabled.fc-button,
.btn-white:disabled,
.wrapper-front .fc-button:disabled {
    background-color: #fff;
    border-color: rgba(0, 0, 0, 0.5)
}

.btn-white:active,
.wrapper-front .fc-button:active,
.btn-white.active,
.wrapper-front .active.fc-button,
.show>.btn-white.dropdown-toggle,
.wrapper-front .show>.dropdown-toggle.fc-button {
    color: #333;
    background-color: #e6e5e5;
    background-image: none;
    border-color: rgba(0, 0, 0, 0.5)
}

.btn,
.wrapper-front .fc-button {
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 400
}

.btn .os-icon,
.wrapper-front .fc-button .os-icon {
    font-size: 18px;
    margin-right: 1.2rem;
    display: inline-block;
    vertical-align: middle
}

.btn .os-icon+span,
.wrapper-front .fc-button .os-icon+span {
    display: inline-block;
    vertical-align: middle
}

.btn.btn-sm .os-icon,
.btn-group-sm>.btn .os-icon,
.wrapper-front .btn-group-sm>.fc-button .os-icon,
.wrapper-front .btn-sm.fc-button .os-icon {
    font-size: 14px;
    margin-right: .5rem
}

.btn.btn-rounded,
.wrapper-front .btn-rounded.fc-button {
    border-radius: 40px
}

.btn.btn-upper,
.wrapper-front .btn-upper.fc-button {
    text-transform: uppercase;
    letter-spacing: 2px;
    line-height: 1
}

.breadcrumb {
    list-style: none;
    margin: 0px;
    padding: 10px 30px 10px 30px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background-color: transparent
}

.breadcrumb li {
    margin-bottom: 0px;
    display: inline-block;
    text-transform: uppercase;
    font-size: .65rem;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif
}

.breadcrumb li a {
    color: #3E4B5B
}

.breadcrumb li span {
    color: rgba(0, 0, 0, 0.4)
}

.breadcrumbs+.content-box {
    padding-top: 0px
}

.text-muted {
    font-weight: 300
}

@-webkit-keyframes showIntroShot1 {
    0% {
        opacity: 0;
        -webkit-transform: perspective(700px) translate3d(200px, -300px, -200px);
        transform: perspective(700px) translate3d(200px, -300px, -200px)
    }
    100% {
        opacity: 1;
        -webkit-transform: perspective(700px) translate3d(0px, 0px, 0px);
        transform: perspective(700px) translate3d(0px, 0px, 0px)
    }
}

@keyframes showIntroShot1 {
    0% {
        opacity: 0;
        -webkit-transform: perspective(700px) translate3d(200px, -300px, -200px);
        transform: perspective(700px) translate3d(200px, -300px, -200px)
    }
    100% {
        opacity: 1;
        -webkit-transform: perspective(700px) translate3d(0px, 0px, 0px);
        transform: perspective(700px) translate3d(0px, 0px, 0px)
    }
}

@-webkit-keyframes showIntroShot2 {
    0% {
        opacity: 0;
        -webkit-transform: perspective(700px) translate3d(250px, -250px, -200px);
        transform: perspective(700px) translate3d(250px, -250px, -200px)
    }
    100% {
        opacity: 1;
        -webkit-transform: perspective(700px) translate3d(0px, 0px, 0px);
        transform: perspective(700px) translate3d(0px, 0px, 0px)
    }
}

@keyframes showIntroShot2 {
    0% {
        opacity: 0;
        -webkit-transform: perspective(700px) translate3d(250px, -250px, -200px);
        transform: perspective(700px) translate3d(250px, -250px, -200px)
    }
    100% {
        opacity: 1;
        -webkit-transform: perspective(700px) translate3d(0px, 0px, 0px);
        transform: perspective(700px) translate3d(0px, 0px, 0px)
    }
}

@-webkit-keyframes showIntroShot3 {
    0% {
        opacity: 0;
        -webkit-transform: perspective(700px) translate3d(200px, -100px, -200px);
        transform: perspective(700px) translate3d(200px, -100px, -200px)
    }
    100% {
        opacity: 1;
        -webkit-transform: perspective(700px) translate3d(0px, 0px, 0px);
        transform: perspective(700px) translate3d(0px, 0px, 0px)
    }
}

@keyframes showIntroShot3 {
    0% {
        opacity: 0;
        -webkit-transform: perspective(700px) translate3d(200px, -100px, -200px);
        transform: perspective(700px) translate3d(200px, -100px, -200px)
    }
    100% {
        opacity: 1;
        -webkit-transform: perspective(700px) translate3d(0px, 0px, 0px);
        transform: perspective(700px) translate3d(0px, 0px, 0px)
    }
}

.shot1 {
    -webkit-animation: 0.6s ease-in-out 0.65s showIntroShot1;
    animation: 0.6s ease-in-out 0.65s showIntroShot1;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.shot2 {
    -webkit-animation: 0.7s ease-in-out 0.5s showIntroShot2;
    animation: 0.7s ease-in-out 0.5s showIntroShot2;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.shot3 {
    -webkit-animation: 0.5s ease-in-out 0.8s showIntroShot3;
    animation: 0.5s ease-in-out 0.8s showIntroShot3;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showIntroMedia {
    0% {
        -webkit-transform: perspective(1260px) rotateY(50deg) rotateX(-30deg) scale(0.8);
        transform: perspective(1260px) rotateY(50deg) rotateX(-30deg) scale(0.8)
    }
    100% {
        -webkit-transform: perspective(1260px) rotateY(-20.2deg) rotateX(10.6deg) scale(1);
        transform: perspective(1260px) rotateY(-20.2deg) rotateX(10.6deg) scale(1)
    }
}

@keyframes showIntroMedia {
    0% {
        -webkit-transform: perspective(1260px) rotateY(50deg) rotateX(-30deg) scale(0.8);
        transform: perspective(1260px) rotateY(50deg) rotateX(-30deg) scale(0.8)
    }
    100% {
        -webkit-transform: perspective(1260px) rotateY(-20.2deg) rotateX(10.6deg) scale(1);
        transform: perspective(1260px) rotateY(-20.2deg) rotateX(10.6deg) scale(1)
    }
}

.intro-media {
    -webkit-animation: 1.4s ease-in-out 0.2s showIntroMedia;
    animation: 1.4s ease-in-out 0.2s showIntroMedia;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showTopMenu {
    0% {
        -webkit-transform: translate3d(0, -100px, 0);
        transform: translate3d(0, -100px, 0)
    }
    100% {
        -webkit-transform: translate3d(0, 0px, 0);
        transform: translate3d(0, 0px, 0)
    }
}

@keyframes showTopMenu {
    0% {
        -webkit-transform: translate3d(0, -100px, 0);
        transform: translate3d(0, -100px, 0)
    }
    100% {
        -webkit-transform: translate3d(0, 0px, 0);
        transform: translate3d(0, 0px, 0)
    }
}

.menu-top-f {
    -webkit-animation: 0.4s ease-out 1.2s showTopMenu;
    animation: 0.4s ease-out 1.2s showTopMenu;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showIntroHeading {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -80px, 0);
        transform: translate3d(0, -80px, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0px, 0);
        transform: translate3d(0, 0px, 0)
    }
}

@keyframes showIntroHeading {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -80px, 0);
        transform: translate3d(0, -80px, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0px, 0);
        transform: translate3d(0, 0px, 0)
    }
}

.intro-heading {
    -webkit-animation: 1s ease 0.4s showIntroHeading;
    animation: 1s ease 0.4s showIntroHeading;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showIntroText {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 20px, 0);
        transform: translate3d(0, 20px, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0px, 0);
        transform: translate3d(0, 0px, 0)
    }
}

@keyframes showIntroText {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 20px, 0);
        transform: translate3d(0, 20px, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0px, 0);
        transform: translate3d(0, 0px, 0)
    }
}

.intro-text {
    -webkit-animation: 0.8s ease 0.8s showIntroText;
    animation: 0.8s ease 0.8s showIntroText;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showIntroDescription {
    0% {
        -webkit-transform: perspective(1260px) rotateY(25deg) rotateX(15deg);
        transform: perspective(1260px) rotateY(25deg) rotateX(15deg)
    }
    100% {
        -webkit-transform: perspective(1260px) rotateY(0deg) rotateX(0deg);
        transform: perspective(1260px) rotateY(0deg) rotateX(0deg)
    }
}

@keyframes showIntroDescription {
    0% {
        -webkit-transform: perspective(1260px) rotateY(25deg) rotateX(15deg);
        transform: perspective(1260px) rotateY(25deg) rotateX(15deg)
    }
    100% {
        -webkit-transform: perspective(1260px) rotateY(0deg) rotateX(0deg);
        transform: perspective(1260px) rotateY(0deg) rotateX(0deg)
    }
}

.intro-description {
    -webkit-animation: 2s ease 0.4s showIntroDescription;
    animation: 2s ease 0.4s showIntroDescription;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showIntroButton {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(40px, 0px, 0px);
        transform: translate3d(40px, 0px, 0px)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px);
        transform: translate3d(0px, 0px, 0px)
    }
}

@keyframes showIntroButton {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(40px, 0px, 0px);
        transform: translate3d(40px, 0px, 0px)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px);
        transform: translate3d(0px, 0px, 0px)
    }
}

.intro-buttons .btn-primary,
.intro-buttons .wrapper-front .fc-button.fc-state-active,
.wrapper-front .intro-buttons .fc-button.fc-state-active {
    -webkit-animation: 0.4s ease 1.4s showIntroButton;
    animation: 0.4s ease 1.4s showIntroButton;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.intro-buttons .btn-link {
    -webkit-animation: 0.4s ease 1.5s showIntroButton;
    animation: 0.4s ease 1.5s showIntroButton;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes fadeInFader2 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-460px, 0px, 0px) rotate(-45deg);
        transform: translate3d(-460px, 0px, 0px) rotate(-45deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(-360px, 0px, 0px) rotate(-45deg);
        transform: translate3d(-360px, 0px, 0px) rotate(-45deg)
    }
}

@keyframes fadeInFader2 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-460px, 0px, 0px) rotate(-45deg);
        transform: translate3d(-460px, 0px, 0px) rotate(-45deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(-360px, 0px, 0px) rotate(-45deg);
        transform: translate3d(-360px, 0px, 0px) rotate(-45deg)
    }
}

@-webkit-keyframes fadeInFader1 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-50px, -200px, 0px) rotate(-45deg);
        transform: translate3d(-50px, -200px, 0px) rotate(-45deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(-150px, -200px, 0px) rotate(-45deg);
        transform: translate3d(-150px, -200px, 0px) rotate(-45deg)
    }
}

@keyframes fadeInFader1 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-50px, -200px, 0px) rotate(-45deg);
        transform: translate3d(-50px, -200px, 0px) rotate(-45deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(-150px, -200px, 0px) rotate(-45deg);
        transform: translate3d(-150px, -200px, 0px) rotate(-45deg)
    }
}

.wrapper-front>.fade1 {
    -webkit-animation: 2s ease 0s fadeInFader1;
    animation: 2s ease 0s fadeInFader1;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.intro-w .fade2 {
    -webkit-animation: 2s ease 0s fadeInFader2;
    animation: 2s ease 0s fadeInFader2;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes fadeInPropertyItem {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0px, 150px, 0px) rotate(0deg);
        transform: translate3d(0px, 150px, 0px) rotate(0deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px) rotate(0deg);
        transform: translate3d(0px, 0px, 0px) rotate(0deg)
    }
}

@keyframes fadeInPropertyItem {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0px, 150px, 0px) rotate(0deg);
        transform: translate3d(0px, 150px, 0px) rotate(0deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px) rotate(0deg);
        transform: translate3d(0px, 0px, 0px) rotate(0deg)
    }
}

.property-items .property-item:nth-child(0) {
    -webkit-animation: 0.5s ease 0s fadeInPropertyItem;
    animation: 0.5s ease 0s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(1) {
    -webkit-animation: 0.5s ease .1s fadeInPropertyItem;
    animation: 0.5s ease .1s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(2) {
    -webkit-animation: 0.5s ease .2s fadeInPropertyItem;
    animation: 0.5s ease .2s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(3) {
    -webkit-animation: 0.5s ease .3s fadeInPropertyItem;
    animation: 0.5s ease .3s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(4) {
    -webkit-animation: 0.5s ease .4s fadeInPropertyItem;
    animation: 0.5s ease .4s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(5) {
    -webkit-animation: 0.5s ease .5s fadeInPropertyItem;
    animation: 0.5s ease .5s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(6) {
    -webkit-animation: 0.5s ease .6s fadeInPropertyItem;
    animation: 0.5s ease .6s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(7) {
    -webkit-animation: 0.5s ease .7s fadeInPropertyItem;
    animation: 0.5s ease .7s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(8) {
    -webkit-animation: 0.5s ease .8s fadeInPropertyItem;
    animation: 0.5s ease .8s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(9) {
    -webkit-animation: 0.5s ease .9s fadeInPropertyItem;
    animation: 0.5s ease .9s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(10) {
    -webkit-animation: 0.5s ease 1s fadeInPropertyItem;
    animation: 0.5s ease 1s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(11) {
    -webkit-animation: 0.5s ease 1.1s fadeInPropertyItem;
    animation: 0.5s ease 1.1s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(12) {
    -webkit-animation: 0.5s ease 1.2s fadeInPropertyItem;
    animation: 0.5s ease 1.2s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(13) {
    -webkit-animation: 0.5s ease 1.3s fadeInPropertyItem;
    animation: 0.5s ease 1.3s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(14) {
    -webkit-animation: 0.5s ease 1.4s fadeInPropertyItem;
    animation: 0.5s ease 1.4s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showPropertyTopBar {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0px, -30px, 0px) rotate(0deg);
        transform: translate3d(0px, -30px, 0px) rotate(0deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px) rotate(0deg);
        transform: translate3d(0px, 0px, 0px) rotate(0deg)
    }
}

@keyframes showPropertyTopBar {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0px, -30px, 0px) rotate(0deg);
        transform: translate3d(0px, -30px, 0px) rotate(0deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px) rotate(0deg);
        transform: translate3d(0px, 0px, 0px) rotate(0deg)
    }
}

.top-bar {
    -webkit-animation: 0.7s ease 0s showPropertyTopBar;
    animation: 0.7s ease 0s showPropertyTopBar;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showPropertyFilter {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-50px, 0px, 0px) rotate(0deg);
        transform: translate3d(-50px, 0px, 0px) rotate(0deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px) rotate(0deg);
        transform: translate3d(0px, 0px, 0px) rotate(0deg)
    }
}

@keyframes showPropertyFilter {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-50px, 0px, 0px) rotate(0deg);
        transform: translate3d(-50px, 0px, 0px) rotate(0deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px) rotate(0deg);
        transform: translate3d(0px, 0px, 0px) rotate(0deg)
    }
}

.rentals-list-w .filter-side {
    -webkit-animation: 0.7s ease 0s showPropertyFilter;
    animation: 0.7s ease 0s showPropertyFilter;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

html {
    height: 100%
}

body {
    background-color: #f8faff
}

body.white {
    background-color: #fff
}

body.white .footer-w {
    background-image: none
}

body.white .footer-w .fade3 {
    display: none
}

b,
strong {
    font-weight: 500
}

.wrapper-front {
    position: relative;
    overflow: hidden
}

.os-container {
    max-width: 1600px;
    margin: 0px auto;
    padding-left: 40px;
    padding-right: 40px
}

.section-header-w {
    position: relative
}

.section-header {
    max-width: 700px;
    padding: 100px 0px 70px 0px
}

.section-header .section-sub-title {
    text-transform: uppercase;
    letter-spacing: 2px
}

.section-header .section-title {
    color: #047bf8;
    font-size: 2.99rem;
    margin: 30px 0px;
    margin-top: 10px;
    position: relative
}

.section-header .section-title:before,
.section-header .section-title:after {
    content: "";
    width: 15px;
    height: 15px;
    top: -29px;
    border-radius: 10px;
    position: absolute
}

.section-header .section-title:before {
    left: -52px;
    background-color: #98c9fd
}

.section-header .section-title:after {
    left: -40px;
    background-color: #047bf8
}

.section-header .section-desc {
    color: #868686;
    font-weight: 300;
    font-size: 1.2rem
}

.section-header.dark .section-sub-title {
    color: #f8c52a
}

.section-header.dark .section-title {
    color: #fff
}

.section-header.dark .section-title:before {
    background-color: #fdedbe
}

.section-header.dark .section-title:after {
    background-color: #f8c52a
}

.section-header.dark .section-desc {
    color: rgba(255, 255, 255, 0.6)
}

.section-header-w.centered {
    text-align: center
}

.section-header-w.centered .section-header {
    margin: 0px auto
}

.section-header-w.centered .section-header .section-title:before,
.section-header-w.centered .section-header .section-title:after {
    width: 26px;
    height: 26px;
    border-radius: 13px;
    top: -60px;
    left: 50%
}

.section-header-w.centered .section-header .section-title:before {
    -webkit-transform: translateX(-90%);
    transform: translateX(-90%);
    background-color: #98c9fd
}

.section-header-w.centered .section-header .section-title:after {
    -webkit-transform: translateX(-10%);
    transform: translateX(-10%);
    background-color: #047bf8
}

.fade1 {
    position: absolute;
    top: -100px;
    right: -300px;
    width: 700px;
    height: 600px;
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(227, 231, 248, 0)), to(#8BBAF5));
    background-image: linear-gradient(90deg, rgba(227, 231, 248, 0) 0%, #8BBAF5 100%);
    -webkit-transform: translate(-150px, -200px) rotate(-45deg);
    transform: translate(-150px, -200px) rotate(-45deg);
    z-index: -1
}

.fade1:before {
    content: "";
    position: absolute;
    right: 200px;
    top: -200px;
    width: 800px;
    height: 600px;
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(227, 231, 248, 0)), to(#E8F2FC));
    background-image: linear-gradient(90deg, rgba(227, 231, 248, 0) 0%, #E8F2FC 100%);
    z-index: -1
}

.fade2 {
    position: absolute;
    bottom: 0px;
    left: 0px;
    width: 800px;
    height: 600px;
    background-image: -webkit-gradient(linear, right top, left top, from(rgba(237, 240, 243, 0)), to(#8BBAF5));
    background-image: linear-gradient(-90deg, rgba(237, 240, 243, 0) 0%, #8BBAF5 100%);
    z-index: -1;
    -webkit-transform: translate(-360px, 0px) rotate(-45deg);
    transform: translate(-360px, 0px) rotate(-45deg)
}

.fade2:before {
    content: "";
    position: absolute;
    right: 200px;
    top: -200px;
    width: 800px;
    height: 600px;
    background-image: -webkit-gradient(linear, right top, left top, from(rgba(227, 231, 248, 0)), to(#E8F2FC));
    background-image: linear-gradient(-90deg, rgba(227, 231, 248, 0) 0%, #E8F2FC 100%);
    z-index: -1
}

.fade3 {
    position: absolute;
    right: -800px;
    top: 0px;
    width: 800px;
    height: 600px;
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(227, 231, 248, 0)), to(#8BBAF5));
    background-image: linear-gradient(90deg, rgba(227, 231, 248, 0) 0%, #8BBAF5 100%);
    -webkit-transform: translate(-360px, 0px) rotate(-45deg);
    transform: translate(-360px, 0px) rotate(-45deg);
    z-index: -1
}

.fade3:before {
    content: "";
    position: absolute;
    right: -40px;
    top: -200px;
    width: 1000px;
    height: 600px;
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(227, 231, 248, 0)), to(#E8F2FC));
    background-image: linear-gradient(90deg, rgba(227, 231, 248, 0) 0%, #E8F2FC 100%);
    z-index: -1
}

.fade4 {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 800px;
    height: 600px;
    background-image: -webkit-gradient(linear, right top, left top, from(rgba(237, 240, 243, 0)), to(rgba(54, 51, 175, 0.57)));
    background-image: linear-gradient(-90deg, rgba(237, 240, 243, 0) 0%, rgba(54, 51, 175, 0.57) 100%);
    z-index: 1;
    -webkit-transform: translate(-360px, 0px) rotate(-45deg);
    transform: translate(-360px, 0px) rotate(-45deg)
}

.fade4:before {
    content: "";
    position: absolute;
    right: 200px;
    top: -200px;
    width: 800px;
    height: 600px;
    background-image: -webkit-gradient(linear, right top, left top, from(rgba(227, 231, 248, 0)), to(rgba(236, 218, 255, 0.43)));
    background-image: linear-gradient(-90deg, rgba(227, 231, 248, 0) 0%, rgba(236, 218, 255, 0.43) 100%);
    z-index: -1
}

.menu-top-f {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.menu-top-f .logo-element {
    content: "";
    width: 26px;
    height: 26px;
    border-radius: 15px;
    position: relative;
    background-color: #98c9fd
}

.menu-top-f .logo-element:after {
    content: "";
    width: 26px;
    height: 26px;
    background-color: #047bf8;
    border-radius: 15px;
    right: -20px;
    position: absolute
}

.menu-top-f .menu-top-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.menu-top-f .logo-w a.logo {
    display: inline-block;
    vertical-align: middle
}

.menu-top-f .logo-w a.logo img {
    width: 50px;
    height: auto;
    display: inline-block
}

.menu-top-f ul.main-menu {
    list-style: none;
    margin: 0px;
    padding: 0px;
    text-transform: uppercase;
    color: #3E4B5B;
    font-weight: 500;
    letter-spacing: 3px;
    font-size: 1rem
}

.menu-top-f ul.main-menu li {
    display: inline-block
}

.menu-top-f ul.main-menu li a {
    display: inline-block;
    padding: 25px 25px;
    color: rgba(0, 0, 0, 0.25);
    position: relative;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-top-f ul.main-menu li a:after {
    content: "";
    background-color: #047bf8;
    position: absolute;
    bottom: 0px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 0px;
    height: 5px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-top-f ul.main-menu li.active a,
.menu-top-f ul.main-menu li:hover a {
    color: #3E4B5B;
    text-decoration: none
}

.menu-top-f ul.main-menu li.active a:after,
.menu-top-f ul.main-menu li:hover a:after {
    width: 100%
}

.menu-top-f ul.small-menu {
    list-style: none;
    margin: 0px;
    padding: 0px;
    text-transform: uppercase;
    font-size: .8rem
}

.menu-top-f ul.small-menu li {
    display: inline-block;
    padding: 0px 10px
}

.menu-top-f ul.small-menu li a {
    color: #3E4B5B;
    display: inline-block;
    vertical-align: middle;
    letter-spacing: 2px
}

.menu-top-f ul.small-menu li a i {
    display: inline-block;
    vertical-align: middle;
    color: #BA8C0A;
    margin-right: 10px
}

.menu-top-f ul.small-menu li a span {
    display: inline-block;
    vertical-align: middle
}

.menu-top-f ul.small-menu li a.highlight {
    background-color: #047bf8;
    color: #fff;
    border-radius: 20px;
    padding: 3px 14px
}

.menu-top-f ul.small-menu li.separate {
    padding-left: 30px;
    margin-left: 15px;
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

.mobile-menu-w,
ul.mobile-menu-holder {
    display: none
}

.mobile-menu-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 20px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.mobile-menu-i .mobile-menu-trigger {
    color: #111;
    display: inline-block
}

.mobile-menu-holder {
    background-color: #fff;
    -webkit-box-shadow: 0px 0px 30px rgba(69, 101, 173, 0.1);
    box-shadow: 0px 0px 30px rgba(69, 101, 173, 0.1);
    position: relative;
    display: none
}

.mobile-menu-holder ul.mobile-menu {
    list-style: none;
    padding: 10px 1rem;
    margin-bottom: 0px
}

.mobile-menu-holder ul.mobile-menu>li {
    display: block;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    text-align: center
}

.mobile-menu-holder ul.mobile-menu>li:last-child {
    border-bottom: none
}

.mobile-menu-holder ul.mobile-menu>li.has-sub-menu>a:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e91c";
    font-size: 7px;
    color: rgba(0, 0, 0, 0.5);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    position: absolute;
    top: 50%;
    right: 10px
}

.mobile-menu-holder ul.mobile-menu>li.has-sub-menu.active .sub-menu {
    display: block
}

.mobile-menu-holder ul.mobile-menu>li>a {
    color: #3E4B5B;
    display: block;
    position: relative;
    padding: 15px
}

.mobile-menu-holder ul.mobile-menu>li>a:focus {
    text-decoration: none
}

.mobile-menu-holder ul.mobile-menu>li>a:hover {
    text-decoration: none
}

.mobile-menu-holder ul.mobile-menu>li .icon-w {
    color: #0073ff;
    font-size: 27px;
    display: block;
    padding: 1rem;
    width: 80px;
    text-align: center;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.mobile-menu-holder ul.mobile-menu>li span {
    padding: 1rem;
    display: block;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.mobile-menu-holder ul.mobile-menu>li .icon-w+span {
    padding-left: 0px
}

.mobile-menu-holder ul.sub-menu {
    padding: 1rem 0px;
    padding-left: 55px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: none
}

.mobile-menu-holder ul.sub-menu li {
    padding: 0.4rem 10px 0.4rem 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.mobile-menu-holder ul.sub-menu li:last-child {
    border-bottom: none
}

.mobile-menu-holder ul.sub-menu li a {
    font-size: .9rem
}

.mobile-menu-holder.color-scheme-dark {
    background-image: -webkit-gradient(linear, left top, left bottom, from(#3D4D75), to(#31395B));
    background-image: linear-gradient(to bottom, #3D4D75 0%, #31395B 100%);
    background-repeat: repeat-x;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#1c4cc3), to(#1c2e7b));
    background-image: linear-gradient(to bottom, #1c4cc3 0%, #1c2e7b 100%);
    background-repeat: repeat-x;
    color: rgba(255, 255, 255, 0.9)
}

.mobile-menu-holder.color-scheme-dark .side-menu-magic {
    background-image: linear-gradient(-154deg, #6d16a3 8%, #5211e6 90%)
}

.mobile-menu-holder.color-scheme-dark ul.sub-menu li {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.mobile-menu-holder.color-scheme-dark ul.sub-menu li a {
    color: #fff
}

.mobile-menu-holder.color-scheme-dark ul.mobile-menu .icon-w {
    color: #9db2ff
}

.mobile-menu-holder.color-scheme-dark ul.mobile-menu>li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05)
}

.mobile-menu-holder.color-scheme-dark ul.mobile-menu>li>a {
    color: #fff
}

.mobile-menu-holder.color-scheme-dark ul.mobile-menu>li>a:before {
    color: #fff
}

.mobile-menu-holder.color-scheme-dark .sub-menu-w {
    -webkit-box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2);
    box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2)
}

.relative {
    position: relative
}

.padded-v {
    padding: 1rem 10px
}

.padded-v-big {
    padding: 2rem 10px
}

.padded {
    padding: 1rem 2rem
}

.b-l {
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

.b-r {
    border-right: 1px solid rgba(0, 0, 0, 0.1)
}

.b-t {
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

.b-b {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.m-t {
    margin-top: 1rem
}

.m-b {
    margin-bottom: 1rem
}

@media (min-width: 992px) {
    .padded-lg {
        padding: 1rem 2rem
    }
    .b-l-lg {
        border-left: 1px solid rgba(0, 0, 0, 0.1)
    }
    .b-r-lg {
        border-right: 1px solid rgba(0, 0, 0, 0.1)
    }
    .b-t-lg {
        border-top: 1px solid rgba(0, 0, 0, 0.1)
    }
    .b-b-lg {
        border-bottom: 1px solid rgba(0, 0, 0, 0.1)
    }
}

@font-face {
    font-family: 'osfont';
    src: url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype");
    font-weight: normal;
    font-style: normal
}

.os-icon {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.os-icon-star-full:before {
    content: "\e970"
}

.os-icon-arrow-2-right:before {
    content: "\e971"
}

.os-icon-minus:before {
    content: "\e96f"
}

.os-icon-arrow-right:before {
    content: "\e90e"
}

.os-icon-arrow-right2:before {
    content: "\e90f"
}

.os-icon-arrow-right3:before {
    content: "\e910"
}

.os-icon-arrow-right4:before {
    content: "\e911"
}

.os-icon-arrow-right5:before {
    content: "\e912"
}

.os-icon-arrow-left:before {
    content: "\e913"
}

.os-icon-arrow-left2:before {
    content: "\e914"
}

.os-icon-arrow-left3:before {
    content: "\e915"
}

.os-icon-arrow-left4:before {
    content: "\e916"
}

.os-icon-arrow-up:before {
    content: "\e917"
}

.os-icon-arrow-down:before {
    content: "\e918"
}

.os-icon-arrow-left5:before {
    content: "\e919"
}

.os-icon-arrow-down2:before {
    content: "\e91a"
}

.os-icon-arrow-down3:before {
    content: "\e91b"
}

.os-icon-arrow-down4:before {
    content: "\e91c"
}

.os-icon-arrow-up2:before {
    content: "\e91d"
}

.os-icon-arrow-up3:before {
    content: "\e91e"
}

.os-icon-arrow-down5:before {
    content: "\e91f"
}

.os-icon-arrow-up4:before {
    content: "\e920"
}

.os-icon-arrow-up5:before {
    content: "\e921"
}

.os-icon-search:before {
    content: "\e92c"
}

.os-icon-ui-34:before {
    content: "\e984"
}

.os-icon-ui-21:before {
    content: "\e983"
}

.os-icon-documents-15:before {
    content: "\e97f"
}

.os-icon-documents-17:before {
    content: "\e980"
}

.os-icon-documents-11:before {
    content: "\e981"
}

.os-icon-documents-13:before {
    content: "\e982"
}

.os-icon-ui-23:before {
    content: "\e97e"
}

.os-icon-home-11:before {
    content: "\e97a"
}

.os-icon-ui-09:before {
    content: "\e97b"
}

.os-icon-old-tv-2:before {
    content: "\e97c"
}

.os-icon-fire:before {
    content: "\e97d"
}

.os-icon-home-10:before {
    content: "\e976"
}

.os-icon-home-09:before {
    content: "\e977"
}

.os-icon-home-13:before {
    content: "\e978"
}

.os-icon-home-34:before {
    content: "\e979"
}

.os-icon-ui-90:before {
    content: "\e975"
}

.os-icon-ui-03:before {
    content: "\e974"
}

.os-icon-ui-83:before {
    content: "\e972"
}

.os-icon-ui-74:before {
    content: "\e973"
}

.os-icon-pencil-12:before {
    content: "\e96e"
}

.os-icon-ui-33:before {
    content: "\e96c"
}

.os-icon-ui-49:before {
    content: "\e96d"
}

.os-icon-grid-10:before {
    content: "\e96b"
}

.os-icon-common-03:before {
    content: "\e968"
}

.os-icon-ui-22:before {
    content: "\e969"
}

.os-icon-ui-46:before {
    content: "\e96a"
}

.os-icon-basic-1-138-quotes:before {
    content: "\e966";
    color: #474a56
}

.os-icon-ui-07:before {
    content: "\e962"
}

.os-icon-social-09:before {
    content: "\e963"
}

.os-icon-finance-28:before {
    content: "\e964"
}

.os-icon-finance-29:before {
    content: "\e965"
}

.os-icon-checkmark:before {
    content: "\e961"
}

.os-icon-ui-93:before {
    content: "\e95d"
}

.os-icon-mail-14:before {
    content: "\e95e"
}

.os-icon-phone-15:before {
    content: "\e95f"
}

.os-icon-phone-18:before {
    content: "\e960"
}

.os-icon-ui-55:before {
    content: "\e95c"
}

.os-icon-mail-19:before {
    content: "\e95a"
}

.os-icon-mail-18:before {
    content: "\e95b"
}

.os-icon-grid-18:before {
    content: "\e950"
}

.os-icon-ui-02:before {
    content: "\e951"
}

.os-icon-ui-37:before {
    content: "\e952"
}

.os-icon-common-07:before {
    content: "\e953"
}

.os-icon-ui-54:before {
    content: "\e954"
}

.os-icon-ui-44:before {
    content: "\e955"
}

.os-icon-ui-15:before {
    content: "\e956"
}

.os-icon-documents-03:before {
    content: "\e957"
}

.os-icon-ui-92:before {
    content: "\e958"
}

.os-icon-phone-21:before {
    content: "\e959"
}

.os-icon-documents-07:before {
    content: "\e94c"
}

.os-icon-others-29:before {
    content: "\e94d"
}

.os-icon-ui-65:before {
    content: "\e94e"
}

.os-icon-ui-51:before {
    content: "\e94f"
}

.os-icon-mail-07:before {
    content: "\e94b"
}

.os-icon-mail-01:before {
    content: "\e949"
}

.os-icon-others-43:before {
    content: "\e94a"
}

.os-icon-mail-12:before {
    content: "\e967"
}

.os-icon-signs-11:before {
    content: "\e946"
}

.os-icon-coins-4:before {
    content: "\e947"
}

.os-icon-user-male-circle2:before {
    content: "\e948"
}

.os-icon-emoticon-smile:before {
    content: "\e943"
}

.os-icon-robot-2:before {
    content: "\e944"
}

.os-icon-robot-1:before {
    content: "\e945"
}

.os-icon-crown:before {
    content: "\e942"
}

.os-icon-cancel-circle:before {
    content: "\e93f"
}

.os-icon-cancel-square:before {
    content: "\e940"
}

.os-icon-close:before {
    content: "\e941"
}

.os-icon-grid-circles:before {
    content: "\e93c"
}

.os-icon-grid-squares-22:before {
    content: "\e93d"
}

.os-icon-grid-squares2:before {
    content: "\e93e"
}

.os-icon-tasks-checked:before {
    content: "\e93a"
}

.os-icon-hierarchy-structure-2:before {
    content: "\e93b"
}

.os-icon-agenda-1:before {
    content: "\e935"
}

.os-icon-cv-2:before {
    content: "\e936"
}

.os-icon-grid-squares-2:before {
    content: "\e937"
}

.os-icon-grid-squares:before {
    content: "\e938"
}

.os-icon-calendar-time:before {
    content: "\e939"
}

.os-icon-twitter:before {
    content: "\e933"
}

.os-icon-facebook:before {
    content: "\e934"
}

.os-icon-pie-chart-2:before {
    content: "\e92d"
}

.os-icon-pie-chart-1:before {
    content: "\e92e"
}

.os-icon-pie-chart-3:before {
    content: "\e92f"
}

.os-icon-donut-chart-1:before {
    content: "\e930"
}

.os-icon-bar-chart-up:before {
    content: "\e931"
}

.os-icon-bar-chart-stats-up:before {
    content: "\e932"
}

.os-icon-hamburger-menu-2:before {
    content: "\e92a"
}

.os-icon-hamburger-menu-1:before {
    content: "\e92b"
}

.os-icon-email-2-at:before {
    content: "\e928"
}

.os-icon-email-2-at2:before {
    content: "\e929"
}

.os-icon-fingerprint:before {
    content: "\e927"
}

.os-icon-basic-2-259-calendar:before {
    content: "\e926";
    color: #474a56
}

.os-icon-arrow-2-up:before {
    content: "\e924"
}

.os-icon-arrow-2-down:before {
    content: "\e925"
}

.os-icon-bar-chart-down:before {
    content: "\e922"
}

.os-icon-graph-down:before {
    content: "\e923"
}

.os-icon-pencil-1:before {
    content: "\e90b"
}

.os-icon-edit-3:before {
    content: "\e90c"
}

.os-icon-edit-1:before {
    content: "\e90d"
}

.os-icon-database-remove:before {
    content: "\e908"
}

.os-icon-pencil-2:before {
    content: "\e909"
}

.os-icon-link-3:before {
    content: "\e90a"
}

.os-icon-email-forward:before {
    content: "\e907"
}

.os-icon-delivery-box-2:before {
    content: "\e900"
}

.os-icon-wallet-loaded:before {
    content: "\e901"
}

.os-icon-newspaper:before {
    content: "\e902"
}

.os-icon-window-content:before {
    content: "\e903"
}

.os-icon-donut-chart-2:before {
    content: "\e904"
}

.os-icon-text-input:before {
    content: "\e905"
}

.os-icon-user-male-circle:before {
    content: "\e906"
}

.intro-w {
    position: relative
}

.intro-w.layout-v1 {
    padding: 1% 0px
}

.intro-w.layout-v1 .intro-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 75vh
}

.intro-w.layout-v1 .intro-description {
    -webkit-box-flex: 0;
    -ms-flex: 0 1 600px;
    flex: 0 1 600px
}

.intro-w.layout-v1 .intro-description .intro-heading {
    font-size: 3.68rem;
    margin-bottom: 1.5rem;
    position: relative
}

.intro-w.layout-v1 .intro-description .intro-heading:before,
.intro-w.layout-v1 .intro-description .intro-heading:after {
    content: "";
    width: 20px;
    height: 20px;
    top: -55px;
    border-radius: 15px;
    position: absolute
}

.intro-w.layout-v1 .intro-description .intro-heading:before {
    left: 5;
    background-color: #b1d6fe
}

.intro-w.layout-v1 .intro-description .intro-heading:after {
    left: 20px;
    background-color: #047bf8
}

.intro-w.layout-v1 .intro-description .intro-heading span {
    color: #047bf8
}

.intro-w.layout-v1 .intro-description .intro-text {
    font-size: 1.3rem;
    /* color: #868686; */
    font-weight: 300
}

.intro-w.layout-v1 .intro-description .intro-buttons {
    padding-top: 30px
}

.intro-w.layout-v1 .intro-description .intro-buttons .btn,
.intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .fc-button,
.wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .fc-button {
    border-radius: 40px;
    font-size: 1.1rem;
    padding: 15px 30px
}

.intro-w.layout-v1 .intro-description .intro-buttons .btn i,
.intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .fc-button i,
.wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .fc-button i {
    font-size: 30px
}

.intro-w.layout-v1 .intro-description .intro-buttons .btn span.highlight,
.intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .fc-button span.highlight,
.wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .fc-button span.highlight {
    display: inline-block;
    vertical-align: middle;
    margin-left: 15px;
    padding-left: 15px;
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

.intro-w.layout-v1 .intro-description .intro-buttons .btn.btn-primary,
.intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .btn-primary.fc-button,
.wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .btn-primary.fc-button,
.intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .fc-button.fc-state-active,
.wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .fc-button.fc-state-active {
    border: transparent;
    margin-right: 50px
}

.intro-w.layout-v1 .intro-description .intro-buttons .btn.btn-link,
.intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .btn-link.fc-button,
.wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .btn-link.fc-button {
    border: transparent;
    border-bottom: 2px solid #047bf8;
    border-radius: 0px;
    padding: 5px 5px
}

.intro-w.layout-v1 .intro-media {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    padding-left: 120px;
    position: relative;
    -webkit-transform: perspective(1260px) rotateY(-20.2deg) rotateX(10.6deg) scale(1);
    transform: perspective(1260px) rotateY(-20.2deg) rotateX(10.6deg) scale(1)
}

.intro-w.layout-v1 .intro-media .shot {
    position: absolute
}

.intro-w.layout-v1 .intro-media .shot .shot-i {
    -webkit-box-shadow: 0 2px 50px 0 rgba(68, 125, 232, 0.5);
    box-shadow: 0 2px 50px 0 rgba(68, 125, 232, 0.5);
    background-size: cover
}

.intro-w.layout-v1 .intro-media .shot1 {
    z-index: 2;
    width: 95%
}

.intro-w.layout-v1 .intro-media .shot1 .shot-i {
    padding-bottom: 57%;
    -webkit-transform: translate(0, -70%);
    transform: translate(0, -70%)
}

.intro-w.layout-v1 .intro-media .shot2 {
    z-index: 1;
    width: 37%
}

.intro-w.layout-v1 .intro-media .shot2 .shot-i {
    padding-bottom: 105%;
    -webkit-transform: translate(-45%, -10%);
    transform: translate(-45%, -10%)
}

.intro-w.layout-v1 .intro-media .shot3 {
    z-index: 3;
    width: 75%
}

.intro-w.layout-v1 .intro-media .shot3 .shot-i {
    padding-bottom: 81%;
    -webkit-transform: translate(30%, -36%);
    transform: translate(30%, -36%)
}

.intro-w.layout-v1 .intro-media .shot4 {
    z-index: 4
}

.intro-w.layout-v1 .intro-media .shot4 .shot-i {
    padding-bottom: 81%;
    width: 500px;
    -webkit-transform: translate(10%, -16%);
    transform: translate(10%, -16%)
}

.counters-w {
    background-image: -webkit-gradient(linear, left top, right top, from(#047bf8), to(#0d1e73));
    background-image: linear-gradient(to right, #047bf8, #0d1e73);
    position: relative
}

.counters-w .decor {
    position: absolute;
    bottom: 100%;
    right: 0px;
    max-width: 100%;
    z-index: 3
}

.counters-w .decor .decor-path {
    fill: #0b3898
}

.counters-w .decor2 {
    position: absolute;
    top: 100%;
    left: 0px;
    max-width: 100%;
    z-index: 3;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.counters-w .decor2 .decor-path {
    fill: #0667dc
}

.counters-w .counters-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: distribute;
    justify-content: space-around;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 40px;
    color: #fff
}

.counters-w .counter {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.counters-w .counter .counter-value-w {
    text-align: center;
    margin-right: 25px;
    padding-right: 25px;
    border-right: 1px solid rgba(255, 255, 255, 0.1)
}

.counters-w .counter .counter-value {
    font-size: 5.06rem;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    line-height: 1
}

.counters-w .counter .counter-name {
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 500;
    padding-top: 0px
}

.counters-w .counter .counter-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.6);
    width: 200px
}

.features-table {
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

.features-table .feature-cell {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 60px;
    background-color: #fff;
    -webkit-box-shadow: 0px 5px 40px rgba(41, 111, 226, 0.3);
    box-shadow: 0px 5px 40px rgba(41, 111, 226, 0.3)
}

.features-table .feature-cell .feature-icon {
    color: #047bf8;
    font-size: 40px;
    margin-bottom: 40px
}

.features-table .feature-cell .feature-title {
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 20px
}

.features-table .feature-cell .feature-text {
    color: #868686;
    font-weight: 300
}

.testimonials-w {
    background-color: #0a1a3d;
    padding-bottom: 100px;
    padding-top: 100px;
    margin-top: 100px
}

.testimonials-w .testimonials-i {
    position: relative;
    z-index: 2
}

.testimonials-slider-w .slick-arrow {
    position: absolute;
    right: 40px;
    top: -50px;
    color: rgba(255, 255, 255, 0.3);
    background-color: transparent;
    border: none;
    font-size: 0px
}

.testimonials-slider-w .slick-arrow:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    display: block;
    font-size: 16px
}

.testimonials-slider-w .slick-arrow:hover {
    color: #fff
}

.testimonials-slider-w .slick-next {
    -webkit-transform: translateX(15px);
    transform: translateX(15px)
}

.testimonials-slider-w .slick-next:before {
    content: "\e910"
}

.testimonials-slider-w .slick-prev {
    -webkit-transform: translateX(-35px);
    transform: translateX(-35px)
}

.testimonials-slider-w .slick-prev:before {
    content: "\e919"
}

.testimonials-slider-w .slide-w {
    display: inline-block;
    width: 540px;
    padding: 20px;
    height: auto
}

.testimonials-slider-w .slide {
    background-color: #fff;
    border-radius: 4px;
    padding: 50px 60px;
    padding-bottom: 30px;
    -webkit-box-shadow: 0px 4px 30px rgba(0, 0, 0, 0.8);
    box-shadow: 0px 4px 30px rgba(0, 0, 0, 0.8);
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    position: relative
}

.testimonials-slider-w .slide:before {
    content: "\e966";
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 120px;
    color: rgba(4, 123, 248, 0.1);
    position: absolute;
    top: 10px;
    left: 20px
}

.testimonials-slider-w .testimonial-title {
    text-transform: uppercase;
    letter-spacing: 2px;
    color: #047bf8;
    margin-bottom: 10px;
    font-weight: 500
}

.testimonials-slider-w .testimonial-content {
    color: #868686;
    font-weight: 300
}

.testimonials-slider-w .testimonial-by {
    text-align: right;
    color: #868686;
    font-weight: 300;
    font-size: .9rem;
    margin-top: 20px
}

.testimonials-slider-w .testimonial-by strong {
    color: #3E4B5B;
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px
}

.testimonials-slider-w .testimonial-by span {
    display: inline-block;
    vertical-align: middle
}

.testimonials-slider-w .testimonial-by .avatar {
    display: inline-block;
    vertical-align: middle;
    margin-left: 20px
}

.testimonials-slider-w .testimonial-by .avatar img {
    width: 50px;
    height: auto;
    border-radius: 30px
}

.projects-slider-w {
    position: relative;
    margin-bottom: 60px
}

.projects-slider-w .slick-arrow {
    position: absolute;
    right: 40px;
    top: -50px;
    color: rgba(4, 123, 248, 0.3);
    background-color: transparent;
    border: none;
    font-size: 0px
}

.projects-slider-w .slick-arrow:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    display: block;
    font-size: 16px
}

.projects-slider-w .slick-arrow:hover {
    color: #fff
}

.projects-slider-w .slick-next {
    -webkit-transform: translateX(15px);
    transform: translateX(15px)
}

.projects-slider-w .slick-next:before {
    content: "\e910"
}

.projects-slider-w .slick-prev {
    -webkit-transform: translateX(-35px);
    transform: translateX(-35px)
}

.projects-slider-w .slick-prev:before {
    content: "\e919"
}

.project-slide-w {
    padding: 40px 20px;
    width: 450px;
    height: auto
}

.project-slide {
    background-color: #fff;
    -webkit-box-shadow: 0 2px 50px 0 rgba(4, 123, 248, 0.13);
    box-shadow: 0 2px 50px 0 rgba(4, 123, 248, 0.13);
    border-radius: 10px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    will-change: transform
}

.project-slide .project-media-w {
    overflow: hidden;
    position: relative;
    padding: 10px
}

.project-slide .decor {
    position: absolute;
    bottom: -1px;
    right: 0px;
    max-width: 100%;
    z-index: 3;
    -webkit-filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.2));
    filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.2));
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease
}

.project-slide .decor .decor-path {
    fill: #fff
}

.project-slide .project-media {
    position: relative;
    overflow: hidden;
    border-radius: 8px 8px 0px 8px
}

.project-slide .project-media .project-media-i {
    border-radius: 8px 8px 0px 8px;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
    padding-bottom: 65%;
    background-size: cover;
    background-position: center center;
    will-change: transform
}

.project-slide .project-content {
    padding: 40px
}

.project-slide .project-title {
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: .8rem;
    font-weight: 500;
    margin-bottom: 15px
}

.project-slide .project-text {
    color: #868686;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-weight: 300;
    font-size: .9rem
}

.project-slide .project-icons-buton {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.project-slide .project-icons-buton .icons-w a {
    display: inline-block;
    vertical-align: middle;
    font-size: 22px;
    color: #047bf8;
    margin-right: 20px
}

.project-slide .project-icons-buton .btn-w {
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    padding-left: 20px;
    margin-left: 20px
}

.project-slide:hover {
    -webkit-box-shadow: 0 2px 40px 0 rgba(4, 123, 248, 0.3);
    box-shadow: 0 2px 40px 0 rgba(4, 123, 248, 0.3);
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0)
}

.project-slide:hover .project-media-i {
    -webkit-transform: scale(1.1);
    transform: scale(1.1)
}

.top-bar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch
}

.top-bar .logo-w {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 400px;
    flex: 0 0 400px;
    background-color: #4472fd;
    padding: 10px 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.top-bar .logo-w .filters-toggler {
    color: rgba(255, 255, 255, 0.7);
    font-size: 20px;
    line-height: 1;
    cursor: pointer
}

.top-bar .logo-w .filters-toggler .os-icon {
    display: inline-block;
    vertical-align: middle;
    line-height: 1
}

.top-bar .logo-w .filters-toggler:hover {
    color: #fff
}

.top-bar .logo-w .logo {
    display: inline-block;
    text-decoration: none
}

.top-bar .logo-w .logo-element {
    content: "";
    width: 26px;
    height: 26px;
    border-radius: 15px;
    position: relative;
    background-color: #98c9fd;
    display: inline-block;
    vertical-align: middle;
    margin-right: 40px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.top-bar .logo-w .logo-element:after {
    content: "";
    width: 26px;
    height: 26px;
    background-color: #fff;
    border-radius: 15px;
    right: -20px;
    position: absolute;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.top-bar .logo-w .logo:hover .logo-element {
    -webkit-transform: translateX(5px);
    transform: translateX(5px)
}

.top-bar .logo-w .logo:hover .logo-element:after {
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px)
}

.top-bar .logo-w .logo:hover .logo-label:after {
    width: 100%;
    background-color: #fff
}

.top-bar .logo-w .logo-label {
    display: inline-block;
    vertical-align: middle;
    color: #fff;
    letter-spacing: 2px;
    text-transform: uppercase;
    font-weight: 500;
    font-size: 1.2rem;
    position: relative
}

.top-bar .logo-w .logo-label:after {
    height: 2px;
    position: absolute;
    width: 0%;
    left: 0px;
    bottom: -5px;
    background-color: #fff;
    content: "";
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.top-bar .filters {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    background-color: #24293d;
    padding: 0px 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.top-bar .filters .filters-header {
    padding-right: 20px
}

.top-bar .filters .filters-header h4 {
    color: #fff;
    letter-spacing: 2px;
    text-transform: uppercase;
    font-weight: 500;
    font-size: 1rem;
    margin: 0px
}

.top-bar .filters .filter-w {
    padding: 15px 20px;
    border-left: 1px solid rgba(255, 255, 255, 0.1)
}

.top-bar .filters .filter-w label {
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 0.4);
    display: inline-block;
    margin-right: 10px
}

.top-bar .filters .filter-w input.form-control {
    background-color: #040407;
    border-color: #040407;
    color: #fff;
    font-weight: 500;
    letter-spacing: 1px
}

.top-bar .filters .filter-w input.form-control.zip-width {
    width: 70px;
    padding-left: 5px
}

.top-bar .filters .filter-w input.form-control.date-range-picker {
    width: 250px;
    padding-left: 5px
}

.top-bar .filters .filter-w .input-group-addon {
    background-color: #040407;
    border-color: #040407;
    color: #4472fd;
    font-size: 20px
}

.top-bar .filters .buttons-w .btn,
.top-bar .filters .buttons-w .wrapper-front .fc-button,
.wrapper-front .top-bar .filters .buttons-w .fc-button {
    font-size: .9rem
}

.top-bar .filters .buttons-w .btn i.os-icon,
.top-bar .filters .buttons-w .wrapper-front .fc-button i.os-icon,
.wrapper-front .top-bar .filters .buttons-w .fc-button i.os-icon {
    margin: 0px
}

.top-bar .filters .buttons-w .btn i.os-icon+span,
.top-bar .filters .buttons-w .wrapper-front .fc-button i.os-icon+span,
.wrapper-front .top-bar .filters .buttons-w .fc-button i.os-icon+span {
    margin: 0px;
    margin-left: 10px
}

.top-bar .filters .buttons-w .btn span+i.os-icon,
.top-bar .filters .buttons-w .wrapper-front .fc-button span+i.os-icon,
.wrapper-front .top-bar .filters .buttons-w .fc-button span+i.os-icon {
    margin: 0px;
    margin-left: 10px
}

.rentals-list-w {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    background-color: #fff;
    margin-bottom: 100px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.rentals-list-w .filter-side {
    background-color: #f6f6f6;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 400px;
    flex: 0 0 400px
}

.rentals-list-w .filter-side .filters-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 16px 20px;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.rentals-list-w .filter-side .filters-header h4 {
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: 1.2rem;
    margin-bottom: 0px
}

.rentals-list-w .filter-side .filters-header .reset-filters {
    color: #BC8F8F;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.rentals-list-w .filter-side .filters-header .reset-filters i {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
    font-size: 10px
}

.rentals-list-w .filter-side .filters-header .reset-filters span {
    display: inline-block;
    vertical-align: middle;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: .7rem
}

.rentals-list-w .filter-side .filter-w {
    position: relative
}

.rentals-list-w .filter-side .filter-w .filter-toggle {
    position: absolute;
    top: 20px;
    right: 20px;
    display: inline-block;
    padding: 4px 5px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.07);
    color: rgba(0, 0, 0, 0.4);
    font-size: 10px;
    line-height: 1;
    vertical-align: middle;
    cursor: pointer;
    z-index: 3
}

.rentals-list-w .filter-side .filter-w .filter-toggle i {
    display: inline-block;
    vertical-align: middle
}

.rentals-list-w .filter-side .filter-w .filter-toggle:hover {
    background-color: #111;
    color: #fff
}

.rentals-list-w .filter-side .filter-w iframe {
    max-width: 100%
}

.rentals-list-w .filter-side .filter-w.no-padding .filter-body {
    padding-left: 0px;
    padding-right: 0px
}

.rentals-list-w .filter-side .filter-w.collapsed .filter-body {
    display: none
}

.rentals-list-w .filter-side .filter-header {
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: .9rem;
    position: relative;
    padding: 40px;
    padding-bottom: 20px;
    padding-top: 20px;
    margin: 0px
}

.rentals-list-w .filter-side .filter-header:before {
    content: "";
    background-color: #047bf8;
    width: 7px;
    height: 7px;
    left: 20px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    position: absolute
}

.rentals-list-w .filter-side .filter-body {
    padding: 10px 40px 30px 40px
}

.rentals-list-w .filter-side .filter-w+.filter-w {
    border-top: 1px solid rgba(0, 0, 0, 0.05)
}

.rentals-list-w .filter-side .select2 {
    display: block;
    width: 100%
}

.rentals-list-w .filter-side .select2.select2-container--default .select2-selection--multiple {
    background-color: transparent;
    border-color: transparent
}

.rentals-list-w .filter-side .select2.select2-container--default .select2-selection--multiple .select2-selection__choice {
    border: 2px solid #047bf8;
    border-radius: 16px;
    font-size: .7rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    background-color: transparent;
    font-weight: 500;
    padding: 3px 8px;
    color: #047bf8;
    margin-right: 7px
}

.rentals-list-w .filter-side .select2.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: #047bf8
}

.rentals-list-w .rentals-list {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.rentals-list-w .rentals-list .list-controls {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 10px 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.rentals-list-w .rentals-list .list-controls .list-info {
    color: rgba(0, 0, 0, 0.4);
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    font-size: .8rem
}

.rentals-list-w .rentals-list .list-controls .list-order {
    margin-left: auto
}

.rentals-list-w .rentals-list .list-controls .list-order label {
    margin-right: 10px;
    color: rgba(0, 0, 0, 0.4);
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    font-size: .8rem;
    margin-bottom: 0px
}

.rentals-list-w .property-items.as-list .property-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.rentals-list-w .property-items.as-list .property-item .item-media-w {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 40%;
    flex: 0 0 40%
}

.rentals-list-w .property-items.as-list .property-item .item-media-w .item-media {
    height: 100%
}

.rentals-list-w .property-items.as-list .property-item .item-info {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.rentals-list-w .property-items.as-grid {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.rentals-list-w .property-items.as-grid .property-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding: 20px
}

.rentals-list-w .property-items.as-grid .property-item:nth-child(odd) {
    border-right: 1px solid rgba(0, 0, 0, 0.1)
}

.rentals-list-w .property-items.as-grid .property-item .item-media-w .item-media {
    padding-bottom: 65%
}

.rentals-list-w .property-items.as-grid .property-item .item-info {
    padding: 30px 30px
}

.rentals-list-w .property-item {
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.rentals-list-w .property-item .item-media-w {
    display: block;
    position: relative;
    overflow: hidden
}

.rentals-list-w .property-item .item-media-w .item-media {
    background-size: cover;
    background-position: center center;
    z-index: 2;
    -webkit-transition: all 0.5s ease;
    transition: all 0.5s ease
}

.rentals-list-w .property-item .item-media-w:after {
    content: "";
    background-color: transparent;
    position: absolute;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    z-index: 3;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px
}

.rentals-list-w .property-item .item-media-w:hover .item-media {
    -webkit-transform: scale(1.05);
    transform: scale(1.05)
}

.rentals-list-w .property-item .item-media-w:hover:after {
    background-color: rgba(0, 0, 0, 0.1)
}

.rentals-list-w .property-item .item-info {
    padding: 60px 50px
}

.rentals-list-w .property-item .item-info .item-title a {
    color: #334152;
    text-decoration: none
}

.rentals-list-w .property-item .item-info .item-title a:hover {
    color: #1f2833
}

.rentals-list-w .property-item .item-price-buttons {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 20px
}

.rentals-list-w .property-item .item-reviews {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-top: 15px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.rentals-list-w .property-item .item-reviews .reviews-count {
    padding-left: 10px;
    margin-left: 10px;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    color: rgba(0, 0, 0, 0.4)
}

.rentals-list-w .property-item .item-price strong {
    color: #047bf8;
    font-size: 2rem
}

.rentals-list-w .property-item .item-price span {
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    color: #999;
    margin-left: 5px
}

.rentals-list-w .property-item .item-features {
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    color: #777;
    margin: 10px 0px
}

.rentals-list-w .property-item .item-features .feature {
    margin-right: 17px;
    position: relative;
    display: inline-block;
    vertical-align: middle
}

.rentals-list-w .property-item .item-features .feature+.feature:before {
    content: "";
    width: 5px;
    height: 5px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.4);
    position: absolute;
    left: -15px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.rentals-list-w .property-item .item-buttons {
    padding-top: 20px
}

.rentals-list-w .property-item .item-buttons .btn,
.rentals-list-w .property-item .item-buttons .wrapper-front .fc-button,
.wrapper-front .rentals-list-w .property-item .item-buttons .fc-button {
    border-width: 2px;
    text-transform: uppercase;
    font-weight: 500;
    padding: 5px 14px;
    letter-spacing: 2px
}

.rentals-list-w .property-item .item-buttons .btn span,
.rentals-list-w .property-item .item-buttons .wrapper-front .fc-button span,
.wrapper-front .rentals-list-w .property-item .item-buttons .fc-button span,
.rentals-list-w .property-item .item-buttons .btn i,
.rentals-list-w .property-item .item-buttons .wrapper-front .fc-button i,
.wrapper-front .rentals-list-w .property-item .item-buttons .fc-button i {
    display: inline-block;
    vertical-align: middle
}

.rentals-list-w .property-item .item-buttons .btn span+i,
.rentals-list-w .property-item .item-buttons .wrapper-front .fc-button span+i,
.wrapper-front .rentals-list-w .property-item .item-buttons .fc-button span+i {
    margin-left: 10px;
    font-size: 10px;
    margin-right: 0px
}

.rentals-list-w .property-item .item-buttons .btn+.btn,
.rentals-list-w .property-item .item-buttons .wrapper-front .fc-button+.btn,
.wrapper-front .rentals-list-w .property-item .item-buttons .fc-button+.btn,
.rentals-list-w .property-item .item-buttons .wrapper-front .btn+.fc-button,
.wrapper-front .rentals-list-w .property-item .item-buttons .btn+.fc-button,
.rentals-list-w .property-item .item-buttons .wrapper-front .fc-button+.fc-button,
.wrapper-front .rentals-list-w .property-item .item-buttons .fc-button+.fc-button {
    margin-left: 10px
}

.rentals-list-w .pagination-w {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 10px 20px
}

.rentals-list-w .pagination-w .pagination-info {
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    color: rgba(0, 0, 0, 0.4)
}

.rentals-list-w .pagination-w .pagination-links .pagination {
    margin-bottom: 0;
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    color: #047bf8
}

.rentals-list-w .pagination-w .pagination-links .pagination .page-link {
    border: none
}

.rentals-list-w .pagination-w .pagination-links .pagination .disabled .page-link {
    color: rgba(0, 0, 0, 0.3)
}

.rentals-list-w.hide-filters .filter-side {
    display: none
}

.rentals-list-w.hide-filters .property-items.as-grid .property-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.3%;
    flex: 0 0 33.3%;
    border-right: 1px solid rgba(0, 0, 0, 0.1)
}

.rentals-list-w.hide-filters .property-items.as-grid .property-item:nth-child(3n) {
    border-right: none
}

.property-single {
    background-color: #fff
}

.property-single .property-media {
    height: 60vh;
    position: relative;
    background-size: cover;
    background-position: center center
}

.property-single .property-media .media-buttons {
    position: absolute;
    left: 40px;
    bottom: 40px
}

.property-single .property-media .media-buttons a {
    background-color: rgba(255, 255, 255, 0.8);
    padding: 6px 12px;
    border-radius: 4px;
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    display: inline-block;
    color: #111
}

.property-single .property-media .media-buttons a:hover {
    background-color: #fff
}

.property-single .property-media .media-buttons a i.os-icon {
    font-size: 20px;
    margin-right: 10px;
    display: inline-block;
    vertical-align: middle
}

.property-single .property-media .media-buttons a span {
    display: inline-block;
    vertical-align: middle
}

.property-single .property-media .media-buttons a+a {
    margin-left: 20px
}

.property-single .property-info-w {
    max-width: 1200px;
    margin: 0px auto;
    padding: 0px 40px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.property-single .property-info-w .property-info-main {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    padding: 60px;
    position: relative
}

.property-single .property-info-w .property-info-main .property-price {
    text-align: center;
    padding: 30px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: absolute;
    width: 400px;
    background-color: #fff;
    top: 0px;
    right: 0px;
    -webkit-transform: translate(100%, -100%);
    transform: translate(100%, -100%)
}

.property-single .property-info-w .property-info-main .property-price strong {
    color: #047bf8;
    font-size: 2.3rem;
    display: block;
    line-height: 1
}

.property-single .property-info-w .property-info-main .property-price span {
    display: inline-block;
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    color: rgba(0, 0, 0, 0.4);
    font-size: .9rem;
    letter-spacing: 1px
}

.property-single .property-info-w .property-info-main h1 {
    font-size: 2.76rem
}

.property-single .property-info-w .property-info-main .property-features-highlight {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px 0px;
    margin-top: 30px
}

.property-single .property-info-w .property-info-main .property-features-highlight .feature {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    text-align: center
}

.property-single .property-info-w .property-info-main .property-features-highlight .feature i {
    display: inline-block;
    font-size: 30px;
    color: #047bf8
}

.property-single .property-info-w .property-info-main .property-features-highlight .feature span {
    display: block;
    margin-top: 10px;
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    font-size: 12px
}

.property-single .property-info-w .property-info-main .property-description {
    font-size: 1.1rem;
    font-weight: 300;
    margin: 30px 0px
}

.property-single .property-info-w .property-info-main .badge {
    border-radius: 0px;
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    padding: 5px 10px
}

.property-single .property-info-w .property-info-main .badge.badge-red {
    background-color: #F70E2D;
    color: #fff
}

.property-single .property-info-w .property-info-main .item-reviews {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-top: 15px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.property-single .property-info-w .property-info-main .item-reviews .reviews-count {
    padding-left: 10px;
    margin-left: 10px;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    color: rgba(0, 0, 0, 0.4)
}

.property-single .property-info-w .property-info-main .item-features {
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    color: #777;
    margin: 10px 0px;
    font-size: 1.1rem;
    position: relative
}

.property-single .property-info-w .property-info-main .item-features:before {
    content: "";
    background-color: #047bf8;
    width: 7px;
    height: 7px;
    left: -63px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    position: absolute
}

.property-single .property-info-w .property-info-main .item-features .feature {
    margin-right: 17px;
    position: relative;
    display: inline-block;
    vertical-align: middle
}

.property-single .property-info-w .property-info-main .item-features .feature+.feature:before {
    content: "";
    width: 5px;
    height: 5px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.4);
    position: absolute;
    left: -15px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.property-single .property-info-w .property-info-side {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 400px;
    flex: 0 0 400px;
    background-color: #fff;
    padding: 0px;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    border-right: 1px solid rgba(0, 0, 0, 0.1)
}

.property-single .property-info-w .property-info-side .side-action-form {
    padding: 30px 40px
}

.property-single .property-info-w .property-info-side .side-action-form .input-group {
    border: 2px solid rgba(0, 0, 0, 0.2);
    border-radius: 4px
}

.property-single .property-info-w .property-info-side .side-action-form .input-group .input-group-addon {
    border: none;
    background-color: #fff;
    color: #111
}

.property-single .property-info-w .property-info-side .side-action-form .input-group .form-control {
    border: none;
    font-weight: 500
}

.property-single .property-info-w .property-info-side .side-action-form .form-buttons {
    text-align: center
}

.property-single .property-info-w .property-info-side .side-action-form .btn,
.property-single .property-info-w .property-info-side .side-action-form .wrapper-front .fc-button,
.wrapper-front .property-single .property-info-w .property-info-side .side-action-form .fc-button {
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    padding: 12px 20px
}

.property-single .property-info-w .property-info-side .side-action-form .btn span,
.property-single .property-info-w .property-info-side .side-action-form .wrapper-front .fc-button span,
.wrapper-front .property-single .property-info-w .property-info-side .side-action-form .fc-button span {
    margin-right: 10px
}

.property-single .property-info-w .property-info-side .side-action-form .btn i.os-icon,
.property-single .property-info-w .property-info-side .side-action-form .wrapper-front .fc-button i.os-icon,
.wrapper-front .property-single .property-info-w .property-info-side .side-action-form .fc-button i.os-icon {
    font-size: 10px;
    margin-right: 0px
}

.property-single .property-info-w .property-info-side .side-magic {
    padding: 15% 10%;
    position: relative;
    color: rgba(255, 255, 255, 0.8)
}

.property-single .property-info-w .property-info-side .side-magic .fader {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    background-color: rgba(0, 0, 0, 0.5);
    content: ""
}

.property-single .property-info-w .property-info-side .side-magic .side-magic-title {
    color: #fff;
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    font-size: 1.75rem;
    position: relative;
    margin-bottom: 20px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2)
}

.property-single .property-info-w .property-info-side .side-magic .side-magic-desc {
    position: relative;
    font-size: 1.2rem
}

.property-single .property-info-w .property-info-side .side-magic .side-magic-btn {
    position: relative;
    background-color: #fff;
    border-radius: 4px;
    -webkit-box-shadow: 0px 3px 12px rgba(0, 0, 0, 0.7);
    box-shadow: 0px 3px 12px rgba(0, 0, 0, 0.7);
    padding: 7px 15px;
    color: #111;
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    font-size: 1.2rem;
    display: inline-block;
    margin-top: 30px;
    text-decoration: none
}

.property-single .property-info-w .property-info-side .side-section .side-section-header {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    position: relative;
    color: #111;
    text-align: center;
    font-size: 1.1rem
}

.property-single .property-info-w .property-info-side .side-section .side-section-header:before {
    content: "";
    background-color: #047bf8;
    width: 7px;
    height: 7px;
    left: 50%;
    top: -4px;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    position: absolute
}

.property-single .property-info-w .property-info-side .side-section .side-section-content {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px 0px
}

.property-single .property-info-w .property-info-side .property-side-features .feature {
    padding: 15px 40px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px
}

.property-single .property-info-w .property-info-side .property-side-features .feature i,
.property-single .property-info-w .property-info-side .property-side-features .feature span,
.property-single .property-info-w .property-info-side .property-side-features .feature strong {
    display: inline-block;
    vertical-align: middle
}

.property-single .property-info-w .property-info-side .property-side-features .feature span {
    color: rgba(0, 0, 0, 0.6)
}

.property-single .property-info-w .property-info-side .property-side-features .feature strong {
    margin-right: 3px;
    color: #111
}

.property-single .property-info-w .property-info-side .property-side-features .feature i {
    color: #047bf8;
    font-size: 30px;
    margin-right: 20px
}

.property-single .property-info-w .property-info-side .property-side-features .feature:last-child {
    border-bottom: none
}

.property-single .property-info-w .property-section .property-section-header {
    padding: 20px 0px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    position: relative;
    color: #111;
    font-size: 1.1rem
}

.property-single .property-info-w .property-section .property-section-header:before {
    content: "";
    background-color: #047bf8;
    width: 7px;
    height: 7px;
    left: -63px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    position: absolute
}

.property-single .property-info-w .property-section .property-section-header .filter-toggle {
    position: absolute;
    top: 20px;
    right: 20px;
    display: inline-block;
    padding: 4px 5px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.07);
    color: rgba(0, 0, 0, 0.4);
    font-size: 10px;
    line-height: 1;
    vertical-align: middle;
    cursor: pointer;
    z-index: 3
}

.property-single .property-info-w .property-section .property-section-header .filter-toggle i {
    display: inline-block;
    vertical-align: middle
}

.property-single .property-info-w .property-section .property-section-header .filter-toggle:hover {
    background-color: #111;
    color: #fff
}

.property-single .property-info-w .property-section .property-section-body {
    padding: 40px 0px;
    font-size: 1.1rem;
    font-weight: 300
}

.property-single .property-info-w .property-section .property-section-body iframe {
    max-width: 100%
}

.property-single .property-info-w .property-section .property-section-body ul li {
    margin-bottom: 10px
}

.property-single .property-info-w .property-section .property-section-body ul li .os-icon {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px;
    font-size: 20px
}

.property-single .property-info-w .property-section .property-section-body ul li span {
    display: inline-block;
    vertical-align: middle
}

.property-single .property-info-w .property-section .property-section-body .property-note {
    padding: 20px;
    background-color: #FBF9F3;
    color: #383328;
    font-size: .9rem;
    margin: 20px 0px
}

.property-single .property-info-w .property-section .property-section-body .property-note:last-child {
    margin-bottom: 0px
}

.property-single .property-info-w .property-section .property-section-body .property-note h6 {
    font-size: .9rem
}

.property-single .property-info-w .property-section .property-section-body .property-note p:last-child {
    margin-bottom: 0px
}

.related-listings-w {
    margin-bottom: 100px;
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

.related-listings-w .property-section-big-header {
    position: relative;
    text-align: center;
    padding: 30px;
    letter-spacing: 3px;
    text-transform: uppercase
}

.related-listings-w .property-section-big-header:before {
    content: "";
    background-color: #047bf8;
    width: 7px;
    height: 7px;
    left: 50%;
    top: -4px;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    position: absolute
}

.related-listings-w .related-listings {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.related-listings-w .property-item {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.3%;
    flex: 0 0 33.3%;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.related-listings-w .property-item+.property-item {
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

.related-listings-w .property-item .item-media-w {
    display: block;
    position: relative;
    overflow: hidden
}

.related-listings-w .property-item .item-media-w .item-media {
    background-size: cover;
    background-position: center center;
    z-index: 2;
    -webkit-transition: all 0.5s ease;
    transition: all 0.5s ease;
    padding-bottom: 65%
}

.related-listings-w .property-item .item-media-w:after {
    content: "";
    background-color: transparent;
    position: absolute;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    z-index: 3;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px
}

.related-listings-w .property-item .item-media-w:hover .item-media {
    -webkit-transform: scale(1.05);
    transform: scale(1.05)
}

.related-listings-w .property-item .item-media-w:hover:after {
    background-color: rgba(0, 0, 0, 0.1)
}

.related-listings-w .property-item .item-info {
    padding: 60px 50px
}

.related-listings-w .property-item .item-info .item-title a {
    color: #334152;
    text-decoration: none
}

.related-listings-w .property-item .item-info .item-title a:hover {
    color: #1f2833
}

.related-listings-w .property-item .item-price-buttons {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-top: 20px
}

.related-listings-w .property-item .item-reviews {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-top: 15px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.related-listings-w .property-item .item-reviews .reviews-count {
    padding-left: 10px;
    margin-left: 10px;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    color: rgba(0, 0, 0, 0.4)
}

.related-listings-w .property-item .item-price strong {
    color: #047bf8;
    font-size: 2rem
}

.related-listings-w .property-item .item-price span {
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    color: #999;
    margin-left: 5px
}

.related-listings-w .property-item .item-features {
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    color: #777;
    margin: 10px 0px
}

.related-listings-w .property-item .item-features .feature {
    margin-right: 17px;
    position: relative;
    display: inline-block;
    vertical-align: middle
}

.related-listings-w .property-item .item-features .feature+.feature:before {
    content: "";
    width: 5px;
    height: 5px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.4);
    position: absolute;
    left: -15px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.related-listings-w .property-item .item-buttons {
    padding-top: 20px
}

.related-listings-w .property-item .item-buttons .btn,
.related-listings-w .property-item .item-buttons .wrapper-front .fc-button,
.wrapper-front .related-listings-w .property-item .item-buttons .fc-button {
    border-width: 2px;
    text-transform: uppercase;
    font-weight: 500;
    padding: 5px 14px;
    letter-spacing: 2px
}

.related-listings-w .property-item .item-buttons .btn span,
.related-listings-w .property-item .item-buttons .wrapper-front .fc-button span,
.wrapper-front .related-listings-w .property-item .item-buttons .fc-button span,
.related-listings-w .property-item .item-buttons .btn i,
.related-listings-w .property-item .item-buttons .wrapper-front .fc-button i,
.wrapper-front .related-listings-w .property-item .item-buttons .fc-button i {
    display: inline-block;
    vertical-align: middle
}

.related-listings-w .property-item .item-buttons .btn span+i,
.related-listings-w .property-item .item-buttons .wrapper-front .fc-button span+i,
.wrapper-front .related-listings-w .property-item .item-buttons .fc-button span+i {
    margin-left: 10px;
    font-size: 10px;
    margin-right: 0px
}

.related-listings-w .property-item .item-buttons .btn+.btn,
.related-listings-w .property-item .item-buttons .wrapper-front .fc-button+.btn,
.wrapper-front .related-listings-w .property-item .item-buttons .fc-button+.btn,
.related-listings-w .property-item .item-buttons .wrapper-front .btn+.fc-button,
.wrapper-front .related-listings-w .property-item .item-buttons .btn+.fc-button,
.related-listings-w .property-item .item-buttons .wrapper-front .fc-button+.fc-button,
.wrapper-front .related-listings-w .property-item .item-buttons .fc-button+.fc-button {
    margin-left: 10px
}

.demos-w {
    text-align: center;
    margin-bottom: 50px
}

.demos-w .demo-w {
    padding: 30px;
    display: block;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease
}

.demos-w .demo-w:hover {
    text-decoration: none;
    -webkit-transform: translate3d(0px, -10px, 0px);
    transform: translate3d(0px, -10px, 0px)
}

.demos-w .demo-w:hover .demo-shot-w {
    -webkit-box-shadow: 0px 5px 50px rgba(4, 123, 248, 0.4);
    box-shadow: 0px 5px 50px rgba(4, 123, 248, 0.4)
}

.demos-w .demo-header {
    color: #047bf8;
    letter-spacing: 2px;
    font-size: 1rem;
    font-weight: 500;
    text-transform: uppercase;
    margin-bottom: 20px
}

.demos-w .demo-shot-w {
    -webkit-box-shadow: 0px 5px 40px rgba(4, 123, 248, 0.2);
    box-shadow: 0px 5px 40px rgba(4, 123, 248, 0.2);
    background-color: #fff;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease
}

.demos-w .demo-shot-w .demo-shot {
    padding-bottom: 70%;
    background-size: cover
}

.call-to-action {
    text-align: center;
    max-width: 800px;
    margin: 0px auto;
    padding: 50px 20px
}

.call-to-action .cta-header {
    font-size: 2.76rem
}

.call-to-action .cta-desc {
    font-weight: 300;
    color: #868686;
    font-size: 1.3rem
}

.call-to-action .cta-btn {
    margin-top: 40px
}

.call-to-action .cta-btn .btn-lg,
.call-to-action .cta-btn .btn-group-lg>.btn,
.call-to-action .cta-btn .wrapper-front .btn-group-lg>.fc-button,
.wrapper-front .call-to-action .cta-btn .btn-group-lg>.fc-button {
    font-size: 1.6rem;
    font-weight: 500;
    padding: 18px 40px;
    text-transform: uppercase;
    letter-spacing: 3px;
    padding-left: 50px
}

.call-to-action .cta-btn span {
    display: inline-block;
    vertical-align: middle
}

.call-to-action .cta-btn i {
    margin-right: 0px;
    font-size: 30px;
    display: inline-block;
    vertical-align: middle
}

.call-to-action .cta-btn span+i {
    margin-left: 10px;
    opacity: 0.4
}

.call-to-action .cta-btn i+i {
    margin-left: -18px
}

.footer-w {
    color: #868686;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#f8faff), to(#fff));
    background-image: linear-gradient(to bottom, #f8faff, #fff);
    font-weight: 300;
    margin-top: 10px;
    position: relative;
    z-index: 2;
    overflow: hidden
}

.footer-w .logo-element {
    content: "";
    width: 26px;
    height: 26px;
    border-radius: 15px;
    position: relative;
    margin-bottom: 30px;
    background-color: #98c9fd
}

.footer-w .logo-element:after {
    content: "";
    width: 26px;
    height: 26px;
    background-color: #047bf8;
    border-radius: 15px;
    right: -20px;
    position: absolute
}

.footer-w .footer-i {
    padding-top: 50px;
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

.footer-w .footer-i .heading-big {
    margin-bottom: 10px;
    text-transform: uppercase;
    font-size: 1.75rem;
    letter-spacing: 3px
}

.footer-w .footer-i .heading-small {
    color: #047bf8;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: .8rem;
    margin-bottom: 20px
}

.footer-w .footer-i ul {
    list-style-type: square;
    color: #047bf8
}

.footer-w .footer-i ul li {
    color: #868686
}

.footer-w .footer-i ul.social-links {
    margin: 0px;
    padding: 0px
}

.footer-w .footer-i ul.social-links li {
    display: inline-block;
    margin-right: 15px
}

.footer-w .footer-i ul.social-links li a {
    display: inline-block;
    vertical-align: middle
}

.footer-w .footer-i ul.social-links li a:hover {
    text-decoration: none
}

.footer-w .footer-i ul.social-links li a i {
    font-size: 50px
}

.footer-w .footer-i ul.social-links li a .os-icon-facebook {
    color: #0d509a
}

.footer-w .footer-i ul.social-links li a .os-icon-twitter {
    color: #2fafff
}

.footer-w .deep-footer {
    text-align: center;
    padding: 20px;
    font-size: .8rem;
    margin-top: 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

.br-theme-osadmin .br-widget {
    height: 28px;
    white-space: nowrap
}

.br-theme-osadmin .br-widget a {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-decoration: none;
    margin-right: 2px
}

.br-theme-osadmin .br-widget a:after {
    content: '\e970';
    color: #d2d2d2
}

.br-theme-osadmin .br-widget a.br-active:after {
    color: #EDB867
}

.br-theme-osadmin .br-widget a.br-selected:after {
    color: #EDB867
}

.br-theme-osadmin .br-widget .br-current-rating {
    display: none
}

.br-theme-osadmin .br-readonly a {
    cursor: default
}

@media print {
    .br-theme-osadmin .br-widget a:after {
        content: '\f006';
        color: black
    }
    .br-theme-osadmin .br-widget a.br-active:after,
    .br-theme-osadmin .br-widget a.br-selected:after {
        content: '\e970';
        color: black
    }
}

.irs-line-mid,
.irs-line-left,
.irs-line-right,
.irs-bar,
.irs-bar-edge,
.irs-slider {
    background-color: #dddddd
}

.irs {
    height: 40px
}

.irs-with-grid {
    height: 60px
}

.irs-line {
    height: 5px;
    top: 25px;
    border-radius: 2px
}

.irs-line-left {
    height: 12px
}

.irs-line-mid {
    height: 12px
}

.irs-line-right {
    height: 12px
}

.irs-bar {
    height: 5px;
    top: 25px;
    background-color: #98c9fd
}

.irs-bar-edge {
    top: 25px;
    height: 12px;
    width: 9px
}

.irs-shadow {
    height: 3px;
    top: 34px;
    background: #000;
    opacity: 0.25
}

.lt-ie9 .irs-shadow {
    filter: alpha(opacity=25)
}

.irs-slider {
    width: 11px;
    height: 11px;
    top: 22px;
    background-color: #047bf8;
    -webkit-box-shadow: 0px 0px 0px 2px #fff;
    box-shadow: 0px 0px 0px 2px #fff;
    border-radius: 20px;
    cursor: pointer
}

.irs-slider:hover {
    background-color: #024994
}

.irs-min,
.irs-max {
    color: #999;
    font-size: 10px;
    line-height: 1.333;
    text-shadow: none;
    top: 0;
    padding: 1px 3px;
    background: #e1e4e9;
    border-radius: 4px
}

.irs-from,
.irs-to,
.irs-single {
    color: #fff;
    font-size: 10px;
    line-height: 1.333;
    text-shadow: none;
    padding: 1px 5px;
    background: #ed5565;
    border-radius: 4px
}

.irs-from:after,
.irs-to:after,
.irs-single:after {
    position: absolute;
    display: block;
    content: "";
    bottom: -6px;
    left: 50%;
    width: 0;
    height: 0;
    margin-left: -3px;
    overflow: hidden;
    border: 3px solid transparent;
    border-top-color: #ed5565
}

.irs-grid-pol {
    background: #e1e4e9
}

.irs-grid-text {
    color: #999
}

.dropzone {
    border: 2px dashed #047bf8
}

.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
    border-color: #cecece
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #e2ebff;
    border: 1px solid #4771d2
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: #474a50;
    margin-right: 4px
}

.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: #eff2ff
}

.form-control {
    font-weight: 300
}

.select2 {
    font-weight: 300
}

body .daterangepicker {
    -webkit-box-shadow: 3px 3px 40px rgba(0, 0, 0, 0.2);
    box-shadow: 3px 3px 40px rgba(0, 0, 0, 0.2);
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif
}

body .daterangepicker td.in-range {
    background-color: #bdd5ff
}

body .daterangepicker td.active,
body .daterangepicker td.active:hover {
    background-color: #047bf8
}

body .daterangepicker th {
    font-weight: 500
}

body .daterangepicker:before {
    border-bottom-color: #3E4B5B
}

body .daterangepicker .calendar td {
    font-weight: 300;
    font-size: .9rem
}

body .daterangepicker .calendar th.month {
    color: #047bf8
}

body .daterangepicker thead tr:first-child th {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 5px
}

body .daterangepicker thead tr:first-child+tr th {
    padding-top: 10px
}

body .daterangepicker .daterangepicker_input i {
    left: 4px;
    top: 3px;
    font-size: 18px
}

body .daterangepicker .fa.fa-calendar.glyphicon.glyphicon-calendar:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e926"
}

body .daterangepicker .fa.fa-chevron-left.glyphicon.glyphicon-chevron-left:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e919";
    font-size: 10px
}

body .daterangepicker .fa.fa-chevron-right.glyphicon.glyphicon-chevron-right:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e910";
    font-size: 10px
}

.dataTables_length select {
    display: inline-block;
    width: 50px;
    margin: 0px 5px;
    vertical-align: middle
}

.dataTables_filter input {
    display: inline-block;
    width: 130px;
    margin: 0px 5px;
    vertical-align: middle
}

.dataTables_wrapper .row:first-child {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    margin-top: 1rem;
    padding-bottom: 0.5rem
}

.dataTables_wrapper .row:last-child {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
    margin-bottom: 1rem;
    padding-top: 0.5rem
}

.fc-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.fc-header td {
    padding: 10px 0px
}

.fc-header h2 {
    text-transform: uppercase;
    font-size: 18px
}

.fc-content {
    color: #fff
}

.fc-event {
    background-color: #3584ff;
    -webkit-box-shadow: 5px 5px 10px 0px #bdd4ff;
    box-shadow: 5px 5px 10px 0px #bdd4ff;
    border: none;
    padding: 6px;
    padding-left: 9px;
    color: #fff;
    border-radius: 4px
}

.fc-day-number {
    color: #6B6862
}

.fc-day-header {
    font-weight: 300;
    color: #6B6862;
    text-transform: uppercase;
    font-size: 12px
}

.fc-other-month {
    background-color: #eee
}

.wrapper-front .fc-button {
    padding: 5px 10px;
    height: auto;
    margin: 0px 5px;
    background-image: none;
    -webkit-box-shadow: none;
    box-shadow: none
}

.wrapper-front .fc-button.fc-state-active {
    outline: none;
    text-shadow: none
}

table.dataTable {
    clear: both;
    margin-top: 6px !important;
    margin-bottom: 6px !important;
    max-width: none !important;
    border-collapse: separate !important
}

table.dataTable td,
table.dataTable th {
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

table.dataTable td.dataTables_empty,
table.dataTable th.dataTables_empty {
    text-align: center
}

table.dataTable.nowrap th,
table.dataTable.nowrap td {
    white-space: nowrap
}

div.dataTables_wrapper div.dataTables_length label {
    font-weight: normal;
    text-align: left;
    white-space: nowrap
}

div.dataTables_wrapper div.dataTables_length select {
    width: 75px;
    display: inline-block
}

div.dataTables_wrapper div.dataTables_filter {
    text-align: right
}

div.dataTables_wrapper div.dataTables_filter label {
    font-weight: normal;
    white-space: nowrap;
    text-align: left
}

div.dataTables_wrapper div.dataTables_filter input {
    margin-left: 0.5em;
    display: inline-block;
    width: auto
}

div.dataTables_wrapper div.dataTables_info {
    padding-top: 0.85em;
    white-space: nowrap
}

div.dataTables_wrapper div.dataTables_paginate {
    margin: 0;
    white-space: nowrap;
    text-align: right
}

div.dataTables_wrapper div.dataTables_paginate ul.pagination {
    margin: 2px 0;
    white-space: nowrap;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

div.dataTables_wrapper div.dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    margin-left: -100px;
    margin-top: -26px;
    text-align: center;
    padding: 1em 0
}

table.dataTable thead>tr>th.sorting_asc,
table.dataTable thead>tr>th.sorting_desc,
table.dataTable thead>tr>th.sorting,
table.dataTable thead>tr>td.sorting_asc,
table.dataTable thead>tr>td.sorting_desc,
table.dataTable thead>tr>td.sorting {
    padding-right: 30px
}

table.dataTable thead>tr>th:active,
table.dataTable thead>tr>td:active {
    outline: none
}

table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_desc,
table.dataTable thead .sorting_asc_disabled,
table.dataTable thead .sorting_desc_disabled {
    cursor: pointer;
    position: relative
}

table.dataTable thead .sorting:before,
table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:before,
table.dataTable thead .sorting_desc_disabled:after {
    position: absolute;
    bottom: 0.9em;
    display: block;
    opacity: 0.3
}

table.dataTable thead .sorting:before,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_desc_disabled:before {
    right: 1em;
    content: "\2191"
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:after {
    right: 0.5em;
    content: "\2193"
}

table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_desc:after {
    opacity: 1
}

table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_desc_disabled:after {
    opacity: 0
}

div.dataTables_scrollHead table.dataTable {
    margin-bottom: 0 !important
}

div.dataTables_scrollBody table {
    border-top: none;
    margin-top: 0 !important;
    margin-bottom: 0 !important
}

div.dataTables_scrollBody table thead .sorting:after,
div.dataTables_scrollBody table thead .sorting_asc:after,
div.dataTables_scrollBody table thead .sorting_desc:after {
    display: none
}

div.dataTables_scrollBody table tbody tr:first-child th,
div.dataTables_scrollBody table tbody tr:first-child td {
    border-top: none
}

div.dataTables_scrollFoot table {
    margin-top: 0 !important;
    border-top: none
}

@media screen and (max-width: 767px) {
    div.dataTables_wrapper div.dataTables_length,
    div.dataTables_wrapper div.dataTables_filter,
    div.dataTables_wrapper div.dataTables_info,
    div.dataTables_wrapper div.dataTables_paginate {
        text-align: center
    }
}

table.dataTable.table-condensed>thead>tr>th {
    padding-right: 20px
}

table.dataTable.table-condensed .sorting:after,
table.dataTable.table-condensed .sorting_asc:after,
table.dataTable.table-condensed .sorting_desc:after {
    top: 6px;
    right: 6px
}

table.table-bordered.dataTable th,
table.table-bordered.dataTable td {
    border-left-width: 0
}

table.table-bordered.dataTable th:last-child,
table.table-bordered.dataTable th:last-child,
table.table-bordered.dataTable td:last-child,
table.table-bordered.dataTable td:last-child {
    border-right-width: 0
}

table.table-bordered.dataTable tbody th,
table.table-bordered.dataTable tbody td {
    border-bottom-width: 0
}

div.dataTables_scrollHead table.table-bordered {
    border-bottom-width: 0
}

div.table-responsive>div.dataTables_wrapper>div.row {
    margin: 0
}

div.table-responsive>div.dataTables_wrapper>div.row>div[class^="col-"]:first-child {
    padding-left: 0
}

div.table-responsive>div.dataTables_wrapper>div.row>div[class^="col-"]:last-child {
    padding-right: 0
}

@media (max-width: 1650px) {
    .section-header {
        padding-left: 40px;
        padding-right: 40px
    }
}

@media (max-width: 1350px) {
    .intro-w.layout-v1 .intro-description .intro-heading {
        font-size: 2.99rem
    }
    .section-header .section-title {
        font-size: 2.53rem
    }
    .features-table .feature-cell {
        padding: 50px 40px
    }
    .call-to-action .cta-header {
        font-size: 2.53rem
    }
    .demos-w .demo-w {
        padding: 30px 20px
    }
    .menu-top-f ul.main-menu li a {
        padding: 20px 15px
    }
    .rentals-list-w .filter-side {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 350px;
        flex: 0 0 350px
    }
    .rentals-list-w .filter-side .filter-body {
        padding: 10px 20px 30px 20px
    }
    .top-bar .logo-w {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 350px;
        flex: 0 0 350px
    }
}

@media (max-width: 1150px) {
    .intro-w.layout-v1 .intro-description .intro-text {
        font-size: 1.1rem
    }
    .intro-w.layout-v1 .intro-description .intro-heading {
        font-size: 2.53rem
    }
    .section-header .section-title {
        font-size: 2.3rem
    }
    .intro-w.layout-v1 .intro-description {
        -ms-flex-preferred-size: 500px;
        flex-basis: 500px
    }
    .menu-top-f ul.small-menu li.separate {
        padding-left: 15px;
        margin-left: 0px
    }
}

@media (max-width: 1024px) {
    .os-container {
        padding: 0px 20px
    }
    .features-table .b-l,
    .features-table .b-r,
    .features-table .b-b,
    .features-table .b-t {
        border-width: 0px
    }
    .menu-top-f .menu-top-i {
        display: none
    }
    .mobile-menu-w {
        display: block
    }
}

@media (min-width: 768px) and (max-width: 1024px) {
    .counters-w .counter .counter-value {
        font-size: 2.99rem
    }
    .counters-w .counter .counter-name {
        font-size: .9rem
    }
    .counters-w .counter .counter-description {
        font-size: 1rem;
        width: 160px
    }
}

@media (max-width: 768px) {
    .intro-w.layout-v1 .intro-i {
        -webkit-box-orient: vertical;
        -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        height: auto
    }
    .intro-w.layout-v1 .intro-description {
        width: auto;
        padding: 50px;
        -ms-flex-preferred-size: auto;
        flex-basis: auto
    }
    .intro-w.layout-v1 .intro-media {
        width: 90%;
        padding-bottom: 45%;
        padding-top: 35%
    }
    .counters-w .counters-i {
        display: block
    }
    .counters-w .counter {
        margin-bottom: 20px;
        padding-bottom: 20px;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1)
    }
    .counters-w .counter:last-child {
        margin-bottom: 10px;
        padding-bottom: 0px;
        border-bottom: none
    }
    .counters-w .counter .counter-value-w {
        width: 140px
    }
    .counters-w .counter .counter-description {
        width: auto;
        -webkit-box-flex: 1;
        -ms-flex: 1 1 auto;
        flex: 1 1 auto;
        font-size: 1.2rem
    }
    .rentals .top-bar {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column
    }
    .rentals .top-bar .logo-w {
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1
    }
    .rentals .top-bar .filters {
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        padding: 30px 0px;
        text-align: center
    }
    .rentals .top-bar .filters .filters-header {
        padding-right: 0px
    }
    .rentals .top-bar .filters .filter-w {
        border-left: none;
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1
    }
    .rentals .top-bar .filters .filter-w .form-group {
        margin-bottom: 0
    }
    .rentals .top-bar .filters .filter-w+.filter-w {
        padding-top: 0px
    }
    .rentals .top-bar .filters .buttons-w {
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1
    }
    .rentals .rentals-list-w {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column
    }
    .rentals .rentals-list-w .filter-side {
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1
    }
    .rentals .rentals-list-w .rentals-list {
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1
    }
    .rentals .rentals-list-w .rentals-list .property-items .property-item {
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
        padding: 0px
    }
    .rentals .rentals-list-w .rentals-list .property-items .property-item .item-info {
        padding: 20px
    }
    .rentals .pagination-w,
    .rentals .list-controls {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        text-align: center
    }
    .rentals .pagination-w .list-order,
    .rentals .list-controls .list-order {
        margin: 0px auto;
        margin-top: 10px
    }
    .rentals .pagination-w .pagination-info,
    .rentals .list-controls .pagination-info {
        margin-bottom: 20px
    }
    .rentals .property-single .property-info-w {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        padding: 0px 20px
    }
    .rentals .property-single .property-info-w .property-info-main {
        padding: 0px;
        padding-top: 20px;
        border-left: none
    }
    .rentals .property-single .property-info-w .property-info-main .property-features-highlight {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap
    }
    .rentals .property-single .property-info-w .property-info-main .property-features-highlight .feature {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        padding: 20px 10px
    }
    .rentals .property-single .property-info-w .property-info-main .property-features-highlight .feature:nth-child(1) {
        border-bottom: 1px solid rgba(0, 0, 0, 0.1)
    }
    .rentals .property-single .property-info-w .property-info-main .property-features-highlight .feature:nth-child(2) {
        border-left: 1px solid rgba(0, 0, 0, 0.1);
        border-bottom: 1px solid rgba(0, 0, 0, 0.1)
    }
    .rentals .property-single .property-info-w .property-info-main .property-features-highlight .feature:nth-child(4) {
        border-left: 1px solid rgba(0, 0, 0, 0.1)
    }
    .rentals .property-single .property-info-w .property-info-main .property-price {
        position: relative;
        top: auto;
        right: auto;
        -webkit-transform: none;
        transform: none;
        width: auto;
        padding: 15px 0px;
        text-align: left;
        border-bottom: none
    }
    .rentals .property-single .property-info-w .property-section .property-section-body {
        padding: 20px 0px
    }
    .rentals .property-single .property-info-w .property-section-header .filter-toggle {
        right: 0px
    }
    .rentals .property-single .property-info-w .property-info-side {
        border-left: none;
        border-right: none
    }
    .rentals .property-single .property-info-w .property-info-side .side-action-form {
        padding: 20px 0px
    }
    .rentals .related-listings {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column
    }
    .rentals .related-listings .property-item {
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1
    }
    .rentals .related-listings .property-item .item-info {
        padding: 20px
    }
}

@media (max-width: 767px) {
    .intro-w.layout-v1 .intro-description .intro-heading {
        font-size: 1.84rem
    }
    .intro-w.layout-v1 .intro-media {
        padding-left: 20px
    }
    .intro-w.layout-v1 .intro-description {
        padding: 40px 20px;
        text-align: center
    }
    .intro-w.layout-v1 .intro-description .intro-heading:before {
        left: 47.5%;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%)
    }
    .intro-w.layout-v1 .intro-description .intro-heading:after {
        left: 52.5%;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%)
    }
    .intro-w.layout-v1 .intro-media {
        padding-top: 40%;
        padding-bottom: 50%
    }
    .intro-w.layout-v1 .intro-description .intro-buttons {
        text-align: center
    }
    .intro-w.layout-v1 .intro-description .intro-buttons .btn.btn-primary,
    .intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .btn-primary.fc-button,
    .wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .btn-primary.fc-button,
    .intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .fc-button.fc-state-active,
    .wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .fc-button.fc-state-active {
        margin-bottom: 20px;
        margin-right: 0px
    }
    .counters-w {
        margin-bottom: 40px
    }
    .section-header .section-title {
        font-size: 1.84rem
    }
    .counters-w .counter {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        text-align: center
    }
    .counters-w .counter .counter-value-w {
        margin-right: 0px;
        padding-right: 0px;
        border-right: none;
        margin-bottom: 10px
    }
    .counters-w .counters-i {
        padding: 40px 10px
    }
    .demos-w .demo-w {
        padding: 20px 10px
    }
    .footer-w .b-r {
        border-width: 0px
    }
    .testimonials-slider-w .slick-arrow,
    .projects-slider-w .slick-arrow {
        top: -20px
    }
    .section-header {
        padding-top: 50px;
        padding-bottom: 50px
    }
    .section-header .section-desc {
        font-size: 1rem
    }
    .project-slide .project-content {
        padding: 20px
    }
    .testimonials-slider-w .slide {
        padding: 50px 40px;
        padding-bottom: 25px;
        text-align: center
    }
    .testimonials-w {
        padding: 50px 0px;
        margin-top: 70px
    }
    .testimonials-slider-w .testimonial-by {
        text-align: center;
        margin-top: 20px
    }
    .testimonials-slider-w .testimonial-by .avatar {
        margin-top: 10px
    }
}
