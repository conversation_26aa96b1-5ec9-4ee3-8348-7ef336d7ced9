<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class SettingsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $settings = [
                array('name' => 'eligibility_gap', 'value' => 1),
                array('name' => 'max_age', 'value' => 65),
                array('name' => 'max_served', 'value' => 35),
                array('name' => 'gap_age', 'value' => 1),
                array('name' => 'gap_served', 'value' => 2),
                array('name' => 'stop_loan_date', 'value' => date('Y-m-d')),
                array('name' => 'multifund_expire', 'value' => 14),
                array('name' => 'snooze_period', 'value' => 1),
                array('name' => 'split_loan', 'value' => 3),
                array('name' => 'withdrawal_limit', 'value' => 1000),
            ];

            foreach ($settings as $setting){
                $check = Setting::where('name', $setting['name'])->where('lender_id', 1)->first();
                if(!$check) {
                    $s = new Setting();
                    $s->name = $setting['name'];
                    $s->value = $setting['value'];
                    $s->lender_id = 1;
                    $s->save();

                    error_log('setting: '.$setting['name']);
                }
            }
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
        }
    }
}
