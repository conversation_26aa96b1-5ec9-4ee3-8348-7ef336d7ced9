/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import moment from 'moment';
import { connect } from 'react-redux';
import Pagination from 'antd/lib/pagination';

import { httpRequest, baseURL, paginate, merchantAPI } from '../config/constants';
import loading from '../assets/img/loading.gif';
import { startBlock, stopBlock } from '../actions/ui-block';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 8;

class GetUser extends Component {
	state = {
		phone: '',
		error: '',
		finding: false,
		users: [],
		currentPage: 1,
		totalPage: 0,
		slicedUsers: [],
	};

	onChange = (e) => this.setState({ phone: e.target.value });

	findUser = async (e) => {
		e.preventDefault();
		try {
			this.setState({ finding: true });
			const { phone } = this.state;
			if (phone.length >= 5) {
				const { lender } = this.props;
				const data = { phone, lender_id: lender.id };
				const rs = await httpRequest(`${baseURL}${merchantAPI}/find/user`, 'POST', true, data);
				this.setState({
					users: rs.users,
					finding: false,
					totalPage: rs.users.length,
					slicedUsers: rs.users.slice(0, pageSize),
				});
			}
		} catch (e) {
			this.setState({
				error: e.message || 'no users found',
				finding: false,
				totalPage: 0,
			});
		}
	};

	handleChange = pageNumber => {
		const { users } = this.state;
		const filteredUsers = paginate(users, pageSize, pageNumber);
		this.setState({ slicedUsers: filteredUsers, currentPage: pageNumber });
	};

	makeMerchant = id => async () => {
		this.props.startBlock();
		try {
			const { user, lender } = this.props;
			const data = { user_id: id, staff_id: user.id, lender_id: lender.id };
			await httpRequest(`${baseURL}${merchantAPI}?q=user`, 'POST', true, data);
			this.props.stopBlock();
			this.props.closeModal();
		} catch (e) {
			this.props.stopBlock();
            const message = e.message || 'could not create merchant user account';
			this.setState({ error: message });
		}
	};

	render() {
		const { closeModal } = this.props;
		const { error, phone, finding, currentPage, totalPage, slicedUsers } = this.state;
		return (
			<div>
                <div className="modal-header faded smaller">
                    <div className="modal-title">
                        <span>FastCash Merchant</span>
                    </div>
                    <button aria-label="Close" className="close" onClick={closeModal} type="button"><span aria-hidden="true"> ×</span></button>
                </div>
				<div className="element-box">
					{error !== '' && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
					<form onSubmit={this.findUser}>
						<div className={`row ${(error !== '' && 'has-error')}`}>
							<div className="col-sm-9">
								<input name="phone" placeholder="Enter phone number or ippis or email" type="text" className="form-control" value={phone} onChange={this.onChange} />
							</div>
							{phone.length >= 5 && (
								<div className="col-sm-3">
									<button className="btn btn-primary" type="submit">
										{finding ? <img src={loading} alt="" /> : 'Find'}
									</button>
								</div>
							)}
						</div>
					</form>
					<div className="table-responsive mt-2">
						<table className="table table-striped">
							<thead>
								<tr>
									<th>Name</th>
									<th>Phone</th>
									<th>Office</th>
									<th>Date Created</th>
									<th className="text-center" />
								</tr>
							</thead>
							<tbody>
								{slicedUsers.map((user, i) => {
									return (
										<tr key={i}>
											<td>{user.name}</td>
											<td>{user.phone}</td>
											<td>{user.office ? user.office.name : '-'}</td>
											<td>{moment(user.created_at).format('DD.MMM.YYYY')}</td>
											<td className="row-actions">
												<a className="text-primary cursor" onClick={this.makeMerchant(user.id)}>
													<i className="os-icon os-icon-user-plus" />
												</a>
											</td>
										</tr>
									);
								})}
							</tbody>
						</table>
					</div>
					<div className="pagination pagination-center mt-4">
						<Pagination
							current={currentPage}
							pageSize={pageSize}
							total={totalPage}
							showTotal={total => `Total ${total} users`}
							itemRender={itemRender}
							onChange={this.handleChange}
						/>
					</div>
				</div>
            </div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		lender: state.lender.profile,
        user: state.user.user,
	}
}

export default connect(mapStateToProps, { startBlock, stopBlock })(GetUser);