/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import Modal from 'react-modal';
import { connect } from 'react-redux';
import truncate from 'lodash.truncate';
import moment from 'moment';
import { Link } from 'react-router-dom';
import qs from 'querystring';
import startCase from 'lodash.startcase';

import CreateTicket from '../components/CreateTicket';
import { loadSupport, toggleUpdated, updateTicket } from '../actions/support';
import supportImg from '../assets/img/support.png';
import Loading from '../components/Loading';
import { httpRequest, baseURL, supportAPI, rootURL } from '../config/constants';
import { doNotify } from '../actions/general';
import ReplyTicket from '../components/ReplyTicket';
import { startBlock, stopBlock } from '../actions/ui-block';

class Support extends Component {
	constructor(props, context) {
        super(props, context);
        this.state = {
			modalIsOpen: false,
			ticket: null,
			hasNext: false,
			hasPrevious: false,
			nextPage: 1,
			previousPage: 1,
			total: 0,
			search: '',
			fetching: false,
        };

        this.openModal = this.openModal.bind(this);
        this.closeModal = this.closeModal.bind(this);
    }
	
	UNSAFE_componentWillMount() {
		Modal.setAppElement('body');
	}

	componentDidMount() {
		const { location } = this.props;
		const path = qs.parse(location.search.replace('?', ''));
		const page = path && path.page ? path.page : 1;
		this.setState({ search: location.search });
		this.fetchSupport(page);
	}

	fetchSupport = async page => {
		const { user, location } = this.props;
		if (user){
			try {
				const path = qs.parse(location.search.replace('?', ''));
				const tid = path && path.tid ? path.tid : '';
				const results = 5;
				this.setState({ fetching: true });
				const rs = await httpRequest(`${baseURL}${supportAPI}?user_id=${user.id}&results=${results}&page=${page}&ticket=${tid}`, 'GET', true);
				const result = rs.result;
				this.props.loadSupport(result.data);
				this.setState({
					fetching: false,
					hasNext: result.hasNext,
					hasPrevious: result.hasPrevious,
					nextPage: parseInt(result.page, 10) + 1,
					previousPage: parseInt(result.page, 10) - 1,
					total: result.total,
					ticket: result.ticket,
				});
			}
			catch (error) {
				const message = error.message || 'error, failed loading support tickets';
				this.props.doNotify({ message, level: 'error', title: '' });
				this.setState({ fetching: false });
			}
		}
	};

	componentWillUpdate(nextProps, nextState) {
		const { location } = nextProps;
		if (location.search !== nextState.search && !nextState.fetching) {
			const path = qs.parse(location.search.replace('?', ''));
			const page = path && path.page ? path.page : 1;
			this.setState({ search: location.search });
			this.fetchSupport(page);
		}
		if (nextProps.updated) {
			const ticket = nextProps.tickets.find(t => t.id === nextState.ticket.id);
			if (ticket) {
				this.setState({ ticket });
			}
		}
	}

	componentDidUpdate(prevProps, prevState) {
		if (prevProps.updated) {
			this.props.toggleUpdated();
		}
	}
    
    openModal() {
        document.body.className="pages modal-open";
        this.setState({ modalIsOpen: true });
    }

    closeModal = () => {
        this.setState({ modalIsOpen: false, ticket: null, total: parseInt(this.state.total, 10) + 1 });
        document.body.className="pages";
    }

    setTicket = ticket => () => {
        this.setState({ ticket });
    }

    onChange = e => {
        console.log(e.target.value);
	}
	
	badge = response => {
		const explode = response.split(',');
		const type = explode[0];

		if(type === 'awaiting response') {
			return 'badge-danger-inverted';
		}
		else if(type === 'answered') {
			return 'badge-success-inverted';
		}
		else if(type === 'closed') {
			return 'badge-default-inverted';
		}

		return '';
	}
	
	pill = response => {
		const explode = response.split(',');
		const type = explode[0];
		
		if(type === 'awaiting response') {
			return 'red';
		}
		else if(type === 'answered') {
			return 'green';
		}
		else if(type === 'closed') {
			return 'dark';
		}

		return '';
	}
	
	closeTicket = ticketId => async () => {
		this.props.startBlock();
		try {
			const { user } = this.props;
			const data = { user_id: user.id };
			const rs = await httpRequest(`${baseURL}${supportAPI}/close/${ticketId}`, 'POST', true, data);
			this.props.updateTicket(rs.support);
			this.props.stopBlock();
			this.props.doNotify({ message: 'ticket closed', level: 'success', title: '' });
		}
		catch (error) {
			const message = error.message || 'error, failed to close ticket';
			this.props.stopBlock();
			this.props.doNotify({ message, level: 'error', title: '' });
		}
	}

	render() {
        const { user, tickets } = this.props;
		const { modalIsOpen, ticket, hasNext, hasPrevious, total, nextPage, previousPage, fetching } = this.state;
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="support-index">
						<div className="support-tickets">
							<div className="support-tickets-header">
								<div className="tickets-control">
									<h5>Tickets</h5>
									<div className="new-ticket">
                                        <button className="btn btn-primary" onClick={this.openModal}><i className="os-icon os-icon-plus mr-1" /> New Ticket</button>
									</div>
								</div>

								{/* <div className="tickets-filter">
									<div className="form-group mr-3">
										<label className="d-none d-md-inline-block mr-2">Status</label>
										<select className="form-control-sm" onChange={this.onChange}>
											<option value="all">All</option>
											<option value="open">Open</option>
											<option value="awaiting-response">Awaiting Response</option>
											<option value="closed">Closed</option>
											<option value="answered">Answered</option>
										</select>
									</div>
								</div> */}
							</div>
							{fetching && <Loading />}
							{!fetching && tickets.map((item, i) => {
								const replies = item.replies;
								const response = replies[replies.length - 1].response.split(',');
								return (
									<div className={`support-ticket ${ticket && ticket.id === item.id ? 'active':''}`} onClick={this.setTicket(item)} key={i}>
										<div className="st-meta">
											<div className={`badge ${this.badge(replies[replies.length - 1].response)}`}>{response[0]}</div>
											<div className={`status-pill ${this.pill(replies[replies.length - 1].response)}`} />
										</div>
										<div className="st-body">
											<div className="ticket-content">
												<h6 className="ticket-title"><span>{`Ticket #${item.id}:`}</span> {item.subject}</h6>
												<div className="ticket-description">{truncate(item.message, { 'length': 58, })}</div>
											</div>
										</div>
										<div className="st-foot">
											<span className="label">Created On:</span>
											<span className="value">{moment(item.created_at).format('D MMM, YYYY [at] h:ma')}</span>
										</div>
									</div>
								);
							})}
							{!fetching && tickets.length === 0 && (
								<div className="support-ticket">
									<div className="st-body">
										<div className="ticket-content">
											<div className="ticket-description">No tickets created</div>
										</div>
									</div>
								</div>
							)}
							{!fetching && (
								<div className="controls-below-table">
									<div className="table-records-info">{`${total} Ticket${total > 1 ? 's':''}`}</div>
									<div className="table-records-pages">
										<ul>
											<li><Link to={`/support?page=${previousPage}`} className={hasPrevious ? '' : 'disabled-link'}>Previous</Link></li>
											<li><Link to={`/support?page=${nextPage}`} className={hasNext ? '' : 'disabled-link'}>Next</Link></li>
										</ul>
									</div>
								</div>
							)}
						</div>
						<div className="support-ticket-content-w">
							{ticket ? (
								<div className="support-ticket-content">
									<div className="support-ticket-content-header" style={{justifyContent: 'space-between'}}>
										<h3 className="ticket-header"><span>{`Ticket #${ticket.id}:`}</span>{ticket.subject}</h3>
										{ticket.status === 0 && (
											<div className="close-ticket">
												<button className="btn btn-primary" onClick={this.closeTicket(ticket.id)}><i className="os-icon os-icon-close mr-1" /> Close Ticket</button>
											</div>
										)}
									</div>
									<div className="ticket-thread">
										{ticket.replies.map((conv, i) => {
											const attachments = conv.attachment && conv.attachment !== '' ? JSON.parse(conv.attachment) : [];
											return (
												conv.status === 1 && conv.response === 'closed,closed' ? (
													<div className="ticket-reply" key={i}>
														<div className="ticket-closed">{conv.message}</div>
													</div>
												) : (
													<div className={`ticket-reply ${conv.user_id === user.id ? '':'highlight'}`} key={i}>
														<div className="ticket-reply-info">
															<a className="author with-avatar">
																<span>{startCase(conv.user.name.toLowerCase())}</span>
															</a>
															{conv.receiver_id ? (
																<span className="info-data">
																	<span className="label">{conv.receiver_id === conv.user_id ? 'replied on':'submitted on'}</span>
																	<span className="value">{moment(conv.created_at).format('D MMM, YYYY [at] h:ma')}</span>
																</span>
															) : (
																<span className="info-data">
																	<span className="label">{conv.user_id === user.id ? 'submitted on':'replied on'}</span>
																	<span className="value">{moment(conv.created_at).format('D MMM, YYYY [at] h:ma')}</span>
																</span>
															)}
														</div>
														<div className="ticket-reply-content">{conv.message}</div>
														{attachments.length > 0 && (
															<div className="ticket-attachments">
																{attachments.map((file, i) => {
																	return (
																		<a className="attachment" target="_blank" rel="noreferrer noopener" href={`${rootURL}/attachment/${file.name}`} key={i}>
																			<i className={`os-icon ${file.icon}`} />
																			<span>{truncate(file.name, { 'length': 20, })}</span>
																		</a>
																	);
																})}
															</div>
														)}
													</div>
												)
											);
										})}
									</div>
									{ticket.status === 0 && (
										<ReplyTicket title="Reply Ticket" id={ticket.id} user={user} />
									)}
								</div>
							) : (
								<div className="support-ticket-content" style={{textAlign: 'center'}}>
									<img src={supportImg} style={{width: '200px'}} alt=""/>
								</div>
							)}
						</div>
					</div>
				</div>
				{modalIsOpen && (
                    <Modal
						isOpen={modalIsOpen}
						onRequestClose={this.closeModal}
						contentLabel="Create Ticket"
						shouldCloseOnOverlayClick={false}
						overlayClassName="modal-dialog modal-md"
						className="modal-content"
					>
						<CreateTicket 
                            closeModal={() => this.closeModal()}
                            status="new"
                            title="Create Ticket"
                            user={user}
                        />
					</Modal>
                )}
                {modalIsOpen? (<div className="modal-backdrop fade show"/>) : ''}
			</div>
		);
	}
}

const mapStateToProps = (state) => {
	return {
		user: state.user.user,
		tickets: state.support.list,
		updated: state.support.updated,
		lender: state.lender.profile,
	}
};

export default connect(mapStateToProps, { doNotify, loadSupport, toggleUpdated, updateTicket, startBlock, stopBlock })(Support);
