/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';

import { baseURL, httpRequest, forgotPasswordAPI, resendEmailAPI } from '../config/constants';
import loading from '../assets/img/loading.gif';
import { doNotify } from '../actions/general';
import { startBlock, stopBlock } from '../actions/ui-block';
import { setUserData } from '../actions/user';

const validate = values => {
    const errors = {}
    if (!values.email) {
        errors.email = 'Enter your email address';
    }
    return errors;
};

const renderField = ({input, id, icon, label, hideclass, type, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')} ${hideclass}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" />
        <div className={`pre-icon os-icon ${icon}`}></div>
        {touched &&
            ((error && <span className="help-block">{error}</span>) ||
                (warning && <span className="help-block">{warning}</span>))}
    </div>
);

class ForgotForm extends Component {
    state = {
        user_id: '',
        user_email: '',
        vsuccess: '',
        verror: '',
        verify: false,
        submitting: false,
    }

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

    resendEmail = async () => {
        const { user_email, user_id } = this.state;
        this.props.startBlock();
        try {
            const data = { email: user_email, user_id };
            await httpRequest(`${baseURL}${resendEmailAPI}`, 'POST', false, data);
            this.setState({ vsuccess: 'Please check your email to verify and activate your account', verror: '', verify: false });
            this.props.stopBlock();
        }
        catch(error) {
            this.props.stopBlock();
            const message = error.message || 'could not re-send email';
            this.notify('', message, 'error');
        }
    };
    
    resetPassword = async data => {
        try {
            this.setState({ verify: false, vsuccess: '', verror: '', user_email: '', user_id: '', submitting: true });
            const details = {
                ...data,
                role: 'user',
                platform: 'user',
                source: 'web',
            };
            const rs = await httpRequest(`${baseURL}${forgotPasswordAPI}`, 'POST', false, details);
            if(rs.error) {
                const { user_email, user_id, error } = rs;
                this.setState({ verify: true, user_email, user_id, verror: error, submitting: false });
                this.props.setUserData(user_id, user_email);
            } else {
                this.notify('', 'Check your email to reset your password!', 'success');
                this.props.reset('forgotform');
                this.setState({ submitting: false });
            }
        } catch (error) {
            const message = error.message || 'error, please try again';
            this.notify('', message, 'error');
            this.setState({ submitting: false });
            throw new SubmissionError({
                _error: message,
            });
        }
    };

    render() {
        const { handleSubmit, error, submitting, pristine } = this.props;
        const { verror, vsuccess, verify } = this.state;
        return (
            <div className="login-box">
                <div className="auth-box-w intro-description">
                    <h4 className="auth-header">Reset Password</h4>
                    {error && <div className="alert alert-danger" dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                    {verror !== '' && <div className="alert alert-danger" dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${verror}`}}></div>}
                    {vsuccess !== '' && <div className="alert alert-success" dangerouslySetInnerHTML={{__html: `<strong>Success!</strong> ${vsuccess}`}}></div>}
                    {verify && <div className="alert alert-danger" style={{marginTop: '5px',}}>Did not recieve email? <a className="cursor" role="button" tabIndex="0" onClick={this.resendEmail}>Resend Email</a> or <Link to="/change-email">Change Email</Link></div>}
                    <form onSubmit={handleSubmit(this.resetPassword)} autoComplete="off">
                        <Field
                            name="email"
                            type="text"
                            component={renderField}
                            label="Email address"
                            icon="os-icon-user-male-circle"
                        />
                        <div>
                            <Link className="btn btn-link" to="/">Login / Sign Up</Link>
                        </div>
                        <div className="buttons-w">
                            <button className="btn btn-primary" disabled={pristine || submitting} type="submit">
                                {submitting? <img src={loading} alt=""/> : 'Send Recovery Mail'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    }
}

ForgotForm = reduxForm({
    form: 'forgotform',
    validate
})(ForgotForm);

export default withRouter(connect(null, { reset, doNotify, startBlock, stopBlock, setUserData })(ForgotForm));
