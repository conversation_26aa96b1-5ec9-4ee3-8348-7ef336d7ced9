<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateUssdLevelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ussd_levels', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('ussd_session_id')->unsigned()->nullable();
            $table->tinyInteger('level')->default(0);
            $table->tinyInteger('sub_level')->default(0);
            $table->text('loan_data')->nullable();
            $table->text('user_data')->nullable();
            $table->integer('repeat')->default(0);
            $table->timestamps();

            $table->foreign('ussd_session_id')->references('id')->on('ussd_sessions')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ussd_levels');
    }
}
