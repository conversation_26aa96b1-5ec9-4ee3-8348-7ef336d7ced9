/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import Pagination from 'antd/lib/pagination';
import moment from 'moment';

import { startBlock, stopBlock } from '../actions/ui-block';
import { baseURL, httpRequest, loanAPI, currency } from '../config/constants';
import loading from '../assets/img/loading.gif';
import { doNotify } from '../actions/general';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 8;

class TransferLoans extends Component {
	state = {
		error: '',
		loans: [],
		lender_id: '',
		selectedLoans: [],
		amount: 0,
		currentPage: 1,
		totalPage: 0,
	};

	componentDidMount() {
		this.fetch(1);
	}
	
	fetch = async pageNumber => {
		try {
			const { user } = this.props;
			this.props.startBlock();
			const rs = await httpRequest(`${baseURL}${loanAPI}?page=${pageNumber}&pagesize=${pageSize}&category=active&q=admin&lender=${user.lender_id}&disbursed=1&when=1`, 'GET', true);
			const result = rs.result;
			this.setState({ currentPage: pageNumber, totalPage: result.total, loans: result.data });
			this.props.stopBlock();
		} catch (e) {
			this.props.stopBlock();
			this.props.doNotify({ message: 'could not load loans', level: 'error' });
		}
	}

	doTransfer = async () => {
		try {
			const { user, lenders } = this.props;
			const { selectedLoans, lender_id, amount } = this.state;

			if (lender_id === '') {
				this.setState({ error: 'select lender' });
				return;
			}

			if (selectedLoans.length === 0) {
				this.setState({ error: 'select loans' });
				return;
			}

			const _lender = lenders.find((l) => l.id === parseInt(lender_id, 10));
			if (_lender) {
				const action = window.confirm(`Do you want to sell loans to ${_lender.name}?`) ? true : false;
				if (action) {
					const details = {
						loans: selectedLoans,
						lender_id,
						staff_id: user.id,
						amount,
						super_lender: user.lender_id,
					};
					const url = `${baseURL}${loanAPI}/sell/loans`;
					await httpRequest(url, 'POST', true, details);
					this.notify('', `loans sold to ${_lender.name}!`, 'success');
					this.props.closeModal();
				}
			} else {
				this.setState({ error: 'lender not selected' });
			}
		} catch (error) {
			const message = error.message || 'could not sell loans';
			this.notify('', message, 'error');
			this.setState({ error: message });
		}
	};
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	onChange = (e, type) => {
		this.setState({ [type]: e.target.value });
	};

	onNavigate = pageNumber => {
		this.fetch(pageNumber);
	};

	selectLoan = e => {
		const { selectedLoans, loans, amount } = this.state;
		const loan = loans.find(l => l.id === parseInt(e.target.value, 10));
		if(loan){
			const _id = selectedLoans.find(id => id === parseInt(e.target.value, 10));
			const _loans = _id ? [...selectedLoans.filter(id => id !== parseInt(e.target.value, 10))] : [...selectedLoans, parseInt(e.target.value, 10)];
			const _amount = _id ? amount - parseFloat(loan.amount) : amount + parseFloat(loan.amount);
			this.setState({ selectedLoans: _loans, amount: _amount });
		}
	}

	render() {
		const { closeModal, lenders, user } = this.props;
		const { error, submitting, loans, currentPage, totalPage, amount, selectedLoans } = this.state;
		return (
			<div>
                <div className="modal-header faded smaller">
                    <div className="modal-title">
                        <span>Sell Loans</span>
                    </div>
                    <button aria-label="Close" className="close" onClick={closeModal} type="button"><span aria-hidden="true"> ×</span></button>
                </div>
                <div className="modal-body">
					{error !== '' && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}/>}
					<div className="row">
						<div className="col-sm-6">
							<div className="form-group">
								<label>Select Lender</label>
								<select name="lender" className="form-control" onChange={(e) => this.onChange(e, 'lender_id')}>
									<option value="">{`Select Lender`}</option>
									{lenders.filter(l => l.id !== parseInt(user.lender_id, 10)).map((c, i) => <option value={c.id} key={i}>{c.name}</option>)}
								</select>
							</div>
						</div>
						<div className="col-sm-6">
							<div className="form-group">
								<label>Amount</label>
								<input name="amount" placeholder="Amount" type="text" className="form-control" readOnly value={currency(amount)} />
							</div>
						</div>
					</div>
					<div className="row">
						<div className="col-sm-12">
							<div className="table-responsive" style={{overflowX: 'scroll',}}>
								<table className="table table-striped table-lightfont">
									<thead>
										<tr>
											<th/>
											<th>Lender</th>
											<th>Name of Employee</th>
											<th>IPPIS/Customer ID</th>
											<th>Amount</th>
											<th>Loan Status</th>
											<th>Disburse</th>
											<th>Tenure</th>
											<th>Repayments</th>
											<th>Request Date</th>
										</tr>
									</thead>
									<tbody>
										{loans.map((loan, i) => {
											const cid = loan.user.platform === 'sme' ? `Phone: ${loan.user.phone}` : (loan.user.ippis && !isNaN(loan.user.ippis) ? `IPPIS: ${loan.user.ippis}` : `CID: ${loan.user.customer_id}`);
											const _id = selectedLoans.find(id => id === loan.id);
											return (
												<tr key={i}>
													<td><input type="checkbox" name="loan" value={loan.id} onChange={this.selectLoan} checked={_id ? true : false}/></td>
													<td>{loan.lender.name}</td>
													<td>{loan.user.name}</td>
													<td nowrap="nowrap">
														{cid}<br/>
														<span className="smaller bold">{`${loan.platform.toUpperCase()}`}</span>
													</td>
													<td nowrap="nowrap">{currency(loan.amount)}</td>
													<td nowrap="nowrap">
														{loan.approved === 1 ? (
															<span>
																<small>{moment(loan.approved_at).format('D.MMM.YYYY')}</small>
																<br />
																<span className="elabel elabel-success">Approved</span>
															</span>
														): (
															<span className="elabel elabel-danger">Not Approved</span>
														)}
													</td>
													<td nowrap="nowrap">
														{loan.disbursed === 1 ? (
															<span>
																{loan.disbursed_at && (
																	<small>{moment(loan.disbursed_at).format('D.MMM.YYYY')}</small>
																)}
																{loan.disbursed_at && <br />}
																<span className="elabel elabel-success">Disbursed</span>
															</span>
														) : (
															<span className="elabel elabel-danger">Not Disbursed</span>
														)}
													</td>
													<td nowrap="nowrap">{loan.tenure ? `${loan.tenure}month${loan.tenure > 1 ? 's' : ''}` : '-'}</td>
													<td nowrap="nowrap">{loan.repayments}</td>
													<td nowrap="nowrap">{moment(loan.created_at).format('D-MMM-YYYY')}</td>
												</tr>
											)
										})}
									</tbody>
								</table>
							</div>
							<div className="pagination pagination-center mt-4">
								<Pagination
									current={currentPage}
									pageSize={pageSize}
									total={totalPage}
									showTotal={total => `Total ${total} loans`}
									itemRender={itemRender}
									onChange={this.onNavigate}
								/>
							</div>
						</div>
					</div>
				</div>
				<div className="modal-footer buttons-on-left">
					<button className="btn btn-teal" disabled={submitting} type="submit" onClick={this.doTransfer}>
						{submitting? <img src={loading} alt=""/> : 'Sell Loans'}
					</button>
					<button className="btn btn-link cursor" onClick={closeModal} type="button"> Cancel</button>
				</div>
            </div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
    return {
		user: state.user.user,
        lenders: state.lender.list,
    }
}

export default connect(mapStateToProps, { doNotify, startBlock, stopBlock })(TransferLoans);
