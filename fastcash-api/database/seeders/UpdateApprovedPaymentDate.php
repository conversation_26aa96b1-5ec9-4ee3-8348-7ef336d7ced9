<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\PaymentDate;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateApprovedPaymentDate extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::beginTransaction();
        try {
            $j=0;
            $payments = DB::table('payment_dates')->groupBy('loan_id')->select('loan_id')->get();
            foreach ($payments as $payment) {
                $loan = Loan::find($payment->loan_id);
                if($loan) {
                    $start_date = Carbon::createFromFormat('Y-m-d H:i:s', $loan->approved_at);
                    $end_date = Carbon::createFromFormat('Y-m-d H:i:s', $loan->approved_at)->addMonths($loan->tenure);

                    $end = Carbon::createFromFormat('Y-m-d H:i:s', $loan->approved_at)->addMonths($loan->tenure + 1);
                    $dates = monthsBtwDates($start_date, $end, false);
                    if($j==0){
                        error_log($start_date->format('Y-m-d H:i:s'));
                        error_log($end_date->format('Y-m-d H:i:s'));
                        error_log(json_encode($dates));
                    }

                    $pay = PaymentDate::where('loan_id', $loan->id)->get();
                    for ($i = 0; $i < count($dates); $i++) {
                        $p = PaymentDate::find($pay[$i]->id);
                        $p->start_date_approved = $start_date->format('Y-m-d H:i:s');
                        $p->end_date_approved = $end_date->format('Y-m-d H:i:s');
                        $p->payment_date_approved = $dates[$i];
                        $p->save();
                    }
                }
                $j++;
            }

            DB::commit();

        } catch (\Exception $e){
            DB::rollBack();
            Log::error($e->getMessage());
        }
    }
}
