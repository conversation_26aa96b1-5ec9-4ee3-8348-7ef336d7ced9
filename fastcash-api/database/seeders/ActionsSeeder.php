<?php

namespace Database\Seeders;

use App\Models\Action;
use Illuminate\Database\Seeder;

class ActionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
       $actions = [
           ['name' => 'loan_request'],
           ['name' => 'loan_verified'],
           ['name' => 'loan_approved'],
           ['name' => 'loan_paid'],
           ['name' => 'deposit'],
           ['name' => 'admin_user_create'],
           ['name' => 'user_create'],
           ['name' => 'loan_deleted'],
           ['name' => 'loan_liquidated'],
           ['name' => 'schedules'],
           ['name' => 'new_ticket'],
           ['name' => 'reply_ticket'],
           ['name' => 'close_ticket'],
           ['name' => 'admin_user_restore'],
           ['name' => 'admin_user_delete'],
           ['name' => 'admin_user_update'],
           ['name' => 'lender_create'],
           ['name' => 'lender_update'],
           ['name' => 'lender_disable'],
           ['name' => 'lender_enable'],
           ['name' => 'approve_deposit'],
           ['name' => 'approve_withdraw'],
           ['name' => 'decline_deposit'],
           ['name' => 'decline_withdraw'],
           ['name' => 'loan_liquidate_approved'],
           ['name' => 'change_lender'],
           ['name' => 'enable_user'],
           ['name' => 'disable_user'],
           ['name' => 'liquidate_declined'],
           ['name' => 'loan_transferred_remita'],
           ['name' => 'remita_loan_repayment'],
           ['name' => 'loan_repayment'],
           ['name' => 'repayment_approved'],
           ['name' => 'transaction_approved'],
           ['name' => 'repayment_declined'],
           ['name' => 'transaction_deleted'],
           ['name' => 'enable_multi_funding'],
           ['name' => 'cancel_multi_funding'],
           ['name' => 'fund_loan'],
           ['name' => 'update_fund_loan'],
           ['name' => 'remove_fund'],
           ['name' => 'admin_customer_update'],
           ['name' => 'schedule_repayment'],
           ['name' => 'new_merchant'],
           ['name' => 'withdraw_request'],
           ['name' => 'withdraw_approve'],
           ['name' => 'withdraw_decline'],
           ['name' => 'withdrawal_transaction'],
           ['name' => 'snooze_user'],
           ['name' => 'un_snooze_user'],
           ['name' => 'new_bank_account'],
           ['name' => 'loan_disbursed'],
           ['name' => 'user_updated'],
           ['name' => 'loan_request_ussd'],
           ['name' => 'edit_loan_request'],
           ['name' => 'transfer_loan'],
           ['name' => 'sell_loans'],
           ['name' => 'buy_loans'],
           ['name' => 'repay_loan'],
           ['name' => 'repay_deduction'],
           ['name' => 'wallet_repayment'],
           ['name' => 'credit_wallet'],
           ['name' => 'start_loan_liquidated'],
           ['name' => 'repay_deduction_transaction'],
           ['name' => 'account_check'],
           ['name' => 'loan_cancel_request'],
           ['name' => 'loan_cancel_approved'],
           ['name' => 'loan_cancel_declined'],
           ['name' => 'loan_referral_commission'],
       ];

        foreach ($actions as $action){
            $check = Action::where('name', $action['name'])->first();
            if($check == null){
                $a = new Action();
                $a->name = $action['name'];
                $a->save();

                error_log('new: '.$a->name);
            }
        }
    }
}
