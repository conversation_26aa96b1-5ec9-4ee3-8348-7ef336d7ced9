/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import Modal from 'react-modal';
import { connect } from 'react-redux';
import Pagination from 'antd/lib/pagination';
import numeral from 'numeral';
import moment from 'moment';
import { bindActionCreators } from 'redux';
import startCase from 'lodash.startcase';
import qs from 'querystring';

import BreadCrumbs from '../components/BreadCrumbs';
import { toggleMenu, setUnliquidatedLoans } from '../actions/user';
import { httpRequest, baseURL, loanAPI, currency } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import { doNotify } from '../actions/general';
import { SHOW_PROFILE } from '../actions/types';
import { setLoanAnalytics } from '../actions/loan';
import ApproveLiqLoan from '../modals/ApproveLiqLoan';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 10;

class ApproveLiquidatedLoans extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			transactions: [],
			currentPage: 1,
			totalPage: 0,
			location: '',
			modalIsOpen: false,
			transaction: null,
			showBypass: false,
			amount: 0,
		};
	}

	componentWillMount() {
		Modal.setAppElement('body');
	}

	componentDidMount() {
		this.props.toggleMenu(false);
		const { location } = this.props;
		const query = qs.parse(location.search.replace('?', ''));
		
		const page = query && query.p ? parseInt(query.p, 10) : 1;

		this.setState({ currentPage: page, location: location.pathname });

		this.fetch(page);
	}

	fetch = async pageNumber => {
		try {
			this.props.startBlock();
			const url = `${baseURL}${loanAPI}/liquidated/pending?page=${pageNumber}&pagesize=${pageSize}`;
			const rs = await httpRequest(url, 'GET', true);
			const result = rs.result;
			this.setState({ currentPage: pageNumber, totalPage: result.total, transactions: result.data });
			this.props.stopBlock();
			window.scrollTo(0, 0);
		} catch (e) {
			this.props.stopBlock();
			this.notify('', 'could not load transactions!', 'error');
		}
	};

	componentWillUnmount() {
		this.props.toggleMenu(true);
	}
		
	showUserProfile = id => this.props.showProfile({ show_profile: true, user_id: id });

	doApprove = async item => {
		try {
			this.props.startBlock();
			const url = `${baseURL}${loanAPI}/balance/${item.loan_id}`;
			const rs = await httpRequest(url, 'GET', true);
			this.props.stopBlock();
			if (rs && rs.result) {
				const { showBypass } = this.state;
				if(showBypass){
					document.body.className="pages modal-open";
					this.setState({ modalIsOpen: true, transaction: item, amount: rs.result.amount });
				} else {
					let action;
					if(parseFloat(item.amount) > parseFloat(rs.result.amount)){
						// tell him the deduction amount has changed
						const amount = currency(rs.result.amount)
						action = window.confirm(`The new liquidation amount is ${amount}. Will you like to approve this liquidation?`) ? true : false;
					} else {
						action = window.confirm("Will you like to approve this liquidation?") ? true : false;
					}
	
					if(action){
						await this.doLiquidateNow(item, rs.result.amount);
					}	
				}
			}
		} catch (error) {
			this.props.stopBlock();
			const message = error.message || 'could not get balance';
			this.notify('', message, 'error');
		}
	};

	doLiquidateNow = async (item, amount) => {
		try {
			this.props.startBlock();
			const { currentPage } = this.state;
			const { user } = this.props;
			const excess_deduction = parseFloat(item.amount) - parseFloat(amount);
			const data = { user_id: user.id, bypass_remita: 0, amount, excess_deduction };
			const url = `${baseURL}${loanAPI}/approve-liquidated/${item.id}`;
			const response = await httpRequest(url, 'POST', true, data);
			this.props.setLoanAnalytics(response.loan_details);
			this.props.setUnliquidatedLoans(response.unliquidated_loans);
			this.notify('', 'Loan liquidated!', 'success');
			this.props.stopBlock();
			this.fetch(currentPage);
		} catch (e) {
			this.props.stopBlock();
			const message = e.message || 'could not approve liquidation';
			this.notify('', message, 'error');
			this.setState({ showBypass: true });
		}
	}

	cancelApprove = () => {
		this.setState({ modalIsOpen: false, id: null });
		document.body.className="pages";
	};

	doDecline = async id => {
		const action = window.confirm("Do you want to decline this liquidation?") ? true : false;
		if(action){
			try {
				const { user } = this.props;
				this.props.startBlock();
				const response = await httpRequest(`${baseURL}${loanAPI}/decline-liquidated/${id}`, 'POST', true, { user_id: user.id });
				this.props.setLoanAnalytics(response.loan_details);
				this.props.setUnliquidatedLoans(response.unliquidated_loans);
				this.notify('', 'Loan liquidation declined!', 'success');
				this.props.stopBlock();
				this.fetch(this.state.currentPage);
			} catch (e) {
				console.log(e);
				this.props.stopBlock();
				const message = e.message || 'could not decline liquidation';
				this.notify('', message, 'error');
			}
		}
	};

	onChange = pageNumber => {
		this.props.history.push(`/approve-liquidated-loans?p=${pageNumber}`);
	};

	componentWillUpdate(nextProps, nextState) {
		const query = qs.parse(nextProps.location.search.replace('?', ''));
		const pageNumber = query && query.p ? parseInt(query.p, 10) : 1;
		if(nextState.currentPage !== pageNumber || nextProps.location.pathname !== nextState.location){
			this.setState({
				location: nextProps.location.pathname,
				currentPage: pageNumber,
			});

			this.fetch(pageNumber);
		} else if(nextProps.notifdone) {
			this.fetch(pageNumber);
		}
	}
	
	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	render() {
		const { transactions, currentPage, totalPage, modalIsOpen, transaction, amount } = this.state;
		return (
			<div>
				<BreadCrumbs url="/dashboard" />
				<div className="content-i">
					<div className="content-box">
						<div className="element-wrapper">
							<h6 className="element-header">Approve Liquidated Loans</h6>
							<div className="element-box">
								<div className="table-responsive" style={{ overflowX: 'scroll' }}>
									<table className="table table-striped table-lightfont">
										<thead>
											<tr>
												<th>Name of Employee</th>
												<th>Lender</th>
												<th>IPPIS/Customer ID</th>
												<th>Monthly Principal</th>
												<th>Monthly Interest</th>
												<th>Tenure</th>
												<th>Liquidated Amount</th>
												<th>Payment Type</th>
												<th>Payment Status</th>
												<th>Date</th>
												<th>&nbsp;</th>
											</tr>
										</thead>
										<tbody>
											{transactions.map((item, i) => {
												return (
													<tr key={i}>
														<td>{item.user.name}</td>
														<td>{item.lender.name}</td>
														<td>
															<a onClick={() => this.showUserProfile(item.user_id)} className="cursor link">{item.user.ippis || item.user.customer_id || item.user.phone}</a>
														</td>
														<td>{item.loan ? `₦${numeral(item.loan.monthly_principal).format('0,0.00')}` : '₦0.00'}</td>
														<td>{item.loan ? `₦${numeral(item.loan.monthly_interest).format('0,0.00')}` : '₦0.00'}</td>
														<td>{item.loan ? item.loan.tenure : '-'}</td>
														<td>{item.amount ? `₦${numeral(item.amount).format('0,0.00')}` : '₦0.00'}</td>
														<td>{item.channel !== 'loan-liquidated-topup' ? startCase(item.payment_type || '-') : 'Topup Liquidation'}</td>
														<td>{item.status}</td>
														<td>{moment(item.created_at).format('D.MMM.YYYY')}</td>
														<td className="flex">
															{item.channel !== 'loan-liquidated-topup' && item.loan && (
																<button className="btn btn-sm btn-primary cursor" onClick={() => this.doApprove(item)}>Approve</button>
															)}
															{item.channel !== 'loan-liquidated-topup' && (
																<button className="btn btn-sm btn-outline-danger cursor ml-1" onClick={() => this.doDecline(item.id)}><i className="os-icon os-icon-trash-2 mr-0"/></button>
															)}
														</td>
													</tr>
												)
											})}
										</tbody>
									</table>
								</div>
								<div className="pagination pagination-center mt-4">
									<Pagination
										current={currentPage}
										pageSize={pageSize}
										total={totalPage}
										showTotal={total => `Total ${total} transactions`}
										itemRender={itemRender}
										onChange={this.onChange}
									/>
								</div>
							</div>
						</div>
					</div>
					<Modal
                        isOpen={modalIsOpen}
                        onRequestClose={this.cancelApprove}
                        contentLabel="Approve Liquidated Loan"
                        shouldCloseOnOverlayClick={false}
                        overlayClassName="modal-dialog modal-sm"
                        className="modal-content"
                    >
                        <ApproveLiqLoan
                            closeModal={() => this.cancelApprove()}
                            item={transaction}
                            amount={amount}
							refetch={() => this.fetch(currentPage)}
                        />
                    </Modal>
					{modalIsOpen? (<div className="modal-backdrop fade show"/>) : ''}
				</div>
			</div>
		);
	}
}

const showProfile = (data) => {
	return { type: SHOW_PROFILE, data };
};

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
		lender: state.lender.profile,
	};
};

const mapDispatchToProps = dispatch => {
	return bindActionCreators({ toggleMenu, startBlock, stopBlock, doNotify, showProfile, setLoanAnalytics, setUnliquidatedLoans }, dispatch);
};

export default connect(mapStateToProps, mapDispatchToProps)(ApproveLiquidatedLoans);
