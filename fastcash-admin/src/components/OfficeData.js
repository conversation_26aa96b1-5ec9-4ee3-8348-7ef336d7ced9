/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Fragment } from 'react';
import { Link, withRouter } from 'react-router-dom';
import moment from 'moment';
import Tooltip from 'antd/lib/tooltip';
import Popover from 'antd/lib/popover';

import { rootURL, currency } from '../config/constants';
import loading from '../assets/img/loading.gif';
import EditLoanInsurance from './EditLoanInsurance';

const DeclineMessage = ({ doHide, doDeclineLoan, loan, reason, onChangeReason, declining }) => {
	return (
		<div className="element-box p-3 m-0">
			<form onSubmit={(e) => doDeclineLoan(e, loan)}>
				<div className="row">
					<div className="col-sm-12">
					editVisible		<div className="form-group">
							<label htmlFor="amount">Reason</label>
							<textarea name="reason" id="reason" placeholder="Reason for decline" type="text" className="form-control" onChange={(e) => onChangeReason(e)} rows="3" value={reason}></textarea>
						</div>
					</div>
				</div>
				<div className="text-center">
					<button className="mr-2 btn btn-primary" disabled={declining} type="submit">
						{declining ? <img src={loading} alt=""/> : 'Decline Loan'}
					</button>
					<button className="btn btn-default" type="button" onClick={doHide}>Cancel</button>
				</div>
			</form>
		</div>
	);
};

const OfficeData = ({ location, customer, loan, user, showProfile, doVerifyLoan, approveLoan, doDeclineLoan, verifyPayment, disburse, bypass, openModal, doSendMail, openRepaymentModal, switchLender, openCoreBankModal, transfer, enableUser, disableUser, doMultiFund, category, reason, onChangeReason, declining, visible, editVisible, loanID, showDeclineReason, showEditLoan, doHide, doSnoozeUser, removeSnooze, liquidateLoan, sendConsentSMS, onSaveLoanInsurance }) => {
	const disbursed = loan && loan.disbursement ? JSON.parse(loan.disbursement) : null;
	const paymentStatus = loan && loan.payment_status ? JSON.parse(loan.payment_status) : null;
	const cid = customer.platform === 'sme' ? `PHONE: ${customer.phone}` : (customer.ippis ? `IPPIS: ${customer.ippis}` : `CID: ${customer.customer_id}`);
	const remita = loan && loan.approved_remita ? JSON.parse(loan.approved_remita) : null;
	const mandateRef = remita ? remita.data.mandateReference : null;
	const _search = location.search.replace('?', '');
	const url = encodeURIComponent(`${location.pathname}${_search !== '' ? `?${_search}` : ''}`);

	return (
		<Fragment>
			<tr>
				<td>
					{loan && loan.children && loan.children.length > 0 && (
						<i className="os-icon os-icon-plus-square"/>
					)}
				</td>
				<td>
					<span>{customer.name}</span><br/>
					<small>{`Balance: ${currency(customer.wallet || 0)}`}</small><br/>
					{category !== '' && (
						<Fragment>
							<small><strong>{customer.lender.name}</strong></small><br/>
						</Fragment>
					)}
					{user.lender_id === customer.lender_id && (user.role_name === 'super' || user.role_name === 'super-l') && (
						<Tooltip title="Edit Customer">
							<Link className="text-primary" to={`/edit-customer/${customer.id}?_q=${location.pathname}${location.search}`}>
								<i className="os-icon os-icon-edit-32" /> edit
							</Link>
						</Tooltip>
					)}
				</td>
				<td>{customer.core_bank_id || '-'}</td>
				<td>
					{loan ? (
						loan.merchant_code ? (
							<Link to={`/merchants/${loan.merchant_code}`}>Merchant</Link>
						) : (
							'Self'
						)
					) : (
						'-'
					)}
				</td>
				<td style={{width:'152px'}}>
					<a onClick={showProfile(customer.id)} className="cursor link">{cid}</a><br/>
					{customer.enabled === 1 && (
						<small>{`SOURCE: ${loan && loan.platform !== 'sme' ? loan.platform.toUpperCase() : '-'}`}{loan && loan.admin_request === 1 && <Tooltip title="Loan Request by Admin"><span className="text-primary ml-1" style={{fontSize: '17px'}}><i className="fa fa-info-circle"/></span></Tooltip>}</small>
					)}
					{mandateRef && <br/>}
					{mandateRef && (
						<small><strong>{`MR: ${mandateRef}`}</strong></small>
					)}
				</td>
				<td nowrap="nowrap">
					{loan && customer.enabled === 1 ? (
						(loan.is_multifund === 0 || (loan.is_multifund === 1 && loan.cancel_multifund === 1)) ? (
							loan.approved === 1 ? (
								<span>
									<small>{moment(loan.approved_at).format('D.MMM.YYYY')}</small>
									<br />
									<span className="elabel elabel-success">Approved</span>
								</span>
							) : (
								loan.verified === 0 ? (
									user.role_category === 'Verifier' ||
									user.role_category === 'Admin' ||
									user.role_category === 'Super Admin' ? (
										user.lender_id === customer.lender_id ? (
											<button className="btn btn-sm btn-outline-danger cursor" onClick={doVerifyLoan(loan.id, user.id)}>Verify</button>
										) : <span className="elabel elabel-danger">awaiting verification</span>
									) : (
										'-'
									)
								) : (
									user.role_category === 'Approver' ||
									user.role_category === 'Admin' ||
									user.role_category === 'Super Admin' ? (
										user.lender_id === customer.lender_id ? (
											!loan.has_approved && <button className="btn btn-sm btn-outline-primary cursor" onClick={approveLoan(loan, user.id)}>Approve</button>
										) : <span className="elabel elabel-danger">awaiting approval</span>
									) : (
										'-'
									)
								)
							)
						) : (
							loan.approved === 1 && (
								<span>
									<small>{moment(loan.approved_at).format('D.MMM.YYYY')}</small>
									<br />
									<span className="elabel elabel-success">Approved</span>
								</span>
							)
						)
					) : (
						<span className="elabel elabel-info">No Loan</span>
					)}
					{loan && loan.approved === 0 && !loan.deleted_by && customer.enabled === 1 && (loan.is_multifund === 0 || (loan.is_multifund === 1 && loan.cancel_multifund === 1)) && (
						user.lender_id === customer.lender_id && (
							<Tooltip title="Decline Loan">
								<Popover
									title=""
									content={
										<DeclineMessage
											loan={loan}
											doHide={doHide}
											declining={declining}
											onChangeReason={onChangeReason}
											doDeclineLoan={doDeclineLoan}
											reason={reason}
										/>
									}
									trigger="click"
									visible={visible && loanID === loan.id}
									onVisibleChange={(status) => showDeclineReason(status, loan)}
								>
									<span className="elabel elabel-danger cursor" style={{ marginLeft: '8px' }}><i className="fa fa-trash" /></span>
								</Popover>
							</Tooltip>
						)
					)}
				</td>
				{loan && customer.enabled === 1 ? loan.lender_id === 1 ? (
					<td nowrap="nowrap">
						<div>
							{loan.approved === 1 ? (
								disbursed ? (
									disbursed.status === 'success' ? (
										paymentStatus ? (
											paymentStatus.status === 'success' ? (
												<span>
													<small>
														{loan && loan.disbursed_at
															? moment(loan.disbursed_at).format('D.MMM.YYYY')
															: ''}
													</small>
													<br />
													<span className="elabel elabel-success">Disbursed</span>
												</span>
											) : (
												<span>
													{loan && (
														parseFloat(loan.disburse_amount) > 0 ? (
															<small>{currency(loan.disburse_amount)}</small>
														) : (
															<small>{currency(loan.amount)}</small>
														)
													)}
													<br />
													<span className="elabel elabel-danger cursor" onClick={verifyPayment(loan)}>
														Failed? Retry
													</span>
												</span>
											)
										) : (
											<span>
												{loan && (
													parseFloat(loan.disburse_amount) > 0 ? (
														<small>{currency(loan.disburse_amount)}</small>
													) : (
														<small>{currency(loan.amount)}</small>
													)
												)}
												<br />
												<span className="elabel elabel-warning cursor" onClick={verifyPayment(loan)}>
													Pending
												</span>
											</span>
										)
									) : (
										<span>
											{loan && (
												parseFloat(loan.disburse_amount) > 0 ? (
													<small>{currency(loan.disburse_amount)}</small>
												) : (
													<small>{currency(loan.amount)}</small>
												)
											)}
											<br />
											<span onClick={disburse(loan)} className="elabel elabel-danger cursor">
												Failed? Retry
											</span>
										</span>
									)
								) : (
									<span>
										{loan && (
											parseFloat(loan.disburse_amount) > 0 ? (
												<small>{currency(loan.disburse_amount)}</small>
											) : (
												<small>{currency(loan.amount)}</small>
											)
										)}
										<br />
										<span onClick={disburse(loan)} className="elabel elabel-info cursor">Disburse</span>
									</span>
								)
							) : (
								loan.verified === 1 && (
									(loan.is_multifund === 0 || (loan.is_multifund === 1 && loan.cancel_multifund === 1)) ? (
										user.role_name === 'super' && <span onClick={doMultiFund(loan, 1)} className="elabel elabel-info cursor">Allow Multi Fund</span>
									) : (
										user.role_name === 'super' && <span onClick={doMultiFund(loan, 0)} className="elabel elabel-danger cursor">Cancel Multi-Fund</span>
									)
								)
							)}{' '}
							{loan && loan.approved === 1 && (
								<input
									type="checkbox"
									onChange={bypass(loan.id)}
									disabled={loan.disbursed === 1}
									checked={loan.disbursed === 1}
								/>
							)}
						</div>
					</td>
				) : (
					<td nowrap="nowrap">
						{loan.approved === 0 ? (
							'-'
						) : (
							loan.disbursed === 1 ? (
								<span>
									<small>{moment(loan.approved_at).format('D.MMM.YYYY')}</small>
									<br />
									<span className="elabel elabel-success">Loan Disbursed</span>
								</span>
							): (
								<span>
									{loan && (
										parseFloat(loan.disburse_amount) > 0 ? (
											<small>{currency(loan.disburse_amount)}</small>
										) : (
											<small>{currency(loan.amount)}</small>
										)
									)}
									<br />
									<span className="elabel elabel-danger">Awaiting Disbursement</span>
								</span>
							)
						)}
					</td>
				) : <td>-</td>}
				{loan && customer.enabled === 1 && loan.start_date && loan.end_date ? (
					<td nowrap="nowrap">
						<small>{`START: ${moment(loan.start_date).format('D.MMM.YYYY')}`}</small><br/>
						<small>{`END: ${moment(loan.end_date).format('D.MMM.YYYY')}`}</small>
					</td>
				) : (
					<td>-</td>
				)}
				<td nowrap="nowrap">{loan && loan.tenure && customer.enabled === 1 ? `${loan.tenure}month${loan.tenure > 1 ? 's' : ''}` : '-'}</td>
				<td nowrap="nowrap">
					{loan && !(!loan.consent_otp && !loan.has_consented) && <>
						{loan.has_consented === 1 ? <>
							<small>{moment(loan.consented_at).format('D.MMM.YYYY h:mma')}</small><br/>
							<span className="elabel elabel-success">Loan Consented</span>
						</>: <>
							<span className="elabel elabel-danger">{moment(loan.consent_otp_expire_at).diff(moment(), 'seconds') < 0 ? 'OTP Expired' : 'Awaiting Consent'}</span>
						</>}
					</>}
				</td>
				<td nowrap="nowrap">{loan && customer.enabled === 1 ? moment(loan.created_at).format('D-MMM-YYYY') : '--'}</td>
				{customer.enabled === 1 && customer.is_snoozed === 0 ? (
					<td className="row-actions">
						{loan && (
							<Tooltip title="View Loan Profile">
								<a className="cursor" onClick={openModal(customer, loan)}>
									<i className="os-icon os-icon-arrow-2-up" />
								</a>
							</Tooltip>
						)}
						{loan && loan.verified === 0 && loan.is_topup === 0 && (
							<Tooltip title="Edit Loan">
								<Link className="cursor" to={`/edit-loan/${loan.id}?_url=${url}`}>
									<i className="os-icon os-icon-edit" />
								</Link>
							</Tooltip>
						)}
						{loan && (
							<Popover
								title=""
								content={
									<EditLoanInsurance
										loan={loan}
										doHide={doHide}
										saveLoanInsurance={onSaveLoanInsurance}
									/>
								}
								trigger="click"
								visible={editVisible && loanID === loan.id}
								onVisibleChange={(status) => showEditLoan(status, loan)}
							>
								<Tooltip title="edit insurance">
									<a className="text-primary cursor">
										<i className="os-icon os-icon-text-input" />
									</a>
								</Tooltip>
							</Popover>
						)}
						{loan && loan.approved === 1 && loan.disbursed === 1 && user.lender_id === customer.lender_id && (
							<Tooltip title="Loan Repayment">
								<a className="cursor" onClick={openRepaymentModal(customer, loan)}>
									<i className="os-icon os-icon-wallet-loaded" />
								</a>
							</Tooltip>
						)}
						{loan && (loan.oletter_updated || loan.oletter) && loan.verified === 1 && (
							<Tooltip title="Download Offer Letter">
								<a target="_blank" href={`${rootURL}/oletter/${(loan.oletter_updated || loan.oletter)}`} rel="noopener noreferrer">
									<i className="icon-paper-clip" />
								</a>
							</Tooltip>
						)}
						{(!loan || (loan && loan.approved  === 0 && loan.is_topup === 0)) && user && user.role === 'super' && user.lender_id === customer.lender_id && (
							<Tooltip title="Change Lender">
								<a className="cursor" onClick={switchLender(customer)}>
									<i className="os-icon os-icon-mail-19" />
								</a>
							</Tooltip>
						)}
						{((customer.requires_core_bank_id && !customer.core_bank_id) || (customer.core_bank_id && user && user.role === 'super')) && user.lender_id === customer.lender_id && (
							<Tooltip title="Core Banking ID">
								<a className="cursor" onClick={openCoreBankModal(customer)}>
									<i className="os-icon os-icon-info" />
								</a>
							</Tooltip>
						)}
						{loan && loan.platform === 'ippis' && loan.approved === 1 && user && user.role === 'super' && user.lender_id === customer.lender_id && (
							<Tooltip title="Transfer to Remita">
								<a className="cursor" onClick={transfer(loan)}>
									<i className="os-icon os-icon-ui-92" />
								</a>
							</Tooltip>
						)}
						{loan && loan.verified === 1 && user.lender_id === customer.lender_id && loan.disbursed === 0 && (
							<Tooltip title="Generate Offer Letter">
								<a className="cursor text-success" onClick={doSendMail(loan.id)}>
									<i className="os-icon os-icon-settings" />
								</a>
							</Tooltip>
						)}
						{loan && loan.has_consented === 0 && loan.approved === 1 && loan.disbursed === 0 && (
							<Tooltip title="Send Consent SMS">
								<a className="cursor text-gray-dark" onClick={sendConsentSMS(loan)}>
									<i className="os-icon os-icon-send" />
								</a>
							</Tooltip>
						)}
						{user.lender_id === customer.lender_id && !loan && (
							<Tooltip title="Ban Customer">
								<a className="cursor text-danger" onClick={disableUser(customer.id)}>
									<i className="os-icon os-icon-trash-2" />
								</a>
							</Tooltip>
						)}
						{user.lender_id === customer.lender_id && !loan && customer.is_snoozed === 0 && (
							<Tooltip title="Snooze Customer">
								<a className="cursor text-danger" onClick={doSnoozeUser(customer.id)}>
									<i className="os-icon os-icon-clock" />
								</a>
							</Tooltip>
						)}
						{loan && loan.disbursed === 1 && user.role === 'super' && (
							<Tooltip title="Liquidate Loan">
								<a className="cursor" onClick={liquidateLoan(loan)}>
									<i className="os-icon os-icon-cancel-square" />
								</a>
							</Tooltip>
						)}
					</td>
				) : (
					<td nowrap="nowrap" className="row-actions">
						{user.lender_id === customer.lender_id && customer.enabled === 0 && (
							<Tooltip title="Enable Customer">
								<a className="cursor text-success" onClick={enableUser(customer.id)}>
									<i className="os-icon os-icon-user-check" />
								</a>
							</Tooltip>
						)}
						{user.lender_id === customer.lender_id && customer.is_snoozed === 1 && (
							<Tooltip title="Remove Snooze">
								<a className="cursor text-success" onClick={removeSnooze(customer.id)}>
									<i className="os-icon os-icon-user-check" />
								</a>
							</Tooltip>
						)}
					</td>
				)}
			</tr>
			{loan && loan.children && loan.children.length > 0 && (
				<tr>
					<td colSpan="">
						<table>
							extra loans
						</table>
					</td>
				</tr>
			)}
		</Fragment>
	);
};

export default withRouter(OfficeData);
