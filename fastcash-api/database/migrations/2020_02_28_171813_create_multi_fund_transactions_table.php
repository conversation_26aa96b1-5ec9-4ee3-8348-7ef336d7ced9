<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateMultiFundTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('multi_fund_transactions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->unsigned();
            $table->bigInteger('loan_id')->unsigned();
            $table->bigInteger('lender_id')->unsigned();
            $table->string('source')->nullable();
            $table->decimal('amount', 20, 2)->nullable();
            $table->decimal('principal', 20, 2)->nullable();
            $table->decimal('interest', 20, 2)->nullable();
            $table->decimal('outst_amount', 20, 2)->nullable();
            $table->string('status')->nullable();
            $table->string('payment_type')->nullable();
            $table->string('description')->nullable();
            $table->string('reference')->nullable();
            $table->tinyInteger('approved')->default(1);
            $table->dateTime('approved_at')->nullable();
            $table->bigInteger('approved_by')->nullable()->unsigned();
            $table->string('channel')->nullable();
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('loan_id')->references('id')->on('loans')->onDelete('cascade');
            $table->foreign('lender_id')->references('id')->on('lenders')->onDelete('cascade');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('multi_fund_transactions');
    }
}
