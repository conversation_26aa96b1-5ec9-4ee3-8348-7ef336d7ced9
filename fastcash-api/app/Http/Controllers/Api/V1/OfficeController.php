<?php

namespace App\Http\Controllers\Api\V1;

use App\Services\MailHandler;
use App\Models\Payslip;
use App\Models\PayslipUpload;
use App\Models\Schedule;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Loan;
use App\Models\Office;
use Carbon\Carbon;
use Excel;
use DB;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use PHPExcel_Style_Alignment;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Log;

class OfficeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $lender = $request->has('lender') ? $request->input('lender') : '';

        if ($lender != '') {
            $q = $request->has('q') && $request->input('q') == '1';
            if ($q) {
                $offices = Office::where('lender_id', $lender)->get(['id', 'name']);
                return response()->json(compact('offices'), 200);
            }

            $limit = $request->has('pagesize') && $request->input('pagesize') != '' ? $request->input('pagesize') : 10;
            $page = $request->has('page') && $request->input('page') != '' ? $request->input('page') : 1;
            $skip = ($page - 1) * $limit;

            $offices = Office::where('lender_id', $lender)->get();
            $offices = $offices->map(function ($item, $key) use ($lender) {
                $rs = getOfficeFinance($item, $lender);

                $item->amount_disbursed = $rs->amount_disbursed;
                $item->principal_paid = array_sum($rs->principal_paid);
                $item->interest_paid = array_sum($rs->interest_paid);
                $item->principal_balance = $rs->amount_disbursed - array_sum($rs->principal_paid);

                return $item;
            });

            $data = collect($offices)->sortByDesc('amount_disbursed')->values();
            $results = array_slice($data->toArray(), $skip, $limit);

            $result = (object)null;
            $result->total = $data->count();
            $result->size = $limit;
            $result->page = $page;
            $result->data = $results;

            return response()->json(compact('result'), 200);
        }

        return response()->json(['error' => 'could not fetch offices'], 500);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'address' => 'required',
            'state' => 'required',
            'city' => 'required',
        ]);
        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }
        try {
            $data = $request->all();
            $office = Office::create([
                'name' => $data['name'],
                'address' => $data['address'],
                'state' => $data['state'],
                'city' => $data['city'],
                'lender_id' => $data['lender_id'],
            ]);

            $office->amount_disbursed = 0;
            $office->principal_paid = 0;
            $office->interest_paid = 0;
            $office->principal_balance = 0;

            return response()->json(compact('office'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'could not create office, try again later'], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $office = Office::find($id);

        $rs = getOfficeFinance($office, $office->lender_id);

        $office->amount_disbursed = $rs->amount_disbursed;
        $office->principal_paid = array_sum($rs->principal_paid);
        $office->interest_paid = array_sum($rs->interest_paid);
        $office->principal_balance = $rs->amount_disbursed - array_sum($rs->principal_paid);

        return response()->json(compact('office'), 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            return response()->json($id, 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Office could not be deleted'], 500);
        }
    }

    public function uploadPayslip(Request $request)
    {
        try {
            $path = $request->file('file')->store('payslip');
            if ($path) {
                $g_path = storage_path();
                $file = $g_path . '/app/' . $path;
                //chmod($file, 0644);

                $lender_id = $request->get('lender_id');

                $payslips = parsePayslip($file);
                $ids = parseData($payslips, $lender_id, $request->get('month'), $request->get('year'));

                if (count($ids) > 0) {
                    $offices = [];
                    foreach ($ids[1] as $name) {
                        $office = Office::where('name', $name)->where('lender_id', $lender_id)->first();
                        if (!$office) {
                            $office = Office::create([
                                'name' => $name,
                                'lender_id' => $lender_id,
                            ]);

                            $offices[] = $office;
                        }
                    }

                    $slips = PayslipUpload::where('lender_id', $lender_id)->orderBy('year', 'desc')->orderBy('month', 'desc')->orderBy('created_at', 'desc')->get();

                    return response()->json(['slips' => $slips, 'numberOfUsers' => count($ids[0]), 'numberOfUpdates' => count($ids[2])], 200);
                }

                return response()->json(['error' => 'File could not be uploaded'], 500);
            }

            return response()->json(['error' => 'File could not be uploaded'], 500);
        } catch (FileException $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'File could not be uploaded'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'File could not be uploaded'], 500);
        }
    }

    public function uploadUsers(Request $request)
    {
        DB::beginTransaction();
        try {
            $path = $request->file('file')->store('payslip');
            if ($path) {
                $g_path = storage_path();
                $file = $g_path . '/app/' . $path;
                //chmod($file, 0644);

                $lender_id = $request->get('lender_id');

                // parse file
                $new_users = Excel::load($file, function ($reader) {
                    $reader->formatDates(true, 'Y-m-d');
                })->toArray();

                $payslips = $ids = [];
                foreach ($new_users as $sheet) {
                    foreach ($sheet as $user) {
                        if (isset($user[1]) && !is_null($user[1]) && $user[1] != 'PHONE NUMBER') {
                            $payslip = Payslip::where('platform', 'sme')->where('phone', $user[1])->where('salary_account_number', $user[11])->first();

                            if(!$payslip){
                                $payslip = Payslip::create([
                                    'name' => $user[0],
                                    'phone' => $user[1],
                                    'biometric' => $user[2],
                                    'birth_date' => $user[3],
                                    'email' => $user[4],
                                    'address' => $user[5],
                                    'location' => $user[6],
                                    'nature_of_business' => $user[7],
                                    'employment_status' => $user[8],
                                    'employer' => $user[9],
                                    'salary_bank' => $user[10],
                                    'salary_account_number' => $user[11],
                                    'turnover' => $user[12],
                                    'eligible_amount' => $user[13],
                                    'lender_id' => $lender_id,
                                    'account_pin' => substr($user[11], -4),
                                    'platform' => 'sme',
                                ]);
                            } else {
                                $payslip->name = $user[0];
                                $payslip->phone = $user[1];
                                $payslip->biometric = $user[2];
                                $payslip->birth_date = $user[3];
                                $payslip->email = $user[4];
                                $payslip->address = $user[5];
                                $payslip->location = $user[6];
                                $payslip->nature_of_business = $user[7];
                                $payslip->employment_status = $user[8];
                                $payslip->employer = $user[9];
                                $payslip->salary_bank = $user[10];
                                $payslip->salary_account_number = $user[11];
                                $payslip->turnover = $user[12];
                                $payslip->eligible_amount = $user[13];
                                $payslip->lender_id = $lender_id;
                                $payslip->account_pin = substr($user[11], -4);
                                $payslip->save();
                            }

                            $payslips[] = $payslip;
                            $ids[] = $payslip->id;

                            $user = User::where('phone', $payslip->phone)->first();
                            if ($user) {
                                $user->payslip_id = $payslip->id;
                                $user->save();
                            }
                        }
                    }
                }

                $office = Office::where('name', 'SME')->where('lender_id', $lender_id)->first();
                if (!$office) {
                    $office = Office::create([
                        'name' => 'SME',
                        'lender_id' => $lender_id,
                    ]);

                    $offices[] = $office;
                }

                DB::commit();

                return response()->json(['numberOfUsers' => count($ids)], 200);
            }

            DB::rollBack();
            return response()->json(['error' => 'File could not be uploaded'], 500);
        } catch (FileException $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'File could not be uploaded'], 500);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'File could not be uploaded'], 500);
        }
    }

    public function generateSchedule(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $office = Office::find($id);

            $month = $request->has('month') ? $request->input('month') : date('n');
            $year = $request->has('year') ? $request->input('year') : date('Y');
            $_uid = $request->has('_uid') && $request->input('_uid') != '' ? $request->input('_uid') : 0;

            $staff = User::where('id', $_uid)->first();

            $startDate = Carbon::createFromDate($year, $month, null)->endOfMonth()->addDays(1)->startOfMonth()->format('Y-m-d H:i:s');
            Log::error('OfficeController.php Line 385 generate schedule --> ' . $startDate);

            $loans = Loan::with('user', 'accountno')->where('office_id', $id)->where('lender_id', $office->lender_id)->where('approved', 1)->where('disbursed', 1)->where('platform', 'ippis')->where('liquidated', 0)->where('start_date', $startDate)->get();

            if (count($loans) == 0) {
                DB::rollBack();
                return response()->json(['message' => 'no loans available to generate schedule', 'data' => ''], 200);
            }

            $schedules = [];
            foreach ($loans as $loan) {
                $disbursed_date = Carbon::parse($loan->disbursed_at)->format('d.m.Y');

                $schedule = Schedule::where('ippis_number', $loan->user->ippis)->where('type', 2)->where('lender_id', $loan->lender_id)->where('booking_date', $disbursed_date)->where('month', $month)->where('year', $year)->first();
                //Log::error(json_encode($schedule));

                if ($schedule == null) {
                    $schedule = new Schedule;
                    $schedule->loan_id = $loan->id;
                    $schedule->ippis_number = $loan->user->ippis;
                    $schedule->name = $loan->user->name;
                    $schedule->office_id = $loan->office_id;
                    $schedule->account_number = $loan->accountno->account_number;
                    $schedule->loan_amount = $loan->amount;
                    $schedule->loan_repayment_amount = $loan->total_deduction;
                    $schedule->outst_loan_amt = calculateOutstanding($loan->id, 0);
                    $schedule->interest = $loan->total_deduction - $loan->amount;
                    $schedule->monthly_dedux = $loan->monthly_deduction;
                    $schedule->tenure = $loan->tenure;
                    $schedule->month = $month;
                    $schedule->month_name = Carbon::createFromDate(null, $month, 1)->format('F');
                    $schedule->year = $year;
                    $schedule->booking_date = $disbursed_date;
                    $schedule->start_date = Carbon::parse($loan->start_date)->format('d.m.Y');
                    $schedule->end_date = Carbon::parse($loan->end_date)->format('d.m.Y');
                    $schedule->type = 2;
                    $schedule->lender_id = $loan->lender_id;
                    $schedule->save();
                }

                unset($schedule->loan_id);
                unset($schedule->office_id);
                unset($schedule->approved_at);
                unset($schedule->updated_at);
                unset($schedule->deleted_at);
                unset($schedule->month);
                unset($schedule->month_name);
                unset($schedule->year);
                unset($schedule->id);
                unset($schedule->type);
                unset($schedule->loan_repayment_amount);
                unset($schedule->lender_id);
                try {
                    unset($schedule->created_at);
                    unset($schedule->default_amount);
                } catch (\Exception $e) {
                }

                $schedules[] = $schedule;
            }

            if (count($schedules) == 0) {
                return response()->json(['message' => 'no schedules available', 'data' => ''], 200);
            }

            $allschedules = json_decode(json_encode($schedules), true);
            //json_encode(json_encode($allschedules));

            $new_schedules = [];
            foreach ($allschedules as $schedule) {
                $new_schedule = [];
                foreach ($schedule as $key => $value) {
                    $new_schedule[ucwords(str_replace("_", " ", $key))] = $value;
                    unset($new_schedule[$key]);
                }
                $new_schedules[] = $new_schedule;
            }

            $filename = time();
            $excel = Excel::create($filename, function ($excel) use ($new_schedules, $office, $month, $year, $staff) {
                $company = env('APP_NAME');
                $title = 'Schedule for ' . $office->name . ' [' . Carbon::createFromDate($year, $month, 1)->format('F') . ', ' . $year . ']';

                $excel->setTitle($title);
                $excel->setCreator(strtolower(ucwords($staff ? $staff->name : '')))->setCompany($company);
                $schedules = $new_schedules;

                $excel->sheet('Sheet1', function ($sheet) use ($schedules, $title) {
                    $sheet->setAutoSize(true);
                    $sheet->fromArray($schedules);
                    $sheet->prependRow(1, array(
                        $title
                    ));
                    $sheet->mergeCells('A1:L1');

                    $style = array(
                        'alignment' => array(
                            'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
                        )
                    );

                    $sheet->getStyle("A1:L1")->applyFromArray($style);
                });
            })->store('xls');

            $g_path = public_path();
            $file = $g_path . '/schedules/' . $filename . '.xls';

            try {
                //chmod($file, 0644);
            } catch (\Exception $e) {
                Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            }

            try {
                if ($staff) {
                    $usr = (object)null;
                    $usr->sender = env("APP_NAME");
                    $usr->to = $staff->name;
                    $usr->email = $staff->email;
                    $usr->mda = $office->name;

                    $mail = (new MailHandler())->sendScheduleMail($usr, $file);
                }
            } catch (\Exception $e) {
                Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            }

            DB::commit();
            return response()->json(['message' => '', 'data' => env('APP_URL') . '/schedules/' . $filename . '.xls'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'could not generate schedule'], 500);
        }
    }

    public function uploadSchedule(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'month' => 'required|numeric',
            'year' => 'required|numeric',
        ], [
            'month.required' => 'Select the schedule month',
            'year.required' => 'Select the schedule year',
            'month.numeric' => 'Select the schedule month',
            'year.numeric' => 'Select the schedule year',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }
        DB::beginTransaction();

        try {
            $path = $request->file('file')->store('schedule');
            if ($path) {
                $g_path = storage_path();
                $file = $g_path . '/app/' . $path;

                $new_schedules = Excel::load($file, function ($reader) {
                    $reader->ignoreEmpty();
                    $reader->formatDates(true, 'Y-m-d');
                })->toArray();
                $schedules = [];

                foreach ($new_schedules as $sheet) {
                    foreach ($sheet as $schedule) {
                        if (isset($schedule[1]) && !is_null($schedule[1])) {
                            try {
                                $start_date = Carbon::createFromFormat('Y-m-d', $schedule[7])->format('d.m.Y');
                                $end_date = Carbon::createFromFormat('Y-m-d', $schedule[8])->format('d.m.Y');
                            } catch (\Exception $e) {
                                $start_date = '';
                                $end_date = '';
                            }

                            $month = $request->get('month');
                            $year = $request->get('year');
                            $staff = $request->get('staff');

                            $sched = Schedule::where('ippis_number', $schedule[1])->where('type', 2)->where('start_date', $start_date)->where('end_date', $end_date)->first();

                            $check = Schedule::where('ippis_number', $schedule[1])->where('type', 1)->where('start_date', $start_date)->where('end_date', $end_date)->where('month', $month)->where('year', $year)->first();

                            if ($sched) {
                                if ($check == null) {
                                    $loan = Loan::with('user')->find($sched->loan_id);

                                    if ($loan) {
                                        $_schedule = new Schedule;
                                        $_schedule->loan_id = $loan->id;
                                        $_schedule->office_id = $loan->office_id;
                                        $_schedule->ippis_number = $schedule[1];
                                        $_schedule->name = $loan->user->name;
                                        $_schedule->account_number = $schedule[3];
                                        $_schedule->loan_amount = $schedule[4];
                                        $_schedule->outst_loan_amt = calculateOutstanding($loan->id, $schedule[5]);
                                        $_schedule->interest = $loan->total_deduction - $loan->amount;
                                        $_schedule->monthly_dedux = $schedule[5];
                                        $_schedule->default_amount = $loan->monthly_deduction > $schedule[5] && Carbon::parse($loan->end_date) >= Carbon::now() ? $loan->monthly_deduction - $schedule[5] : 0;
                                        $_schedule->tenure = $schedule[6];
                                        $_schedule->month = $month;
                                        $_schedule->month_name = Carbon::createFromDate(null, $month, 1)->format('F');
                                        $_schedule->year = $year;
                                        $_schedule->start_date = Carbon::parse($loan->start_date)->format('d.m.Y');
                                        $_schedule->end_date = Carbon::parse($loan->end_date)->format('d.m.Y');
                                        $_schedule->booking_date = $sched->booking_date;
                                        $_schedule->loan_repayment_amount = $sched->loan_repayment_amount;
                                        $_schedule->type = 1;
                                        $_schedule->lender_id = $loan->lender_id;
                                        $_schedule->save();

                                        $_schedule->office = Office::find($loan->office_id);

                                        $schedules[] = $_schedule;

                                        $transaction = Transaction::create([
                                            'user_id' => $loan->user_id,
                                            'loan_id' => $loan->id,
                                            'office_id' => $loan->office_id,
                                            'schedule_id' => $_schedule->id,
                                            'source' => strtoupper($loan->platform),
                                            'amount' => $_schedule->monthly_dedux,
                                            'status' => 'Pending',
                                            'lender_id' => $loan->lender_id,
                                            'approved' => 0,
                                            'channel' => 'schedule-repayment',
                                            'transaction_flag' => 'deduction',
                                            'repayment_source' => 'ippis',
                                            'repayment_date' => date('Y-m-d'),
                                            'uploaded_by' => $staff,
                                            'principal' => $_schedule->monthly_dedux - $loan->monthly_interest,
                                            'interest' => $loan->monthly_interest,
                                            'outst_amount' => calcOutstRemita($loan->id, $loan->total_deduction, 0)
                                        ]);

                                        $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'schedule_repayment', 'success', NULL, $loan->lender_id, $staff);
                                    } else {
                                        Log::error('OfficeController.php Line 483 no loan, ' . $sched->loan_id);
                                    }
                                } else {
                                    Log::error('OfficeController.php Line 487 already uploaded, ' . $check->id);
                                }
                            } else {
                                Log::error('OfficeController.php Line 491 no schedule, ' . $schedule[1]);
                            }
                        }
                    }
                }

                DB::commit();
                return response()->json(compact('schedules'), 200);
            }

            return response()->json(['error' => 'File could not be uploaded'], 500);
        } catch (FileException $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'File could not be uploaded'], 500);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'File could not be uploaded'], 500);
        }
    }
}
