import React from 'react';
import { connect } from 'react-redux';

import ManageSettings from '../components/ManageSettings';
import ManageLenderSettings from '../components/ManageLenderSettings';
import DepositFunds from '../components/DepositFunds';
import WithdrawFunds from '../components/WithdrawFunds';
import UssdSettings from '../components/UssdSettings';

const Settings = ({ user }) => {
	return (
		<div className="content-i">
			<div className="content-box">
				<div className="element-wrapper" style={{ paddingBottom: 0 }}>
					<h6 className="element-header">Settings</h6>
				</div>
				<div className="row">
					<div className="col-lg-7 col-xxl-6">
						<div className="element-wrapper">
							{user && user.lender_id === 1 && <ManageSettings />}
							<ManageLenderSettings />
						</div>
					</div>
					<div className="col-lg-5 col-xxl-6">
						<div className="element-wrapper">
							<DepositFunds />
						</div>
						<div className="element-wrapper">
							<WithdrawFunds />
						</div>
						<div className="element-wrapper">
							<UssdSettings />
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
	};
};

export default connect(mapStateToProps)(Settings);
