import React, { Component } from 'react';
import { connect } from 'react-redux';

import LoanData from '../components/LoanData';
import UserData from '../components/UserData';
import RemitaUserData from '../components/RemitaUserData';
import LoanDeductions from '../components/LoanDeductions';
import RepaymentSchedule from '../components/RepaymentSchedule';

class Dashboard extends Component {
	render() {
		const { loan, user, lender, location } = this.props;
		
		return (
			<div className="content-i" style={{tableLayout: 'fixed', display: 'table', width: '100%'}}>
				{user && (
					<div className="content-box">
						<div className="row">
							{loan && loan.liquidate_approve === 0 && loan.liquidated === 1 && (
								<div className="col-sm-12">
									<div className="alert alert-warning mb-3" style={{margin: '0', marginBottom: '15px'}}>Your liquidation request has been received and awaiting approval.</div>
								</div>
							)}
							<div className="col-sm-12">
								<LoanData
									name={user.name}
									loan={loan}
									user={user}
								/>
							</div>
						</div>
						{loan && loan.repayments && (
							<RepaymentSchedule repayments={loan.repayments}/>
						)}
						{loan && <LoanDeductions
							loan={loan}
							transactions={loan?.transactions || []}
						/>}
					</div>
				)}
				{(user && (user.platform === 'ippis' || user.platform === 'sme')) && (
					<UserData user={user} lender={lender} location={location} />
				)}
				{(user && user.platform === 'remita') && (
					<RemitaUserData user={user} lender={lender} location={location} />
				)}
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
    return {
        loan: state.loan.current,
        user: state.user.user,
        lender: state.lender.profile,
    }
}

export default connect(mapStateToProps)(Dashboard);
