<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class LiquidateApprovalLoan extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->tinyInteger('liquidate_approve')->default(0)->after('liquidated');
            $table->bigInteger('liquidate_approve_by')->unsigned()->nullable()->after('liquidate_approve');
            $table->dateTime('liquidate_approve_at')->nullable()->after('liquidate_approve_by');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropColumn(['liquidate_approve', 'liquidate_approve_by', 'liquidate_approve_at']);
        });
    }
}
