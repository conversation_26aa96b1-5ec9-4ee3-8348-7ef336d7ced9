<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Models\BankCode;
use App\Models\Office;
use App\Models\Payslip;
use App\Models\Role;
use App\Models\User;
use App\Services\MailHandler;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Log;

class AuthController extends Controller
{
    /**
     * API SignUp
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function signUp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|digits:11|unique:users',
            'password' => 'required|string|min:6|confirmed',
            'email' => 'required|email|unique:users',
            'lender_id' => 'required',
            'employer' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        DB::beginTransaction();
        try {
            $input = $request->all();

            $payslip = isset($input['id']) ? Payslip::where('id', $input['id'])->first() : null;

            if ($input['platform'] == 'remita') {
                $office = Office::where('lender_id', $input['lender_id'])->where('name', 'Remita')->first();
            } else if ($input['platform'] == 'sme') {
                $office = Office::where('lender_id', $input['lender_id'])->where('name', 'SME')->first();
            } else {
                if ($payslip) {
                    $office = Office::where('name', 'LIKE', '%' . $payslip->employer . '%')->first();
                } else {
                    $office = Office::where('name', 'LIKE', '%' . $input['employer'] . '%')->first();
                    if (!$office) {
                        $office = Office::create([
                            'name' => $input['employer'],
                            'lender_id' => $input['lender_id'],
                        ]);
                    }
                }
            }

            $tkn_hash = hash_hmac('sha256', Str::random(40), config('app.key'));
            $code = floor(random() * 1101233);

            $_user = User::create([
                'password' => bcrypt($input['password']),
                'name' => $input['name'],
                'email' => isset($input['email']) && $input['email'] != '' ? $input['email'] : null,
                'bvn' => isset($input['bvn']) ? $input['bvn'] : null,
                'phone' => $input['phone'],
                'payslip_id' => ($payslip ? $payslip->id : null),
                'office_id' => ($office ? $office->id : null),
                'platform' => $input['platform'],
                'customer_id' => $input['customer_id'] == '0' ? null : $input['customer_id'],
                'lender_id' => $input['lender_id'],
                'employer' => $input['employer'],
                'is_admin_created' => 0,
                'email_token' => $input['verified'] == 1 ? null : $tkn_hash,
                'email_code' => $input['verified'] == 1 ? null : $code,
                'verified' => $input['verified'],
            ]);
            $_user->roles()->attach(Role::where('name', 'user')->first());

            $user = User::with('loan', 'office')->where('id', $_user->id)->first();

            if ($input['platform'] == 'remita') {
                $bank_code = $input['bank_code'] == '0' ? null : $input['bank_code'];
                $bank = BankCode::where('code', $bank_code)->first();

                $account_number = $input['account_number'] == '0' ? null : $input['account_number'];
                $bank_name = $bank ? $bank->name : null;
                $bank_id = $bank ? $bank->id : null;

                createbankAccount($user->id, $account_number, $bank_name, $bank_code, 1, $input['phone'], $bank_id, $input['bvn']);
            } else {
                $bankname = str_replace('- ', '', $payslip->salary_bank);
                $bank = BankCode::where('name', $bankname)->first();
                info('bank name: ' . $bankname . ', bank: ' . json_encode($bank));

                $bank_code = $bank ? $bank->code : null;
                $bank_id = $bank ? $bank->id : null;

                createbankAccount($user->id, $payslip->salary_account_number, $payslip->salary_bank, $bank_code, 1, $input['phone'], $bank_id, $input['bvn']);
            }

            $notify = doNotify($user->id, 'users', $user->id, 'user_create', 'success', null, $user->lender_id, null);

            DB::commit();

            if ($user->verified == 0) {
                try {
                    $mail = (object)null;
                    $mail->from_name = 'FastCash';
                    $mail->from_email = '<EMAIL>';
                    $mail->to_name = str_replace(',', '', $user->name);
                    $mail->to_email = $user->email;
                    $mail->login = env('MAIN_URL');
                    $mail->platform = $user->platform;

                    if ($user->verified == 0) {
                        $mail->template = 'email.verify_account';
                        $mail->subject = 'Verify Your Email';
                        $mail->activation_url = env('MAIN_URL') . '/activate/' . $user->email_token;
                        $mail->code = $code;
                        $mail->platform = isset($input['__umtp']) && $input['__umtp'] != '' ? $input['__umtp'] : '';
                    } else {
                        $mail->template = 'email.new_customer';
                        $mail->subject = 'Welcome to FastCash';
                        $mail->ippis = $user->ippis ?? '';
                        $mail->phone = $user->phone;
                    }

                    $send = (new MailHandler())->sendMail($mail);
                } catch (\Exception $e) {
                    Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                }
            }

            $user->user_id = $user->id;
            $user->user_email = $user->email;

            return response()->json(['user' => $user], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'error occurred while creating your account'], 500);
        }
    }
}
