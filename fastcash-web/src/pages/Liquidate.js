import React, { Component } from 'react';
import { connect } from 'react-redux';
import Rave from 'react-flutterwave-rave';
import numeral from 'numeral';

import { httpRequest, baseURL, loanAPI, RAVE_PUBKEY } from '../config/constants';
import { updateCurrentLoan } from '../actions/loan';
import { doNotify } from '../actions/general';
import loading from '../assets/img/loading.gif';
import { startBlock, stopBlock } from '../actions/ui-block';

class Liquidate extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			key: RAVE_PUBKEY,
			email: '',
			amount: 0,
			agree: false,
			scheduleError: '',
			payment: 0,
			status: false,
			liquidated: false,
			paying: false,
			payment_type: '',
			trans_id: null,
		};
		this.onChange = this.onChange.bind(this);
	}

	UNSAFE_componentWillMount() {
		const { user } = this.props;
		this.setState({ email: user.email, amount: 0 });
	}

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	async componentDidMount() {
		const { current } = this.props;
		if(current && current.approved === 1 && current.disbursed === 1){
			this.setState({ scheduleError: '' });
			const rs = await httpRequest(`${baseURL}${loanAPI}/balance/${current.id}`, 'GET', true);
			if (rs && rs.result) {
				this.setState({ amount: parseFloat(rs.result.amount) });
			} else {
				this.setState({ scheduleError: 'could not get balance, please refresh your page.' });
			}
		}
	}

	startPayment = async () => {
		try {
			this.props.startBlock();
			const { current } = this.props;
			const { amount } = this.state;
			const data = { amount, payment_type: 'card' }
			const url = `${baseURL}${loanAPI}/start-liquidate/${current.id}`;
			const response = await httpRequest(url, 'POST', true, data);
			this.props.stopBlock();
			if(response && response.transaction){
				this.setState({ trans_id: response.transaction.id });
			}
		} catch(error){
			console.log(error)
			this.props.stopBlock();
		}
	};

	callback = async response => {
		const { current } = this.props;
		const { amount, trans_id } = this.state;

		if (current && amount > 0) {
			this.setState({ status: true, paying: true });

			if (document.getElementsByTagName('iframe')) {
				document.getElementsByTagName('iframe')[0].style.opacity = "0";
				document.getElementsByTagName('iframe')[0].style.pointerEvents = "none";
				document.getElementsByTagName('iframe')[0].style.zIndex = "-1";
				document.getElementsByTagName('iframe')[0].src = "https://ravemodal.herokuapp.com/?";
			}

			document.body.style.overflow = "";

			const data = {
				data: response.data,
				reference: response.tx.txRef,
				amount,
				platform: current.platform,
				payment_type: 'card',
				trans_id,
			};

			try {
				const url = `${baseURL}${loanAPI}/liquidate/${current.id}`;
				const rs = await httpRequest(url, 'POST', true, data);
				this.props.updateCurrentLoan(rs.loan);
				this.setState({ liquidated: true, paying: false });
			} catch (error) {
				console.log(error)
				this.setState({ paying: false });
				const message = error.message || 'Error, check your connection and try again';
				this.notify('', message, 'error');
			}
		}
	};

	close = () => {
		this.notify('', 'payment cancelled', 'error');
		this.setState({ paying: false });
	};

	getReference = () => {
		let text = '';
		let possible =
			'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-.=';
		for (let i = 0; i < 10; i++) {
			text += possible.charAt(Math.floor(Math.random() * possible.length));
		}
		return text;
	};

	onChange = (e) => this.setState({ agree: e.target.checked });

	onSelect = (e) => {
		const payment_type = e.target.value;
		this.setState({ payment_type });
		if(payment_type === 'card'){
			this.startPayment();
		}
	}

	bankPayment = async () => {
		const { current } = this.props;
		const { amount } = this.state;
		if (current && amount > 0) {
			this.setState({ status: true, paying: true });

			const data = {
				amount,
				platform: current.platform,
				payment_type: 'bank',
			};

			const url = `${baseURL}${loanAPI}/liquidate/${current.id}`;
			return httpRequest(url, 'POST', true, data)
				.then((response) => {
					this.props.updateCurrentLoan(response.loan);
					this.setState({ liquidated: true, paying: false });
				})
				.catch((error) => {
					this.setState({ paying: false });
					const message = error.message || 'Error, check your connection and try again';
					this.notify('', message, 'error');
				});
		}
	};

	render() {
		const { user, current } = this.props;
		const { amount, email, scheduleError, agree, liquidated, paying, payment_type } = this.state;

		return (
			<div className="content-i">
                {current && user ? (
					<div className="content-box">
						{liquidated && (
							<div className="alert alert-success" style={{margin: '0', marginBottom: '15px'}}>{`Your liquidation request has been received and awaiting approval. ${payment_type === 'bank' ? 'Please check your email to see our account information, do well to notify us after you have made payment. ' : ''}Thanks!`}</div>
						)}
						{scheduleError !== '' ? (
							<div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>{scheduleError}</div>
						) : (
							current.disbursed === 1 ? (
								current.liquidate_approve === 0 && current.liquidated === 1 ? (
									<div className="alert alert-warning" style={{margin: '0', marginBottom: '15px'}}>Your liquidation request has been received and awaiting approval.</div>
								) : (
									<div className="row">
										<div className="col-sm-8">
											<div className="element-wrapper">
												<h6 className="element-header">Pay-off Total Loan Balance</h6>
												<div className="element-box">
													<div>
														<div className="row">
															<div className="col-sm-6">
																<div className="form-group">
																	<label htmlFor="amount">Amount</label>
																	<input name="amount" placeholder="Amount" type="text" className="form-control" value={`NGN ${numeral(amount).format('0,0.00')}`} readOnly />
																</div>
															</div>
															<div className="col-sm-6">
																<div className="form-group">
																	<label htmlFor="email">Email Adress</label>
																	<input name="email" placeholder="Email address" type="email" className="form-control" value={email} readOnly />
																</div>
															</div>
														</div>
														<div className="row">
															<div className="col-sm-12">
																<div className={`form-check ${agree ? '' : 'has-error'}`}>
																	<label htmlFor="agree" className="form-check-label">
																		<input name="agree" id="agree" type="checkbox" className="form-check-input" onChange={this.onChange} value={agree} />{`By Clicking the 'Submit' button i ${user.name} authorize fastcash to charge the card provided to liquidate my current loan balance.`}
																	</label>
																	{!agree && <span className="help-block form-text with-errors form-control-feedback">please check the agreement box to continue</span>}
																</div>
															</div>
														</div>
														{agree && (
															<div className="row">
																<div className="col-sm-3">
																	<label>Select Payment Type: </label>
																</div>
																<div className="col-sm-4">
																	<select name="payment_type" className="form-control" onChange={this.onSelect}>
																		<option value="">Select Payment Type</option>
																		<option value="bank">Bank Transfer/Deposit</option>
																		<option value="card">Card Payment</option>
																	</select>
																</div>
															</div>
														)}
														<div className="form-buttons-w text-right">
															{amount > 0 && agree &&  payment_type !== '' && (
																paying ? (
																	<button className="btn btn-teal">
																		<img src={loading} alt="" />
																	</button>
																) : (
																	payment_type === 'card' ? (
																		<Rave
																			pay_button_text="Make Card Payment"
																			class="btn btn-teal"
																			reference={this.getReference()}
																			customer_email={email}
																			customer_phone={user.phone}
																			amount={amount.toString()}
																			ravePubKey={this.state.key}
																			callback={this.callback}
																			onclose={this.close}
																		/>
																	) : (
																		<button className="btn btn-teal" onClick={this.bankPayment}>Make Bank Payment</button>
																	)
																)
															)}
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								)
							) : (
								<div className="alert alert-warning" style={{margin: '0', marginBottom: '15px'}}>your loan amount has not been disbursed yet.</div>
							)
						)}
					</div>
				) : (
					<div className="content-box">
						<div className="row">
							<div className="col-sm-12 m-b">
								<div className="alert alert-warning">you dont have any loans at the moment.</div>
							</div>
						</div>
					</div>
				)}
            </div>
		);
	}
}

const mapStateToProps = (state) => {
	return {
		user: state.user.user,
		current: state.loan.current,
	}
};

export default connect(mapStateToProps, { doNotify, updateCurrentLoan, startBlock, stopBlock })(Liquidate);