<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\PaymentDate;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class FixBadTransaction extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $transactions = Transaction::where('loan_id', '188')->whereNull('outst_amount')->get();
            foreach ($transactions as $t){
                $loan = Loan::where('id', $t->loan_id)->first();
                $pay = PaymentDate::where('transaction_id', $t->id)->where('loan_id', $loan->id)->first();
                if ($pay) {
                    $month = Carbon::createFromFormat('Y-m-d H:i:s', $pay->payment_date)->format('F');

                    $transaction = Transaction::where('id', $t->id)->first();
                    $transaction->outst_amount = calcOutstRemita($loan->id, $loan->total_deduction, $transaction->amount);
                    $transaction->description = ($loan->platform == 'remita' ? strtoupper($loan->platform).' ' : '').'Deduction for the Month of ' .$month;
                    $transaction->save();

                    info('fixed: '.$t->id);
                }
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }
    }
}
