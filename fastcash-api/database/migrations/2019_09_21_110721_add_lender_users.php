<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddLenderUsers extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            DB::statement('ALTER TABLE `users` CHANGE `state_of_origin` `state_of_origin` INT(11) UNSIGNED NULL DEFAULT NULL');
            DB::statement('ALTER TABLE `users` CHANGE `lga_of_origin` `lga_of_origin` INT(11) UNSIGNED NULL DEFAULT NULL');
            DB::statement('ALTER TABLE `users` CHANGE `office_id` `office_id` BIGINT(20) UNSIGNED NULL DEFAULT NULL');
            DB::statement('ALTER TABLE `users` CHANGE `loan_id` `loan_id` BIGINT(20) UNSIGNED NULL DEFAULT NULL');

            $table->bigInteger('lender_id')->unsigned()->nullable()->after('username');

            $table->foreign('state_of_origin')->references('id')->on('states');
            $table->foreign('lga_of_origin')->references('id')->on('localgovts');

            $table->foreign('office_id')->references('id')->on('offices');
            $table->foreign('loan_id')->references('id')->on('loans');
            $table->foreign('lender_id')->references('id')->on('lenders');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            Schema::disableForeignKeyConstraints();
            $table->dropForeign(['lender_id', 'office_id', 'loan_id', 'state_of_origin', 'lga_of_origin']);
            $table->dropColumn(['lender_id']);
            Schema::enableForeignKeyConstraints();
        });
    }
}
