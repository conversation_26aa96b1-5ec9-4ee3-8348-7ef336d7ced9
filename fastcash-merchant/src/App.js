/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Route, Switch, withRouter } from 'react-router-dom';
import ReduxBlockUi from 'react-block-ui/redux';
import Loadable from 'react-loadable';
import NotificationSystem from 'react-notification-system';
import moment from 'moment';

import { putSettings } from './actions/settings';
import loader from './assets/img/loader.gif';
import Loading from './components/Loading';
import { doNotify } from './actions/general';

const NoMatch = Loadable({ loader: () => import('./pages/NoMatch'), loading: Loading });
const SignIn = Loadable({ loader: () => import('./pages/SignIn'), loading: Loading });
const Logout = Loadable({ loader: () => import('./pages/Logout'), loading: Loading });
const Merchant = Loadable({ loader: () => import('./pages/Merchant'), loading: Loading });
const ForgotPassword = Loadable({ loader: () => import('./pages/ForgotPassword'), loading: Loading });
const ResetPassword = Loadable({ loader: () => import('./pages/ResetPassword'), loading: Loading });

class App extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			notified: false,
		};
		this.notifRef = null;
	}

	componentWillUpdate(nextProps, nextState) {
		if (!nextState.notified && nextProps.notifdata) {
			const { title, message, level } = nextProps.notifdata;
			const id = moment().format('X');
			this.notify(title, message, level, id);
			this.setState({ notified: true });
			this.props.doNotify(null);
		}
	}

	notify = (title, message, level, id) => {
		this.refs.notifRef.addNotification({
			title,
			message,
			level,
			autoDismiss: 4,
			uid: id,
			onRemove: this.onRemove,
		});
	};

	onRemove = () => this.setState({ notified: false });

	render() {
		const { preloading } = this.props;
		return preloading == true ? (
			<div className="loading-page">
				<img src={loader} alt="" />
			</div>
		) : (
			<div>
				<ReduxBlockUi block="REQUEST_START" unblock="REQUEST_STOP">
					<NotificationSystem ref="notifRef" />
					<Switch>
						<Route exact path="/" component={SignIn} />
						<Route path="/dashboard/:merchantCode" component={Merchant} />
						<Route path="/forgot-password" component={ForgotPassword} />
						<Route path="/reset-password" component={ResetPassword} />
						<Route path="/logout" component={Logout} />
						<Route component={NoMatch} />
					</Switch>
				</ReduxBlockUi>
			</div>
		);
	}
}

const mapStateToProps = state => {
	return {
		preloading: state.user.preloading,
		lender: state.lender.profile,
		notifdata: state.general.notifdata,
	};
};

export default withRouter(
	connect(
		mapStateToProps,
		{ putSettings, doNotify },
	)(App),
);
