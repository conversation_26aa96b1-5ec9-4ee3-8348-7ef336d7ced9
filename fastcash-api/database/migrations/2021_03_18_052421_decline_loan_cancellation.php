<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DeclineLoanCancellation extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->mediumText('cancel_decline_reason')->nullable()->after('cancelled_by');
            $table->dateTime('cancel_declined_at')->nullable()->after('cancel_decline_reason');
            $table->bigInteger('cancel_declined_by')->unsigned()->nullable()->after('cancel_declined_at');

            $table->foreign('cancel_declined_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropForeign(['cancel_declined_by']);
            $table->dropColumn(['cancel_decline_reason', 'cancel_declined_at', 'cancel_declined_by']);
        });
    }
}
