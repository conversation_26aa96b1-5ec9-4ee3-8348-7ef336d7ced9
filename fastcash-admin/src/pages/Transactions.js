/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import Pagination from 'antd/lib/pagination';
import qs from 'querystring';
import $ from 'jquery';
import moment from 'moment';
import Tooltip from 'antd/lib/tooltip';

import ListHOC from '../container/ListHOC';
import { httpRequest, baseURL, transactionAPI, currency } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 12;

class Transactions extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			currentPage: 1,
			totalPage: 0,
			transactions: [],
			offices: [],
			office: 0,
			location: '',
		};
	}

	componentDidMount() {
		const { location } = this.props;
		const query = qs.parse(location.search.replace('?', ''));
		
		const page = query && query.p ? parseInt(query.p, 10) : 1;
		const office = query && query.office ? parseInt(query.office, 10) : 0;

		this.setState({ office, currentPage: page, location: location.pathname });

		this.fetch(page, office);
	}

	fetch = async (pageNumber, office) => {
		try {
			const { lender } = this.props;
			this.props.startBlock();
			const rs = await httpRequest(`${baseURL}${transactionAPI}?page=${pageNumber}&pagesize=${pageSize}&q=admin&lender=${lender.id}&office=${office}&status=Completed`, 'GET', true);
            const result = rs.result;
			this.setState({ currentPage: pageNumber, totalPage: result.total, transactions: result.data, offices: result.offices });
			this.props.stopBlock();
			this.props.notifyDone(false);
            window.scrollTo(0, 0);
		} catch (e) {
			this.props.stopBlock();
			this.props.doNotify({ message: 'could not load transactions', level: 'error' });
		}
	};

	onChange = pageNumber => {
		const { location } = this.props;
		const query = qs.parse(location.search.replace('?', ''));
		if(query.office){
			this.props.history.push(`/transactions?office=${query.office}&p=${pageNumber}`);
		} else {
			this.props.history.push(`/transactions?p=${pageNumber}`);
		}
	};

	onSelect = e => {
		const office = e.target.value;
		$(e.currentTarget).blur();
		this.props.history.push(`/transactions?office=${office}&p=1`);
	};

	componentWillUpdate(nextProps, nextState) {
		const query = qs.parse(nextProps.location.search.replace('?', ''));
		const pageNumber = query && query.p ? parseInt(query.p, 10) : 1;
		const office = query && query.office ? parseInt(query.office, 10) : 0;
		if(nextState.currentPage !== pageNumber || nextProps.location.pathname !== nextState.location || office !== nextState.office){
			this.setState({
				office,
				location: nextProps.location.pathname,
				currentPage: pageNumber,
			});

			this.fetch(pageNumber, office);
		} else if(nextProps.notifdone) {
			this.fetch(pageNumber, office);
		}
	}

	render() {
        const { currentPage, totalPage, transactions, offices, office } = this.state;
        const { showUserProfile } = this.props;
		return (
			<div className="element-wrapper">
				<h6 className="element-header">Transactions</h6>
				<div className="element-box">
					<div className="row">
						<label className="col-sm-1 col-form-label">Filter by: </label>
						<label className="col-sm-1 col-form-label text-right justify-content-end" htmlFor="">Office: </label>
						<div className="col-sm-3">
							<select name="office" className="form-control" onChange={this.onSelect} value={office}>
								<option value="0">All Offices</option>
								{offices.map(o => <option key={o.id} value={o.id}>{o.name}</option>)}
							</select>
						</div>
					</div>
				</div>
				<div className="element-box">
					<div className="table-responsive" style={{overflowX: 'scroll',}}>
						<table className="table table-striped table-lightfont">
							<thead>
								<tr>
									<th>S/N</th>
									<th>Name of Employee</th>
									<th>Office</th>
									<th>IPPIS/Customer ID</th>
									<th>Source</th>
									<th>Repayment Source</th>
									<th>Amount</th>
									<th>Description</th>
									<th className="text-center">Status</th>
									<th>Date</th>
									<th>&nbsp;</th>
								</tr>
							</thead>
							<tbody>
								{transactions.map((item, i) => {
                                    const cid = item.user.platform === 'sme' ? item.user.phone : (item.user.ippis && !isNaN(item.user.ippis) ? item.user.ippis : item.user.customer_id);
                                    return (
                                        <tr key={i}>
                                            <td>{item.id}</td>
                                            <td>{item.user.name}</td>
                                            <td>{item.office ? item.office.name : '-'}</td>
                                            <td><a onClick={() => showUserProfile(item.user.id)} className="cursor link">{cid}</a></td>
                                            <td>{item.source}</td>
                                            <td>{item.repayment_source ? item.repayment_source.toUpperCase() : '-'}</td>
                                            <td>{currency(item.amount)}</td>
                                            <td>{item.description}</td>
                                            <td className="text-center">
                                                <Tooltip placement="top" title={(item.loan?.liquidate_approve === 0 && item.description === 'Loan Liquidated') || item.approved === 0 ? 'Pending Approval' : item.status}>
                                                    <div className={`status-pill ${(item.loan?.liquidate_approve === 0 && item.description === 'Loan Liquidated') || item.approved === 0 ? 'yellow' : (item.status === 'Completed' ? 'green' : 'yellow')}`}/>
                                                </Tooltip>
                                            </td>
                                            <td>{moment(item.created_at).format('D.MMM.YYYY')}</td>
                                        </tr>
                                    )
                                })}
							</tbody>
						</table>
					</div>
					<div className="pagination pagination-center mt-4">
						<Pagination
							current={currentPage}
							pageSize={pageSize}
							total={totalPage}
							showTotal={total => `Total ${total} transactions`}
							itemRender={itemRender}
							onChange={this.onChange}
						/>
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
		lender: state.lender.profile,
	}
};

export default ListHOC(connect(mapStateToProps, { startBlock, stopBlock })(Transactions));
