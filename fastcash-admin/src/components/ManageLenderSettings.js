import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, SubmissionError } from 'redux-form';

import { doNotify } from '../actions/general';
import { baseURL, httpRequest, lenderAPI } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import <PERSON><PERSON> from './Naira';
import { setLenderProfile } from '../actions/lender';

const validate = values => {
    const errors = {}
    if (!values.merchant_commission) {
        errors.merchant_commission = 'Enter merchant commission';
	}
    if (!values.min_loan_tenure) {
        errors.min_loan_tenure = 'Enter minimum loan tenure';
	}
    if (!values.max_loan_tenure) {
        errors.max_loan_tenure = 'Enter maximum loan tenure';
	}
    return errors;
};

const renderAppendField = ({input, id, label, type, append, disabled, meta: {touched, error, warning}}) => (
	<div className={`form-group row ${(touched && error && 'has-error')}`}>
		<label className="col-sm-5 col-form-label text-right" htmlFor={id}>{label}</label>
		<div className="col-sm-7">
			<div className="input-group">
				<input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
				<div className="input-group-append">
					<div className="input-group-text">{append}</div>
				</div>
			</div>
			{touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
		</div>
	</div>
);

class ManageLenderSettings extends Component {
	doSaveLender = async data => {
		this.props.startBlock();
		try {
			const { lender, user } = this.props;
			const details = { ...data, staff_id: user.id };
			const url = `${baseURL}${lenderAPI}/${lender.id}`;
			const rs = await httpRequest(url, 'PUT', true, details);
			this.props.setLenderProfile(rs.lender);
			this.props.stopBlock();
			this.notify('', 'settings saved!', 'success');
		}
		catch (error) {
			this.props.stopBlock();
			const message = error.message || 'could not save settings';
			this.notify('', message, 'error');
			throw new SubmissionError({
				_error: message,
			});
		}
	};
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	render() {
		const { handleSubmit, error } = this.props;
		return (
			<div className="element-box">
				{error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
				<form onSubmit={handleSubmit(this.doSaveLender)}>
					<Field
						name="interest_rate"
						id="interest_rate"
						type="number"
						component={renderAppendField}
						label="Interest Rate"
						append="%"
					/>
					<Field
						name="merchant_commission"
						id="merchant_commission"
						type="number"
						component={renderAppendField}
						label="Merchant Commission"
						append={<Naira/>}
					/>
					<Field
						name="min_loan_tenure"
						id="min_loan_tenure"
						type="number"
						component={renderAppendField}
						label="Minimum Loan Tenure"
						append="Months"
					/>
					<Field
						name="max_loan_tenure"
						id="max_loan_tenure"
						type="number"
						component={renderAppendField}
						label="Maximum Loan Tenure"
						append="Months"
					/>
					<div className="form-buttons-w">
						<button className="btn btn-primary" type="submit">Save Changes</button>
					</div>
				</form>
			</div>
		);
	}
}

ManageLenderSettings = reduxForm({
    form: 'savelender',
    validate,
})(ManageLenderSettings);

const mapStateToProps = (state, ownProps) => {
	const lender = state.lender.profile;
	
	return {
		initialValues: {
            merchant_commission: lender.merchant_commission,
            min_loan_tenure: lender.min_loan_tenure,
            max_loan_tenure: lender.max_loan_tenure,
            interest_rate: lender.interest_rate,
		},
		lender: state.lender.profile,
		user: state.user.user,
	}
};

export default connect(mapStateToProps, { setLenderProfile, startBlock, stopBlock, doNotify })(ManageLenderSettings);
