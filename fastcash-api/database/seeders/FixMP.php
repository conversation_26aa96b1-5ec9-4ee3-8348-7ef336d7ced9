<?php

namespace Database\Seeders;

use App\Models\Loan;
use Illuminate\Database\Seeder;

class FixMP extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $fixed = [];
            $loans = Loan::where('monthly_principal', 0)->get();
            foreach ($loans as $l) {
                $loan = Loan::where('id', $l->id)->first();
                if ($loan) {
                    $loan->monthly_principal = $loan->monthly_deduction - $loan->monthly_interest;
                    $loan->save();

                    $fixed[] = $loan->id;
                }
            }

            error_log(count($fixed) . ' loans fixed');
        } catch (\Exception $e) {
            error_log($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
        }
    }
}
