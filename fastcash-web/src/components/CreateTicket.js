/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import truncate from 'lodash.truncate';

import loading from '../assets/img/loading.gif';
import { httpRequest, baseURL, supportAPI, httpRequest2, uploadDocAPI } from '../config/constants';
import { addSupport, updateTicket } from '../actions/support';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {}
    if (!values.email) {
        errors.email = 'Enter your email address';
    }
    if (!values.subject) {
        errors.subject = 'Enter the subject';
    }
    if (!values.message) {
        errors.message = 'Enter the message';
    }
    return errors;
};

const renderField = ({input, id, label, type, disabled, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label className="lighter" htmlFor={id}>{label}</label>
        <div className="input-group mb-2 mr-sm-2 mb-sm-0">
            <input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
        </div>
        {touched &&
            ((error && <span className="help-block">{error}</span>) ||
                (warning && <span className="help-block">{warning}</span>))}
    </div>
);

const renderTAField = ({input, id, label, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label className="lighter" htmlFor={id}>{label}</label>
        <div className="input-group mb-2 mr-sm-2 mb-sm-0">
            <textarea {...input} placeholder={label} className="form-control" rows="8" />
        </div>
        {touched &&
            ((error && <span className="help-block">{error}</span>) ||
                (warning && <span className="help-block">{warning}</span>))}
    </div>
);

class CreateTicket extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			submitting: false,
			files: [],
		};

		this.saveTicket = this.saveTicket.bind(this);
	}

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	saveTicket = async data => {
		const { user } = this.props;
		const { files } = this.state;
		let details = { ...data, user_id: user.id, response: 'awaiting response,new ticket', attachment: files, };
		this.setState({ submitting: true });
		try {
			const rs = await httpRequest(`${baseURL}${supportAPI}`, 'POST', true, details);
			this.props.addSupport(rs.support);
			this.setState({ submitting: false });
			this.props.reset('ticket-frm');
			this.notify('', 'Thanks for contacting us, we generally respond within 3 working days', 'success');
			this.props.closeModal();
		} catch (error) {
			this.setState({ submitting: false });
			const message = error.message || 'could not send ticket';
			this.notify('', message, 'error');
			throw new SubmissionError({
				_error: message,
			});
		}
	};

	onChange = async e => {
		const file = e.target.files[0];
		if (file) {
			this.setState({ uploading: true });
			
			let formData = new FormData();
			formData.append('file', file);
			formData.append('name', file.name);

			try {
				const response = await httpRequest2(`${baseURL}${uploadDocAPI}`, 'POST', formData);
				this.setState({ files: [...this.state.files, response.result], uploading: false });
			} catch (error) {
				this.setState({ uploading: false });
				if (error.message) {
					this.notify('', error.message, 'error');
				} else {
					this.notify('', 'Error, could not upload file', 'error');
				}
			}
		}
	};

	remove = file => () => {
		this.setState({ files: [...this.state.files.filter(f => f.name !== file.name)] });
	};

	render() {
		let uploadAttachment;
        const { pristine, handleSubmit, error, closeModal, title } = this.props;
		const { submitting, files, uploading } = this.state;
		
		return (
			<div className="element-box">
                {error && <div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                <form onSubmit={handleSubmit(this.saveTicket)} autoComplete="off">
                    <h6 className="element-box-header">{title}</h6>
                    <div className="row">
						<div className="col-sm-6">
							<Field
								name="email"
								id="email"
								type="text"
								component={renderField}
								label="Email"
								disabled="disabled"
							/>
						</div>
						<div className="col-sm-6">
							<Field
								name="subject"
								id="subject"
								type="text"
								component={renderField}
								label="Subject"
							/>
						</div>
					</div>
                    <div className="row">
                        <div className="col-sm-12">
                            <Field
                                name="message"
                                id="message"
                                component={renderTAField}
                                label="Description"
                            />
                        </div>
                    </div>
					<div className="actions-left">
						<input className="hidden" onClick={(e) => { e.target.value = null; }} type="file" ref={(el) => { uploadAttachment = el; }} onChange={(e) => this.onChange(e)} />
						<a className="btn btn-link" onClick={() => { uploadAttachment.click() }}>
							<i className="os-icon os-icon-ui-51"/>
							<span>Add Attachment</span>
						</a>
						<div className="message-attachments">
							<div className="attachments-docs">
								{files.map((file, i) => {
									return (
										<a key={i} className="badge badge-info-inverted badge-light ml-2">
											<span>{truncate(file.name, { 'length': 20, })}</span>
											<i onClick={this.remove(file)} className="os-icon os-icon-ui-15 text-danger ml-2" />
										</a>
									);
								})}
							</div>
						</div>
					</div>
                    <div className="form-buttons-w text-right compact">
						<button className="mr-2 btn btn-default" onClick={closeModal}>Cancel</button>
                        <button className="mr-2 btn btn-primary" disabled={pristine || uploading || submitting} type="submit">
                            {(submitting || uploading) ? <img src={loading} alt=""/> : 'Submit'}
                        </button>
                    </div>
                </form>
            </div>
		);
	}
}

CreateTicket = reduxForm({
    form: 'ticket-frm',
    validate,
})(CreateTicket);

const mapStateToProps = (state, ownProps) => {
    return {
		initialValues: {
            email: ownProps.user.email,
		},
    }
}

export default connect(mapStateToProps, { reset, addSupport, updateTicket, doNotify })(CreateTicket);
