<?php

namespace Database\Seeders;

use App\Models\PaymentDate;
use App\Models\Transaction;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class FixRepayments extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $repayments = Transaction::where('channel', 'staff-repayment')->where('approved', 0)->get();
            foreach ($repayments as $item) {
                $transaction = Transaction::find($item->id);
                if($transaction){
                    $transaction->interest = NULL;
                    $transaction->principal = NULL;
                    $transaction->outst_amount = NULL;
                    $transaction->description = NULL;
                    $transaction->save();

                    Log::debug(json_encode($transaction));

                    $pay = PaymentDate::where('transaction_id', $transaction->id)->first();
                    if($pay) {
                        info(json_encode($pay));

                        $pay->paid = 0;
                        $pay->paid_at = NULL;
                        $pay->transaction_id = NULL;
                        $pay->save();
                    }
                }
            }
        }
        catch (\Exception $e){
            Log::error($e->getLine().' '.$e->getMessage().' '.$e->getFile());
        }
    }
}
