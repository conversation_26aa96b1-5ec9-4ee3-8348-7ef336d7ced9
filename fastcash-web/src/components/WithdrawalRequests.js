/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react';
import moment from 'moment';
import startCase from 'lodash.startcase';
import Pagination from 'antd/lib/pagination';

import { mask, currency } from '../config/constants';
import Loading from './Loading';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};

const pageSize = 12;

const WithdrawalRequests = ({ withdrawals, currentPage, totalPage, handleChange, fetching }) => {
	return (
		<div className="element-wrapper">
			<h6 className="element-header">Withdrawal Requests</h6>
			{fetching && <Loading />}
			{!fetching && (
				<div className="element-box-tp">
					<div className="table-responsive">
						<table className="table table-padded">
							<thead>
								<tr>
									<th>Date</th>
									<th>Bank Account</th>
									<th className="text-center">Status</th>
									<th className="text-right">Amount</th>
								</tr>
							</thead>
							<tbody>
								{withdrawals.map((item, i) => {
									return (
										<tr key={i}>
											<td>
												<span>{moment(item.created_at).format('DD.MMM.YYYY')}</span>
												<span className="smaller lighter">{moment(item.created_at).format('h:mma')}</span>
											</td>
											<td className="cell-with-media">
												<span>{`${startCase(item.accountno.bank_name)} ${mask(item.accountno.account_number)}`}</span>
											</td>
											<td className="text-center">
												<a className={`badge badge-${!item.deleted_at ? (item.approved === 0 ? 'warning' : 'success text-white') : 'danger text-white'}`}>
													{!item.deleted_at ? (item.approved === 0 ? 'Pending' : 'Completed') : 'Declined'}
												</a>
											</td>
											<td className="text-right bolder nowrap">
												<span className={`text-${!item.deleted_at ? (item.approved === 0 ? 'danger' : 'success') : 'danger'}`}>{`- ${currency(item.amount)}`}</span>
											</td>
										</tr>
									)
								})}
								{withdrawals.length === 0 && (
									<tr>
										<td colSpan="4">No withdrawal requests</td>
									</tr>
								)}
							</tbody>
						</table>
					</div>
					<div className="pagination pagination-center mt-4">
						<Pagination
							current={currentPage}
							pageSize={pageSize}
							total={totalPage}
							showTotal={total => `Total ${total} loans`}
							itemRender={itemRender}
							onChange={e => handleChange(e)}
						/>
					</div>
				</div>
			)}
		</div>
	);
};

export default WithdrawalRequests;
