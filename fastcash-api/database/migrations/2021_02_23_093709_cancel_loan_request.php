<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CancelLoanRequest extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->tinyInteger('cancel_request')->default(0)->after('decline_reason');
            $table->mediumText('cancel_reason')->nullable()->after('cancel_request');
            $table->dateTime('cancelled_at')->nullable()->after('cancel_reason');
            $table->bigInteger('cancelled_by')->unsigned()->nullable()->after('cancelled_at');

            $table->foreign('cancelled_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropForeign(['cancelled_by']);
            $table->dropColumn(['cancel_request', 'cancel_reason', 'cancelled_at', 'cancelled_by']);
        });
    }
}
