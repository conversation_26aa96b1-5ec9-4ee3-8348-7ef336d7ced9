<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddStopLoanResponseToLoansTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->text('stop_loan_response')->nullable()->comment('remita stop loan notification response')->after('payment_status');
            $table->tinyInteger('stopped_loan_deduction')->default(0)->after('stop_loan_response');
            $table->text('stop_deduction_narration')->nullable()->after('stopped_loan_deduction');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropColumn(['stop_loan_response', 'stopped_loan_deduction', 'stop_deduction_narration']);
        });
    }
}
