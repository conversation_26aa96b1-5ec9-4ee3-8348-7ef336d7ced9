<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ConsentOtp extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->string('consent_otp', 10)->nullable()->after('verified_at');
            $table->dateTime('consent_otp_expire_at')->nullable()->after('consent_otp');
            $table->tinyInteger('has_consented')->default(0)->after('consent_otp_expire_at');
            $table->dateTime('consented_at')->nullable()->after('has_consented');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropColumn('consent_otp');
            $table->dropColumn('consent_otp_expire_at');
            $table->dropColumn('has_consented');
            $table->dropColumn('consented_at');
        });
    }
}
