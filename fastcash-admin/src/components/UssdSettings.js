import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';

import { baseURL, httpRequest, updateUSSDAPI } from '../config/constants';
import { setUSSDSettings } from '../actions/loan';
import { startBlock, stopBlock } from '../actions/ui-block';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {}
    if (!values.limit) {
        errors.limit = 'Enter limit...';
	}
	if(!values.message) {
		errors.message = 'Enter Message...';
	}
    return errors;
};

const renderField = ({input, id, label, type, placeholder, meta: {touched, error, warning}}) => (
	<div className={`form-group ${(touched && error && 'has-error')}`}>
		<label className="lighter" htmlFor={id}>{label}</label>
		<div className="input-group mb-2 mr-sm-2 mb-sm-0">
			<input {...input} placeholder={placeholder} type={type} className="form-control" />
		</div>
		{touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
	</div>
);

class USSDSettings extends Component {
	updateUSSD = data => {
        this.props.startBlock();
		const info = { limitNo: data.limit, messageToDisplay: data.message };
		return httpRequest(`${baseURL}${updateUSSDAPI}`, 'POST', true, info)
			.then(response => {
                const infoData = {limit: response.data.limitNo, message: response.data.messageToDisplay };
                this.props.setUSSDSettings(infoData);
				this.props.reset('updateUSSD');
				this.props.stopBlock();
				this.notify('', response.message, 'success');
			})
			.catch(error => {
				this.props.stopBlock();
				const message = error.message || 'Error, check your connection and try again';
				this.notify('', message, 'error');
				throw new SubmissionError({
					_error: message,
				});
			});
	};

    getUSSDSettings = () => {
        this.props.startBlock();
		return httpRequest(`${baseURL}${updateUSSDAPI}`, 'GET', true)
			.then(response => {
                const data = response.data;
                const infoData = {limit: data.limitNo, message: data.messageToDisplay };
                this.props.setUSSDSettings(infoData);
				this.props.stopBlock();
			})
			.catch(error => {
				this.props.stopBlock();
				const message = error.message || 'Error, check your connection and try again';
				this.notify('', message, 'error');
				throw new SubmissionError({
					_error: message,
				});
			});
    };

	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

    componentDidMount() {
        this.getUSSDSettings();
    }

	render() {
		const { handleSubmit, error } = this.props;
		return (
			<div className="element-box">
				{error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
				<form onSubmit={handleSubmit(this.updateUSSD)}>
					<h5 className="element-box-header">USSD Settings</h5>
					<div className="row">
						<div className="col-sm-12">
							<Field
								name="limit"
								id="limit"
								type="number"
								component={renderField}
								label="Display Limit"
								placeholder="Enter Limit..."
							/>
						</div>
					</div>
                    <div className="row">
						<div className="col-sm-12">
							<Field
								name="message"
								id="message"
								type="text"
								component={renderField}
								label="Message"
								placeholder="Enter Text..."
							/>
						</div>
					</div>
                    <div className="form-buttons-w compact clearfix">
						<button className="btn btn-primary float-right" type="submit">
							<span>Save Changes</span>
						</button>
					</div>
				</form>
			</div>
		);
	}
}

USSDSettings = reduxForm({
    form: 'updateUSSD',
    validate,
    enableReinitialize: true,
})(USSDSettings);

const mapStateToProps = (state, ownProps) => {
    const infoData = state.user.ussd_settings;
    
	return {
		initialValues: {
            limit: infoData?.limit,
            message: infoData?.message
		},
		user: state.user.user,
		lender: state.lender.profile,
	}
};

export default connect(mapStateToProps, { reset, setUSSDSettings, startBlock, stopBlock, doNotify })(USSDSettings);