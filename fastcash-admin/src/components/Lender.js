/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react';
import Tooltip from 'antd/lib/tooltip';
import { Link } from 'react-router-dom';

const Lender = ({ item, enableLender, disableLender, editLender }) => {
	return (
		<tr>
			<td>{item.lender_code}</td>
			<td>{item.name}</td>
			<td>{item.email}</td>
			<td>{item.phone_number}</td>
			<td>
				{item.status === 1 ? (
					<span className="elabel elabel-success">Enabled</span>
				) : (
					<span className="elabel elabel-danger">Disabled</span>
				)}
			</td>
			<td className="row-actions">
				<Tooltip title="View Lender">
					<Link className="text-gray-dark cursor" to={`/lenders/view/${item.id}`}>
						<i className="icon-eye" />
					</Link>
				</Tooltip>
				{item.status === 1 && (
					<Tooltip title="Edit Lender">
						<a className="text-info cursor" onClick={() => editLender(item.id)}>
							<i className="os-icon os-icon-pencil-1" />
						</a>
					</Tooltip>
				)}
				{item.id !== 1 && (
					item.status === 0 ? (
						<Tooltip title="Enable Lender">
							<a className="text-primary cursor" onClick={() => enableLender(item.id)}>
								<i className="os-icon os-icon-unlock" />
							</a>
						</Tooltip>
					) : (
						<Tooltip title="Disable Lender">
							<a className="text-danger cursor" onClick={() => disableLender(item.id)}>
								<i className="os-icon os-icon-database-remove" />
							</a>
						</Tooltip>
					)
				)}
			</td>
		</tr>
	);
};

export default Lender;
