<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSupportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('supports', function (Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('user_id')->unsigned()->index();
            $table->string('subject')->nullable();
            $table->integer('reply_id')->nullable()->index();
            $table->longText('message')->nullable();
            $table->text('attachment')->nullable();
            $table->dateTime('closed_at')->nullable();
            $table->bigInteger('closed_by')->nullable()->unsigned();
            $table->tinyInteger('top_query')->nullable();
            $table->tinyInteger('status')->default(0);
            $table->string('response')->nullable();
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users');
            $table->foreign('closed_by')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('supports');
    }
}
