import React, { Component } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';
import { Table } from 'antd';

import { baseURL, httpRequest2, uploadPayslipAPI } from '../config/constants';
import { setSlips } from '../actions/general';
import { startBlock, stopBlock } from '../actions/ui-block';
import Upload from '../components/Upload';
import { doNotify } from '../actions/general';

class UploadPayslip extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			files: [],
			error: '',
			month: '',
			year: '',
			message: '',
			disabled: true,
		};
		this.changeMonth = this.changeMonth.bind(this);
		this.changeYear = this.changeYear.bind(this);
		this.onOpenDropzone = this.onOpenDropzone.bind(this);
		this.checkDisabled = this.checkDisabled.bind(this);
	}

	displayError = (error, type) => {
		this.setState({ error, disabled: true });
		this.props.stopBlock();
	};

	onDrop = async file => {
		this.props.startBlock();
		const { lender } = this.props;
		const { files, month, year } = this.state;
		const { name } = file[0];
		
		this.setState({ message: '' });

		let formData = new FormData();
		formData.append('file', file[0]);
		formData.append('month', month);
		formData.append('year', year);
		formData.append('lender_id', lender.id);

		try {
			const response = await httpRequest2(`${baseURL}${uploadPayslipAPI}`, 'POST', formData);
			this.props.setSlips(response.slips);
			this.props.stopBlock();
			const message = `${response.numberOfUsers} user profiles uploaded, ${response.numberOfUpdates} user profiles updated`;
			this.setState({ files: [...files, { name, status: 1 }], disabled: true, message });
			this.props.doNotify({ message, level: 'success' });
		}
		catch (error) {
			this.setState({ files: [...files, { name, status: 0 }] });
			this.setState({ month: '', year: '' });
			if (error.message) {
				this.displayError(error.message, 'text');
			}
			else {
				this.displayError('Error, check your connection and try again', 'text');
			}
		}
	};

	checkDisabled = (m, y) => {
		if (m !== '' && y !== '') {
			this.setState({ disabled: false });
		} else {
			this.setState({ disabled: true });
		}
	};

	changeMonth = e => {
		this.setState({ month: e.target.value });
		this.checkDisabled(e.target.value, this.state.year);
	};

	changeYear = e => {
		this.setState({ year: e.target.value });
		this.checkDisabled(this.state.month, e.target.value);
	};

	onOpenDropzone = e => {
		this.setState({ error: '' });
	};

	render() {
		const { error, year, month, files, disabled, message } = this.state;
		const { slips } = this.props;
		const thisYear = moment().format('YYYY');
		const years = [...Array(4).keys()].map(y => thisYear - y);
		const months = [...Array(12).keys()].map(m => moment().month(m).format('MMMM'));
		const columns = [
			{
				title: 'Office',
				dataIndex: 'employer',
			},
			{
				title: 'Date',
				render: (text, record) => moment(`${record.month}-${record.year}`, 'M-YYYY').format('MMM-YYYY'),
			},
		];

        return (
            <div className="content-i">
                <div className="content-box">
                    <div className="element-wrapper compact pt-4">
                        <h6 className="element-header">Upload Payslips</h6>
                        <div className="element-box-tp">
                            <div className="row">
                                <div className="col-lg-6 col-xxl-6">
                                    <Upload
                                        error={error}
                                        message={message}
                                        changeMonth={this.changeMonth}
                                        month={month}
                                        months={months}
                                        changeYear={this.changeYear}
                                        year={year}
                                        years={years}
                                        onDrop={this.onDrop}
                                        onOpenDropzone={this.onOpenDropzone}
                                        disabled={disabled}
										files={files}
										schedule={false}
                                    />
                                </div>
								<div className="col-lg-6 col-xxl-6">
									<div className="element-wrapper">
										<h6 className="element-header">Last Payslips Uploaded</h6>
										<div className="element-box-tp">
											<div className="table-responsive">
												<Table
													className="table table-striped table-lightfont"
													rowKey={record => record.id}
													columns={columns}
													dataSource={slips}
													size="middle"
													pagination={{'pageSize': 8}}
												/>
											</div>
										</div>
									</div>
								</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
	}
}

const mapStateToProps = state => {
	return {
		slips: state.general.slips,
		lender: state.lender.profile,
	};
};

export default connect(
	mapStateToProps,
	{ startBlock, stopBlock, setSlips, doNotify },
)(UploadPayslip);
