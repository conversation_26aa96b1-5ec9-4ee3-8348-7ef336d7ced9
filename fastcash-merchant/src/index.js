import React from 'react';
import ReactDOM from 'react-dom';
import { Provider } from 'react-redux';
import createHistory from 'history/createBrowserHistory';
import { Router } from 'react-router-dom';

import 'antd/dist/antd.css';
import 'font-awesome/css/font-awesome.css';
import 'simple-line-icons/css/simple-line-icons.css';
import 'react-block-ui/style.css';

import './assets/css/main.css';
import './assets/css/pages.css';
import './assets/css/authenticate.css';
import './assets/css/icons.css';
import './assets/css/custom.css';

import App from './App';
import configureStore from './store';
import { getUserID, httpRequest, baseURL, userAPI, settingKey, authKey } from './config/constants';
import { CHANGE_USER_DATA } from './actions/types';
import { changeUserData, stopPreloading } from './actions/user';
import { setLoans } from './actions/loan';
import { putSettings } from './actions/settings';

const store = configureStore();
const history = createHistory();

const checkResetToken = async () => {
	const location = history.location.pathname.split('/');
	if (location.length > 1 && location[1] === 'reset-password') {
		try {
			const rs = await httpRequest(`${baseURL}/auth/check-token/${location[2]}`, 'GET', false);
			if (rs.reset) {
			} else {
				store.dispatch(stopPreloading());
				history.push('/page-not-found');
			}
		} catch (e) {
			store.dispatch(stopPreloading());
			history.push('/page-not-found');
		}
	}
};

checkResetToken();

const userID = getUserID();
if (userID) {
	httpRequest(`${baseURL}${userAPI}/${userID}?platform=merchant`, 'GET', true)
		.then(response => {
			const user = response.user;
			if (user.role === 'merchant' || user.role === 'u-merchant') {
				store.dispatch(changeUserData(user, true));
				store.dispatch(setLoans(response.loans));
				store.dispatch(stopPreloading());
				store.dispatch(putSettings(response.settings));
				history.push({ pathname: `/dashboard/${user.merchant_code}` });
			} else {
				localStorage.removeItem(authKey);
				localStorage.removeItem(settingKey);
				store.dispatch({ type: CHANGE_USER_DATA, user: null, status: false });
				store.dispatch(stopPreloading());
				history.push('/?not-authenticated');
			}
		})
		.catch(error => {
			localStorage.removeItem(authKey);
			localStorage.removeItem(settingKey);
			store.dispatch({ type: CHANGE_USER_DATA, user: null, status: false });
			store.dispatch(stopPreloading());
			history.push('/?not-authenticated');
		});
} else {
	const location = history.location.pathname.split('/');
	store.dispatch(stopPreloading());
	if (location.length > 1 && (location[1] !== 'forgot-password' && location[1] !== 'reset-password')) {
		history.push('/?not-authenticated');
	}
}

ReactDOM.render(
	<Provider store={store}>
		<Router history={history}>
			<App />
		</Router>
	</Provider>,
	document.getElementById('main'),
);
