<?php

use App\Models\Accountno;
use App\Models\Loan;
use App\Models\Localgovt;
use App\Models\PaymentDate;
use App\Models\State;
use App\Models\User;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

Route::get('/pdf/{id}', function (int $id) {
    $pay_dates = PaymentDate::where('loan_id', $id)->get();

    $dates = [];
    foreach ($pay_dates as $pay) {
        $dates[] = $pay->payment_date_approved;
    }

    $loan = Loan::where('id', $id)->first();

    $_user = User::with('office')->where('id', $loan->user_id)->first();
    $_user->state_oo = State::find($_user->state_of_origin);
    $_user->lga_oo = Localgovt::where('serialno', $_user->lga_of_origin)->where('state_id', $_user->state_of_origin)->first();

    $last_date = array_pop($dates);
    $dates = count($dates) > 0 ? array_merge([$loan->approved_at], $dates) : [];

    $user = (object)null;
    $user->user = $_user;
    $user->loan = $loan;
    $user->acn = Accountno::where('id', $loan->accountno_id)->first();
    $user->dates = $dates;
    $user->monthly_deduction = $loan->monthly_deduction;

    return view('pdf.offer_letter', compact('user'));
});
