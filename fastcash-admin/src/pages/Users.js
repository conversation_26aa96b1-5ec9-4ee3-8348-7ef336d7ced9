/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import Modal from 'react-modal';
import moment from 'moment';
import sha512 from 'crypto-js/sha512';
import utf8 from 'crypto-js/enc-utf8';
import aes from 'crypto-js/aes';
import pkcs7 from 'crypto-js/pad-pkcs7';
import { mode } from 'crypto-js';
import { bindActionCreators } from 'redux';
import qs from 'querystring';
import Pagination from 'antd/lib/pagination';
import $ from 'jquery';

import loading from '../assets/img/loading.gif';
import { liveStatus, ENC_Key, ENC_Vector, paymentMerchantId, paymentApiKey, paymentApiToken, merchantId, apiKey, apiToken, bankCode, bankAccount, httpRequest, baseURL, userAPI, loanAPI, reportAPI, currency, remitaLoanHistoryRequestAPI } from '../config/constants';
import { approveMoney, verifyLoan, setLoanAnalytics, declineLoan, disburseMoney, verifyMoneyPaid, bypassDisburse, sendMail, consentSMS } from '../actions/loan';
import { startBlock, stopBlock } from '../actions/ui-block';
import { SHOW_PROFILE } from '../actions/types';
import { toggleMenu } from '../actions/user';
import { doNotify, notifyDone } from '../actions/general';

import LoanProfile from '../components/LoanProfile';
import BreadCrumbs from '../components/BreadCrumbs';
import OfficeData from '../components/OfficeData';
import LoanRepayment from '../components/LoanRepayment';
import TransferToLender from '../components/TransferToLender';
import CoreBankID from '../components/CoreBankID';
import LiquidateLoan from '../components/LiquidateLoan';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 8;

class Users extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
            modalIsOpen: false,
            repModalIsOpen: false,
            lenModalIsOpen: false,
            coreBankModalIsOpen: false,
            id: '',
			opage: 1,
			currentPage: 1,
			totalPage: 0,
			customer: null,
			loan: null,
			location: '',
			loan_month: '',
			loan_year: '',
			loan_status: '',
			users: [],
			offices: [],
			reason: '',
            declining: false,
			loan_id: null,
			visible: false,
			editVisible: false,
			exporting: false,
			liqModalIsOpen: false,
        };
        this.openModal = this.openModal.bind(this);
        this.closeModal = this.closeModal.bind(this);
		this.doDeclineLoan = this.doDeclineLoan.bind(this);
		this.showProfile = this.showProfile.bind(this);
		this.approveLoan = this.approveLoan.bind(this);
		this.doSendConsentSMS = this.doSendConsentSMS.bind(this);
		this.onSaveLoanInsurance = this.onSaveLoanInsurance.bind(this);
    }
	
	componentWillMount() {
		Modal.setAppElement('body');
	}
	
	componentDidMount() {
		this.props.toggleMenu(false);
		const { location } = this.props;

		const query = qs.parse(location.search.replace('?', ''));
		const page = query && query.p ? parseInt(query.p, 10) : 1;
		const office = query && query.office ? parseInt(query.office, 10) : 0;
		const lender_id = query && query.lender ? parseInt(query.lender, 10) : 0;
		const loan_status = query && query.status ? query.status : '';

		this.setState({
			office,
			currentPage: page,
			location: location.pathname,
			lender_id,
			loan_status,
		});
		
		this.fetch(page, office, lender_id, loan_status);
	}

	fetch = async (pageNumber, office, lender_id, loan_status) => {
		try {
			this.props.startBlock();
			const rs = await httpRequest(`${baseURL}${userAPI}?page=${pageNumber}&pagesize=${pageSize}&q=customers&lender=${lender_id}&office=${office}&status=${loan_status}`, 'GET', true);
            const result = rs.result;
			this.setState({
				currentPage: pageNumber,
				totalPage: result.total,
				users: result.data,
				offices: result.offices,
			});
			this.props.stopBlock();
			this.props.notifyDone(false);
            window.scrollTo(0, 0);
		} catch (e) {
			console.log(e)
			this.props.stopBlock();
			this.props.doNotify({ message: 'could not load users', level: 'error' });
		}
	};

	onChange = pageNumber => {
		const { location } = this.props;
		const { lender_id, loan_status } = this.state;
		const query = qs.parse(location.search.replace('?', ''));
		if(query.office){
			this.props.history.push(`/users?lender=${lender_id}&office=${query.office}&p=${pageNumber}&status=${loan_status}`);
		} else {
			this.props.history.push(`/users?lender=${lender_id}&p=${pageNumber}&status=${loan_status}`);
		}
	};

	componentWillUpdate(nextProps, nextState) {
		const { lender_id } = nextState;
		const query = qs.parse(nextProps.location.search.replace('?', ''));
		const pageNumber = query && query.p ? parseInt(query.p, 10) : 1;
		const office = query && query.office ? parseInt(query.office, 10) : 0;
		const loan_status = query && query.status ? query.status : '';
		if(nextState.currentPage !== pageNumber || nextProps.location.pathname !== nextState.location || office !== nextState.office || loan_status !== nextState.loan_status){
			this.setState({
				office,
				location: nextProps.location.pathname,
				currentPage: pageNumber,
				loan_status,
			});

			this.fetch(pageNumber, office, lender_id, loan_status);
		} else if(nextProps.notifdone) {
			this.fetch(pageNumber, office, lender_id, loan_status);
		}
	}
	
    componentWillUnmount() {
		this.props.toggleMenu(true);
	}
	
    openModal = (customer, loan) => () => {
        document.body.className="pages modal-open";
        this.setState({ modalIsOpen: true, customer, loan });
    };
	
    openRepModal = (customer, loan) => () => {
        document.body.className="pages modal-open";
        this.setState({ repModalIsOpen: true, customer, loan });
    };

	openLenModal = customer => () => {
		const action = window.confirm("Do you want to transfer to another lending platform?") ? true : false;
		if(action){
			document.body.className="pages modal-open";
        	this.setState({ lenModalIsOpen: true, customer });
		}
	};
	
    openCoreBankModal = customer => () => {
        document.body.className="pages modal-open";
        this.setState({ coreBankModalIsOpen: true, customer });
    };

    closeModal = () => {
        this.setState({ modalIsOpen: false });
		document.body.className="pages";
	}

    closeRepModal = () => {
        this.setState({ repModalIsOpen: false, customer: null });
		document.body.className="pages";
		const { currentPage, office, lender_id, loan_status } = this.state;
		this.fetch(currentPage, office, lender_id, loan_status);
	}

    closeLenModal = () => {
        this.setState({ lenModalIsOpen: false, customer: null });
		document.body.className="pages";
		const { currentPage, office, lender_id, loan_status } = this.state;
		this.fetch(currentPage, office, lender_id, loan_status);
	}

    closeCoreBankModal = () => {
        this.setState({ coreBankModalIsOpen: false, customer: null });
		document.body.className="pages";
		const { currentPage, office, lender_id, loan_status } = this.state;
		this.fetch(currentPage, office, lender_id, loan_status);
	}

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};
	
	showProfile = (id) => () => this.props.doShowProfile({ show_profile: true, user_id: id });

	success = (message, _) => {
		this.props.stopBlock();
		this.notify('', message, 'success');
		const { currentPage, office, lender_id, loan_status } = this.state;
		this.fetch(currentPage, office, lender_id, loan_status);
	}

    doVerifyLoan = (id, userID) => () => {
		this.props.startBlock();
		this.props.verifyLoan(id, userID)
			.then((response) => {
				this.props.setLoanAnalytics(response.loan_details);
				this.success("Loan verified!", response);
			})
			.catch(error => {
				console.log(error);
				this.props.stopBlock();
				const message = error.message || 'could not verify loan';
				this.notify('', message, 'error');
			});
	};
	
	AES_128_ENCRYPT = (rawData) => {
		let key = ENC_Key;
		let iv  = ENC_Vector;
		key = utf8.parse(key);
		iv = utf8.parse(iv);
		const encryptData = aes.encrypt(utf8.parse(rawData), key,
			{
				keySize: 128 / 8,
				iv: iv,
				mode: mode.CBC,
				padding: pkcs7
			});
		return encryptData;
	};

	onChangeReason = e => {
		this.setState({ reason: e.target.value });
	};

	doHide = () => {
		this.setState({ visible: false, editVisible: false, loan_id: null, reason: '' });
	};

	showDeclineReason = (status, loan) => {
		this.setState({ visible: status, loan_id: loan.id, reason: '' });
	};

	showEditLoan = (status, loan) => {
		this.setState({ editVisible: status, loan_id: loan.id, reason: '' });
	};
	
	doDeclineLoan = (e, loan) => {
		e.preventDefault();
		const { reason } = this.state;
		if(reason === '') {
			this.notify('', 'you need to add your reason for declining the loan', 'error');
			return;
		}
		const action = window.confirm("Do you want to decline this loan?") ? true : false;
		if(loan && action){
			const { user } = this.props;
			this.setState({ declining: true });
			this.props.startBlock();
			const data = { staff: user.id, id: loan.id, reason };

			this.props.declineLoan(data)
				.then(response => {
					this.setState({ declining: false, visible: false, loan_id: null, reason: '' });
					this.props.setLoanAnalytics(response.loan_details);
					this.success("Loan declined!");
				})
				.catch(error => {
					this.setState({ declining: false });
					this.props.stopBlock();
					const message = error.message || 'could not decline loan';
					this.notify('', message, 'error');
				});
		}
	}

	verifyPayment = loan => () => {
		this.props.startBlock();
		const { user } = this.props;
		if(loan){
			const disbursement = loan.disbursement ? JSON.parse(loan.disbursement) : null;
			if(disbursement){
				const transRef = this.AES_128_ENCRYPT(disbursement.data.transRef);
				const dfBody = {
					transRef: `${transRef}`,
				};
				
				const d = new Date();
				const requestId = d.getTime();
				const apiPaymentHash = sha512(paymentApiKey + requestId + paymentApiToken);
	
				let dd = d.getDate();
				let mm = d.getMonth()+1; //January is 0!
				const yyyy = d.getFullYear();
				if(dd<10){
					dd='0'+dd;
				} 
				if(mm<10){
					mm='0'+mm;
				} 
				const hours = d.getUTCHours();
				const minutes = d.getUTCMinutes();
				const seconds = d.getUTCSeconds();
				const requestTS = yyyy+'-'+mm+'-'+dd+'T'+hours+':'+minutes+':'+seconds+'+000000';
	
				const datum = { id: loan.id, paymentMerchantId, paymentApiKey, requestId, requestTS, apiPaymentHash: `${apiPaymentHash}`, dfBody, staff: user.id, };
				this.props.verifyMoneyPaid(datum)
					.then(response => {
						const retLoan = response.loan;
						const paymentStatus = retLoan.payment_status ? JSON.parse(retLoan.payment_status) : null;
						if(paymentStatus && paymentStatus.status === 'success'){
							this.props.setLoanAnalytics(response.loan_details);
							this.success("Payment Verified!");
						}
						else {
							this.props.stopBlock();
							const message = paymentStatus.data.responseDescription;
							this.notify('', message, 'error');
							const { currentPage, office, lender_id, loan_status } = this.state;
							this.fetch(currentPage, office, lender_id, loan_status);
						}
					})
					.catch(error => {
						this.props.stopBlock();
						const message = error.message || 'could not verify payment';
						this.notify('', message, 'error');
					});
			}
			else {
				this.props.stopBlock();
				const message = 'loan amount not disbursed yet!';
				this.notify('', message, 'error');
			}
		}
		else {
			this.props.stopBlock();
			const message = 'loan not found!';
			this.notify('', message, 'error');
		}
	}

	disburse = loan => () => {
		this.props.startBlock();
		if(loan){
			const user = loan.user;

			let fromBank = bankCode;
			let debitAccount = bankAccount;
			let toBank = liveStatus ? loan.accountno.bank_code : '058';
			let creditAccount = liveStatus ? loan.accountno.account_number : '*************';
			let narration = 'Loan Disbursement';
			let amount = loan.amount;
			let beneficiaryEmail = user.email;
			
			const d = new Date();
			const requestId = d.getTime();
			const randomnumber = Math.floor(Math.random()*1101233);
			let transRef = randomnumber;
			const apiPaymentHash = sha512(paymentApiKey + requestId + paymentApiToken);
			
			toBank = this.AES_128_ENCRYPT(toBank);
			creditAccount = this.AES_128_ENCRYPT(creditAccount);
			narration = this.AES_128_ENCRYPT(narration);
			amount = this.AES_128_ENCRYPT(amount);
			transRef = this.AES_128_ENCRYPT(transRef);
			fromBank = this.AES_128_ENCRYPT(fromBank);
			debitAccount = this.AES_128_ENCRYPT(debitAccount);
			beneficiaryEmail = this.AES_128_ENCRYPT(beneficiaryEmail);

			let dd = d.getDate();
			let mm = d.getMonth()+1; //January is 0!
			const yyyy = d.getFullYear();
			if(dd<10){
				dd='0'+dd;
			} 
			if(mm<10){
				mm='0'+mm;
			} 
			const hours = d.getUTCHours();
			const minutes = d.getUTCMinutes();
			const seconds = d.getUTCSeconds();
			const requestTS = yyyy+'-'+mm+'-'+dd+'T'+hours+':'+minutes+':'+seconds+'+000000';

			const dfBody = {
				toBank: `${toBank}`,
				creditAccount: `${creditAccount}`,
				narration: `${narration}`,
				amount: `${amount}`,
				transRef: `${transRef}`,
				fromBank: `${fromBank}`,
				debitAccount: `${debitAccount}`,
				beneficiaryEmail: `${beneficiaryEmail}`,
			};

			const datum = { id: loan.id, paymentMerchantId, paymentApiKey, requestId, requestTS, apiPaymentHash: `${apiPaymentHash}`, dfBody };
			this.props.disburseMoney(datum)
				.then(response => {
					const retLoan = response.loan;
					const disbursement = retLoan.disbursement ? JSON.parse(retLoan.disbursement) : null;
					if(disbursement && disbursement.status === 'success'){
						this.verifyPayment(loan);
						this.props.setLoanAnalytics(response.loan_details);
						this.success("Money Disbursed!");
					}
					else {
						this.props.stopBlock();
						const message = 'could not disburse loan';
						this.notify('', message, 'error');
						const { currentPage, office, lender_id, loan_status } = this.state;
						this.fetch(currentPage, office, lender_id, loan_status);
					}
				})
				.catch(error => {
					this.props.stopBlock();
					const message = error.message || 'could not disburse loan';
					this.notify('', message, 'error');
				});
		}
		else {
			this.props.stopBlock();
			const message = 'loan not found!';
			this.notify('', message, 'error');
		}
	}

	approveLoan = (loan, staffID) => async () => {
		if(loan){
			try {
				this.props.startBlock();
				const is_topup = loan.is_topup && loan.is_topup !== '' && loan.is_topup === 1 ? 1 : 0;
				const url = `${baseURL}${loanAPI}/balance/${loan.id}?is_topup=${is_topup}`;
				const rs = await httpRequest(url, 'GET', true);
				this.props.stopBlock();
				if (rs && rs.result) {
					let action;
					if(parseFloat(rs.result.disburse_amount) > parseFloat(loan.disburse_amount) && is_topup === 1){
						// tell him the disburse amount has changed
						const amount = currency(rs.result.disburse_amount)
						action = window.confirm(`The new disburse amount is ${amount}. Will you like to approve this loan?`) ? true : false;
					} else {
						action = window.confirm("Will you like to approve this loan?") ? true : false;
					}
	
					if(action){
						this.props.startBlock();
						const { lender } = this.props;
						const user = loan.user;
						// const account = loan.accountno;
						const post = { id: loan.id, staff: staffID, platform: loan.platform };
						let data = null;
						
						const d = new Date();
						const requestId = d.getTime();
						const apiHash = sha512(apiKey + requestId + apiToken);

						let dd = d.getDate();
						let mm = d.getMonth()+1; //January is 0!
						const yyyy = d.getFullYear();
						if(dd<10){
							dd='0'+dd;
						} 
						if(mm<10){
							mm='0'+mm;
						} 
						const hours = d.getUTCHours();
						const minutes = d.getUTCMinutes();
						const seconds = d.getUTCSeconds();
						const requestTS = yyyy+'-'+mm+'-'+dd+'T'+hours+':'+minutes+':'+seconds+'+000000';

						if(loan.platform === 'remita'){
							if(!user.customer_id){
								this.props.stopBlock();
								const message = 'could not approve loan; customer id not available';
								this.notify('', message, 'error');
								return false;
							}

							const authCheck = await httpRequest(`${baseURL}${loanAPI}/auth-check/${loan.auth_code}/${loan.id}`, 'GET', true);

							const authorisationCode = authCheck.auth_code;
							const authorization = `remitaConsumerKey=${apiKey}, remitaConsumerToken=${apiHash}`;

							const body = {
								customerId: user.customer_id,
								authorisationCode,
								authorisationChannel: "USSD",
								phoneNumber: user.phone,
								accountNumber: **********,
								currency: "NGN",
								loanAmount: loan.amount,
								collectionAmount: loan.monthly_deduction,
								totalCollectionAmount: loan.total_deduction,
								dateOfDisbursement: moment().format('DD-MM-YYYY HH:mm:ss+0000'),
								dateOfCollection: moment().format('DD-MM-YYYY HH:mm:ss+0000'),
								numberOfRepayments: loan.tenure,
								bankCode: '011'
							};
							data = { authorization, body };
						}

						const datum = {
							...data,
							...post,
							merchantId,
							apiKey,
							requestId,
							requestTS,
							loan_balance: rs.result.amount,
							disburse_amount: rs.result.disburse_amount,
						};
						console.log(datum)

						this.props.approveMoney(datum)
							.then(response => {
								if (response.fails && parseInt(response.fails, 10) > 0) {
									this.props.stopBlock();
									const message = `could not approve loan, ${response.fails} loan approvals failed`;
									this.notify('', message, 'error');
								} else {
									this.props.setLoanAnalytics(response.loan_details);
									this.success("Loan approved!", response);
									if(lender.auto_disburse === 1){
										this.disburse(loan);
									}
								}
							})
							.catch(error => {
								this.props.stopBlock();
								const message = error.message || 'could not approve loan';
								this.notify('', message, 'error');
							});
					}
				}
			} catch (error) {
				this.props.stopBlock();
				const message = error.message || 'could not get balance';
				this.notify('', message, 'error');
			}
		}
		else {
			this.props.stopBlock();
			const message = 'loan not found!';
			this.notify('', message, 'error');
		}
	}

	bypass = id => () => {
		const action = window.confirm("Have you already disbursed this loan?") ? true : false;
		if(action){
			this.props.startBlock();
			const data = { staff: this.props.user.id, id };

			this.props.bypassDisburse(data)
				.then(response => {
					this.props.setLoanAnalytics(response.loan_details);
					this.success("Loan disbursed!", response);
				})
				.catch(error => {
					this.props.stopBlock();
					const message = error.message || 'error please try again';
					this.notify('', message, 'error');
				});
		}
	}

	doSendMail = id => () => {
		const action = window.confirm("Do you want to generate offer letter?") ? true : false;
		if(action){
			this.props.startBlock();
			const data = { staff: this.props.user.id, id };

			this.props.sendMail(data)
				.then(response => {
					this.success("Loan offer letter generated!", response);
					window.location.reload(true);
				})
				.catch(error => {
					this.props.stopBlock();
					const message = error.message || 'could not generate offer letter!';
					this.notify('', message, 'error');
				});
		}
	}

	getCustomerId = async user => {
		const remitaData = { phone: user.phone };

		const rs = await  httpRequest(`${baseURL}${remitaLoanHistoryRequestAPI}?fetch=customer_id`, 'POST', true, remitaData);
		return rs.result;
	};

	doTransfer = loan => async () => {
		const action = window.confirm("Do you want to transfer this loan to remita?") ? true : false;
		if(action){
			const user = loan.user;
			const account = loan.accountno;

			const d = new Date();
			const requestId = d.getTime();
			const apiHash = sha512(apiKey + requestId + apiToken);

			let dd = d.getDate();
			let mm = d.getMonth()+1; //January is 0!
			const yyyy = d.getFullYear();
			if(dd<10){
				dd='0'+dd;
			} 
			if(mm<10){
				mm='0'+mm;
			} 
			const hours = d.getUTCHours();
			const minutes = d.getUTCMinutes();
			const seconds = d.getUTCSeconds();
			const requestTS = yyyy+'-'+mm+'-'+dd+'T'+hours+':'+minutes+':'+seconds+'+000000';
			
			let customer_id = 0;
			let auth_code = 0;
			let loan_amount = 0;
			let total_deduction = 0;
			let loan_tenure = 0;
			let remita_id = null;

			if(loan.platform === 'ippis'){
				try {
					const _rs = await this.getCustomerId(user);
					customer_id = _rs.customerId;
					auth_code = _rs.authCode;
					
					loan_amount = _rs.loan_amount;
					total_deduction = _rs.total_deduction;
					loan_tenure = _rs.loan_tenure;
					remita_id = _rs.remitaId;
				} catch (error) {
					this.props.stopBlock();
					const message = error.message || 'customer loan profile not available on remita!';
					this.notify('', message, 'error');
					return false;
				}

				if(customer_id === 0 && auth_code === 0 && loan_amount === 0 && total_deduction === 0 && loan_tenure === 0) {
					this.props.stopBlock();
					const message = 'customer loan profile not available on remita!';
					this.notify('', message, 'error');
					return false;
				}

				const post = { id: loan.id, staff: this.props.user.id, platform: 'remita', auth_code, customer_id };

				const authorisationCode = auth_code;
				const authorization = `remitaConsumerKey=${apiKey}, remitaConsumerToken=${apiHash}`;

				const body = {
					customerId: customer_id,
					authorisationCode,
					authorisationChannel: "USSD",
					phoneNumber: user.phone,
					accountNumber: account.account_number,
					currency: "NGN",
					loanAmount: loan_amount,
					collectionAmount: loan.monthly_deduction,
					totalCollectionAmount: total_deduction,
					dateOfDisbursement: moment().format('DD-MM-YYYY HH:mm:ss+0000'),
					dateOfCollection: moment().format('DD-MM-YYYY HH:mm:ss+0000'),
					numberOfRepayments: loan_tenure
				};
				const data = { authorization, body };
				const datum = { ...data, ...post, merchantId, apiKey, requestId, requestTS, remita_id };

				try {
					this.props.startBlock();
					const rs = await httpRequest(`${baseURL}${loanAPI}/transfer/${loan.id}`, 'POST', true, datum);
					this.success("Loan transferred to remita!", rs);
				} catch (e) {
					console.log(e.message);
					this.props.stopBlock();
					const message = e.message || 'could not transfer loan to remita!';
					this.notify('', message, 'error');
				}

			} else {
				this.props.stopBlock();
				const message = 'could not transfer loan to remita, loan is already handled by remita';
				this.notify('', message, 'error');
			}
		}
	}

	onSelectLoanStatus = e => {
		const loan_status = e.target.value;
		$(e.currentTarget).blur();
		const { lender_id, currentPage, office } = this.state;
		this.props.history.push(`/users?lender=${lender_id}&office=${office}&p=${currentPage}&status=${loan_status}`);
	};
	
	doEnableUser = id => async () => {
		const action = window.confirm("Do you want to enable this customer?") ? true : false;
		if(action){
			try {
				this.props.startBlock();
				const { user } = this.props;
				const rs = await httpRequest(`${baseURL}${userAPI}/enable/${id}`, 'POST', true, {staff: user.id});
				this.props.stopBlock();
				this.success("customer has been enabled!", rs);
			} catch (e) {
				this.props.stopBlock();
				const message = e.message || 'could not enable customer!';
				this.notify('', message, 'error');
			}
		}
	}
	
	doDisableUser = id => async () => {
		const action = window.confirm("Do you want to ban this customer?") ? true : false;
		if(action){
			try {
				this.props.startBlock();
				const { user } = this.props;
				const rs = await httpRequest(`${baseURL}${userAPI}/disable/${id}`, 'POST', true, {staff: user.id});
				this.props.stopBlock();
				this.success("customer has been banned from the application!", rs);
			} catch (e) {
				this.props.stopBlock();
				const message = e.message || 'could not disable customer!';
				this.notify('', message, 'error');
			}
		}
	}
	
	doMultiFund = (loan, status) => async () => {
		const _status = status === 1 ? 'enable' : 'cancel';
		const __status = status === 1 ? 'enabled' : 'cancelled';
		const action = window.confirm(`Do you want to ${_status} multi funding?`) ? true : false;
		if(action) {
			try {
				this.props.startBlock();
				const { user } = this.props;
				const data = { staff: user.id, status };
				const rs = await httpRequest(`${baseURL}${loanAPI}/multi-fund/${loan.id}`, 'POST', true, data);
				this.props.stopBlock();
				this.props.setLoanAnalytics(rs.loan_details);
				this.success(`multi fund ${__status} for loan!`, rs);
			} catch (e) {
				this.props.stopBlock();
				const message = e.message || `could not ${_status} multi funding!`;
				this.notify('', message, 'error');
			}
		}
	}

	exportReport = async e => {
		e.preventDefault();
		try {
			this.setState({ exporting: true });
			const url = `${baseURL}${reportAPI}/export/users-no-loan`;
			const rs = await httpRequest(url, 'GET', true);
			if(rs.file) {
				window.open(rs.file, '_blank');
				this.setState({ exporting: false });
				this.notify('', 'user report has been exported', 'success');
			} else {
				this.notify('', rs.message || 'could not export user report', 'success');
				this.setState({ exporting: false });
			}
		} catch(error) {
			this.setState({ exporting: false });
			const message = error.message || 'could not export user report';
			this.notify('', message, 'error');
		}
	};
	
	doSnoozeUser = id => async () => {
		const action = window.confirm("Do you want to snooze this customer?") ? true : false;
		if(action){
			try {
				this.props.startBlock();
				const { user, settings } = this.props;
				const details = {staff_id: user.id};
				const rs = await httpRequest(`${baseURL}${userAPI}/snooze/${id}`, 'PUT', true, details);
				this.props.stopBlock();
				this.success(`customer has been snoozed and cannot take a loan until after ${settings.snooze_period}days!`, rs);
			} catch (e) {
				this.props.stopBlock();
				const message = e.message || 'could not snooze customer!';
				this.notify('', message, 'error');
			}
		}
	}
	
	removeSnooze = id => async () => {
		this.props.startBlock();
		try {
			const { user } = this.props;
			const details = {staff_id: user.id};
			const rs = await httpRequest(`${baseURL}${userAPI}/unsnooze/${id}`, 'PUT', true, details);
			this.props.stopBlock();
			this.success('customer snooze removed', rs);
		} catch (e) {
			this.props.stopBlock();
			const message = e.message || 'could not remove snooze customer!';
			this.notify('', message, 'error');
		}
	}
	
	doLiquidateLoan = loan => async () => {
		document.body.className="pages modal-open";
		this.setState({ liqModalIsOpen: true, loan });
	}

	doSendConsentSMS = loan => () => {
		const action = window.confirm("Do you want to send the sms?") ? true : false;
		if(action){
			this.props.startBlock();
			const data = { id: loan.id };

			this.props.consentSMS(data)
				.then(response => {
					this.success("Consent SMS sent!", response);
				})
				.catch(error => {
					this.props.stopBlock();
					const message = error.message || 'could not send sms!';
					this.notify('', message, 'error');
				});
		}
	}

	closeLiqModal = () => {
        this.setState({ liqModalIsOpen: false, loan: null });
		document.body.className="pages";
		const { currentPage, office, lender_id, loan_status } = this.state;
		this.fetch(currentPage, office, lender_id, loan_status);
	}

	onSaveLoanInsurance = () => {
		this.success("Loan Saved");
	}
	
    render() {
        const { user, lenders } = this.props;
		const { modalIsOpen, repModalIsOpen, currentPage, totalPage, customer, loan, lenModalIsOpen, coreBankModalIsOpen, users, office, offices, lender_id, reason, declining, visible, editVisible, loan_id, exporting, liqModalIsOpen } = this.state;
        
        return (
            <div>
                <BreadCrumbs url="/dashboard" />
                <div className="content-i">
                    <div className="content-box">
                        <div className="element-wrapper">
							<div className="element-actions">
								{exporting ? (
									<a className="btn btn-primary btn-sm btn-export-sm pointer"><img src={loading} alt=""/></a>
								) : (
									<a className="btn btn-primary btn-sm btn-export-sm pointer" role="button" tabIndex="0" onClick={this.exportReport}><i className="os-icon os-icon-download-cloud"/><span>Export Users With No Loan</span></a>
								)}
							</div>
                            <h6 className="element-header">Customers</h6>
							<div className="control-header">
								<div className="row align-items-center">
									<div className="col-12">
										<div className="form-inline row">
											<div className="col form-group" style={{width:'110px'}}>
												<label className="text-right" htmlFor="">Filter By:</label>
											</div>
											<label className="col-sm-1 col-form-label text-right justify-content-end" htmlFor="">Office</label>
											<div className="col gen_input">
												<select name="office" className="form-control" onChange={this.onSelect} value={office}>
													<option value="0">All Offices</option>
													{offices.map(o => <option key={o.id} value={o.id}>{o.name}</option>)}
												</select>
											</div>
											<label className="col-sm-1 col-form-label text-right justify-content-end ml-2" htmlFor="">Status</label>
											<div className="col-sm-2">
												<select className="form-control" onChange={this.onSelectLoanStatus}>
													<option value="">All</option>
													<option value="no-loans">No Loans</option>
													<option value="not-verified">Not Verified</option>
													<option value="verified">Verified/Not Approved</option>
													<option value="approved">Approved/Not Disbursed</option>
													<option value="disbursed">Disbursed</option>
													<option value="admin-created">Admin Created Customers</option>
												</select>
											</div>
											<label className="col-sm-1 col-form-label text-right justify-content-end" htmlFor="">Lender</label>
											<div className="col">
												<select name="lender" className="form-control" value={lender_id} disabled="disabled">
													<option value="0">All Lenders</option>
													{lenders.map(o => <option key={o.id} value={o.id}>{o.name}</option>)}
												</select>
											</div>
										</div>
									</div>
								</div>
							</div>
                            <div className="element-box" style={{padding: '1.0rem 0.4rem'}}>
                                <div className="table-responsive" style={{overflowX: 'scroll',}}>
                                    <table className="table table-striped table-lightfont">
                                        <thead>
                                            <tr>
												<th/>
                                                <th>Name of Employee</th>
                                                <th>Core Bank ID</th>
                                                <th>Merchant</th>
                                                <th>IPPIS / Customer ID</th>
                                                <th>Loan Status</th>
                                                <th>Disburse</th>
                                                <th>Loan Period</th>
                                                <th>Tenure</th>
												<th>Loan Consent</th>
                                                <th>Request Date</th>
                                                <th>&nbsp;</th>
                                            </tr>
                                        </thead>
                                        <tbody>
											{users.map((customer, i) => (
												<OfficeData
													key={i}
													customer={customer}
													loan={customer.loan}
													user={user}
													category="list"
													showProfile={this.showProfile}
													doVerifyLoan={this.doVerifyLoan}
													approveLoan={this.approveLoan}
													doDeclineLoan={this.doDeclineLoan}
													verifyPayment={this.verifyPayment}
													disburse={this.disburse}
													bypass={this.bypass}
													doSendMail={this.doSendMail}
													openModal={this.openModal}
													openRepaymentModal={this.openRepModal}
													switchLender={this.openLenModal}
													openCoreBankModal={this.openCoreBankModal}
													transfer={this.doTransfer}
													enableUser={this.doEnableUser}
													disableUser={this.doDisableUser}
													doMultiFund={this.doMultiFund}
													visible={visible}
													editVisible={editVisible}
													loanID={loan_id}
													reason={reason}
													declining={declining}
													showDeclineReason={this.showDeclineReason}
													showEditLoan={this.showEditLoan}
													doHide={this.doHide}
													onChangeReason={this.onChangeReason}
													doSnoozeUser={this.doSnoozeUser}
													removeSnooze={this.removeSnooze}
													liquidateLoan={this.doLiquidateLoan}
													sendConsentSMS={this.doSendConsentSMS}
													onSaveLoanInsurance={this.onSaveLoanInsurance}
												/>
											))}
                                        </tbody>
                                    </table>
                                </div>
								<div className="pagination pagination-center mt-4">
									<Pagination
										current={currentPage}
										pageSize={pageSize}
										total={totalPage}
										showTotal={total => `Total ${total} users`}
										itemRender={itemRender}
										onChange={this.onChange}
									/>
								</div>
                            </div>
                        </div>
                    </div>
					<Modal
						isOpen={liqModalIsOpen}
						onRequestClose={this.closeLiqModal}
						contentLabel="Liquidate Loan"
						shouldCloseOnOverlayClick={false}
						overlayClassName="modal-dialog modal-sm sd"
						className="modal-content"
						portalClassName="ReactModalPortall"
						bodyOpenClassName="ReactModal__Body--openl"
					>
						<LiquidateLoan closeModal={this.closeLiqModal} loan={loan}/>
					</Modal>
					{liqModalIsOpen? (<div className="modal-backdrop fade show"/>) : ''}
					
					<Modal
                        isOpen={coreBankModalIsOpen}
                        onRequestClose={this.closeCoreBankModal}
                        contentLabel="Core Banking ID"
                        shouldCloseOnOverlayClick={false}
                        overlayClassName="modal-dialog modal-sm"
                        className="modal-content"
						portalClassName="ReactModalPortalc"
						bodyOpenClassName="ReactModal__Body--openc"
                    >
                        {customer && (
							<CoreBankID
								closeModal={() => this.closeCoreBankModal()}
								customer={customer}
							/>
						)}
                    </Modal>
					{coreBankModalIsOpen? (<div className="modal-backdrop fade show"/>) : ''}

                    <Modal
                        isOpen={modalIsOpen}
                        onRequestClose={this.closeModal}
                        contentLabel="Loan Profile"
                        shouldCloseOnOverlayClick={false}
                        overlayClassName="modal-dialog modal-lg modal-extra-lg"
                        className="modal-content"
						portalClassName="ReactModalPortalv"
						bodyOpenClassName="ReactModal__Body--openv"
                    >
                        <LoanProfile
                            closeModal={() => this.closeModal()}
                            user={customer}
                            loan={loan}
                        />
                    </Modal>
					{modalIsOpen? (<div className="modal-backdrop fade show"/>) : ''}

                    <Modal
                        isOpen={repModalIsOpen}
                        onRequestClose={this.closeRepModal}
                        contentLabel="Loan Repayment"
                        shouldCloseOnOverlayClick={false}
                        overlayClassName="modal-dialog modal-sm"
                        className="modal-content"
						portalClassName="ReactModalPortalr"
						bodyOpenClassName="ReactModal__Body--openr"
                    >
                        <LoanRepayment
                            closeModal={() => this.closeRepModal()}
                            customer={customer}
                            loan={loan}
                        />
                    </Modal>
					{repModalIsOpen? (<div className="modal-backdrop fade show"/>) : ''}

					<Modal
                        isOpen={lenModalIsOpen}
                        onRequestClose={this.closeLenModal}
                        contentLabel="Transfer To Lender"
                        shouldCloseOnOverlayClick={false}
                        overlayClassName="modal-dialog modal-sm"
                        className="modal-content"
                    >
                        <TransferToLender
                            closeModal={() => this.closeLenModal()}
                            customer={customer}
                        />
                    </Modal>
					{lenModalIsOpen? (<div className="modal-backdrop fade show"/>) : ''}
                </div>
            </div>
        );
    }
}

const mapStateToProps = (state) => {
    return {
        user: state.user.user,
		customers: state.user.customers,
		lender: state.lender.profile,
		lenders: state.lender.list,
    }
}

const doShowProfile = (data) => {
	return { type: SHOW_PROFILE, data };
};

const mapDispatchToProps = (dispatch) => {
	return bindActionCreators({ approveMoney, verifyLoan, startBlock, stopBlock, setLoanAnalytics, declineLoan, doShowProfile, disburseMoney, verifyMoneyPaid, bypassDisburse, toggleMenu, sendMail, doNotify, notifyDone, consentSMS }, dispatch);
};

export default connect(mapStateToProps, mapDispatchToProps)(Users);
