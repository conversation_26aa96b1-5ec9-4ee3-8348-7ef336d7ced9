<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\V1\TransactionController;
use Illuminate\Console\Command;

class CheckRepayment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:repayments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check repayments from remita';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $transactions = app(TransactionController::class)->checkPayments();
        $this->info(json_encode($transactions));
    }
}
