<?php

namespace Database\Seeders;

use App\Models\Lender;
use App\Models\Loan;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateCommissions extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            DB::beginTransaction();

            $loans = Loan::withTrashed()->get();
            foreach ($loans as $l){
                $loan = Loan::withTrashed()->where('id', $l->id)->first();
                if($loan) {
                    $lender = Lender::where('id', $loan->lender_id)->first();

                    $monthly_commission = $lender->commission > 0 ? ($lender->commission / 100) * $loan->monthly_interest : 0.00;

                    $loan->commission_percent = $lender->commission;
                    $loan->interest_percent = 100 - $lender->commission;
                    $loan->monthly_commission = round($monthly_commission, 2);
                    $loan->total_commission = round($monthly_commission * $loan->tenure, 2);
                    $loan->save();
                }
            }

            DB::commit();
        } catch (\Exception $e){
            DB::rollBack();
            Log::error($e->getMessage());
        }
    }
}
