/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import moment from 'moment';
import { connect } from 'react-redux';
import Tooltip from 'antd/lib/tooltip';

import { httpRequest, baseURL, userAPI } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import { doNotify } from '../actions/general';
import { notifyDone } from '../actions/general';

class List extends Component {
	constructor(props, context) {
        super(props, context);
        this.state = {
            users: [],
        }
		this.removeUser = this.removeUser.bind(this);
    }

    componentDidMount() {
        this.fetch();
    }
    
    fetch = async () => {
		const { lender } = this.props;
		try {
			this.props.startBlock();
			const rs = await httpRequest(`${baseURL}${userAPI}?q=admin&lender=${lender.id}`, 'GET', true);
            this.setState({ users: rs.users });
            this.props.notifyDone(false);
			this.props.stopBlock();
		} catch (e) {
			this.props.stopBlock();
			this.props.doNotify({ message: 'could not load users', level: 'error' });
		}
    };
    
    componentWillUpdate(nextProps, nextState) {
        if(nextProps.notifdone) {
            this.fetch();
        }
    }

	enableUser = id => {
		const action = window.confirm('Are you sure you want to enable user?') ? true : false;
		if (action) {
			const { user } = this.props;
            this.props.startBlock();
			httpRequest(`${baseURL}${userAPI}/enable/${id}`, 'POST', true, {user_id: user.id})
                .then(_ => {
                    this.props.stopBlock();
                    this.props.doNotify({ message: 'user enabled!', level: 'success' });
                    this.fetch();
                })
                .catch(error => {
                    this.props.stopBlock();
                    const message = error.message || 'user could not be enabled';
                    this.props.doNotify({ message: message, level: 'error' });
                });
		}
	};

	removeUser = id => {
		const action = window.confirm('Are you sure you want to delete admin user?') ? true : false;
		if (action) {
            const { user } = this.props;
            this.props.startBlock();
			httpRequest(`${baseURL}${userAPI}/${id}`, 'DELETE', true, {user_id: user.id})
                .then(_ => {
                    this.props.stopBlock();
                    this.props.doNotify({ message: 'user removed', level: 'success' });
                    this.fetch();
                })
                .catch(error => {
                    this.props.stopBlock();
                    const message = error.message || 'user could not be removed';
                    this.props.doNotify({ message: message, level: 'error' });
                });
		}
	};

	render() {
		const { users } = this.state;
		return (
			<div className="element-wrapper">
				<h6 className="element-header">List of Users</h6>
				<div className="element-box">
					<div className="table-responsive" style={{overflowX: 'scroll'}}>
						<table className="table table-striped">
							<thead>
								<tr>
									<th>Name</th>
									<th>Email</th>
									<th>Phone</th>
									<th>Enabled</th>
									<th>Role</th>
									<th>Date</th>
									<th className="text-center"></th>
								</tr>
							</thead>
							<tbody>
								{users.map((u, i) => {
									return (
										<tr key={i}>
											<td>{u.name}</td>
											<td>
                                                <span>{u.email}</span><br/>
                                                <span className="badge badge-dark badge-pill">{`username: ${u.username}`}</span>
                                            </td>
											<td>{u.phone}</td>
											<td>
												{u.enabled === 1 && (u.deleted_at === null || u.deleted_at === '') ? (
													<span className="elabel elabel-success">Active</span>
												) : (
													<span className="elabel elabel-danger">Not Active</span>
												)}
											</td>
											<td>{u.role_category}</td>
											<td>{moment(u.created_at).format('DD.MMM.YYYY')}</td>
											<td className="row-actions">
												<a className="text-info cursor" onClick={() => this.props.editUser(u)}>
													<i className="os-icon os-icon-edit" />
												</a>
												{u.role_name !== 'super' && (
													u.enabled === 1 && (u.deleted_at === null || u.deleted_at === '') ? (
														<Tooltip title="Disable User">
															<a className="text-danger cursor" onClick={() => this.removeUser(u.id)}>
																<i className="os-icon os-icon-database-remove" />
															</a>
														</Tooltip>
													) : (
														<Tooltip title="Enable User">
															<a
																className="text-primary cursor"
																onClick={() => this.enableUser(u.id)}>
																<i className="os-icon os-icon-unlock" />
															</a>
														</Tooltip>
													)
												)}
											</td>
										</tr>
									);
								})}
							</tbody>
						</table>
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
    return {
        user: state.user.user,
        notifdone: state.general.notifdone,
        lender: state.lender.profile,
    }
};

export default connect(
	mapStateToProps,
	{ startBlock, stopBlock, doNotify, notifyDone },
)(List);
