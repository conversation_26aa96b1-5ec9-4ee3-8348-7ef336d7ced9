<?php

namespace Database\Seeders;

use App\Models\Wallet;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AddDebits extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::beginTransaction();
        try {
            $results = [];
            $wallets = Wallet::where('category', 'repay_loan')->get();
            foreach ($wallets as $item) {
                $count = Wallet::where('transaction_id', $item->transaction_id)->where('loan_id', $item->loan_id)->count();
                if($count == 2) {
                    // debit user account
                    $wallet = Wallet::create([
                        'user_id' => $item->user_id,
                        'loan_id' => $item->loan_id,
                        'category' => 'repay_loan',
                        'type' => 'debit',
                        'amount' => $item->amount * -1,
                        'transaction_id' => $item->transaction_id,
                        'approved' => 1,
                        'approved_by' => 1,
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $item->lender_id,
                        'is_lender' => 0,
                        'payment_date' => $item->payment_date,
                        'paid' => 1,
                    ]);

                    $results[] = $wallet->id;
                }
            }

            DB::commit();

            info(json_encode($results));
        } catch (\Exception $e) {
            DB::rollBack();
            error_log($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
        }
    }
}
