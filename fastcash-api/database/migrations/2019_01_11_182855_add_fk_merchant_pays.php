<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddFkMerchantPays extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('merchant_pays', function (Blueprint $table) {
            $table->bigInteger('loan_id')->unsigned()->change();
            $table->integer('merchant_id')->unsigned()->change();

            $table->foreign('loan_id')->references('id')->on('loans')->onDelete('cascade');
            $table->foreign('merchant_id')->references('id')->on('merchants')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('merchant_pays', function (Blueprint $table) {
            $table->dropForeign(['loan_id', 'merchant_id']);
        });
    }
}
