import React, { Component } from 'react';
import Dropzone from 'react-dropzone'

class UploadRemita extends Component {
    render() {
        const { error, onDrop, onOpenDropzone, onChangeType, file, disabled, schedule, type, file_type, report } = this.props;
        const status = file && (file.status === 1 ? `<span class="elabel elabel-success">done</span>`:'<span class="elabel elabel-danger">error</span>');

        return (
            <div className={`element-box ${type ? 'm-0 dp-padd' : 'mt-3'}`}>
                {error !== '' && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                <div className="panel panel-default">
                    <div className="panel-body extra-info">
                        <Dropzone className={`dropzone dz-clickable${type && type === 'slim' ? ' dp-slim':''}`} onDrop={onDrop} onClick={onOpenDropzone} disabled={disabled && schedule}>
                            <div className="dz-message">
                                <div>
                                    <h6 style={{fontSize: 14}}>Drop files here or click to upload.</h6>
                                    {file && (
                                        <div className="text-muted text-left">
                                            <p style={{ marginBottom: '2px' }} dangerouslySetInnerHTML={{__html: `${file.name} - ${status}`}}/>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </Dropzone>
                        {report==='bank' && (
                            <select className="form-control-sm" onChange={onChangeType} value={file_type}>
                                <option value="">Select bank</option>
                                <option value="cmfb">Online</option>
                                <option value="bank_one">Bank One</option>
                            </select>
                        )}
                    </div>
                </div>
            </div>
        );
    }
}

export default UploadRemita;