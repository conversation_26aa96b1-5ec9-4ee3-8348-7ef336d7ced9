<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateReferenceTransaction extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->string('repayment_reference')->nullable()->after('reference');
            $table->date('repayment_date')->nullable()->after('repayment_reference');
            $table->bigInteger('remita_transaction_id')->unsigned()->nullable()->after('repayment_date');
            $table->bigInteger('uploaded_by')->unsigned()->nullable()->after('repayment_date');

            $table->foreign('remita_transaction_id')->references('id')->on('remita_transactions')->onDelete('cascade');
            $table->foreign('uploaded_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('transactions', function (Blueprint $table) {
            Schema::disableForeignKeyConstraints();
            $table->dropForeign(['remita_transaction_id', 'uploaded_by']);
            $table->dropColumn(['repayment_reference', 'remita_transaction_id', 'repayment_date', 'uploaded_by']);
            Schema::enableForeignKeyConstraints();
        });
    }
}
