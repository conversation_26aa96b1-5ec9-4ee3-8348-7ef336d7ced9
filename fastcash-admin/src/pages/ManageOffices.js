/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import Pagination from 'antd/lib/pagination';
import qs from 'querystring';

import Office from '../components/Office';
import { deleteOffice } from '../actions/office';
import { httpRequest, baseURL, officeAPI } from '../config/constants';
import { doNotify } from '../actions/general';
import { startBlock, stopBlock } from '../actions/ui-block';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 12;

class ManageOffices extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			currentPage: 1,
			totalPage: 0,
			offices: [],
		};
		this.deleteOffice = this.deleteOffice.bind(this);
	}

	deleteOffice = id => {
		const action = window.confirm('Are you sure you want to delete office?') ? true : false;
		if (action) {
			this.props.startBlock();
			this.props.deleteOffice(id)
				.then(_ => {
					this.props.stopBlock();
					this.fetch(this.state.currentPage);
				})
				.catch(error => {
					this.props.stopBlock();
					this.props.doNotify({ message: error.message || 'could not delete office', level: 'error' });
				});
		}
	};

	componentDidMount() {
		const query = qs.parse(this.props.location.search.replace('?', ''));
		const page = query && query.p ? parseInt(query.p, 10) : 1;
		this.fetch(page);
	}

	fetch = async pageNumber => {
		try {
			this.props.startBlock();
			const { lender } = this.props;
			const rs = await httpRequest(`${baseURL}${officeAPI}?page=${pageNumber}&pagesize=${pageSize}&lender=${lender.id}`, 'GET', true);
			const result = rs.result;
			this.setState({ currentPage: pageNumber, totalPage: result.total, offices: result.data });
			this.props.stopBlock();
			window.scrollTo(0, 0);
		} catch (e) {
			this.props.stopBlock();
			this.props.doNotify({ message: 'could not load offices', level: 'error' });
		}
	};

	onChange = pageNumber => {
		this.fetch(pageNumber);
	};

	render() {
		const { currentPage, totalPage, offices } = this.state;

		return (
			<div className="content-i">
				<div className="content-box">
					<div className="element-wrapper">
						<h6 className="element-header">List of Offices</h6>
						<div className="element-box">
							<div className="table-responsive">
								<table className="table table-striped table-lightfont">
									<thead>
										<tr>
											<th>Name of Establishment</th>
											<th>Loan Disbursed</th>
											<th>Principal Paid</th>
											<th>Interest Paid</th>
											<th>Principal Balance</th>
											<th className="text-center">&nbsp;</th>
										</tr>
									</thead>
									<tbody>
										{offices.map((o, i) => {
											return (
												<Office
													key={i}
													o={o}
													currentPage={currentPage}
													deleteOffice={this.deleteOffice}
												/>
											);
										})}
									</tbody>
								</table>
							</div>
							<div className="pagination pagination-center mt-4">
								<Pagination
									current={currentPage}
									pageSize={pageSize}
									total={totalPage}
									showTotal={total => `Total ${total} offices`}
									itemRender={itemRender}
									onChange={this.onChange}
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
		lender: state.lender.profile,
	};
};

export default connect(
	mapStateToProps,
	{ deleteOffice, doNotify, startBlock, stopBlock },
)(ManageOffices);
