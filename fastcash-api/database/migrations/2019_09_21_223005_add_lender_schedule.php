<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddLenderSchedule extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('schedules', function (Blueprint $table) {
            $table->bigInteger('lender_id')->unsigned()->nullable()->after('office_id');

            $table->foreign('lender_id')->references('id')->on('lenders');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('schedules', function (Blueprint $table) {
            Schema::disableForeignKeyConstraints();
            $table->dropForeign(['lender_id']);
            $table->dropColumn(['lender_id']);
            Schema::enableForeignKeyConstraints();
        });
    }
}
