import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, SubmissionError } from 'redux-form';

import { baseURL, httpRequest, userAPI } from '../config/constants';
import loading from '../assets/img/loading.gif';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {}
    if (!values.core_bank_id) {
        errors.core_bank_id = 'Enter core bank id';
    }
    return errors;
};

const renderField = ({input, id, label, type, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" />
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class CoreBankID extends Component {
    updateCoreBankingID = async data => {
        try {
            const { user, customer } = this.props;
            const details = { ...data, staff_id: user.id };
            const url = `${baseURL}${userAPI}/core-bank/${customer.id}`;
            await httpRequest(url, 'PUT', true, details);
            this.notify('', 'core banking id updated!', 'success');
            this.props.closeModal();
        } catch (error) {
			const message = error.message || 'could not update user';
			this.notify('', message, 'error');
			throw new SubmissionError({
				_error: message,
			});
		}
    };
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	render() {
        const { closeModal, handleSubmit, error, submitting, pristine } = this.props;
		return (
			<div>
                <div className="modal-header faded smaller">
                    <div className="modal-title">
                        <span>Core Banking ID</span>
                    </div>
                    <button aria-label="Close" className="close" onClick={closeModal} type="button"><span aria-hidden="true"> ×</span></button>
                </div>
                <form onSubmit={handleSubmit(this.updateCoreBankingID)}>
                    <div className="modal-body">
                        {error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}/>}
                        <div className="row">
                            <div className="col-sm-6">
								<Field
									name="core_bank_id"
									id="core_bank_id"
									type="text"
									component={renderField}
									label="Core Banking ID"
								/>
                            </div>
                        </div>
                    </div>
                    <div className="modal-footer buttons-on-left">
                        <button className="btn btn-teal" disabled={pristine || submitting} type="submit">
                            {submitting? <img src={loading} alt=""/> : 'Save'}
                        </button>
                        <button className="btn btn-link cursor" onClick={closeModal} type="button"> Cancel</button>
                    </div>
                </form>
            </div>
		);
	}
}

CoreBankID = reduxForm({
    form: 'corebankid',
    validate,
})(CoreBankID);

const mapStateToProps = (state, ownProps) => {
    return {
        initialValues: {
            core_bank_id: ownProps.customer.core_bank_id,
		},
		user: state.user.user,
    }
}

export default connect(mapStateToProps, { doNotify })(CoreBankID);
