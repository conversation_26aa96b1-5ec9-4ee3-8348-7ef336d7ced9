import React, { Component } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';

import { calculateLoan, setTenure, setEarnings, doRefresh, calculateSMELoan, setRemitaID } from '../actions/loan';
import { httpRequest, baseURL, dateLimit, adminFindUserAPI } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import LoanForm from '../components/LoanForm';
import LoanDetails from '../components/LoanDetails';
import loader from '../assets/img/loader.gif';
import FindUser from "../components/FindUser";

class LoanRequest extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
            eligible: 1,
            current: null,
            done: 0,
            type: '',
            title: '',
			message: '',
			error_msg: '',
			platform: '',
			has_fetched: false,
        };

        this.loanMessage = this.loanMessage.bind(this);
        this.findUser = this.findUser.bind(this);
	}
	
    async componentDidMount() {
		const { payslip, current, eligibilityGap, user, snoozePeriod } = this.props;
		
		if(user.is_snoozed === 1) {
			const snoozed_date = user.snoozed_at;
			const end_of_snooze = moment(snoozed_date).add(snoozePeriod, 'days').fromNow(true);

			this.setState({ error_msg: `You are not allowed to request a loan at this time. Try again after ${end_of_snooze}.`, has_fetched: true });
			return;
		}

		this.setState({ error_msg: '', has_fetched: false });
        const date_diff = payslip ? moment(payslip.retire_expected_at).diff(Date.now(), 'years') : 0;
        const eligibility = date_diff - parseInt(eligibilityGap, 10);
        if(eligibility <= 0 && user && user.platform === 'ippis'){
            this.setState({ eligible: 0 });
        }
		this.setState({ current, has_fetched: current !== null });
	}

    componentWillUnmount = () => {
		const { maxLoanTenure } = this.props;
		this.props.setTenure(parseInt(maxLoanTenure, 10));
    };

    loanMessage = (type, title, message) => {
        this.setState({ done: 1, type, title, message, has_fetched: true });
    };

	findUser = async (e, accountNumber, bankId)  => {
		e.preventDefault();
		
		const { user, banks, earnings, tenure, lender } = this.props;

		try {
			if (accountNumber === '') {
				this.setState({ error_msg: 'Enter salary account number', has_fetched: true });
				return;
			}
			
			if (bankId === '') {
				this.setState({ error_msg: 'Enter salary bank', has_fetched: true });
				return;
			}
			
			this.setState({ error_msg: '', has_fetched: false });
			this.props.startBlock();
			this.props.doRefresh(true);

			const bank = banks.find(b => Number(b.id) === Number(bankId));
			const data = { phone: user.phone, account_number: accountNumber, bank_code: bank?.code || '', type: 'loan', platform: 'remita' };
			const url = `${baseURL}${adminFindUserAPI}`;
			const rs = await httpRequest(url, 'POST', true, data);
			const response = rs.result;

			// --------------- parse result
			const ir = lender.interest_rate;

			let dates = [];
			for (let d=1; d<=parseInt(dateLimit, 10); d++) {
				dates = [ ...dates, { date: moment().subtract(d, 'month').format('M-YYYY') } ];
			}
		
			let ippisNewEarnings = [];
			for (let i = 0; i < earnings.length; i++) {
				const earning = earnings[i];
				const found = ippisNewEarnings.find(e => e.month === earning.month && e.year === earning.year && parseFloat(e.net_earning) === parseFloat(earning.net_earning));
				if(!found){
					ippisNewEarnings = [...ippisNewEarnings, earning];
				}
			}
			// console.log('-------------- ippis --------------');
			// console.log(earnings);
			// console.log(ippisNewEarnings);
			const ippisFilteredEarnings = ippisNewEarnings.filter(e => {
				return dates.find(d => d.date === `${e.month}-${e.year}`) && parseInt(e.net_earning, 10) > 0;
			});
			// console.log(ippisFilteredEarnings);
			ippisFilteredEarnings.sort((a, b) => b.year - a.year || b.month - a.month);
			// console.log(ippisFilteredEarnings);
			// const newEarnings = ippisFilteredEarnings.slice(0, 3);
			// console.log(newEarnings);
			// console.log('-------------- ippis --------------');

			if(response.earnings.length > 0){
				let remitaNewEarnings = [];
				for (let i = 0; i < response.earnings.length; i++) {
					const earning = response.earnings[i];
					const found = remitaNewEarnings.find(e => e.month === earning.month && e.year === earning.year && parseFloat(e.net_earning) === parseFloat(earning.net_earning));
					if(!found){
						remitaNewEarnings = [...remitaNewEarnings, earning];
					}
				}

				// console.log('-------------- remita --------------');
				// console.log(response.earnings);
				// console.log(remitaNewEarnings);
				const remitaFilteredEarnings = remitaNewEarnings.filter(e => {
					return dates.find(d => d.date === `${e.month}-${e.year}`) && parseInt(e.net_earning, 10) > 0;
				});
				// console.log(remitaFilteredEarnings);
				remitaFilteredEarnings.sort((a, b) => b.year - a.year || b.month - a.month);
				// console.log(remitaFilteredEarnings);

				const netEarnings = remitaFilteredEarnings.slice(0, 3);
				// console.log(netEarnings);
				
				this.props.setEarnings(netEarnings);
				this.setState({ error_msg: '', platform: 'remita', has_fetched: true });
				this.props.stopBlock();

				const map_earnings = netEarnings.map(r => r.net_earning);
				// console.log(map_earnings);
				const total_net_earning = map_earnings.reduce((total, amount) => parseFloat(amount) + total, 0);
				const net_earning = (total_net_earning / 3).toFixed(2);
				// console.log(net_earning);
				// console.log('-------------- remita --------------');
				this.props.calculateLoan(net_earning, tenure, ir, 0.4);
				this.props.doRefresh(false);
				this.props.setRemitaID(response.remita_id);
			} else {
				this.setState({ error_msg: 'You do not have any net earnings yet', has_fetched: true });
				this.props.stopBlock();
				this.props.doRefresh(false);
			}
			// --------------- parse result
		} catch(e) {
			this.setState({ error_msg: 'Could not retrieve your net earnings', has_fetched: true });
			this.props.stopBlock();
			this.props.doRefresh(false);
		}
	};
    
    render() {
        const { user, myAmount, myMonthlyDeductions, monthlyDeductions, netEarning, eligibleAmt, tenure, bankAccounts, stopLoanDate, refresh } = this.props;
		const { eligible, current, done, type, title, message, error_msg, platform, has_fetched } = this.state;
		const monthLegible = moment(stopLoanDate).date() < moment().date();
        return (
			<React.Fragment>
			{done === 0 && eligible === 1 && !current && <div className="content-i"><div className="content-box"><FindUser user={user} onFindUser={(e, accountNumber, bankId) => this.findUser(e, accountNumber, bankId)} /></div></div>}
			{refresh ? <div className="content-i"><div className="content-box" style={{textAlign:'center', paddingTop: '0px'}}><img src={loader} alt=""/></div></div> :
            <div className="content-i">
                {has_fetched && <div className="content-box">
					{(
						error_msg !== '' ? (
							<div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>{error_msg}</div>
						) : 
						<div>
							{done === 1 && (
								<div className="element-box">
									<div className="element-box-content">
										<div className={`alert alert-${type}`} role="alert">
											<h4 className="alert-heading">{title}</h4>
											<p>{message}</p>
											<p className="mb-0"></p>
										</div>
									</div>
								</div>
							)}
							{eligible === 0 ? 
								(<div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>you are not legible to take any loans at this moment</div>) 
								: '' 
							}
							{
								monthLegible ? (
									<div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>{`you are not legible to take any loans at this moment, please try again on or before the ${moment(stopLoanDate).format('Do')} of next month.`}</div>
								) : (current ? (
										<div className="alert alert-warning" style={{margin: '0', marginBottom: '15px'}}>you cant take a loan at this moment because you have a current loan running</div>
									) : (
										done === 0 && (
											<div className="row">
												<div className="col-sm-8">
													<LoanDetails
														amount={myAmount}
														myMonthlyDeductions={myMonthlyDeductions}
														monthlyDeductions={monthlyDeductions}
														netEarning={netEarning}
														eligibleAmt={eligibleAmt}
														tenure={tenure}
														platform={platform}
														topup={false}
													/>
												</div>
												{(current === null && eligible === 1) ? (
													<div className="col-sm-4">
														<div className="element-wrapper">
															<div className="element-wrapper">
																<LoanForm
																	user={user}
																	tenure={tenure}
																	netEarning={netEarning}
																	eligibleAmt={eligibleAmt}
																	loanMessage={(type, title, message) => this.loanMessage(type, title, message)}
																	bankAccounts={bankAccounts || []}
																	platform={platform}
																	topup= {0}
																	old_loan= {null}
																/>
															</div>
														</div>
													</div>
												) : '' }
											</div>
										)
									)
								)
							}
						</div>
					)}
                </div>}
            </div>}
			</React.Fragment>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	const lender = state.user.lender;

    return {
        user: state.user.user,
        myAmount: state.loan.myAmount,
        myMonthlyDeductions: state.loan.myMonthlyDeductions,
        monthlyDeductions: state.loan.monthlyDeductions,
        netEarning: state.loan.netEarning,
        eligibleAmt: state.loan.eligibleAmt,
        tenure: state.loan.tenure,
        current: state.loan.current,
        payslip: state.user.payslip,
		earnings: state.user.earnings,
		bankAccounts: state.user.bank_accounts,
		refresh: state.loan.refresh,
		maxLoanTenure: lender.max_loan_tenure,
		stopLoanDate: state.settings.stop_loan_date,
		snoozePeriod: state.settings.snooze_period,
		eligibilityGap: state.settings.eligibility_gap,
        lender: state.user.lender,
		banks: state.general.banks,
    }
};

export default connect(mapStateToProps, { calculateLoan, setTenure, setEarnings, startBlock, stopBlock, doRefresh, calculateSMELoan, setRemitaID })(LoanRequest);