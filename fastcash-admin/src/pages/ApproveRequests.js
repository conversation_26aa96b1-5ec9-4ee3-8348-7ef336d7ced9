/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import Pagination from 'antd/lib/pagination';
import qs from 'querystring';
import $ from 'jquery';

import ListHOC from '../container/ListHOC';
import { httpRequest, baseURL, loanAPI } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import LoanData from '../components/LoanData';
import { notifyDone } from '../actions/general';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 12;

class ApproveRequests extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			currentPage: 1,
			totalPage: 0,
			loans: [],
			offices: [],
			office: 0,
			lender_id: 0,
			location: '',
			editVisible: false,
			loan_id: null,
		};
	}

	componentDidMount() {
		const { location } = this.props;
		const query = qs.parse(location.search.replace('?', ''));
		
		const page = query && query.p ? parseInt(query.p, 10) : 1;
		const office = query && query.office ? parseInt(query.office, 10) : 0;
		const lender_id = query && query.lender ? parseInt(query.lender, 10) : 0;

		this.setState({
			office,
			currentPage: page,
			location: location.pathname,
			lender_id,
		});

		this.fetch(page, office, lender_id);
	}

	fetch = async (pageNumber, office, lender_id) => {
		try {
			this.props.startBlock();
			const rs = await httpRequest(`${baseURL}${loanAPI}?page=${pageNumber}&pagesize=${pageSize}&category=unapproved&q=admin&lender=${lender_id}&office=${office}`, 'GET', true);
            const result = rs.result;
			this.setState({ currentPage: pageNumber, totalPage: result.total, loans: result.data, offices: result.offices });
			this.props.stopBlock();
			this.props.notifyDone(false);
            window.scrollTo(0, 0);
		} catch (e) {
			this.props.stopBlock();
			this.props.doNotify({ message: 'could not load loans', level: 'error' });
		}
	};

	onChange = pageNumber => {
		const { lender_id } = this.state;
		const { location } = this.props;
		const query = qs.parse(location.search.replace('?', ''));
		if(query.office){
			this.props.history.push(`/approve-loans?lender=${lender_id}&office=${query.office}&p=${pageNumber}`);
		} else {
			this.props.history.push(`/approve-loans?lender=${lender_id}&p=${pageNumber}`);
		}
	};

	onSelect = e => {
		const { lender_id } = this.state;
		const office = e.target.value;
		$(e.currentTarget).blur();
		this.props.history.push(`/approve-loans?lender=${lender_id}&office=${office}&p=1`);
	};

	componentWillUpdate(nextProps, nextState) {
		const { lender_id } = nextState;
		const query = qs.parse(nextProps.location.search.replace('?', ''));
		const pageNumber = query && query.p ? parseInt(query.p, 10) : 1;
		const office = query && query.office ? parseInt(query.office, 10) : 0;
		if(nextState.currentPage !== pageNumber || nextProps.location.pathname !== nextState.location || office !== nextState.office){
			this.setState({
				office,
				location: nextProps.location.pathname,
				currentPage: pageNumber,
			});

			this.fetch(pageNumber, office, lender_id);
		} else if(nextProps.notifdone) {
			this.fetch(pageNumber, office, lender_id);
		}
	}

	doHide = () => {
		this.setState({ editVisible: false, loan_id: null });
	};

	showEditLoan = (status, loan) => {
		this.setState({ editVisible: status, loan_id: loan.id });
	};

	render() {
		const { currentPage, totalPage, loans, offices, office, lender_id, editVisible, loan_id } = this.state;
		const { showUserProfile, user, doApproveLoan, openModal, doVerifyLoan, doMultiFund, lenders, sendConsentSMS, doSendMail, onSaveLoanInsurance } = this.props;
		return (
			<div className="element-wrapper">
				<h6 className="element-header">Approve Loans</h6>
				<div className="control-header">
					<div className="row align-items-center">
						<div className="col-12">
							<div className="form-inline row">
								<div className="col form-group" style={{width:'110px'}}>
									<label className="text-right" htmlFor="">Filter By:</label>
								</div>
								<label className="col-sm-1 col-form-label text-right justify-content-end" htmlFor="">Office</label>
								<div className="col">
									<select name="office" className="form-control" onChange={this.onSelect} value={office}>
										<option value="0">All Offices</option>
										{offices.map(o => <option key={o.id} value={o.id}>{o.name}</option>)}
									</select>
								</div>
								<label className="col-sm-1 col-form-label text-right justify-content-end" htmlFor="">Lender</label>
								<div className="col">
									<select name="lender" className="form-control" value={lender_id} disabled="disabled">
										<option value="0">All Lenders</option>
										{lenders.map(o => <option key={o.id} value={o.id}>{o.name}</option>)}
									</select>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="element-box">
					<div className="table-responsive" style={{overflowX: 'scroll',}}>
						<table className="table table-striped table-lightfont">
							<thead>
								<tr>
									<th/>
									<th>Lender</th>
									<th>Name of Employee</th>
									<th>IPPIS/Customer ID</th>
									<th>Amount</th>
									<th>Loan Status</th>
									{user.role_name === 'super' && <th>Multi-Funding</th>}
									<th>Tenure</th>
									<th>Loan Consent</th>
									<th>Request Date</th>
									<th>&nbsp;</th>
								</tr>
							</thead>
							<tbody>
								{loans.map((loan, i) => (
									<LoanData
										key={i}
										loan={loan}
										user={user}
										showUserProfile={showUserProfile}
										doVerifyLoan={doVerifyLoan}
										doApproveLoan={doApproveLoan}
										openModal={openModal}
										category="approve"
										doMultiFund={doMultiFund}
										showLender={true}
										sendConsentSMS={sendConsentSMS}
										doSendMail={doSendMail}
										onSaveLoanInsurance={onSaveLoanInsurance}
										editVisible={editVisible}
										loanID={loan_id}
										showEditLoan={this.showEditLoan}
										doHide={this.doHide}
									/>
								))}
							</tbody>
						</table>
					</div>
					<div className="pagination pagination-center mt-4">
						<Pagination
							current={currentPage}
							pageSize={pageSize}
							total={totalPage}
							showTotal={total => `Total ${total} loans`}
							itemRender={itemRender}
							onChange={this.onChange}
						/>
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
		notifdone: state.general.notifdone,
		lender: state.lender.profile,
		lenders: state.lender.list,
	}
};

export default ListHOC(connect(mapStateToProps, { startBlock, stopBlock, notifyDone })(ApproveRequests));
