<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddDisburseamountLoan extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            DB::statement('ALTER TABLE `loans` CHANGE `topup` `topup` BIGINT(20) UNSIGNED NULL DEFAULT NULL');

//            $table->dateTime('liquidated_at')->nullable()->change();
//            $table->bigInteger('topup')->unsigned()->nullable()->change();
//            $table->decimal('disburse_amount', 20, 2)->default('0.00')->after('amount');

            $table->foreign('topup')->references('id')->on('loans')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropForeign(['topup']);
            $table->dropColumn(['disburse_amount']);
        });
    }
}
