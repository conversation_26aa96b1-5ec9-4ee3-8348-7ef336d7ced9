import React, { Component } from 'react';
import { Link } from 'react-router-dom';
import $ from 'jquery';

import Footer from '../components/Footer';
import logo from '../assets/img/fc-logo.png';

class PrivacyPolicy extends Component {
    constructor(props) {
		super(props);
        this.toggleMobileMenu = this.toggleMobileMenu.bind(this);
    }

    componentDidMount() {
        document.body.className="";
    }

    toggleMobileMenu = (e) => {
        e.preventDefault();
        $(this.refs.mmenu).toggleClass('show-mobile');
    };

    render() {
        return (
            <div className="wrapper-front">
                <div className="desktop-menu menu-top-f menu-activated-on-hover">
                    <div className="menu-top-i os-container">
                        <div className="logo-w">
                            <Link className="logo" to="/">
                                <img alt="logo" src={logo} style={{width:"150px"}}/>
                            </Link>
                        </div>
                        <ul className="main-menu">
                            <li><Link to="/">Home</Link></li>
                            {/* <li><Link to="/signin">Merchant</Link></li> */}
                        </ul>
                    </div>
                    <div className="mobile-menu-w">
                        <div className="mobile-menu-holder color-scheme-dark" ref='mmenu'>
                            <ul className="mobile-menu">
                                <li><Link to="/">Home</Link></li>
                            </ul>
                        </div>
                        <div className="mobile-menu-i">
                            <div className="mobile-logo-w">
                                <Link className="logo" to="/">
                                    <img alt="logo" src={logo} style={{width:"150px"}}/>
                                </Link>
                            </div>
                            <div className="mobile-menu-trigger" onClick={this.toggleMobileMenu}>
                                <i className="os-icon os-icon-hamburger-menu-1"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="intro-w layout-v1">
                    <div className="os-container">
                        <div className="intro-i">
                            &nbsp;
                        </div>
                    </div>
                </div>
                <Footer/>
            </div>
        );
    }
}

export default PrivacyPolicy;