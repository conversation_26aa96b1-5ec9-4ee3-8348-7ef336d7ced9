import React, { Component } from 'react';
import { connect } from 'react-redux';

import { baseURL, httpRequest2, uploadUsersAPI } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import Upload from '../components/Upload';
import { doNotify } from '../actions/general';

class UploadCustomers extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			files: [],
			error: '',
		};
		this.onOpenDropzone = this.onOpenDropzone.bind(this);
	}

	displayError = (error, type) => {
		this.setState({ error });
		this.props.stopBlock();
	};

	onDrop = file => {
		this.props.startBlock();
		const { lender } = this.props;
		const { files } = this.state;
        const { name } = file[0];

		let formData = new FormData();
		formData.append('file', file[0]);
		formData.append('lender_id', lender.id);

		return httpRequest2(`${baseURL}${uploadUsersAPI}`, 'POST', formData)
			.then(response => {
				this.setState({ files: [...files, { name, status: 1, ids: response.ids }] });
				this.props.stopBlock();
				this.props.doNotify({ message: `${response.numberOfUsers} user profiles uploaded`, level: 'success' });
			})
			.catch(error => {
				this.setState({ files: [...files, { name, status: 0 }] });
				if (error.message) {
					this.displayError(error.message, 'text');
				} else {
					this.displayError('Error, check your connection and try again', 'text');
				}
			});
	};

	onOpenDropzone = e => {
		this.setState({ error: '' });
	};

	render() {
		const { error, files } = this.state;
		return (
			<div className="content-i">
                <div className="content-box">
                    <div className="element-wrapper compact pt-4">
                        <h6 className="element-header">Upload Customers</h6>
                        <div className="element-box-tp">
                            <div className="row">
                                <div className="col-lg-6 col-xxl-6">
                                    <Upload
                                        error={error}
                                        message=''
                                        changeMonth={() => {}}
                                        month={''}
                                        months={[]}
                                        changeYear={() => {}}
                                        year={''}
                                        years={[]}
                                        onDrop={this.onDrop}
                                        onOpenDropzone={this.onOpenDropzone}
                                        disabled={false}
										files={files}
										schedule={false}
                                    />
                                </div>
								<div className="col-lg-6 col-xxl-6"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
		);
	}
}

const mapStateToProps = state => {
	return {
		lender: state.lender.profile,
	};
};

export default connect(mapStateToProps, { startBlock, stopBlock, doNotify })(UploadCustomers);
