/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react';
import numeral from 'numeral';
import moment from 'moment';
import Tooltip from 'antd/lib/tooltip';

const LenderTransactions = ({ lender, user, approve, decline }) => {
	return (
		<div className="table-responsive" style={{overflowX: 'scroll'}}>
			<table className="table table-lightborder">
				<tbody>
					<tr>
						<th>Date</th>
						<th>Amount</th>
						<th>Category</th>
						<th>Status</th>
						{user && user?.lender_id === 1 && <th/>}
					</tr>
					{lender.transactions?.map((transact, i) => {
						return (
							<tr key={i}>
								<td>{moment(transact.created_at).format('D.MMM.YYYY')}</td>
								<td className="text-left">{`₦${numeral(transact.amount).format('0,0.00')}`}</td>
								<td>
									<div className="badge badge-dark">{transact.category}</div>
								</td>
								<td><span className={`badge badge-${transact.deleted_at ? 'danger' : (transact.approved === 1 ? 'success' : 'dark')} text-white`}>{transact.deleted_at ? 'declined' : (transact.approved === 1 ? 'approved' : 'pending')}</span></td>
								{user && user.lender_id === 1 && transact.approved === 0 && !transact.deleted_at && (
									<td className="text-right">
										<button className="btn btn-sm btn-primary cursor" onClick={approve(transact.id)}>approve</button>
										<Tooltip title="Decline">
											<a className="cursor text-danger ml-1" onClick={decline(transact.id)}>
												<i className="os-icon os-icon-trash-2" />
											</a>
										</Tooltip>
									</td>
								)}
							</tr>
						);
					})}
				</tbody>
			</table>
		</div>
	);
};

export default LenderTransactions;