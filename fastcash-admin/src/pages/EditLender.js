import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import { doNotify, notifyDone } from '../actions/general';

import { baseURL, httpRequest, lenderAPI, lender<PERSON>ey } from '../config/constants';
import loading from '../assets/img/loading.gif';
import all_states from '../assets/json/states';

const validate = values => {
	const errors = {};
	if (!values.name) {
		errors.name = 'Enter the lender name';
	}
	if (!values.email) {
		errors.email = 'Enter email address';
	}
	if (!values.address) {
		errors.address = 'Enter your address';
	}
	if (!values.phone) {
		errors.phone = 'Please give us the phone number';
	}
	if (!values.bank) {
		errors.bank = 'Select bank';
	}
	if (!values.state) {
		errors.state = 'Select state';
	}
	if (!values.account_number) {
		errors.account_number = 'Enter account number';
	}
	if (!values.next_of_kin) {
		errors.next_of_kin = 'Enter next of kin';
	}
	return errors;
};

const renderField = ({ input, id, label, type, disabled, meta: { touched, error } }) => (
	<div className={`form-group ${touched && error && 'has-error'}`}>
		<label htmlFor={id}>{label}</label>
		<input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
		{touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
	</div>
);

const renderSelectField = ({ input, label, data, disabled, meta: { touched, error } }) => (
	<div className={`form-group ${touched && error && 'has-error'}`}>
		<label>{label}</label>
		<select {...input} className="form-control" disabled={disabled}>
			<option value="">{`Select ${label}`}</option>
			{data.map((c, i) => (
				<option value={c.id} key={i}>
					{c.name}
				</option>
			))}
		</select>
		{touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
	</div>
);

class EditLender extends Component {
	doEditLender = async data => {
		const { user, lender } = this.props;
		try {
			const details = {
				...data,
				user_id: user.id,
				auto_disburse: data.auto_disburse ? 1 : 0,
			};
			await httpRequest(`${baseURL}${lenderAPI}/update/${lender.id}`, 'PUT', true, details);
			this.props.reset('edit-lender');
			localStorage.removeItem(lenderKey);
			this.props.notifyDone(true);
			this.notify('', 'lender profile updated!', 'success');
			this.props.history.push('/lenders');
		}
		catch (error) {
			const message = error.message || 'could not update lender profile';
			this.notify('', message, 'error');
			throw new SubmissionError({
				_error: message,
			});
		}
	};

	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	render() {
		const { handleSubmit, error, submitting, pristine, banks } = this.props;
		const states = all_states.map(({ state }) => ({ id: state.id, name: state.name }));
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="row">
						<div className="col-lg-12">
							<div className="padded-lg" style={{ paddingRight: 0, paddingLeft: 0 }}>
								<div className="element-wrapper">
									<h6 className="element-header">Edit Lender Profile</h6>
									<div className="element-box">
										{error && (
											<div
												className="alert alert-danger"
												style={{ marginBottom: '15px' }}
												dangerouslySetInnerHTML={{
													__html: `<strong>Error!</strong> ${error}`,
												}}></div>
										)}
										<form onSubmit={handleSubmit(this.doEditLender)}>
											<div className="row">
												<div className="col-sm-4">
													<Field
														name="name"
														id="name"
														type="text"
														component={renderField}
														label="Lender/Organization Name"
														disabled={true}
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="email"
														id="email"
														type="email"
														component={renderField}
														label="Email"
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="phone"
														id="phone"
														type="text"
														component={renderField}
														label="Phone Number"
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="address"
														id="address"
														type="text"
														component={renderField}
														label="Address"
														disabled={true}
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="state"
														component={renderSelectField}
														label="State"
														data={states}
														disabled={true}
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="country"
														id="country"
														type="text"
														component={renderField}
														label="Country"
														disabled={true}
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="bank"
														component={renderSelectField}
														label="Bank"
														data={banks}
														disabled={true}
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="account_number"
														id="account_number"
														type="text"
														component={renderField}
														label="Account Number"
														disabled={true}
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="next_of_kin"
														id="next_of_kin"
														type="text"
														component={renderField}
														label="Next of Kin"
														disabled={true}
													/>
												</div>
											</div>
											<fieldset>
												<legend className="text-gray-dark">Loan Settings</legend>
												<div className="row">
													<div className="col-sm-4">
														<div className="form-group row">
															<label className="col-sm-5 col-form-label text-right" htmlFor="auto_disburse">Auto Disburse</label>
															<div className="col-sm-7">
																<div className="input-group">
																	<Field
																		name="auto_disburse"
																		id="auto_disburse"
																		component="input"
																		type="checkbox"
																		style={{marginTop: '8px'}}
																	/>
																</div>
															</div>
														</div>
													</div>
												</div>
											</fieldset>
											<div className="form-buttons-w">
												<button
													className="btn btn-primary"
													disabled={pristine || submitting}
													type="submit">
													{submitting ? <img src={loading} alt="" /> : 'Save Profile'}
												</button>
											</div>
										</form>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}

EditLender = reduxForm({
	form: 'edit-lender',
	validate,
})(EditLender);

const mapStateToProps = state => {
	const lender = state.lender.edit;

	return {
		initialValues: {
			email: lender.email,
			address: lender.address,
			phone: lender.phone_number,
			bank: lender.bank_code_id,
			account_number: lender.account_number,
			name: lender.name,
			next_of_kin: lender.next_of_kin,
			state: lender.state_id,
			country: 'Nigeria',
			auto_disburse: lender.auto_disburse === 1,
		},
		user: state.user.user,
		banks: state.general.banks,
		lender,
	};
};

export default connect(
	mapStateToProps,
	{ reset, doNotify, notifyDone },
)(EditLender);
