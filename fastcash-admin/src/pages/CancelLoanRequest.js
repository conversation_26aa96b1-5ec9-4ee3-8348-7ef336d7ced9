/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react'
import qs from 'querystring';
import { connect } from 'react-redux';
import Pagination from 'antd/lib/pagination';
import DatePicker from 'react-datepicker';
import Tooltip from 'antd/lib/tooltip';
import Popover from 'antd/lib/popover';

import ListHOC from '../container/ListHOC';
import { startBlock, stopBlock } from '../actions/ui-block';
import { doNotify } from '../actions/general';
import { setLoanCancelRequests } from '../actions/user';
import { loadNotifications } from '../actions/notification';
import { httpRequest, baseURL, loanAPI, currency, formatDate } from '../config/constants';
import loading from '../assets/img/loading.gif';

const DeclineMessage = ({ doHide, doDeclineRequest, request, reason, onChangeReason, declining, loading }) => {
	return (
		<div className="element-box p-3 m-0">
			<form onSubmit={(e) => doDeclineRequest(e, request)}>
				<div className="row">
					<div className="col-sm-12">
						<div className="form-group">
							<label htmlFor="amount">Reason</label>
							<textarea name="reason" id="reason" placeholder="Reason for decline" type="text" className="form-control" onChange={(e) => onChangeReason(e)} rows="3" value={reason}></textarea>
						</div>
					</div>
				</div>
				<div className="text-center">
					<button className="mr-2 btn btn-primary" disabled={declining} type="submit">
						{declining ? <img src={loading} alt=""/> : 'Proceed'}
					</button>
					<button className="btn btn-default" type="button" onClick={doHide}>Cancel</button>
				</div>
			</form>
		</div>
	);
};

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};

class CancelLoanRequest extends Component {
    state = {
		requests: [],
		currentPage: 1,
		totalPage: 0,
		location: '',
		status: '',
		fromDate: null,
		toDate: null,
		pageSize: 12,
        reason: '', 
        declining: false,
		visible: false,
        request: null,
        requestID: null
	};

    componentDidMount() {
		const { location } = this.props;
		
		const query = qs.parse(location.search.replace('?', ''));
		
		const page = query && query.p ? parseInt(query.p, 10) : 1;

		this.setState({ currentPage: page, location: location.pathname });

		this.fetch(page);

	}

	fetch = async (pageNumber, status = '', from = '', to = '') => {
		this.props.startBlock();
        try {
			const { lender } = this.props;
            const { pageSize } = this.state;
            const rs = await httpRequest(`${baseURL}${loanAPI}?page=${pageNumber}&pagesize=${pageSize}&category=cancelled&q=admin&lender=${lender.id}&status=${status}&from=${from}&to=${to}`, 'GET', true);
			const result = rs.result;
			this.setState({ currentPage: pageNumber, totalPage: result.total, requests: result.data });
			this.props.stopBlock();
			window.scrollTo(0, 0);
		} catch (e) {
            console.log(e);
			this.props.stopBlock();	
			this.notify('', 'Could not load Loan Cancel Requests!', 'error');
		}
    }

    componentWillUpdate(nextProps, nextState) {
		const query = qs.parse(nextProps.location.search.replace('?', ''));
		const pageNumber = query && query.p ? parseInt(query.p, 10) : 1;
		if(nextState.currentPage !== pageNumber || nextProps.location.pathname !== nextState.location){
			this.setState({
				location: nextProps.location.pathname,
				currentPage: pageNumber,
			});

			this.fetch(pageNumber);
		}
	}

    onChange = pageNumber => {
		this.setState({ currentPage: pageNumber });
        this.props.history.push(`/cancel-loan-requests?p=${pageNumber}`);
    }

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

    approveRequest = async id => {
		const action = window.confirm("Do you want to approve this loan cancel request?") ? true : false;
		if(action) {
			this.props.startBlock();			
			try {
				const { user, lender } = this.props;
    			const { currentPage } = this.state;

				const data = { staff_id: user.id };
				const rs = await httpRequest(`${baseURL}${loanAPI}/cancel/${id}/approve`, 'POST', true, data);
    			this.notify('', 'Loan Cancel Request Approved!', 'success');
                this.props.setLoanCancelRequests(rs.cancel_requests);
				this.props.stopBlock();
				this.fetch(currentPage);
                const param = { lender_id: lender.id }
                this.props.loadNotifications(param)
			} catch (e) {
				console.log(e);
				this.props.stopBlock();
				const message = e.message || 'Could not approve request';
    			this.notify('', message, 'error');
			}
		}
	}

    doDeclineRequest = async (e, id) => {
        e.preventDefault();
		const { reason } = this.state;
		if(reason === '') {
			this.notify('', 'you need to add your reason for declining this request', 'error');
			return;
		}
		
		const action = window.confirm("Do you want to decline this loan cancel request?") ? true : false;
		if(action) {
			this.setState({ declining: true });
			
			try {
				const { user, lender } = this.props;
    			const { currentPage, reason } = this.state;

				const data = { staff_id: user.id, lender_id: lender.id, reason };
				const rs = await httpRequest(`${baseURL}${loanAPI}/cancel/${id}/decline`, 'POST', true, data);
                this.props.setLoanCancelRequests(rs.cancel_requests);
    			this.notify('', 'Loan Cancel Request Declined!', 'success');
    			this.setState({ declining: false, reason: '', visible: false });
				this.fetch(currentPage);
			} catch (e) {
				console.log(e);
    			this.setState({ declining: false });
				const message = e.message || 'could not decline request';
    			this.notify('', message, 'error');
			}
		}
	}

    onSelectStatus = e => {
        const status = e.target.value;	
		const { currentPage, fromDate, toDate } = this.state;
		this.setState({ status: status });
		const from = fromDate ? fromDate.format("DD-MM-YYYY") : "";
		const to = toDate ? toDate.format("DD-MM-YYYY") : "";
		this.props.history.push(`/cancel-loan-requests?p=${currentPage}&status=${status}&from=${from}&to=${to}`);
		this.fetch(currentPage, status, from, to);
	}

    handleChangeDate = (date, type) => {
		const { fromDate, currentPage, status } = this.state;

        if(type === 'fromDate'){
    		this.setState({ fromDate: date, toDate: null });
        }

        if(type === 'toDate'){
    		this.setState({ toDate: date });
            const from = fromDate.format("DD-MM-YYYY");
			const to = date.format("DD-MM-YYYY");
			this.props.history.push(`/cancel-loan-requests?p=${currentPage}&status=${status}&from=${from}&to=${to}`);				    
			this.fetch(currentPage, status, from, to);
        }
    }   

    doHide = () => {
		this.setState({ visible: false, reason: '', requestID: null });
	};

    onChangeReason = e => {
		this.setState({ reason: e.target.value });
	};

    showDeclineReason = (status, id) => {
		this.setState({ visible: status, reason: '', requestID: id });
	};

	render() {
		const { showUserProfile } = this.props;
		const { requests, currentPage, pageSize, totalPage, fromDate, status, toDate, reason, declining, visible, requestID } = this.state;
		return (        
            <div className="content-i">
                <div className="content-box">
                    <div className="row">
                        <div className="col-sm-12">
                            <div className="element-wrapper">
                                <h6 className="element-header">Cancel Loan Requests</h6>

                                <div className="row mb-3">
                                    <div className="col-12 col-lg-4 d-flex align-items-center">                                
                                        <label>Status:</label>
                                        <div className="col">                                    
                                            <select name="lender" className="form-control" onChange={this.onSelectStatus} value={status}>
                                                <option value="pending">Pending</option>
                                                <option value="approved">Approved</option>
                                                <option value="declined">Declined</option>                                    
                                            </select>
                                        </div>
                                    </div>

                                    <div className="col-12 col-lg-6">
                                        <div className="row">
                                            <div className="col-lg-6 col-12 date_input mr-5 d-flex align-items-center">
                                                <label className='mr-2'>From:</label>										
                                                <DatePicker
                                                    selected={fromDate}
                                                    onChange={e => this.handleChangeDate(e, 'fromDate')}
                                                    dateFormat="DD-MMM-YYYY"
                                                    placeholderText="From Date"
                                                    className="form-control white-bg"
                                                    showMonthDropdown
                                                    showYearDropdown
                                                    dropdownMode="select"
                                                    strictParsing
                                                    peekNextMonth={false}
                                                />
                                            </div>
                                            <div className="col-12 col-lg-6 date_input d-flex align-items-center ml-3">
                                                <label className='mr-2'>To:</label>										
                                                <DatePicker
                                                    selected={toDate}
                                                    onChange={e => this.handleChangeDate(e, 'toDate')}
                                                    dateFormat="DD-MMM-YYYY"
                                                    placeholderText="To Date"
                                                    className="form-control white-bg"
                                                    disabled={fromDate === null}
                                                    minDate={fromDate}
                                                    showMonthDropdown
                                                    showYearDropdown
                                                    dropdownMode="select"
                                                    strictParsing
                                                />
                                            </div>
                                        </div>									
                                    </div>                                
                                </div>

                                
                                <div className="element-box">
                                    <div className="table-responsive">
                                        <table className="table table-striped table-lightfont">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Amount</th>
                                                    <th>Outstanding</th>
                                                    <th>Month Deduction</th>
                                                    <th>Tenure</th>
                                                    <th>Cancel Reason</th>
                                                    <th>Request Date</th>
                                                    <th className="text-center">&nbsp;</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {requests.map((item, i) => {     
                                                    return (
                                                        <tr key={i}>
                                                            <td><a onClick={() => showUserProfile(item.user.id)} className="cursor link">{item.user.name}</a></td>
                                                            <td>{currency(item.amount)}</td>
                                                            <td>{currency(item.total_deduction)}</td>
                                                            <td>{currency(item.monthly_deduction)}</td>
                                                            <td>{`${item.tenure} month${item.tenure > 1 ? 's':''}`}</td>                                                    
                                                            <td>
                                                                <Tooltip title={item.cancel_reason}>
                                                                    <span className="elabel elabel-info cursor">reason</span>
                                                                </Tooltip>
                                                            </td>
                                                            <td>{formatDate(item.created_at, 'YYYY-MM-DD HH:mm:ss')}</td>
                                                            <td nowrap="nowrap">
                                                                {item.cancel_request === -1 && (
                                                                    <>
                                                                        <button className="btn btn-sm btn-primary cursor" onClick={() => this.approveRequest(item.id)}>Approve</button>
                                                                        <Tooltip title="Decline Request">
                                                                            <Popover
                                                                                title=""
                                                                                content={
                                                                                    <DeclineMessage
                                                                                        request={item.id}
                                                                                        doHide={this.doHide}
                                                                                        declining={declining}
                                                                                        onChangeReason={this.onChangeReason}
                                                                                        doDeclineRequest={this.doDeclineRequest}
                                                                                        reason={reason}
                                                                                        loading={loading}
                                                                                    />
                                                                                }
                                                                                trigger="click"
											                                    visible={visible && requestID === item.id}
                                                                                onVisibleChange={(status) => this.showDeclineReason(status, item.id)}
                                                                            >
                                                                                <button className="btn btn-sm btn-danger cursor ml-1"><i className="fa fa-trash" /></button>
                                                                            </Popover>
                                                                        </Tooltip>
                                                                    </>
                                                                )}
                                                            </td>
                                                        </tr>
                                                    );
                                                })}                                            
                                            </tbody>
                                        </table>
                                    </div>				
                                    <div className="pagination pagination-center mt-4">
                                        <Pagination
                                            current={currentPage}
                                            pageSize={pageSize}
                                            total={totalPage}
                                            showTotal={total => `Total ${total} requests`}
                                            itemRender={itemRender}
                                            onChange={this.onChange}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

}

const mapStateToProps = (state) => {
	return {
		user: state.user.user,
		lender: state.lender.profile,
	};
};

export default ListHOC(connect(mapStateToProps, { doNotify, startBlock, stopBlock, setLoanCancelRequests, loadNotifications })(CancelLoanRequest));
