<?php

namespace Database\Seeders;

use App\Models\PaymentDate;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class UpdateTransactionDescription extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $dates = Paymentdate::where('paid', 1)->get();
            foreach ($dates as $payment){
                $pay = PaymentDate::find($payment->id);

                $month = Carbon::createFromFormat('Y-m-d H:i:s', $pay->payment_date)->format('F');

                $transaction = Transaction::where('id', $pay->transaction_id)->first();
                if($transaction){
                    $transaction->description = 'Deduction for the Month of '.$month;
                    $transaction->save();
                }
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }
    }
}
