import React, { Component } from 'react';
import { connect } from 'react-redux';

import LoanEditPage from '../components/LoanEditPage';
import { setUserPayslip } from '../actions/user';
import { startBlock, stopBlock } from '../actions/ui-block';
import { httpRequest, baseURL, loanAPI } from '../config/constants';

class EditLoan extends Component {
	state = {
		errorMessage: '',
	};

	componentDidMount() {
		const { match } = this.props;
		this.findLoan(match.params.id);
	}

	findLoan = async id => {
		try {
			this.props.startBlock();
			this.setState({ errorMessage: '' });

			const url = `${baseURL}${loanAPI}/${id}?is_admin=1`;
			const rs = await httpRequest(url, 'GET', true);
			this.props.setUserPayslip(rs.result);
			this.props.stopBlock();
		} catch(e) {
			this.setState({ errorMessage: e.message || 'loan not found' });
			this.props.stopBlock();
		}
	};

	componentWillUnmount() {
		this.props.setUserPayslip(null);
	}

	render() {
		const { user } = this.props;
		const { errorMessage } = this.state;
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="element-wrapper">
						<h6 className="element-header">Edit Loan Request</h6>
						{errorMessage !== '' && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${errorMessage}`}}></div>}
						{user && user.profile && (
							<div className="element-box">
								<div className="element-wrapper p-0">
									<div className="element-content">
										<div className="row">
											<div className="col-sm-4">
												<div className="element-box el-tablo">
													<div className="label">NAME</div>
													<div className="value">{user.profile.name}</div>
												</div>
											</div>
											<div className="col-sm-4">
												<div className="element-box el-tablo">
													<div className="label">EMPLOYER</div>
													<div className="value">{user.profile.employer}</div>
												</div>
											</div>
											<div className="col-sm-4">
												<div className="element-box el-tablo">
													<div className="label">USER PLATFORM</div>
													<div className="value">{user.profile.platform}</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						)}
						{user && user.profile && (
							<div className="element-box">
								<LoanEditPage
									user={user.profile}
									earnings={user.earnings}
									bank_accounts={user.bank_accounts}
									payslip={user.payslip}
									loan={user.loan}
								/>
							</div>
						)}
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.payslip,
	}
};

export default connect(mapStateToProps, { setUserPayslip, startBlock, stopBlock })(EditLoan);
