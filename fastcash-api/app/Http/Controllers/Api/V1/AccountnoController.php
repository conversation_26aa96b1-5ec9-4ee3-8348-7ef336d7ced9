<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Accountno;
use App\Models\BankCode;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Illuminate\Support\Facades\Validator;
use Log;

class AccountnoController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'account_number' => 'required|numeric',
            'bank_code' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        DB::beginTransaction();
        try {
            $data = $request->all();

            $user = User::where('id', $data['user_id'])->first();

            $account = Accountno::where('account_number', $data['account_number'])->where('user_id', $user->id)->first();

            if (!$account) {
                $bank = BankCode::where('id', $data['bank_code'])->first();

                $account = Accountno::create([
                    'user_id' => $user->id,
                    'account_number' => $data['account_number'],
                    'bank_name' => $bank->name,
                    'bank_code' => $bank->code,
                    'verified' => 0,
                    'bvn' => $user->bvn,
                    'phone' => $user->phone,
                    'bank_code_id' => $bank->id,
                    'status' => 'secondary',
                ]);

                $notify = doNotify($data['user_id'], 'accountnos', $account->id, 'new_bank_account', 'success', null, $user->lende_id, null);

                DB::commit();

                return response()->json(compact('account'), 200);
            }

            return response()->json(['error' => 'account number already exist'], 500);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not save account number, try again later'], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
