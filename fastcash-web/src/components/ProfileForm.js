import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import { withRouter } from 'react-router-dom';
import ReactPixel from 'react-facebook-pixel';

import { baseURL2, httpRequest, signupAPI } from '../config/constants';
import { doNotify } from '../actions/general';
import loading from '../assets/img/loading.gif';

const validate = values => {
    const errors = {};
    if (!values.bvn) {
        errors.bvn = 'Enter your bvn';
    }
    if (!values.phone) {
        errors.phone = 'Enter your phone number';
    }
    if (!values.email) {
        errors.email = 'Enter your email address';
    }
    if (!values.password) {
        errors.password = 'Enter your password';
    }
    if (!values.password_confirmation) {
        errors.password_confirmation = 'Re-Enter your password';
    }
    if (values.password && values.password_confirmation && values.password !== values.password_confirmation) {
        errors.password_confirmation = 'Your passwords are not the same';
    }
    if (!values.lender_id) {
        errors.lender_id = 'Select lending platform';
    }
    if (!values.employer) {
        errors.employer = 'Enter employer office';
    }
    if (!values.agree) {
        errors.agree = 'You have not agreed to our terms and conditions';
    }
    return errors;
};

const renderField = ({input, id, label, type, disabled, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

const renderSelectField = ({input, label, readOnly, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label>{label}</label>
        <select {...input} className="form-control" readOnly={readOnly}>
            {!readOnly && <option value="">{`Select ${label}`}</option>}
            {data.map((c, i) => <option value={c.id} key={i}>{c.name}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

const renderCheckbox = ({input, label, id, meta: {touched, error}}) => (
    <div className={`form-check ${(touched && error && 'has-error')}`}>
        <label htmlFor={id} className="form-check-label">
            <input {...input} id={id} type="checkbox" className="form-check-input" />{label}
        </label>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class ProfileForm extends Component {
	state = {
		message: '',
	};

	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};
    
    signUp = async data => {
        const { user, bvn, remitaDatum } = this.props;

        const details = {
            ...user,
            ...data,
            platform: user ? user.platform : 'remita',
            name: remitaDatum ? remitaDatum.name : user.name,
            customer_id: remitaDatum ? remitaDatum.customer_id : '0',
            bank_code: remitaDatum ? remitaDatum.bank_code : '0',
            account_number: remitaDatum ? remitaDatum.account_number : '0',
            isAdminCreated: 0,
            verified: 0,
        };

        if(bvn !== details.bvn && user && user.platform === 'sme') {
            window.scrollTo(0, 0);
            const message = 'Error, incorrect bvn';
            this.notify('', message, 'error');
            throw new SubmissionError({
                _error: message,
            });
        }

        try {
            this.setState({ message: '' });
            const rs = await httpRequest(`${baseURL2}${signupAPI}`, 'POST', false, details);
            this.props.reset('authform');
            window.scrollTo(0, 0);
            ReactPixel.track('CompleteRegistration', {content_name: rs.user.name});
            this.notify('', 'Registration successful! Please check your email to verify and activate your account.', 'success');
            this.setState({ message: 'Registration successful! Please check your email to verify and activate your account.' });
        } catch (error) {
            window.scrollTo(0, 0);
            const info = error.message || 'Error, check your connection and try again';
            this.notify('', info, 'error');
            throw new SubmissionError({
                _error: info,
            });
        }
    };

    render() {
        const { handleSubmit, error, pristine, submitting, lenders, lender, phone, lenderId } = this.props;
        const { message } = this.state;

        if(lenderId !== ''){
            const lenderInfo = lenders.find(l => l.id === parseInt(lenderId, 10));
            if(!lenderInfo){
                return (
                    <div className="element-wrapper">
                        <div className="element-box">
                            <div className="element-info">
                                <div className="element-info-with-icon">
                                    <div className="element-info-icon">
                                        <div className="os-icon os-icon-wallet-loaded"/>
                                    </div>
                                    <div className="element-info-text">
                                        <h5 className="element-inner-header">Profile Setup</h5>
                                        <div className="element-inner-desc">
                                            Please Take time to fill out the form below correctly. The phone number on this page will be used for notifications on all necessary transactions. <a href="http://consumermfb.com.ng" target="_blank" rel="noopener noreferrer">Feel free to contact our help desk if you need a  walk-through.</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div className="alert alert-danger" style={{marginBottom: '15px'}}>Lender not found</div>
                            </div>
                        </div>
                    </div>
                )
            }
        }

        return (
            <div className="element-wrapper">
                <div className="element-box">
                    <div className="element-info">
                        <div className="element-info-with-icon">
                            <div className="element-info-icon">
                                <div className="os-icon os-icon-wallet-loaded"/>
                            </div>
                            <div className="element-info-text">
                                <h5 className="element-inner-header">Profile Setup</h5>
                                <div className="element-inner-desc">
                                    Please Take time to fill out the form below correctly. The phone number on this page will be used for notifications on all necessary transactions. <a href="http://consumermfb.com.ng" target="_blank" rel="noopener noreferrer">Feel free to contact our help desk if you need a  walk-through.</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        {error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                        {message !== '' && <div className="alert alert-success" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Success!</strong> ${message}`}}></div>}
                        <form onSubmit={handleSubmit(this.signUp)}>
                            {lender && lenderId === '' && (
                                <div className="row">
                                    <div className="col-sm-6">
                                        <Field
                                            name="lender_id"
                                            component={renderSelectField}
                                            label="Lending Office"
                                            data={lenders.filter(l => l.id === lender.id)}
                                        />
                                    </div>
                                    <div className="col-sm-6"/>
                                </div>
                            )}
                            {lenderId !== '' && (
                                <div className="row">
                                    <div className="col-sm-6">
                                        <Field
                                            name="lender_id"
                                            component={renderSelectField}
                                            label="Lending Office"
                                            data={lenders.filter(l => l.id === parseInt(lenderId, 10))}
                                            readOnly={true}
                                        />
                                    </div>
                                    <div className="col-sm-6"/>
                                </div>
                            )}
                            <div className="row">
                                <div className="col-sm-6">
                                    <Field
                                        name="bvn"
                                        id="bvn"
                                        type="number"
                                        component={renderField}
                                        label="BVN"
                                    />
                                </div>
                                <div className="col-sm-6">
                                    <Field
                                        name="employer"
                                        id="employer"
                                        type="text"
                                        component={renderField}
                                        label="Employer Office"
                                    />
                                </div>
                            </div>
                            <div className="row">
                                <div className="col-sm-6">
                                    <Field
                                        name="email"
                                        id="email"
                                        type="email"
                                        component={renderField}
                                        label="Email Address"
                                    />
                                </div>
                                <div className="col-sm-6">
                                    <Field
                                        name="phone"
                                        id="phone"
                                        type="text"
                                        component={renderField}
                                        label="Phone Number"
                                        disabled={phone !== ''}
                                    />
                                </div>
                            </div>
                            <div className="row">
                                <div className="col-sm-6">
                                    <Field
                                        name="password"
                                        id="password"
                                        type="password"
                                        component={renderField}
                                        label="Password"
                                    />
                                </div>
                                <div className="col-sm-6">
                                    <Field
                                        name="password_confirmation"
                                        id="password_confirmation"
                                        type="password"
                                        component={renderField}
                                        label="Confirm Password"
                                    />
                                </div>
                            </div>
                            <Field
                                name="agree"
                                id="agree"
                                component={renderCheckbox}
                                label="I agree to terms and conditions"
                            />
                            <div className="form-buttons-w">
                                <button className="btn btn-primary" disabled={pristine || submitting} type="submit">
                                    {submitting? <img src={loading} alt=""/> : 'Submit'}
                                </button>
                                <button className="btn btn-secondary" type="button" onClick={() => this.props.history.push({ pathname: `/` })}>Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        );
    }
}

ProfileForm = reduxForm({
    form: 'profileform',
    validate
})(ProfileForm);

const mapStateToProps = (state, ownProps) => {
	return {
		initialValues: {
            phone: state.user.phone,
            lender_id: ownProps.lenderId !== '' ? ownProps.lenderId : (state.lender.profile ? state.lender.profile.id : 1),
            employer: ownProps.user ? ownProps.user.employer : '',
            email: ownProps.user ? ownProps.user.email : '',
		},
		phone: state.user.phone,
        lenders: state.lender.list,
        lender: state.lender.profile,
        employer: ownProps.user ? ownProps.user.employer : '',
	}
};

export default withRouter(connect(mapStateToProps, { reset, doNotify })(ProfileForm));
