<?php

namespace Database\Seeders;

use App\Models\Earning;
use App\Models\Loan;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class FixCustomerID extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $users = User::withTrashed()->whereNull('username')->get();
        foreach ($users as $user){
            if($user->platform == 'remita'){
                $user = User::find($user->id);
                $user->ippis = NULL;
                $user->save();
            }
        }

        $loans = Loan::where('platform', 'remita')->get();
        foreach ($loans as $loan){
            $earnings = Earning::where('auth_code', $loan->auth_code)->get();

            if(count($earnings) > 0){
                $user = User::find($loan->user_id);
                if($user && ($user->customer_id == null || $user->customer_id == '')){
                    Log::error('customer id: '.$earnings[0]->customer_id);
                    $user->customer_id = $earnings[0]->customer_id;
                    $user->save();
                }
            }
        }
    }
}
