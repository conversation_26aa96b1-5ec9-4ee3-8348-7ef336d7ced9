<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\PaymentDate;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UndoTransactions extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::beginTransaction();
        try {
            $transactions = DB::select("SELECT n.id, n.user_id, n.lender_id, n.category, n.category_id, t.repayment_reference, t.approved, t.loan_id, t.user_id as t_user_id, t.amount FROM notifies n join actions a on a.id = n.action_id join transactions t on t.id=n.category_id where a.name = 'repayment_approved' and n.created_at like '%2022-01-05%' and t.approved=1");
            foreach ($transactions as $item) {
                $transaction = Transaction::where('id', $item->category_id)->first();
                if ($transaction) {
                    $loan = Loan::where('id', $item->loan_id)->first();

                    $all_transactions = Transaction::where('loan_id', $loan->id)->where('transaction_flag', 'deduction')->get();

                    if ($loan && $loan->monthly_deduction != $transaction->amount) {
                        $loan->liquidated = 0;
                        $loan->liquidate_approve = 0;
                        $loan->liquidate_approve_by = null;
                        $loan->liquidate_approve_at = null;
                        $loan->liquidated_at = null;
                        $loan->save();

                        $transaction->amount = $loan->monthly_deduction;
                        $transaction->principal = $loan->monthly_principal;
                        $transaction->interest = $loan->monthly_interest;
                        $transaction->outst_amount = 0;
                        $transaction->status = 'Pending';
                        $transaction->description = null;
                        $transaction->repayment_date = null;
                        $transaction->approved = 0;
                        $transaction->approved_by = null;
                        $transaction->approved_at = null;
                        $transaction->save();

                        $user = User::where('id', $loan->user_id)->first();

                        if ($user->loan_id && $user->loan_id != $loan->id) {
                            $l = Loan::where('id', $user->loan_id)->delete();
                        }

                        $user->loan_id = $loan->id;
                        $user->save();

                        $payment_date = PaymentDate::where('loan_id', $loan->id)->where('transaction_id', $transaction->id)->first();
                        if ($payment_date) {
                            $payment_date->transaction_id = null;
                            $payment_date->paid = 0;
                            $payment_date->paid_at = null;
                            $payment_date->save();
                        }
                    }
                }
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());
        }
    }
}
