/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react';
import { Link } from 'react-router-dom';
import numeral from 'numeral';
import moment from 'moment';
import Tooltip from 'antd/lib/tooltip';
import Popover from 'antd/lib/popover';

import ScheduleDate from './ScheduleDate';

const Office = ({ o, currentPage, deleteOffice }) => {
	return (
		<tr>
			<td>{o.name}</td>
			<td>{`₦${numeral(o.amount_disbursed).format('0,0.00')}`}</td>
			<td>{`₦${numeral(o.principal_paid).format('0,0.00')}`}</td>
			<td>{`₦${numeral(o.interest_paid).format('0,0.00')}`}</td>
			<td>{`₦${numeral(o.principal_balance).format('0,0.00')}`}</td>
			<td className="row-actions">
				<Tooltip title="View Office">
					<Link to={`/offices/view/${o.id}?p=${currentPage}&office=${o.name}`}>
						<i className="os-icon os-icon-link-3" />
					</Link>
				</Tooltip>
				{o.name !== 'Remita' && (
					<Tooltip title="View Schedule">
						<Link to={`/offices/schedule/${o.id}?z=${moment().format('YYYY')}&p=${currentPage}&office=${o.name}`}>
							<i className="os-icon os-icon-arrow-2-up" />
						</Link>
					</Tooltip>
				)}
				{o.name !== 'Remita' && (
					<Popover content={<ScheduleDate office={o} />} trigger="click" title="Generate Schedule">
						<a className="cursor">
							<i className="icon-hourglass" />
						</a>
					</Popover>
				)}
				{o.name !== 'Remita' && o.amount_disbursed === 0 && (
					<Tooltip title="Delete Office">
						<a className="text-danger cursor" onClick={() => deleteOffice(o.id)}>
							<i className="os-icon os-icon-database-remove" />
						</a>
					</Tooltip>
				)}
			</td>
		</tr>
	);
};

export default Office;
