<?php

use App\Http\Controllers\Api\V1;
use App\Http\Controllers\Api\V1\AccountnoController;
use App\Http\Controllers\Api\V1\BankController;
use App\Http\Controllers\Api\V1\LenderController;
use App\Http\Controllers\Api\V1\LoanController;
use App\Http\Controllers\Api\V1\MailController;
use App\Http\Controllers\Api\V1\MerchantController;
use App\Http\Controllers\Api\V1\MultiFundController;
use App\Http\Controllers\Api\V1\NotifyController;
use App\Http\Controllers\Api\V1\OfficeController;
use App\Http\Controllers\Api\V1\RemitaTransController;
use App\Http\Controllers\Api\V1\ReportController;
use App\Http\Controllers\Api\V1\ScheduleController;
use App\Http\Controllers\Api\V1\SettingController;
use App\Http\Controllers\Api\V1\SupportController;
use App\Http\Controllers\Api\V1\TransactionController;
use App\Http\Controllers\Api\V1\UserController;
use App\Http\Controllers\Api\V1\UssdController;
use App\Http\Controllers\Api\V1\UssdLiveController;
use App\Http\Controllers\Api\V1\WalletController;
use App\Http\Controllers\Api\V1\WithdrawController;
use App\Http\Controllers\Api\V2\AuthController;
use Illuminate\Support\Facades\Route;


/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/


Route::get('/', function () {
    return response()->json(['message' => 'Welcome to Fastcash']);
});

Route::prefix('v1')->group(function () {
    Route::prefix('auth')->group(function () {
        Route::post('login', [V1\AuthController::class, 'login']);
        Route::post('verify', [V1\AuthController::class, 'verify']);
        Route::post('refresh', [V1\AuthController::class, 'refresh']);
        Route::post('signup', [V1\AuthController::class, 'signUp']);
        Route::post('signin', [V1\AuthController::class, 'signin']);
        Route::post('signin-merchant', [V1\AuthController::class, 'signinMerchant']);
        Route::post('forgot-password', [V1\AuthController::class, 'forgot']);
        Route::get('check-token/{token}', [V1\AuthController::class, 'checkToken']);
        Route::get('check-code/{code}', [V1\AuthController::class, 'checkCode']);
        Route::post('reset-password/{token}', [V1\AuthController::class, 'resetPswd']);
        Route::post('admin/reset-password/{token}', [V1\AuthController::class, 'adminResetPswd']);
        Route::post('merchant/reset-password/{token}', [V1\AuthController::class, 'merchantResetPswd']);
        Route::get('verify-email/{token}', [V1\AuthController::class, 'verifyEmail']);
        Route::post('verify-email', [V1\AuthController::class, 'verifyEmailCode']);
        Route::post('resend-email', [V1\AuthController::class, 'resendEmail']);
        Route::post('change-email', [V1\AuthController::class, 'changeEmail']);
    });

    Route::middleware(['auth:api'])->group(function () {
        Route::post('admin/find-user', [V1\AuthController::class, 'findUser']);

        Route::get('user', [V1\AuthController::class, 'user']);
        Route::post('logout', [V1\AuthController::class, 'logout']);

        Route::resource('users', UserController::class);            // user
        Route::post('users/change-password/{id}', [UserController::class, 'changePassword']); // change password
        Route::post('users/enable/{id}', [UserController::class, 'enable']);            // user
        Route::post('users/change-lender/{id}', [UserController::class, 'changeLender']);            // user
        Route::post('users/enable/{id}', [UserController::class, 'enableCustomer']);            // enable user
        Route::post('users/disable/{id}', [UserController::class, 'disableCustomer']);            // disable user
        Route::put('users/core-bank/{id}', [UserController::class, 'setCoreBank']);            // update core bank
        Route::put('users/update-customer/{id}', [UserController::class, 'updateCustomer']);            // update core bank
        Route::put('users/snooze/{id}', [UserController::class, 'snooze']);            // snooze user
        Route::put('users/unsnooze/{id}', [UserController::class, 'unsnooze']);            // un snooze user
        Route::put('users/profile/{id}', [UserController::class, 'updateProfile']);            // update profile
        Route::post('users/{id}/update-cid', [V1\UserController::class, 'updateCustomerId']);   // save only customer id

        Route::resource('loans', LoanController::class);            // loan
        Route::post('loans/{id}/update-insurance', [LoanController::class, 'updateInsurance']);            // save loan insurance
        Route::get('loans/admin/audit', [LoanController::class, 'audit']);            // audit loan
        Route::post('loans/verify/{id}', [LoanController::class, 'verifyLoan']);            // verify the loan
        Route::post('loans/approve/{id}', [LoanController::class, 'approveLoan']);            // approve loan
        Route::get('loans/consent-sms/{id}', [LoanController::class, 'sendConsentSMS']);            // send consent loan sms
        Route::post('loans/disburse-loan/{id}', [LoanController::class, 'disburseLoan']);    // transfer the loan amount to customer
        Route::post('loans/bypass/{id}', [LoanController::class, 'bypassPayment']);    // manual disburse
        Route::post('loans/email/{id}', [LoanController::class, 'email']);    // manual disburse
        Route::post('loans/verify-payment/{id}', [LoanController::class, 'verifyPayment']);            // verify payment
        Route::get('loans/balance/{id}', [LoanController::class, 'getLoanBalance']);            // get loan balance
        Route::get('loans/liquidated/pending', [LoanController::class, 'pendingLiquidated']);            // get loans
        Route::post('loans/start-liquidate/{id}', [LoanController::class, 'startLiquidateLoan']);  // start liquidate loan
        Route::post('loans/liquidate/{id}', [LoanController::class, 'liquidateLoan']);            // liquidate loan
        Route::post('loans/approve-liquidated/{id}', [LoanController::class, 'liquidateApprove']);     // approve liquidate loan
        Route::post('loans/decline-liquidated/{id}', [LoanController::class, 'liquidateDecline']);     // decline liquidate loan
        Route::post('loans/transfer/{id}', [LoanController::class, 'transferLoan']);     // transfer ippis loan to remita
        Route::get('loans/export/{slug}', [LoanController::class, 'export']);            // export loan
        Route::post('loans/multi-fund/{id}', [LoanController::class, 'multiFund']);            // multi fund loan
        Route::get('loans/overview/{lender_id}/{main_lender}', [LoanController::class, 'loanAnalytics']);       // loan analytics
        Route::post('loans/audit-passed/{id}', [LoanController::class, 'auditPassed']);       // loan audit-passed
        Route::post('loans/regenerate-wallet/{id}', [LoanController::class, 'regenerateWallet']);       // loan reg-wallet
        Route::post('loans/audited/{id}', [LoanController::class, 'audited']);       // loan audited
        Route::post('loans/sell/loans', [LoanController::class, 'transferLoans']);       // sell loans by transfer
        Route::get('loans/auth-check/{code}/{id}', [LoanController::class, 'authCodeCheck']);       // check duplicate auth code

        Route::put('loans/cancel/{id}', [LoanController::class, 'cancelLoan']);       // store loan cancel request
        Route::post('loans/cancel/{id}/approve', [LoanController::class, 'approveCancelLoan']);       // approve loan cancel requests
        Route::post('loans/cancel/{id}/decline', [LoanController::class, 'declineCancelLoan']);       // decline cancel requests

        Route::resource('offices', OfficeController::class);            // offices/mda
        Route::get('offices/schedule/{id}', [OfficeController::class, 'generateSchedule']); // generate schedule
        Route::resource('merchants', MerchantController::class);            // merchants
        Route::post('merchants/pay/{id}', [MerchantController::class, 'payMerchant']);            // pay merchant
        Route::post('merchants/single-pay', [MerchantController::class, 'singlePayMerchant']);            // pay merchant for single loan
        Route::post('merchants/find/user', [MerchantController::class, 'findUser']);            // find user for merchant

        Route::post('upload/payslip', [OfficeController::class, 'uploadPayslip']);            // upload payslip
        Route::post('upload/schedule', [OfficeController::class, 'uploadSchedule']);            // upload schedule
        Route::post('upload/users', [OfficeController::class, 'uploadUsers']);            // upload users

        Route::resource('multi-funds', MultiFundController::class);            // multi funding
        Route::resource('accounts', AccountnoController::class)->only(['store']);      // bank accounts

        Route::get('schedules/{id}/office', [ScheduleController::class, 'office']);            // schedules by office
        Route::get('schedules/{id}/loan', [ScheduleController::class, 'loan']);            // schedules by loan
        Route::delete('schedules/{id}', [ScheduleController::class, 'destroy']);            // delete schedules

        Route::get('report/fetch/{slug}', [ReportController::class, 'index']);              // fetch reports
        Route::get('report/export/{slug}', [ReportController::class, 'export']);              // export report

        Route::resource('wallet', WalletController::class);              // wallets
        Route::post('wallet/approve/{id}', [WalletController::class, 'approve']);              // wallets
        Route::post('wallet/credit/{loan_id}', [WalletController::class, 'credit']);              // wallets
        Route::post('wallet/debit-request/{id}', [WalletController::class, 'debitRequest']);              // debit wallet request
        Route::post('wallet/approve-debit/{id}', [WalletController::class, 'approveDebitRequest']);              // approve debit wallet request
        Route::post('wallet/decline-debit/{id}', [WalletController::class, 'declineDebitRequest']);              // approve debit wallet request

        Route::post('settings', [SettingController::class, 'store']);

        Route::resource('transactions', TransactionController::class);
        Route::get('transactions/{id}/loan', [TransactionController::class, 'loan']);
        Route::put('transactions/approve/{id}', [TransactionController::class, 'approve']);
        Route::get('transactions/approval/pending', [TransactionController::class, 'pendingTransactions']);
        Route::put('transactions/wallet/{id}', [TransactionController::class, 'wallet']);
        Route::put('transactions/liquidate/{id}', [TransactionController::class, 'liquidate']);

        Route::post('loan-history', [LoanController::class, 'loanHistory']);
        Route::post('loan-history-request', [LoanController::class, 'loanHistoryRequest']);
        Route::get('search', [UserController::class, 'search']);
        Route::get('profile/{id}', [UserController::class, 'profile']);            // get user profile
        Route::get('user-transactions/{id}', [UserController::class, 'transactions']);            // get user transactions

        Route::resource('supports', SupportController::class);            // support
        Route::post('supports/reply/{id}', [SupportController::class, 'reply']);            // support
        Route::post('supports/close/{id}', [SupportController::class, 'close']);            // support
        Route::get('search-ticket', [SupportController::class, 'search']);

        Route::resource('notifications', NotifyController::class);
        Route::resource('support-mails', MailController::class);
        Route::post('upload/attachment', [MailController::class, 'upload']);

        Route::resource('lenders', LenderController::class);                     // lender
        Route::put('lenders/update/{id}', [LenderController::class, 'profileUpdate']);
        Route::post('lenders/enable/{id}', [LenderController::class, 'enable']);
        Route::post('lenders/disable/{id}', [LenderController::class, 'disable']);

        Route::resource('remita-transactions', RemitaTransController::class);
        Route::post('remita-transactions/{type}/upload', [RemitaTransController::class, 'upload']);
        Route::get('remita-transactions/report/download', [RemitaTransController::class, 'download']);

        Route::resource('withdrawals', WithdrawController::class);
        Route::post('withdrawals/approve/{id}', [WithdrawController::class, 'approve']);
        Route::post('withdrawals/decline/{id}', [WithdrawController::class, 'decline']);
        Route::get('withdrawals/export/report', [WithdrawController::class, 'export']);
    });

    Route::get('settings', [SettingController::class, 'index']);
    Route::get('banks', [BankController::class, 'index']);
    Route::post('repayment/notification', [TransactionController::class, 'repayment']);

    Route::post('ussd/service', [UssdController::class, 'index']);
    Route::post('ussd/service/setting', [UssdController::class, 'createOrUpdateSetting']);
    Route::get('ussd/service/setting', [UssdController::class, 'getSetting']);
    // Route::post('ussd/service/setting', [UssdController::class, 'createOrUpdateSetting'])->middleware('auth:api');
    // Route::get('ussd/service/setting', [UssdController::class, 'getSetting'])->middleware('auth:api');

    
    Route::get('mandates', [LoanController::class, 'getDuplicateMandates']);
    Route::get('wallet/export/credit', [WalletController::class, 'exportCredit']);

    Route::post('loans/grant/consent', [LoanController::class, 'grantConsent']);            // grant consent
    Route::get('loans/by/phone', [LoanController::class, 'loanPhone']);            // find loan

    Route::get('/', function () {
        return response()->json(['message' => 'Fastcash API V1']);
    });
});

Route::prefix('v2')->group(function () {
    Route::prefix('auth')->group(function () {
        Route::post('signup', [AuthController::class, 'signUp']);
    });

    Route::get('/', function () {
        return response()->json(['message' => 'Fastcash API V2']);
    });
});

