<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\PaymentDate;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class FixLiquidatedLoan extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $users = User::whereNotNull('loan_id')->get();
            foreach ($users as $user) {
                $loan = Loan::where('liquidated', 1)->where('liquidate_approve', 1)->where('id', $user->loan_id)->first();
                if($loan) {
                    $_user = User::find($user->id);
                    $_user->loan_id = NULL;
                    $_user->save();

                    $transaction = Transaction::where('loan_id', $loan->id)->where('transaction_flag', 'liquidate')->first();

                    $pd = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)->update(['paid' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'transaction_id' => $transaction->id]);

                    $notify = doNotify($user->id, 'loans', $loan->id, 'loan_liquidated', 'success', NULL, $loan->lender_id, 1);

                    Log::debug(json_encode($loan));
                }
            }
        } catch (\Exception $e) {
            Log::error($e->getLine().' '.$e->getMessage().' '.$e->getFile());
        }
    }
}
