import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import numeral from 'numeral';

import { baseURL, httpRequest, walletAPI } from '../config/constants';
import { setLoanAnalytics } from '../actions/loan';
import { startBlock, stopBlock } from '../actions/ui-block';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {}
    if (!values.amount) {
        errors.amount = 'Enter amount';
	}
	if(values.amount && values.amount < 10000) {
		errors.amount = 'You shouldn\'t be depositing anything less that NGN10,000.00';
	}
    return errors;
};

const renderField = ({input, id, label, type, placeholder, meta: {touched, error, warning}}) => (
	<div className={`form-group ${(touched && error && 'has-error')}`}>
		<label className="lighter" htmlFor={id}>{label}</label>
		<div className="input-group mb-2 mr-sm-2 mb-sm-0">
			<input {...input} placeholder={placeholder} type={type} className="form-control" />
			<div className="input-group-append">
				<div className="input-group-text">NGN</div>
			</div>
		</div>
		{touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
	</div>
);

class DepositFunds extends Component {
	depositFunds = data => {
		this.props.startBlock();
		const { user, lender } = this.props;
		const info = { ...data, user_id: user.id, lender_id: lender.id, approved: lender.id === 1 ? 1 : 0, category: 'deposit', is_lender: 1, type: 'credit' };
		return httpRequest(`${baseURL}${walletAPI}`, 'POST', true, info)
			.then(response => {
				this.props.setLoanAnalytics(response.details);
				this.props.reset('depositfunds');
				this.props.stopBlock();
				const message = `${numeral(data.amount).format('0,0.00')} has been deposited!`;
				this.notify('', message, 'success');
			})
			.catch(error => {
				this.props.stopBlock();
				const message = error.message || 'Error, check your connection and try again';
				this.notify('', message, 'error');
				throw new SubmissionError({
					_error: message,
				});
			});
	};

	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	render() {
		const { handleSubmit, error } = this.props;
		return (
			<div className="element-box">
				{error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
				<form onSubmit={handleSubmit(this.depositFunds)}>
					<h5 className="element-box-header">Deposit Funds</h5>
					<div className="row">
						<div className="col-sm-12">
							<Field
								name="amount"
								id="amount"
								type="number"
								component={renderField}
								label="Select Amount"
								placeholder="Enter Amount..."
							/>
						</div>
					</div>
					<div className="form-buttons-w compact clearfix">
						<button className="btn btn-primary float-right" type="submit">
							<span>Deposit</span>
						</button>
					</div>
				</form>
			</div>
		);
	}
}

DepositFunds = reduxForm({
    form: 'depositfunds',
    validate,
})(DepositFunds);

const mapStateToProps = (state, ownProps) => {
	return {
		initialValues: {
            amount: 0,
		},
		user: state.user.user,
		lender: state.lender.profile,
	}
};

export default connect(mapStateToProps, { reset, setLoanAnalytics, startBlock, stopBlock, doNotify })(DepositFunds);