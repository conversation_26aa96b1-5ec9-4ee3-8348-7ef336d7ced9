<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateAccount extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('accountnos', function (Blueprint $table) {
            $table->string('bank_code')->nullable()->after('bank_branch');
            $table->tinyInteger('verified')->default(0)->after('bank_code');
            $table->string('bvn')->nullable()->after('verified');
            $table->string('phone')->nullable()->after('bvn');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('accountnos', function (Blueprint $table) {
            //
        });
    }
}
