<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class Remita
{
	/**
     * @param $merchantID
     * @param $APIKey
     * @param $requestID
     * @param $authorization
     * @param $bd
     * @return null|object
     * @internal param $body
     */
    public function getLoanHistory($api, $merchantID, $APIKey, $requestID, $authorization, $bd)
    {
        try {
            $url = env("APP_DEBUG") ? env("REMITA_DEBUG_URL") : env("REMITA_URL");

            $client = new Client([
                'base_uri' => $url,
                'verify' => false,
            ]);

            $response = $client->post($api, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'MERCHANT_ID' => $merchantID,
                    'API_KEY' => $APIKey,
                    'REQUEST_ID' => $requestID,
                    'AUTHORIZATION' => $authorization,
                ],
                'body' => json_encode($bd),
            ]);

            $body = $response->getBody();
            $stringBody = (string) $body;

            // error_log('string body....: ' . $stringBody);

            return json_decode($stringBody);
        } catch (\Exception $e) {
            // error_log('error body....: ' . $e->getMessage() . ' ' . $e->getFile());
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }

	/**
     * @param $merchantID
     * @param $APIKey
     * @param $requestID
     * @param $authorization
     * @param $bd
     * @return null|object
     * @internal param $body
     */
    public function getLoanHistoryAccountNumber($merchantID, $APIKey, $requestID, $authorization, $bd)
    {
        try {
            $url = env("APP_DEBUG") ? env("REMITA_DEBUG_URL") : env("REMITA_URL");

            $client = new Client([
                'base_uri' => $url,
                'verify' => false,
            ]);

            // $response = $client->post('remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/salary/history/ph', [
            $response = $client->post('remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/salary/history/provideCustomerDetails', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'MERCHANT_ID' => $merchantID,
                    'API_KEY' => $APIKey,
                    'REQUEST_ID' => $requestID,
                    'AUTHORIZATION' => $authorization,
                ],
                'body' => json_encode($bd),
            ]);

            $body = $response->getBody();
            $stringBody = (string) $body;

            // error_log('string body for account number....: ' . $stringBody);

            return json_decode($stringBody);
        } catch (\Exception $e) {
            error_log('error body for account number....: ' . $e->getMessage() . ' ' . $e->getFile());
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }

	/**
     * @param $merchantID
     * @param $APIKey
     * @param $requestID
     * @param $authorization
     * @param $bd
     * @return null|string
     * @internal param $body
     */
	public function checkRepayments($merchantID, $APIKey, $requestID, $authorization, $bd)
    {
        try {
            $url = env("APP_DEBUG") ? env("REMITA_DEBUG_URL") : env("REMITA_URL");

            $client = new Client([
                'base_uri' => $url
            ]);

            $response = $client->post('remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/loan/payment/history', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'MERCHANT_ID' => $merchantID,
                    'API_KEY' => $APIKey,
                    'REQUEST_ID' => $requestID,
                    'AUTHORIZATION' => $authorization,
                ],
                'body' => json_encode($bd),
                'debug' => false,
            ]);

            $body = $response->getBody();
            $stringBody = (string) $body;
            return json_decode($stringBody);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }

	/**
     * @param $merchantID
     * @param $APIKey
     * @param $requestID
     * @param $authorization
     * @param $bd
     *
     * @return null|string
     */
    public function sendLoanNotification($merchantID, $APIKey, $requestID, $authorization, $bd)
    {
        try {
            $url = env("APP_DEBUG") ? env("REMITA_DEBUG_URL") : env("REMITA_URL");

            $client = new Client([
                'base_uri' => $url
            ]);

            $response = $client->post('remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/post/loan', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'API_KEY' => $APIKey,
                    'MERCHANT_ID' => $merchantID,
                    'REQUEST_ID' => $requestID,
                    'AUTHORIZATION' => $authorization,
                ],
                'body' => json_encode($bd),
                'debug' => false,
            ]);

            $body = $response->getBody();
            $stringBody = (string) $body;
            return json_decode($stringBody);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }

	/**
     * @param $merchantID
     * @param $APIKey
     * @param $requestID
     * @param $authorization
     * @param $bd
     *
     * @return null|string
     */
    public function stopLoanCollection($merchantID, $APIKey, $requestID, $authorization, $bd)
    {
        try {
            $url = env("APP_DEBUG") ? env("REMITA_DEBUG_URL") : env("REMITA_URL");

            $client = new Client([
                'base_uri' => $url
            ]);

            $response = $client->post('remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/stop/loan', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'API_KEY' => $APIKey,
                    'MERCHANT_ID' => $merchantID,
                    'REQUEST_ID' => $requestID,
                    'AUTHORIZATION' => $authorization,
                ],
                'body' => json_encode($bd),
                'debug' => false,
            ]);

            $body = $response->getBody();
            $stringBody = (string) $body;
            return json_decode($stringBody);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }

	/**
	 * The APIs below will enable you debit your account to credit a sole beneficiary in any bank.
	 */
	public function disburseFund($merchantID, $APIKey, $requestID, $requestTS, $apiDetailsHash, $bd)
    {
        try {
            $url = env("APP_DEBUG") ? env("REMITA_DEBUG_URL") : env("REMITA_URL");

            $client = new Client([
                'base_uri' => $url
            ]);

            $response = $client->post('remita/exapp/api/v1/send/api/rpgsvc/rpg/api/v2/merc/payment/singlePayment.json', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'MERCHANT_ID' => $merchantID,
                    'API_KEY' => $APIKey,
                    'REQUEST_ID' => $requestID,
                    'REQUEST_TS' => $requestTS,
                    'API_DETAILS_HASH' => $apiDetailsHash,
                ],
                'body' => json_encode($bd),
                'debug' => false,
            ]);

            $body = $response->getBody();
            $stringBody = (string) $body;
            return json_decode($stringBody);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }

	public function verifyPayment($merchantID, $APIKey, $requestID, $requestTS, $apiDetailsHash, $bd)
    {
        try {
            $url = env("APP_DEBUG") ? env("REMITA_DEBUG_URL") : env("REMITA_URL");

            $client = new Client([
                'base_uri' => $url
            ]);

            $response = $client->post('remita/exapp/api/v1/send/api/rpgsvc/rpg/api/v2/merc/payment/status', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'MERCHANT_ID' => $merchantID,
                    'API_KEY' => $APIKey,
                    'REQUEST_ID' => $requestID,
                    'REQUEST_TS' => $requestTS,
                    'API_DETAILS_HASH' => $apiDetailsHash,
                ],
                'body' => json_encode($bd),
                'debug' => false,
            ]);

            $body = $response->getBody();
            $stringBody = (string) $body;
            return json_decode($stringBody);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}
