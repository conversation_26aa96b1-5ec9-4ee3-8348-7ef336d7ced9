<!doctype html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Loan History</title>
</head>
<style type="text/css">
    /* latin-ext */
    @font-face {
        font-family: 'Italianno';
        font-style: normal;
        font-weight: 400;
        src: local('Italianno'), local('Italianno-Regular'), url(https://fonts.gstatic.com/s/italianno/v7/dg4n_p3sv6gCJkwzT6RXhpwoYQAugxW4.woff2) format('woff2');
        unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
    }

    /* latin */
    @font-face {
        font-family: 'Italianno';
        font-style: normal;
        font-weight: 400;
        src: local('Italianno'), local('Italianno-Regular'), url(https://fonts.gstatic.com/s/italianno/v7/dg4n_p3sv6gCJkwzT6RXiJwoYQAugw.woff2) format('woff2');
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
    }

    html {
        font-family: sans-serif;
        line-height: 1.15;
        -ms-text-size-adjust: 100%;
        -webkit-text-size-adjust: 100%
    }

    body {
        margin: 0
    }

    article, aside, footer, header, nav, section {
        display: block
    }

    b, strong {
        font-weight: bolder
    }

    img {
        border-style: none
    }

    html {
        -webkit-box-sizing: border-box;
        box-sizing: border-box
    }

    *, *::before, *::after {
        -webkit-box-sizing: inherit;
        box-sizing: inherit
    }

    body {
        font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-size: 10px;
        font-weight: 400;
        line-height: 1.5;
        color: #3E4B5B;
        background-color: #fff
    }

    h1, h2, h3, h4, h5, h6 {
        margin-top: 0;
        margin-bottom: .5rem
    }

    p {
        margin-top: 0;
        margin-bottom: 16px
    }

    ol, ul, dl {
        margin-top: 0;
        margin-bottom: 16px
    }

    ol ol, ul ul, ol ul, ul ol {
        margin-bottom: 0
    }

    img {
        vertical-align: middle
    }

    table {
        border-collapse: collapse;
        background-color: transparent
    }

    th {
        text-align: left
    }

    label {
        display: inline-block;
        margin-bottom: .5rem
    }

    output {
        display: inline-block
    }

    [hidden] {
        display: none !important
    }

    h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
        margin-bottom: .5rem;
        font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-weight: 500;
        line-height: 1.1;
        color: #334152
    }

    small, .small {
        font-size: 80%;
        font-weight: 400
    }

    .row {
        display: block;
        margin-right: -15px;
        margin-left: -15px
    }

    .col-width {
        position: relative;
        width: 100%;
        min-height: 1px;
        padding-right: 15px;
        padding-left: 15px;
    }

    .clearfix::after {
        display: block;
        content: "";
        clear: both
    }

    html {
        height: 100%
    }

    body {
        min-height: 100%;
        position: relative;
        padding: 50px;
        overflow-x: hidden
    }

    body:before {
        content: "";
        position: absolute;
        z-index: -1;
        top: 0px;
        left: 0px;
        bottom: 0px;
        right: 0px
    }

    .invoice-w {
        padding: 20px 1rem;
        /*margin-bottom: 14px;*/
        border-radius: 4px;
        background-color: #fff;
        -webkit-box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1);
        box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1)
    }

    .invoice-w {
        font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        width: 640px;
        margin-left: auto;
        margin-right: auto;
        position: relative;
        overflow: hidden;
        padding-bottom: 0;
    }

    /*.invoice-w:before {
        width: 140%;
        height: 215px;
        background-color: #faf9f3;
        position: absolute;
        top: -13%;
        left: -24%;
        !*-webkit-transform: rotate(-27deg);*!
        !*transform: rotate(-27deg);*!
        -webkit-transform: rotate(-2deg);
        transform: rotate(-2deg);
        content: "";
        z-index: 1
    }*/

    .invoice-w .infos {
        position: relative;
        z-index: 2;
    }

    .invoice-w .infos .info-1 {
        font-size: 10px;
        text-align: right
    }

    .invoice-w .infos .info-1 .company-name {
        font-size: 12px;
        margin-bottom: 0.1rem;
        /*margin-top: 4px*/
    }

    .invoice-w .infos .info-1 .company-extra {
        font-size: 9px;
        color: rgba(0, 0, 0, 0.4);
        margin-top: 8px
    }

    .invoice-w .infos .info-2 {
        padding-top: 4px;
        font-size: 10px;
        text-align: right
    }

    .invoice-w .infos .info-2 .company-name {
        font-size: 12px;
        margin-bottom: 0.1rem;
    }

    .invoice-w .terms {
        font-size: 8px;
    }

    .invoice-w .terms .terms-header {
        font-size: 9px;
        margin-bottom: 10px
    }

    .invoice-w .terms .terms-content {
        color: rgba(0, 0, 0, 0.6);
        font-size: 8px;
    }

    .invoice-heading {
        margin-bottom: 16px;
        margin-top: 10px;
        position: relative;
        z-index: 2
    }

    .invoice-footer {
        padding-top: 8px;
        padding-bottom: 8px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        display: block;
        margin-top: 2rem;
        font-size: 8px;
    }

    .invoice-footer .invoice-logo img {
        vertical-align: middle;
        height: 20px;
        width: auto;
        display: inline-block
    }

    .invoice-footer .invoice-logo span {
        vertical-align: middle;
        margin-left: 10px;
        display: inline-block
    }

    .invoice-footer .invoice-info span {
        display: inline-block
    }

    .invoice-footer .invoice-info span + span {
        margin-left: 16px;
        padding-left: 16px;
        border-left: 1px solid rgba(0, 0, 0, 0.1)
    }

    .invoice-body {
        display: block;
    }

    .invoice-logo-w img {
        width: 180px;
    }

    .profile-tile-meta ul {
        padding: 0 0 0 20px;
        margin: 0;
    }

    .profile-tile-meta ol {
        padding: 0 0 0 20px;
        margin: 0;
    }

    .float-left {
        float: left;
    }

    .float-right {
        float: right;
    }

    .motto {
        font-family: 'Italianno', cursive;
        font-weight: 100;
        font-size: 16px !important;
        font-style: italic;
    }

    .table {
        margin-top: 20px;
    }

</style>
<body style="padding: 5px;">
<div class="invoice-w">
    <div class="infos" style="position: relative;">
        <div class="invoice-logo-w" style="position: absolute; left: 0; top: 0;"><img alt="logo" src="img/consumer-logo.png"/>
        </div>
        <div class="info-1">
            <div class="company-name"><strong>CONSUMER MICROFINANCE BANK LTD</strong></div>
            <div class="company-name motto">Become a richer you</div>
            <div style="margin-top: 15px">
                <small style="display: block;font-size: 8px;">PLOT 2087, AISHA HOUSE, HERBERT MACAULAY WAY, (OPPOSITE
                    FEBSON MALL) WUSE ZONE 5, ABUJA.</small>
                <small style="display: block;font-size: 8px;">Website: www.consumermfb.com E-mail:
                    <EMAIL></small>
            </div>
        </div>
    </div>
    <div class="invoice-body">
        <div class="invoice-table">
            <div class="terms row">
                <table class="table">
                    <tr>
                        <td style="vertical-align: top;width: 50%;">
                            <div class="col-width">
                                <div class="invoice-heading">
                                    <h5 style="font-size: 9px;font-weight: 600;">LOAN HISTORY</h5>
                                    <div class="terms-content">{{ \Carbon\Carbon::now()->format('d.m.Y') }}</div>
                                    <div class="terms-content"
                                         style="text-transform: uppercase;font-weight: 700;">{{ $history->user->name }}</div>
                                    <div class="terms-content"
                                         style="text-transform: uppercase;">{{ $history->office->name }}</div>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            @foreach ($history->loans as $loan)
                <div style="background-color: #F4F4F4; margin: 20px 0px 0px">
                    <div style="padding: 20px; text-transform: uppercase; color: #8D929D; font-size: 11px; font-weight: bold; letter-spacing: 1px; text-align: center;">Loan Description: {{ $history->user->loan_id && $history->user->loan_id == $loan->id ? ($loan->is_topup == 1 ? 'Current Topup-Loan' : 'Current Loan') : ($loan->is_topup === 1 ? 'Topup-Loan' : 'Loan') }} Request</div>
                    <table style="border-collapse: collapse; width: 100%;">
                        <tbody>
                        <tr>
                            <td style="padding: 10px 40px; color: #111; border: 1px solid #e7e7e7; border-left: none; border-right: none;">
                                <div style="text-transform: uppercase; letter-spacing: 1px; color: #B8B8B8; font-size: 10px; font-weight: bold; margin-bottom: 3px;">
                                    Amount
                                </div>
                                <div style="font-weight: bold;">NGN{{ number_format($loan->amount, 2) }}</div>
                            </td>
                            <td style="padding: 10px 40px; color: #111; border: 1px solid #e7e7e7; border-right: none; width: 25%;">
                                <div style="text-transform: uppercase; letter-spacing: 1px; color: #B8B8B8; font-size: 10px; font-weight: bold; margin-bottom: 3px;">
                                    Tenure
                                </div>
                                <div style="font-weight: bold;">{{ $loan->tenure }} Months</div>
                            </td>
                            <td style="padding: 10px 40px; color: #111; border: 1px solid #e7e7e7; border-right: none; width: 25%;">
                                <div style="text-transform: uppercase; letter-spacing: 1px; color: #B8B8B8; font-size: 10px; font-weight: bold; margin-bottom: 3px;">
                                    Date
                                </div>
                                <div style="font-weight: bold;">{{ \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $loan->created_at)->format('d-M-Y') }}</div>
                            </td>
                            <td style="padding: 10px 40px; color: #111; border: 1px solid #e7e7e7; width: 25%;">
                                <div style="text-transform: uppercase; letter-spacing: 1px; color: #B8B8B8; font-size: 10px; font-weight: bold; margin-bottom: 3px;">
                                    Status
                                </div>
                                <div style="font-weight: bold;{{ $loan->deleted_at == '' || $loan->deleted_at == null ? ($loan->liquidate_approve == 1 ? 'color: #047bf8' : 'color: #71c21a;') : 'color: #D62525;' }}">{{ $loan->deleted_at == '' || $loan->deleted_at == null ? ($loan->liquidated == 1 ? 'ended' : 'active') : 'declined' }}</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <table style="border-collapse: collapse; width: 100%;">
                        <tbody>
                        <tr>
                            <td style="padding: 10px 40px; color: #111; border: 1px solid #e7e7e7; border-left: none; border-right: none;">
                                <div style="text-transform: uppercase; letter-spacing: 1px; color: #B8B8B8; font-size: 10px; font-weight: bold; margin-bottom: 3px;">
                                    Loan Request
                                </div>
                                <div style="font-weight: bold;">{{ \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $loan->created_at)->format('d-M-Y h:ia') }}</div>
                            </td>
                            <td style="padding: 10px 40px; color: #111; border: 1px solid #e7e7e7; border-right: none; width: 25%;">
                                <div style="text-transform: uppercase; letter-spacing: 1px; color: #B8B8B8; font-size: 10px; font-weight: bold; margin-bottom: 3px;">
                                    Loan Approval
                                </div>
                                <div style="font-weight: bold;">{{ $loan->approved_at == null ? '--' : \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $loan->approved_at)->format('d-M-Y h:ia') }}</div>
                            </td>
                            <td style="padding: 10px 40px; color: #111; border: 1px solid #e7e7e7; border-right: none; width: 25%;">
                                <div style="text-transform: uppercase; letter-spacing: 1px; color: #B8B8B8; font-size: 10px; font-weight: bold; margin-bottom: 3px;">
                                    Loan Consent
                                </div>
                                @if ($loan->has_consented == '1')
                                    <div style="font-weight: bold;">{{ \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $loan->consented_at)->format('d-M-Y h:ia') }}</div>
                                @else
                                    <div style="font-weight: bold;">No Consent</div>
                                @endif
                            </td>
                            <td style="padding: 10px 40px; color: #111; border: 1px solid #e7e7e7; width: 25%;">
                                <div style="text-transform: uppercase; letter-spacing: 1px; color: #B8B8B8; font-size: 10px; font-weight: bold; margin-bottom: 3px;">
                                    Loan Disbursement
                                </div>
                                <div style="font-weight: bold;">{{ $loan->disbursed_at == null ? '--' : \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $loan->disbursed_at)->format('d-M-Y h:ia') }}</div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                @if (count($loan->transactions) > 0)
                    <div class="invoice-table" style="padding: 0px 40px;">
                        <table style="margin-top: 20px; width: 100%;">
                            <tbody>
                            <tr>
                                <th style="text-transform: uppercase; letter-spacing: 1px; color: #222; font-size: 10px; font-weight: bold; text-align: center" colspan="5">
                                    <div style="padding: 0 0 12px">Transactions</div>
                                </th>
                            </tr>
                            <tr>
                                <td style="text-transform: uppercase; letter-spacing: 1px; color: #B8B8B8; font-size: 10px; font-weight: bold;">
                                    Description
                                </td>
                                <td style="text-transform: uppercase; letter-spacing: 1px; color: #B8B8B8; font-size: 10px; font-weight: bold;">
                                    Amount Paid
                                </td>
                                <td style="text-transform: uppercase; letter-spacing: 1px; color: #B8B8B8; font-size: 10px; font-weight: bold; text-align: right;">
                                    Outst. Amount
                                </td>
                                <td style="text-transform: uppercase; letter-spacing: 1px; color: #B8B8B8; font-size: 10px; font-weight: bold; text-align: right;">
                                    Date
                                </td>
                                <td style="text-transform: uppercase; letter-spacing: 1px; color: #B8B8B8; font-size: 10px; font-weight: bold; text-align: right;">
                                    Status
                                </td>
                            </tr>
                            @foreach ($loan->transactions as $item)
                                <tr>
                                    <td style="padding: 12px 8px 12px 0;border-bottom: 1px solid rgba(0,0,0,0.05);">
                                        <div style="color: #111; font-size: 10px;">
                                            {{ $loan->is_topup === 1 ? 'Topup' : '' }} {{ $item->description != '' && $item->description != null ? $item->description : 'Pending Deduction' }}
                                        </div>
                                    </td>
                                    <td style="padding: 12px 8px; font-size: 10px; font-weight: bold; color: #111; border-bottom: 1px solid rgba(0,0,0,0.05);">
                                        NGN{{ $item->description == 'Loan Taken' ? number_format($loan->amount, 2) : number_format($item->amount, 2) }}
                                    </td>
                                    <td style="padding: 12px 8px; font-size: 10px; font-weight: bold; color: #111; border-bottom: 1px solid rgba(0,0,0,0.05);">
                                        NGN{{ number_format($item->outst_amount, 2) }}
                                    </td>
                                    <td nowrap="nowrap" style="padding: 12px 8px;border-bottom: 1px solid rgba(0,0,0,0.05);">
                                        <div style="color: #111; font-size: 10px;">
                                            {{ \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $item->created_at)->format('d-M-Y') }}
                                        </div>
                                    </td>
                                    <td style="padding: 12px 0 12px 8px;border-bottom: 1px solid rgba(0,0,0,0.05);">
                                        <div style="color: #111; font-size: 10px;">
                                            {{ ($loan->liquidate_approve == 0 && $item->description === 'Loan Liquidated') || $item->approved === 0 ? 'Pending' : $item->status }}
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif
            @endforeach
        </div>
    </div>
    <div class="invoice-footer clearfix">
        <div class="invoice-logo" style="float: left">
            <img alt="logo" src="img/consumer-logo.png"/>
            <span>Consumer Microfinance Bank</span>
        </div>
        <div class="invoice-info" style="float: right">
            <span><EMAIL></span>
            <span>0704.634.6454</span>
        </div>
    </div>
</div>
</body>
</html>
