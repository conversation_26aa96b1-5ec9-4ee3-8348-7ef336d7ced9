import React, { Component } from 'react';
import { connect } from "react-redux";

class ConsentWrapper extends Component {
	render() {
		const { loggedIn } = this.props;

		if (loggedIn) {
			return this.props.children;
		}

		return (
			<div className="all-wrapper menu-side">
				<div className="layout-w">
					<div className="content-w">
						{this.props.children}
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = state => {
	return {
		loggedIn: state.user.loggedIn,
	};
};

export default connect(mapStateToProps)(ConsentWrapper);
