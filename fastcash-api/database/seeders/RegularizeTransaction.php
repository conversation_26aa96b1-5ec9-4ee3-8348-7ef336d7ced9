<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\Transaction;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class RegularizeTransaction extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $transactions = Transaction::all();
            foreach ($transactions as $t){
                $loan = Loan::where('id', $t->loan_id)->first();

                if($loan){
                    $transaction = Transaction::where('id', $t->id)->first();
                    $transaction->lender_id = $loan->lender_id;
                    $transaction->save();
                } else {
                    info('loan not found: '.$t->id);
                }
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }
    }
}
