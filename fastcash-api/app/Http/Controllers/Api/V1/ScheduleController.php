<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Loan;
use App\Models\Office;
use App\Models\Schedule;
use App\Models\ScheduleHistory;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Log;

class ScheduleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function office(Request $request, $id)
    {
        $lender = $request->has('lender') ? $request->input('lender') : '';

        if ($lender != '') {
            $year = $request->has('year') ? $request->input('year') : date('Y');
            $schedules = Schedule::where('lender_id', $lender)->where('office_id', $id)->where('year', $year)->where('type', 1)->get();
            $schedules = $schedules->map(function ($item, $key) use ($lender) {
                $item->office = Office::where('id', $item->office_id)->first();
                $loan = Loan::where('id', $item->loan_id)->first(['id', 'user_id']);
                $loan->user = User::where('id', $loan->user_id)->first(['id', 'customer_id', 'platform', 'phone']);

                return $item;
            });
            return response()->json(compact('schedules'), 200);
        }

        return response()->json(['error' => 'could not fetch schedule'], 500);
    }

    /**
     * Display a listing of the resource.
     *
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function loan($id)
    {
        try {
            $my_schedules = Schedule::where('loan_id', $id)->where('type', 1)->get();
            $my_schedules = collect($my_schedules)->groupBy('month_name');
            $new_schedules = [];
            foreach ($my_schedules as $key => $schedule) {
                $new_schedules[] = $schedule[0];
            }

            $schedules = collect($new_schedules)->map(function ($item, $key) {
                $item->payment = Schedule::where('loan_id', $item->loan_id)->where('type', 1)->where('month', $item->month)->where('year', $item->year)->first();
                return $item;
            });

            return response()->json(compact('schedules'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            $schedules = [];
            return response()->json(compact('schedules'), 200);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $schedules = ScheduleHistory::where('schedule_id', $id)->get();
            foreach ($schedules as $schedule) {
                $wallet = Wallet::find($schedule->wallet_id);
                if ($wallet) {
                    $wallet->delete();
                }

                $transaction = Transaction::where('schedule_id', $schedule->schedule_id)->first();
                if ($transaction) {
                    $transaction->delete();
                }

                $schedul = Schedule::find($schedule->schedule_id);
                if ($schedul) {
                    $schedul->delete();
                }

                $user = User::where('id', $schedule->user_id)->update(['loan_id' => $schedule->loan_id]);
            }

            return response()->json(['id' => $id], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'Schedules could not be deleted'], 500);
        }
    }
}
