import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';

import { baseURL, httpRequest, merchantAPI, officeAPI } from '../config/constants';
import { addMerchant } from '../actions/merchant';
import loading from '../assets/img/loading.gif';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {}
    if (!values.firstname) {
        errors.firstname = 'Enter the first name';
    }
    if (!values.lastname) {
        errors.lastname = 'Enter the last name';
    }
    if (!values.email) {
        errors.email = 'Enter email address';
    }
    if (!values.phone) {
        errors.phone = 'Please give us the phone number';
    }
    if (!values.office) {
        errors.office = 'Select the office';
    }
    if (!values.gender) {
        errors.gender = 'Select the gender';
    }
    return errors;
};

const renderField = ({input, id, label, type, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" />
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

const renderSelectField = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label>{label}</label>
        <select {...input} className="form-control">
            <option value="">{`Select ${label}`}</option>
            {data.map((c, i) => <option value={c.id} key={i}>{c.name}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class CreateMerchant extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
            offices: [],
        };
    }
    
    async componentDidMount() {
        const { lender } = this.props;
        try {
            const response = await httpRequest(`${baseURL}${officeAPI}?q=1&lender=${lender.id}`, 'GET', true);
            this.setState({ offices: response.offices });
        }
        catch (error) {
            console.log(error);
        }
    }
    
    createMerchant = async data => {
        const { user, lender } = this.props;
		const details = { ...data, staff_id: user.id, lender_id: lender.id };
		try {
            await httpRequest(`${baseURL}${merchantAPI}`, 'POST', true, details);
            this.props.reset('createmerchant');
            this.notify('', 'merchant user account created!', 'success');
            this.props.closeModal();
        }
        catch (error) {
            const message = error.message || 'could not create merchant user account';
            this.notify('', message, 'error');
            throw new SubmissionError({
                _error: message,
            });
        }
    };
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

    render() {
        const { closeModal, handleSubmit, error, submitting, pristine } = this.props;
        const { offices } = this.state;
        const genders = [
            {id: 'male', name: 'Male'},
            {id: 'female', name: 'Female'},
        ];
        return (
            <div>
                <div className="modal-header faded smaller">
                    <div className="modal-title">
                        <span>FastCash Merchant</span>
                    </div>
                    <button aria-label="Close" className="close" onClick={closeModal} type="button"><span aria-hidden="true"> ×</span></button>
                </div>
                <form onSubmit={handleSubmit(this.createMerchant)}>
                    <div className="modal-body">
                        {error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}/>}
                        <div className="form-group">
                            <Field
                                name="office"
                                component={renderSelectField}
                                label="Offices"
                                data={offices}
                            />
                        </div>
                        <div className="row">
                            <div className="col-sm-6">
                                <Field
                                    name="firstname"
                                    id="firstname"
                                    type="text"
                                    component={renderField}
                                    label="First Name"
                                />
                            </div>
                            <div className="col-sm-6">
                                <Field
                                    name="lastname"
                                    id="lastname"
                                    type="text"
                                    component={renderField}
                                    label="Last Name"
                                />
                            </div>
                        </div>
                        <div className="row">
                            <div className="col-sm-12">
                                <Field
                                    name="email"
                                    id="email"
                                    type="email"
                                    component={renderField}
                                    label="Email"
                                />
                            </div>
                        </div>
                        <div className="row">
                            <div className="col-sm-6">
                                <Field
                                    name="gender"
                                    component={renderSelectField}
                                    label="Gender"
                                    data={genders}
                                />
                            </div>
                            <div className="col-sm-6">
                                <Field
                                    name="phone"
                                    id="phone"
                                    type="text"
                                    component={renderField}
                                    label="Phone Number"
                                />
                            </div>
                        </div>
                    </div>
                    <div className="modal-footer buttons-on-left">
                        <button className="btn btn-teal" disabled={pristine || submitting} type="submit">
                            {submitting? <img src={loading} alt=""/> : 'Create'}
                        </button>
                        <button className="btn btn-link cursor" onClick={closeModal} type="button"> Cancel</button>
                    </div>
                </form>
            </div>
        );
    }
}

CreateMerchant = reduxForm({
    form: 'createmerchant',
    validate,
})(CreateMerchant);

const mapStateToProps = (state, ownProps) => {
    return {
        initialValues: {
            gender: "",
            office: "",
        },
        user: state.user.user,
        lender: state.lender.profile,
    }
}

export default connect(mapStateToProps, { reset, addMerchant, doNotify })(CreateMerchant);
