<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class UpdateCurrentLoans extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $users = User::whereNotNull('loan_id')->get();
            info('loans: '.count($users));
            foreach ($users as $user){
                $loan = Loan::where('id', $user->loan_id)->first();
                $loan->is_current = 1;
                $loan->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }
    }
}
