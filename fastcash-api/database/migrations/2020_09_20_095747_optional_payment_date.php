<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class OptionalPaymentDate extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('payment_dates', function (Blueprint $table) {
            $table->dateTime('start_date_approved')->nullable()->after('end_date')->comment('approved date');
            $table->dateTime('end_date_approved')->nullable()->after('start_date_approved')->comment('generate from approved date');
            $table->dateTime('payment_date_approved')->nullable()->after('payment_date')->comment('generate payment date from approved date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('payment_dates', function (Blueprint $table) {
            $table->dropColumn(['start_date_approved', 'end_date_approved', 'payment_date_approved']);
        });
    }
}
