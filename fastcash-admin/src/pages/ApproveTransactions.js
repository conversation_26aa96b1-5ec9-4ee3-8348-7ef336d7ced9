/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import Modal from 'react-modal';
import { connect } from 'react-redux';
import Pagination from 'antd/lib/pagination';
import moment from 'moment';
import { bindActionCreators } from 'redux';
import startCase from 'lodash.startcase';
import qs from 'querystring';
import $ from 'jquery';
import { withRouter } from 'react-router-dom';
import debounce from 'lodash.debounce';

import BreadCrumbs from '../components/BreadCrumbs';
import UploadRemita from '../components/UploadRemita';
import { toggleMenu, setUnapprovedTransactions } from '../actions/user';
import { httpRequest, baseURL, transactionAPI, httpRequest2, remitaTransactionAPI, currency } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import { doNotify } from '../actions/general';
import { SHOW_PROFILE } from '../actions/types';
import { setLoanAnalytics } from '../actions/loan';
import loading from '../assets/img/loading.gif';
import ApproveTransaction from '../modals/ApproveTransaction';
import LoanProfile from '../components/LoanProfile';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 10;

class ApproveTransactions extends Component {
	state = {
		transactions: [],
		currentPage: 1,
		totalPage: 0,
		transaction_type: '',
		error: '',
		file: null,
		disabled: true,
		report: 'remita',
		location: '',
		exporting: false,
		searchText: '',
		qText: '',
		modalIsOpen: false,
		id: null,
		loan: null,
		customer: null,
		profileModalIsOpen: false,
		file_type: '',
	};

	componentWillMount() {
		Modal.setAppElement('body');
	}

	componentDidMount() {
		this.props.toggleMenu(false);
		
		const { location } = this.props;
		
		const query = qs.parse(location.search.replace('?', ''));
		
		const page = query && query.p ? parseInt(query.p, 10) : 1;
		const transaction_type = query && query.type ? query.type : '';
		const searchText = query && query.search ? query.search : '';

		this.setState({ currentPage: page, location: location.pathname, transaction_type, searchText });
		this.fetchTransactions(page, transaction_type, searchText);
	}

	fetchTransactions = async (pageNumber, type, search = '') => {
		try {
			this.props.startBlock();
			const rs = await httpRequest(`${baseURL}${transactionAPI}/approval/pending?page=${pageNumber}&pagesize=${pageSize}&transaction_type=${type}&search=${search}`, 'GET', true);
			const result = rs.result;
			this.setState({ currentPage: pageNumber, totalPage: result.total, transactions: result.data });
			this.props.stopBlock();
			window.scrollTo(0, 0);
		} catch (e) {
			this.props.stopBlock();
			this.notify('', 'could not load transactions!', 'error');
		}
	};

	componentWillUnmount() {
		this.props.toggleMenu(true);
	}
		
	showUserProfile = id => this.props.showProfile({ show_profile: true, user_id: id });

	doDecline = async id => {
		const action = window.confirm("Do you want to delete transaction?") ? true : false;
		if(action) {
			const { currentPage, transaction_type } = this.state;
			const { user } = this.props;
			this.props.startBlock();
			try {
				const response = await httpRequest(`${baseURL}${transactionAPI}/${id}`, 'DELETE', true, {
					staff_id: user.id,
				});
				this.props.setLoanAnalytics(response.loan_details);
				this.props.setUnapprovedTransactions(response.unapproved_transactions);
				this.notify('', 'Transaction Deleted!', 'success');
				this.props.stopBlock();
				this.fetchTransactions(currentPage, transaction_type);
			} catch (e) {
				this.props.stopBlock();
				const message = e.message || 'could not delete transaction';
				this.notify('', message, 'error');
			}
		}
	};

	onChange = pageNumber => {
		const { transaction_type, searchText } = this.state;
		const search = searchText !== '' ? `search=${searchText}&` : '';
		this.props.history.push(`/approve-transactions?${search}p=${pageNumber}&type=${transaction_type}`);
	};
	
	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	onSelectType = e => {
		const transaction_type = e.target.value;
		$(e.currentTarget).blur();
		this.props.history.push(`/approve-transactions?p=1&type=${transaction_type}`);
	};

	onChangeReport = e => {
		const type = e.target.value;
		$(e.currentTarget).blur();
		this.setState({ report: type });
	};

	onOpenDropzone = e => {
		this.setState({ error: '' });
	};

	onChangeType = e => {
		$(e.currentTarget).blur();
		this.setState({ file_type: e.target.value });
	};

	onDrop = async file => {
		this.props.startBlock();
		const { user } = this.props;
		const { report, transaction_type, file_type } = this.state;
        const { name } = file[0];

		let formData = new FormData();
		formData.append('file', file[0]);
		formData.append('staff', user.id);
		formData.append('file_type', file_type);

		try {
			const rs = await httpRequest2(`${baseURL}${remitaTransactionAPI}/${report}/upload`, 'POST', formData);
			this.setState({ file: { name, status: 1 }, disabled: true });
			this.props.stopBlock();
			this.fetchTransactions(1, transaction_type);
			if(report === 'bank'){
				const response = rs.report.split(':')[1];
				this.notify('', `${response} transactions approved!`, 'success');
			} else {
				this.notify('', 'remita transactions uploaded!', 'success');
			}
		}
		catch (error) {
			this.setState({ file: { name, status: 0 } });
			if (error.message) {
				this.displayError(error.message);
			}
			else {
				this.displayError('Error, check your connection and try again');
			}
		}
	};

	componentWillUpdate(nextProps, nextState) {
		const query = qs.parse(nextProps.location.search.replace('?', ''));
		const pageNumber = query && query.p ? parseInt(query.p, 10) : 1;
		const transaction_type = query && query.type ? query.type : '';
		const searchText = query.search ? query.search : '';

		if(nextState.currentPage !== pageNumber || nextProps.location.pathname !== nextState.location || transaction_type !== nextState.transaction_type || searchText !== nextState.qText){
			this.setState({
				location: nextProps.location.pathname,
				currentPage: pageNumber,
				transaction_type,
				qText: searchText,
				searchText,
			});

			this.fetchTransactions(pageNumber, transaction_type, searchText);
		}
	}

	displayError = (error) => {
		this.setState({ error, disabled: true });
		this.props.stopBlock();
	};

	exportReport = async e => {
		e.preventDefault();
		try {
			this.setState({ exporting: true });
			const rs = await httpRequest(`${baseURL}${remitaTransactionAPI}/report/download`, 'GET', true)
			if(rs.file) {
				this.setState({ exporting: false });
				this.notify('', 'remita repayment report has been exported', 'success');
				window.open(rs.file, '_blank');
			} else {
				this.notify('', rs.message || 'could not export remita repayment report', 'success');
				this.setState({ exporting: false });
			}
		} catch(error) {
			this.setState({ exporting: false });
			const message = error.message || 'could not export remita repayment report';
			this.notify('', message, 'error');
		}
	};

	doAddToWallet = async (id, type) => {
		const prompt = type === 'reconcile' ? 'Do you want to approve transaction?' : 'Do you want to credit to wallet?';
		const action = window.confirm(prompt) ? true : false;
		if(action) {
			try {
				const { user } = this.props;
				const { currentPage, transaction_type } = this.state;
				this.props.startBlock();
				const data = { staff_id: user.id, };
				const url = `${baseURL}${transactionAPI}/wallet/${id}`;
				const response = await httpRequest(url, 'PUT', true, data);
				this.props.setLoanAnalytics(response.loan_details);
				this.props.setUnapprovedTransactions(response.unapproved_transactions);
				this.notify('', 'Transaction Approved!', 'success');
				this.props.stopBlock();
				this.fetchTransactions(currentPage, transaction_type);
			} catch (e) {
				console.log(e);
				this.props.stopBlock();
				const message = e.message || 'could not approve transaction';
				this.notify('', message, 'error');
			}
		}
	};

	onSearch = e => {
		const searchText = e.target.value;
		this.setState({ searchText });
		
		if(searchText.length >= 3){
			this.doSearch(searchText);
		}

		if(searchText.length === 0){
			this.clearSearch();
		}
	};

	clearSearch = () => {
		this.setState({ searchText: '' });
		this.props.history.push(`/approve-transactions`);
	};

	doSearch = debounce(text => {
		this.props.history.push(`/approve-transactions?search=${text}`);
	}, 1500);

	doApproveTransaction = (id, loan, customer) => {
		document.body.className="pages modal-open";
        this.setState({ modalIsOpen: true, id, loan, customer });
	};

	cancelApprove = () => {
		this.setState({ modalIsOpen: false, id: null, loan: null, customer: null });
		document.body.className="pages";
	};
	
    openProfile = (customer, loan) => {
        document.body.className="pages modal-open";
        this.setState({ profileModalIsOpen: true, customer, loan });
    };

    closeProfileModal = () => {
        this.setState({ profileModalIsOpen: false, customer: null, loan: null });
		document.body.className="pages";
	}

	render() {
		const { transactions, currentPage, totalPage, error, file, disabled, report, transaction_type, exporting, searchText, modalIsOpen, id, loan, customer, profileModalIsOpen, file_type } = this.state;
		return (
			<div>
				<BreadCrumbs url="/dashboard" />
				<div className="content-i">
					<div className="content-box">
						<div className="element-wrapper">
							<div className="element-actions">
								{exporting ? (
									<a className="btn btn-primary btn-sm btn-export-sm pointer"><img src={loading} alt=""/></a>
								) : (
									<a className="btn btn-primary btn-sm btn-export-sm pointer" href="#" onClick={this.exportReport}><i className="os-icon os-icon-file-text"></i><span>Export Remita Loan Report</span></a>
								)}
							</div>
							<h6 className="element-header">Approve Transactions</h6>
							<div className="control-header m-0">
								<div className="row align-items-center">
									<div className="col-4">
										<div className="form-inline">
											<div className="form-group mr-4">
												<label className="mr-2" htmlFor="">Select Report Type </label>
												<select className="form-control-sm" onChange={this.onChangeReport} value={report}>
													<option value="remita">Remita Report</option>
													<option value="bank">Bank Statement</option>
												</select>
											</div>
										</div>
									</div>
									<div className="col-lg-5">
										<UploadRemita
											error={error}
											onDrop={this.onDrop}
											onOpenDropzone={this.onOpenDropzone}
											disabled={disabled}
											file={file}
											schedule={false}
											type="slim"
											file_type={file_type}
											report={report}
											onChangeType={this.onChangeType}
										/>
									</div>
								</div>
							</div>
							<div className="control-header m-0">
								<div className="row align-items-center">
									<div className="search-block mt-4 col-4">
										<div className="element-search input-group">
											<input placeholder="IPPIS or Phone number or Customer ID" type="text" className="form-control" value={searchText} onChange={this.onSearch} />
											{searchText !== '' && (
												<div className="input-group-append" onClick={this.clearSearch}>
													<span className="input-group-text"><i className="fa fa-close"/></span>
												</div>
											)}
										</div>
									</div>
									<div className="col-8">
										<div className="form-inline">
											<div className="form-group">
												<label className="text-right mr-3" htmlFor="">Filter By:</label>
											</div>
											<div className="form-group mr-4">
												<label className="mr-2" htmlFor="">Transaction Type</label>
												<select className="form-control-sm" onChange={this.onSelectType} value={transaction_type}>
													<option value="">All</option>
													<option value="remita-repayments">Remita Repayment</option>
													<option value="schedule-repayment">Schedule Repayment</option>
													<option value="staff-repayment">Admin Related Repayment</option>
												</select>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div className="element-box">
								<div className="table-responsive" style={{ overflowX: 'scroll' }}>
									<table className="table table-striped table-lightfont">
										<thead>
											<tr>
												<th>Name of Employee</th>
												<th>Lender</th>
												<th>IPPIS/Customer ID</th>
												<th>Loan ID</th>
												<th>Monthly Repayment</th>
												<th>Amount Paid</th>
												<th>Transaction Type</th>
												<th>Re-Payment Date</th>
												<th>Date Created</th>
												<th>&nbsp;</th>
											</tr>
										</thead>
										<tbody>
											{transactions.map((item, i) => {
												return (
													<tr key={i} className={item.approved < 0 ? 'tr-warning' : ''}>
														<td>
															<span>{item.user.name}</span>
														</td>
														<td>{item.lender.name}</td>
														<td>
														<a onClick={() => this.showUserProfile(item.user_id)} className="cursor link">{item.user.ippis || item.user.customer_id || item.user.phone}</a>
														</td>
														<td><a onClick={() => this.openProfile(item.user, item.loan)} className="cursor link">{item.loan_id}</a></td>
														<td>{item.loan ? currency(item.loan.monthly_deduction) : '-'}</td>
														<td>{currency(item.amount)}</td>
														<td>{startCase(item.channel)}</td>
														<td>{item.status === 'Pending' && item.channel === 'remita-repayments' && item.approved === 0 && !item.repayment_date ? '-' : (item.repayment_date ? moment(item.repayment_date, 'YYYY-MM-DD').format('D.MMM.YYYY') : '-')}</td>
														<td>{moment(item.created_at).format('D.MMM.YYYY')}</td>
														<td nowrap="nowrap">
															{item.approved === 0 && (item.channel !== 'remita-repayments' || (item.channel === 'remita-repayments' && item.repayment_date)) && (
																<button className="btn btn-sm btn-outline-primary cursor" onClick={() => this.doApproveTransaction(item.id, item.loan, item.user)}>
																	Approve
																</button>
															)}
															{item.approved === -1 && (
																<button className="btn btn-sm btn-primary cursor" onClick={() => this.doAddToWallet(item.id, 'wallet')}>
																	Add To Wallet
																</button>
															)}
															{item.approved === -2 && (
																<button className="btn btn-sm btn-primary cursor" onClick={() => this.doAddToWallet(item.id, 'reconcile')}>
																	Reconcile
																</button>
															)}
															{item.approved !== 1 && (
																<button className="btn btn-sm btn-outline-danger cursor ml-1" onClick={() => this.doDecline(item.id)}><i className="os-icon os-icon-trash-2 mr-0"/></button>
															)}
														</td>
													</tr>
												)
											})}
										</tbody>
									</table>
								</div>
								<div className="pagination pagination-center mt-4">
									<Pagination
										current={currentPage}
										pageSize={pageSize}
										total={totalPage}
										showTotal={total => `Total ${total} loans`}
										itemRender={itemRender}
										onChange={this.onChange}
									/>
								</div>
							</div>
						</div>
					</div>
				</div>

				<Modal
					isOpen={profileModalIsOpen}
					onRequestClose={this.closeProfileModal}
					contentLabel="Loan Profile"
					shouldCloseOnOverlayClick={false}
					overlayClassName="modal-dialog modal-lg modal-extra-lg"
					className="modal-content"
					portalClassName="ReactModalPortalv"
					bodyOpenClassName="ReactModal__Body--openv"
				>
					<LoanProfile
						closeModal={() => this.closeProfileModal()}
						user={customer}
						loan={loan}
					/>
				</Modal>
				{profileModalIsOpen? (<div className="modal-backdrop fade show"/>) : ''}

				<Modal
					isOpen={modalIsOpen}
					onRequestClose={this.cancelApprove}
					contentLabel="Approve Transaction"
					shouldCloseOnOverlayClick={false}
					overlayClassName="modal-dialog modal-sm"
					className="modal-content"
				>
					<ApproveTransaction
						closeModal={() => this.cancelApprove()}
						transID={id}
						loan={loan}
						customer={customer}
						refetch={() => this.fetchTransactions(currentPage, transaction_type)}
					/>
				</Modal>
				{modalIsOpen? (<div className="modal-backdrop fade show"/>) : ''}
			</div>
		);
	}
}

const showProfile = (data) => {
	return { type: SHOW_PROFILE, data };
};

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
		lender: state.lender.profile,
	};
};

const mapDispatchToProps = dispatch => {
	return bindActionCreators({ toggleMenu, startBlock, stopBlock, doNotify, showProfile, setLoanAnalytics, setUnapprovedTransactions }, dispatch);
};

export default withRouter(connect(mapStateToProps, mapDispatchToProps)(ApproveTransactions));
