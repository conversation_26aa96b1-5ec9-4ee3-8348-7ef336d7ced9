APP_NAME=FastCash
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8000

LOG_CHANNEL=daily
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=fastcash
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

SUPER_LENDER=1
MAIN_URL=http://localhost:3000
ADMIN_URL=http://localhost:3001
MERCHANT_URL=http://locahost:3002
PHONE_NUMBER=

REMITA_URL=https://login.remita.net
REMITA_DEBUG_URL=https://demo.remita.net

MERCHANT_ID=
API_KEY=
API_TOKEN=

DEBUG_MERCHANT_ID=
DEBUG_API_KEY=
DEBUG_API_TOKEN=

SMS_DEBUG=false
SMS_URL=https://sms.hollatags.com
SMS_USERNAME=
SMS_PASSWORD=
SMS_DEBUG_USERNAME=
SMS_DEBUG_PASSWORD=
SMS_FROM=FastCash
SMS_SEND_API=/api/send/

RAVE_PUBLIC_KEY=
RAVE_SECRET_KEY=
RAVE_TITLE=FastCash
RAVE_ENVIRONMENT=staging
RAVE_LOGO=
RAVE_SECRET_HASH=

PAYSTACK_SECRET=
PAYSTACK_PUBLIC=

PAYSTACK_TEST_SECRET=
PAYSTACK_TEST_PUBLIC=

WITHDRAW_LIMIT=5000

JWT_SECRET=
JWT_TTL=14400
