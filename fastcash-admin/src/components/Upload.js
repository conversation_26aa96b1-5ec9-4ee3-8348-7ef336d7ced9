import React, { Component } from 'react';
import Dropzone from 'react-dropzone';

class Upload extends Component {
    render() {
		const { error, changeMonth, month, months, changeYear, year, years, onDrop, onOpenDropzone, files, disabled, schedule, message } = this.props;
        return (
            <div className="element-box">
                {error !== '' && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                {message !== '' && <div className="alert alert-success" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Done!</strong> ${message}`}}></div>}
                <div className={`row ${schedule? '': 'hidden'}`}>
                    <div className="col-sm-6">
                        <div className="form-group">
                            <label>Month</label>
                            <select className="form-control" onChange={changeMonth} value={month}>
                                <option value="">Select month</option>
                                {months.map((m, i) => <option key={i} value={i + 1}>{m}</option>)}
                            </select>
                        </div>
                    </div>
                    <div className="col-sm-6">
                        <div className="form-group">
                            <label>Year</label>
                            <select className="form-control" onChange={changeYear} value={year}>
                                <option value="">Select year</option>
                                {years.map((y, i) => <option key={i} value={y}>{y}</option>)}
                            </select>
                        </div>
                    </div>
                </div>
                <br/>
                <div className="panel panel-default">
                    <div className="panel-body">
                        <Dropzone className="dropzone dz-clickable" onDrop={onDrop} onClick={onOpenDropzone} disabled={disabled && schedule}>
                            <div className="dz-message">
                                <div>
                                    <h6 style={{fontSize: 14}}>Drop files here or click to upload.</h6>
                                    <div className="text-muted text-left">
                                        <h6>Dropped files:</h6>
                                        {[ ...files ].reverse().map((f, i) => {
                                            const status = (f.status === 1 ? `<span class="elabel elabel-success">done</span>`:'<span class="elabel elabel-danger">error</span>');
                                            return <p style={{ marginBottom: '2px' }} key={i} dangerouslySetInnerHTML={{__html: `${f.name} - ${status}`}}></p>;
                                        })}
                                    </div>
                                </div>
                            </div>
                        </Dropzone>
                    </div>
                </div>
            </div>
        );
    }
}

export default Upload;
