import React, { Component } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';

import { calculateLoan, setTenure, setEarnings, doRefresh, calculateSMELoan, setRemitaID } from '../actions/loan';
import { httpRequest, baseURL, remitaLoanHistoryAPI, dateLimit } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import LoanForm from '../components/LoanForm';
import LoanDetails from '../components/LoanDetails';
import loader from '../assets/img/loader.gif';

class LoanRequest extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
            eligible: 1,
            current: null,
            done: 0,
            type: '',
            title: '',
			message: '',
			error_msg: '',
			platform: '',
        };

        this.loanMessage = this.loanMessage.bind(this);
	}
	
    async componentDidMount() {
		const { tenure, payslip, current, eligibilityGap, earnings, user, lender, snoozePeriod } = this.props;
		
		if(user.is_snoozed === 1) {
			const snoozed_date = user.snoozed_at;
			const end_of_snooze = moment(snoozed_date).add(snoozePeriod, 'days').fromNow(true);

			this.setState({ error_msg: `You are not allowed to request a loan at this time. Try again after ${end_of_snooze}.` });
			return;
		}

		this.setState({ error_msg: '' });
        const ir = lender.interest_rate;
        const date_diff = payslip ? moment(payslip.retire_expected_at).diff(Date.now(), 'years') : 0;
        const eligibility = date_diff - parseInt(eligibilityGap, 10);
        if(eligibility <= 0 && user && user.platform === 'ippis'){
            this.setState({ eligible: 0 });
        }
		this.setState({ current });

		let dates = [];
		for (let d=1; d<=parseInt(dateLimit, 10); d++) {
			dates = [ ...dates, { date: moment().subtract(d, 'month').format('M-YYYY') } ];
		}
		
		let ippisNewEarnings = [];
		for (let i = 0; i < earnings.length; i++) {
			const earning = earnings[i];
			const found = ippisNewEarnings.find(e => e.month === earning.month && e.year === earning.year && parseFloat(e.net_earning) === parseFloat(earning.net_earning));
			if(!found){
				ippisNewEarnings = [...ippisNewEarnings, earning];
			}
		}
		// console.log('-------------- ippis --------------');
		// console.log(earnings);
		// console.log(ippisNewEarnings);
		const ippisFilteredEarnings = ippisNewEarnings.filter(e => {
			return dates.find(d => d.date === `${e.month}-${e.year}`) && parseInt(e.net_earning, 10) > 0;
		});
		// console.log(ippisFilteredEarnings);
		ippisFilteredEarnings.sort((a, b) => b.year - a.year || b.month - a.month);
		// console.log(ippisFilteredEarnings);
		const newEarnings = ippisFilteredEarnings.slice(0, 3);
		// console.log(newEarnings);
		// console.log('-------------- ippis --------------');

		if(!current){
			this.props.startBlock();
			this.props.doRefresh(true);

			const remitaData = { phone: user.phone };
			
			try {
				const response = await httpRequest(`${baseURL}${remitaLoanHistoryAPI}`, 'POST', true, remitaData);
				if(response.earnings.length > 0){
					let remitaNewEarnings = [];
					for (let i = 0; i < response.earnings.length; i++) {
						const earning = response.earnings[i];
						const found = remitaNewEarnings.find(e => e.month === earning.month && e.year === earning.year && parseFloat(e.net_earning) === parseFloat(earning.net_earning));
						if(!found){
							remitaNewEarnings = [...remitaNewEarnings, earning];
						}
					}

					// console.log('-------------- remita --------------');
					// console.log(response.earnings);
					// console.log(remitaNewEarnings);
					const remitaFilteredEarnings = remitaNewEarnings.filter(e => {
						return dates.find(d => d.date === `${e.month}-${e.year}`) && parseInt(e.net_earning, 10) > 0;
					});
					// console.log(remitaFilteredEarnings);
					remitaFilteredEarnings.sort((a, b) => b.year - a.year || b.month - a.month);
					// console.log(remitaFilteredEarnings);

					const netEarnings = remitaFilteredEarnings.slice(0, 3);
					// console.log(netEarnings);
					
					this.props.setEarnings(netEarnings);
					this.setState({ error_msg: '', platform: 'remita' });
					this.props.stopBlock();

					const map_earnings = netEarnings.map(r => r.net_earning);
					// console.log(map_earnings);
					const total_net_earning = map_earnings.reduce((total, amount) => parseFloat(amount) + total, 0);
					const net_earning = (total_net_earning / 3).toFixed(2);
					// console.log(net_earning);
					// console.log('-------------- remita --------------');
					this.props.calculateLoan(net_earning, tenure, ir, 0.4);
					this.props.doRefresh(false);
					this.props.setRemitaID(response.remita_id);
				} else {
					this.continue(user, newEarnings, tenure, ir)
				}
			}
			catch (error) {
				this.continue(user, newEarnings, tenure, ir)
			}
		}
	}

	continue = (user, newEarnings, tenure, ir) => {
		const { payslip } = this.props;

		if(user.platform === 'sme'){
			this.props.setEarnings([]);
			this.setState({ error_msg: '', platform: 'sme' });
			this.props.stopBlock();

			this.props.calculateSMELoan(payslip.eligible_amount || 0);
			this.props.doRefresh(false);
		}
		else {
			this.setIppisEarnings(user, newEarnings, tenure, ir);
		}
	}
	
	setIppisEarnings = (user, newEarnings, tenure, ir) => {
		if(newEarnings.length >= 1 && user.platform === 'ippis'){
			this.props.setEarnings(newEarnings);
			this.setState({ error_msg: '', platform: newEarnings[0].platform });
			this.props.stopBlock();

			// console.log('-------------- ippis 2 --------------');
			const map_earnings = newEarnings.map(r => r.net_earning);
			// console.log(map_earnings);
			const net_earning = Math.min( ...map_earnings );
			// console.log(net_earning);
			// console.log('-------------- ippis 2 --------------');
			this.props.calculateLoan(net_earning, tenure, ir, 0.33);
			this.props.doRefresh(false);
		} else {
			const message = 'could not retrieve your net earnings';
			this.setState({ error_msg: message });
			this.props.stopBlock();
			this.props.doRefresh(false);
		}
	}

    componentWillUnmount = () => {
		const { maxLoanTenure } = this.props;
		this.props.setTenure(parseInt(maxLoanTenure, 10));
    };

    loanMessage = (type, title, message) => {
        this.setState({ done: 1, type, title, message });
    };
    
    render() {
        const { user, myAmount, myMonthlyDeductions, monthlyDeductions, netEarning, eligibleAmt, tenure, bankAccounts, stopLoanDate, refresh } = this.props;
		const { eligible, current, done, type, title, message, error_msg, platform } = this.state;
		const monthLegible = moment(stopLoanDate).date() < moment().date();
        return (
			refresh ? <div className="content-i"><div className="content-box" style={{textAlign:'center'}}><img src={loader} alt=""/></div></div> :
            <div className="content-i">
                <div className="content-box">
					{(
						error_msg !== '' ? (
							<div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>{error_msg}</div>
						) : 
						<div>
							{done === 1 && (
								<div className="element-box">
									<div className="element-box-content">
										<div className={`alert alert-${type}`} role="alert">
											<h4 className="alert-heading">{title}</h4>
											<p>{message}</p>
											<p className="mb-0"></p>
										</div>
									</div>
								</div>
							)}
							{eligible === 0 ? 
								(<div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>you are not legible to take any loans at this moment</div>) 
								: '' 
							}
							{
								monthLegible ? (
									<div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>{`you are not legible to take any loans at this moment, please try again on or before the ${moment(stopLoanDate).format('Do')} of next month.`}</div>
								) : (current ? (
										<div className="alert alert-warning" style={{margin: '0', marginBottom: '15px'}}>you cant take a loan at this moment because you have a current loan running</div>
									) : (
										done === 0 && (
											<div className="row">
												<div className="col-sm-8">
													<LoanDetails
														amount={myAmount}
														myMonthlyDeductions={myMonthlyDeductions}
														monthlyDeductions={monthlyDeductions}
														netEarning={netEarning}
														eligibleAmt={eligibleAmt}
														tenure={tenure}
														platform={platform}
														topup={false}
													/>
												</div>
												{(current === null && eligible === 1) ? (
													<div className="col-sm-4">
														<div className="element-wrapper">
															<div className="element-wrapper">
																<LoanForm
																	user={user}
																	tenure={tenure}
																	netEarning={netEarning}
																	eligibleAmt={eligibleAmt}
																	loanMessage={(type, title, message) => this.loanMessage(type, title, message)}
																	bankAccounts={bankAccounts}
																	platform={platform}
																	topup= {0}
																	old_loan= {null}
																/>
															</div>
														</div>
													</div>
												) : '' }
											</div>
										)
									)
								)
							}
						</div>
					)}
                </div>
            </div>
        );
    }
}

const mapStateToProps = (state, ownProps) => {
	const lender = state.user.lender;

    return {
        user: state.user.user,
        myAmount: state.loan.myAmount,
        myMonthlyDeductions: state.loan.myMonthlyDeductions,
        monthlyDeductions: state.loan.monthlyDeductions,
        netEarning: state.loan.netEarning,
        eligibleAmt: state.loan.eligibleAmt,
        tenure: state.loan.tenure,
        current: state.loan.current,
        payslip: state.user.payslip,
		earnings: state.user.earnings,
		bankAccounts: state.user.bank_accounts,
		refresh: state.loan.refresh,
		maxLoanTenure: lender.max_loan_tenure,
		stopLoanDate: state.settings.stop_loan_date,
		snoozePeriod: state.settings.snooze_period,
		eligibilityGap: state.settings.eligibility_gap,
        lender: state.user.lender,
    }
};

export default connect(mapStateToProps, { calculateLoan, setTenure, setEarnings, startBlock, stopBlock, doRefresh, calculateSMELoan, setRemitaID })(LoanRequest);