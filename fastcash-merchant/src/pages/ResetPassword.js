/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { <PERSON>, withRouter } from 'react-router-dom';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';

import { baseURL, httpRequest, resetPasswordAPI, authKey, settingKey } from '../config/constants';
import { changeUserData, signOut } from '../actions/user';
import { setLoans } from '../actions/loan';
import loading from '../assets/img/loading.gif';
import logoBig from '../assets/img/fc-logo.png';
import { putSettings } from '../actions/settings';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {}
    if (!values.password) {
        errors.password = 'Enter your password';
    }
    if (!values.password_confirmation) {
        errors.password_confirmation = 'Enter your password again';
    }
    return errors;
};

const renderField = ({input, id, icon, label, hideclass, type, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')} ${hideclass}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" />
        <div className={`pre-icon os-icon ${icon}`}></div>
        {touched &&
            ((error && <span className="help-block">{error}</span>) ||
                (warning && <span className="help-block">{warning}</span>))}
    </div>
);

class ResetPassword extends Component {
	constructor(props, context) {
		super(props, context);
		this.resetPassword = this.resetPassword.bind(this);
	}

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	resetPassword = async data => {
        const token = this.props.history.location.pathname.split('/').pop();
		try {
            const response = await httpRequest(`${baseURL}${resetPasswordAPI}/${token}`, 'POST', true, data);
            const user = response.user;
            if (user.role === 'merchant' || user.role === 'u-merchant') {
                this.props.changeUserData(user, true);
                this.props.putSettings(response.settings);
                this.props.setLoans(response.loans);
                localStorage.setItem(authKey, response.token);
                this.props.reset('reset-password');
                this.notify('', 'you have reset your password!', 'success');
                this.props.history.push({ pathname: `/dashboard/${user.merchant_code}` });
            }
            else {
                localStorage.removeItem(authKey);
                localStorage.removeItem(settingKey);
                this.props.signOut();
                this.props.history.push({ pathname: '/' });
            }
        }
        catch (error) {
            const message = error.message || 'invalid sign in';
            this.notify('', message, 'error');
            throw new SubmissionError({
                _error: message,
            });
        }
	};

	componentDidMount() {
		document.body.className = 'auth-wrapper pages';
	}

    render() {
        const { handleSubmit, error, submitting, pristine } = this.props;
        return (
            <div className="all-wrapper menu-side with-pattern">
                <div className="auth-box-wh">
                    <div className="logo-w" style={{padding: '10%'}}>
                        <Link to="/"><img alt="logo" src={logoBig} style={{width: '50%'}}/></Link>
                    </div>
                    <h4 className="auth-header">Change Password</h4>
                    {error && <div className="alert alert-danger" dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                    <form onSubmit={handleSubmit(this.resetPassword)} autoComplete="off">
                        <Field
                            name="password"
                            id="password"
                            type="password"
                            component={renderField}
                            label="Password"
                            icon="os-icon-fingerprint"
                        />
                        <Field
                            name="password_confirmation"
                            id="password"
                            type="password"
                            component={renderField}
                            label="Confirm Password"
                            icon="os-icon-fingerprint"
                        />
                        <div className="buttons-w">
                            <button className="btn btn-primary" type="submit" disabled={pristine || submitting}>
                                {submitting? <img src={loading} alt=""/> : 'Save Password'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    }
}

ResetPassword = reduxForm({
    form: 'reset-password',
    validate
})(ResetPassword);

export default withRouter(connect(null, { putSettings, changeUserData, setLoans, reset, doNotify, signOut  })(ResetPassword));