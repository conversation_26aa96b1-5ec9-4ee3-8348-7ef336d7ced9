<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddStaffNotify extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('notifies', function (Blueprint $table) {
            $table->bigInteger('staff_id')->unsigned()->nullable()->after('description');

            $table->foreign('staff_id')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('notifies', function (Blueprint $table) {
            Schema::disableForeignKeyConstraints();
            $table->dropForeign(['staff_id']);
            $table->dropColumn(['staff_id']);
            Schema::enableForeignKeyConstraints();
        });
    }
}
