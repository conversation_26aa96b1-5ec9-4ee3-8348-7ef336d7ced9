<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;

class RoleTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $roles = [
            ['title'=> 'Super Admin', 'name'=> 'super', 'category'=> 'super', 'description'=> 'Super Administrator',],
            ['title'=> 'Admin', 'name'=> 'admin', 'category'=> 'admin', 'description'=> 'Administrator',],
            ['title'=> 'Approver', 'name'=> 'admin', 'category'=> 'admin-a', 'description'=> 'Administrator',],
            ['title'=> 'Verifier', 'name'=> 'admin', 'category'=> 'admin-v', 'description'=> 'Administrator',],
            ['title'=> 'User', 'name'=> 'user', 'category'=> 'user', 'description'=> 'Client',],
            ['title'=> 'Merchant', 'name'=> 'merchant', 'category'=> 'merchant', 'description'=> 'Merchant',],
            ['title'=> 'Super Admin', 'name'=> 'super', 'category'=> 'super-l', 'description'=> 'Lender Super Administrator',],
            ['title'=> 'Merchant User', 'name'=> 'u-merchant', 'category'=> 'u-merchant', 'description'=> 'Merchant and User',],
        ];

        foreach ($roles as $role) {
            $r = Role::where('category', $role['category'])->first();

            if($r == null){
                $r = Role::create([
                    'title' => $role['title'],
                    'name' => $role['name'],
                    'category' => $role['category'],
                    'description' => $role['description'],
                ]);
            }
        }
    }
}
