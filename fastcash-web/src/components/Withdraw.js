import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import startCase from 'lodash.startcase';

import { doNotify } from '../actions/general';
import { baseURL, httpRequest, withdrawAPI, mask } from '../config/constants';
import loading from '../assets/img/loading.gif';
import { updateWallet } from '../actions/user';

const validate = values => {
    const errors = {}
    if (!values.amount) {
        errors.amount = 'Enter amount';
    }
    if (values.bank_account === '') {
        errors.bank_account = 'Select bank account';
    }
    return errors;
};

const renderIGField = ({input, id, label, type, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label className="lighter" htmlFor={id}>{label}</label>
		<div className="input-group mb-2 mr-sm-2 mb-sm-0">
			<input {...input} placeholder={label} type={type} className="form-control" />
			<div className="input-group-append">
				<div className="input-group-text">₦</div>
			</div>
		</div>
        {touched && error && <span className="help-block">{error}</span>}
    </div>
);

const renderSelectField = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label>{label}</label>
        <select {...input} className="form-control">
            <option value="">{`Select ${label}`}</option>
            {data.map((c, i) => <option value={c.id} key={i}>{c.name}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class Withdraw extends Component {
	state = {
		submitting: false,
	};

	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};
    
    withdrawalRequest = async data => {
		const { user, lender, currentPage } = this.props;
		if(data.amount > user.wallet) {
			throw new SubmissionError({
				_error: 'you have insufficient funds in your wallet to make this withdrawal',
            });
		}
		
        try {
			this.setState({ submitting: true });
			const details = { ...data, user_id: user.id, lender_id: lender.id };
			const rs = await httpRequest(`${baseURL}${withdrawAPI}`, 'POST', true, details);
			this.props.updateWallet(rs.result);
			this.notify('', 'Withdrawal request sent, allow 3 working days to get your fund!', 'success');
			this.props.reset('withdrawform');
			this.setState({ submitting: false });
			this.props.fetch(currentPage);
        }
        catch (error) {
            const message = error.message || 'error, please try again';
            this.setState({ submitting: false });
            throw new SubmissionError({
                _error: message,
            });
        }
    };

	render() {
		const { submitting } = this.state;
		const { handleSubmit, error, bank_accounts } = this.props;
		return (
			<div className="element-box">
				<form onSubmit={handleSubmit(this.withdrawalRequest)} autoComplete="off">
					<h5 className="element-box-header">Withdraw Money</h5>
					{error && <div className="alert alert-danger" dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
					<div className="row">
						<div className="col-sm-5">
							<Field
								name="amount"
								id="amount"
								component={renderIGField}
								label="Enter Amount"
								type="text"
							/>
						</div>
						<div className="col-sm-7">
							<Field
								name="bank_account"
								component={renderSelectField}
								label="Bank Account"
								data={bank_accounts.filter(a => a.account_number !== '0').map(item => ({name: `${startCase(item.bank_name)} ${mask(item.account_number)}`, id: item.id}))}
							/>
						</div>
					</div>
					<div className="form-buttons-w text-right compact">
						<button className="btn btn-primary" type="submit" disabled={submitting}>
							{submitting? <img src={loading} alt=""/> : (
								<Fragment>
									<span>Withdraw</span>
									<i className="os-icon os-icon-grid-18" />
								</Fragment>
							)}
						</button>
					</div>
				</form>
			</div>
		);
	}
}

Withdraw = reduxForm({
    form: 'withdrawform',
    validate
})(Withdraw);

const mapStateToProps = (state, ownProps) => {
	return {
		initialValues: {
            amount: 0,
            bank_account: '',
		},
		user: state.user.user,
		bank_accounts: state.user.bank_accounts,
		lender: state.lender.profile,
	};
};

export default connect(mapStateToProps, { doNotify, updateWallet, reset })(Withdraw);
