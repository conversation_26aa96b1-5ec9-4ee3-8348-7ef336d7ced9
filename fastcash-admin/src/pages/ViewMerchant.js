/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import numeral from 'numeral';
import moment from 'moment';
import Pagination from 'antd/lib/pagination';
import Modal from 'react-modal';

import { toggleMenu } from '../actions/user';
import BreadCrumbs from '../components/BreadCrumbs';
import EditCommission from '../components/EditCommission';
import loading from '../assets/img/loading.gif';
import { baseURL, httpRequest, merchantAPI, paginate } from '../config/constants';
import { updateMerchant } from '../actions/merchant';
import Naira from '../components/Naira';
import { doNotify } from '../actions/general';
import { SHOW_PROFILE } from '../actions/types';
import Tooltip from 'antd/lib/tooltip';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 10;

class ViewMerchant extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			submitting: false,
			merchant: null,
			loans: [],
			currentPage: 1,
			totalPage: 0,
			slicedLoans: [],
            selectedLoan: null,
            submittingSingle: false,
            approvedNotPaid: []
		};
		this.payMerchant = this.payMerchant.bind(this);
	}

	componentDidMount() {
		this.props.toggleMenu(false);
		const { merchants, match } = this.props;
		const merchantCode = match.params.merchantCode;
		const merchant = merchants.find((m) => m.merchant_code === merchantCode);
		if (merchant) {
			const { loans, ...data } = merchant;
            loans.sort((a, b) => a.paid - b.paid );         
			this.setState({
				merchant: data,
				loans,
                slicedLoans: loans.slice(0, pageSize),
                totalPage: loans.length,
                approvedNotPaid: loans.filter(loan => loan.approved == 1 && loan.paid == 0 ),
                paidLoans: loans.filter(loan => loan.approved == 1 && loan.paid == 1 )
			});
		} else {
			this.props.history.push('/merchants');
		}
	}

	componentWillUnmount() {
		this.props.toggleMenu(true);
	}

	payMerchant = async (merchantID) => {
        const action = window.confirm('Are you sure?') ? true : false;
		if (!action) {
			return;
		}   

        const { user } = this.props;
        try {
            this.setState({ submitting: true });
            const body = { staff_id: user.id }
            
            const url = `${baseURL}${merchantAPI}/pay/${merchantID}`;
			const response = await httpRequest(url, 'POST', true, body);
			this.props.updateMerchant(response.merch);
            let { loans, merchant } = this.state;
            merchant.approved_count = response.merch.approved_count;
            merchant.paid_count = response.merch.paid_count;
            this.setState({
                loans: loans.map(loan => {
                    loan.paid = 1;
                    return loan;
                })
            });            
			this.setState({ submitting: false });
			this.notify('', 'merchant payment successful!', 'success');
			// this.props.closeModal();
		} catch (error) {
			this.setState({ submitting: false });
			const message = error.message || 'could not pay merchant';
			this.notify('', message, 'error');
		}
	};

    singlePayMerchant = async (loanID, merchantID, staffID) => {   
        const action = window.confirm('Are you sure?') ? true : false;
		if (!action) {
			return;
		}   

        let { slicedLoans, merchant } = this.state;
        let body = { loan_id: loanID, merchant_id: merchantID, staff_id: staffID }
        try {
            this.setState({ submittingSingle: true, selectedLoan: loanID });
            const url = `${baseURL}${merchantAPI}/single-pay`;
			const response = await httpRequest(url, 'POST', true, body);
			this.props.updateMerchant(response.merch);
            this.setState({
                slicedLoans: slicedLoans.map(loan => {
                    if(loan.id == loanID) loan.paid = 1;
                    return loan;
                })
            });
            merchant.approved_count = response.merch.approved_count;
            merchant.paid_count = response.merch.paid_count;

            this.setState({ submittingSingle: false, selectedLoan: null });
			this.notify('', 'merchant payment successful!', 'success');
		} catch (error) {
            this.setState({ submittingSingle: false, selectedLoan: null });			
			const message = error.message || 'could not pay merchant';
			this.notify('', message, 'error');
		}
	};

	handleChange = (pageNumber) => {
		const { loans } = this.state;
		const filteredLoans = paginate(loans, pageSize, pageNumber);
		this.setState({ slicedLoans: filteredLoans, currentPage: pageNumber });
	};

	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

    openEditModal = () => {
		document.body.className = 'pages modal-open';
		this.setState({ editModalIsOpen: true });
	};

    closeEditModal = () => {
		this.setState({ editModalIsOpen: false });
		document.body.className = 'pages';
	};

    showUserProfile = id => this.props.showProfile({ show_profile: true, user_id: id });
    
    render() {
        const { user, lender } = this.props;
        const { submitting, submittingSingle, selectedLoan, merchant, currentPage, totalPage, slicedLoans, loans, approvedNotPaid, editModalIsOpen } = this.state;

        let amount, paidAmount, totalReferralAmt, commission_balance = 0;
        let approvedLoans = [];
        if(merchant){
            // amount = merchant.count * parseInt(lender.merchant_commission, 10);
            // percent1 = parseInt((parseInt(merchant.count, 10) / 20) * 100, 10);
            // percent2 = parseInt((parseInt(merchant.count, 10) / 20) * 100 * 1.5, 10);
            // percent3 = percent2 > 100 ? 100 : percent2;
            
            loans.forEach(loan => {
                if (loan.approved == 1) approvedLoans.push(loan)
            });
            amount = approvedLoans.length * parseInt(lender.merchant_commission, 10);
            console.log(amount)
            
            paidAmount = merchant.paid_count * lender.merchant_commission;
            totalReferralAmt = loans.length * lender.merchant_commission;
            commission_balance = totalReferralAmt - paidAmount;
        }

        return (
            <div>
                {user && (user.role === 'admin' || user.role === 'super') && (
                    <BreadCrumbs url={'/merchants'} />
                )}
                <div className="content-i">
                    <div className="content-box">
                        {merchant && (
                            <div className="row">
                                <div className="col-lg-6">
                                    <div className="padded-lg">
                                        <div className="element-wrapper">                                            
                                            <div className="element-box">
                                                <div className="row">
                                                    <div className="col-sm-12">
                                                        <div className="element-box el-tablo" style={{marginBottom: '22px'}}>
                                                            <div className="el-tablo highlight bigger">
                                                                <div className="lead">{merchant.user}</div>
                                                            </div>
                                                            <div className="label">{lender.name}</div>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-12">
                                                        <div className="element-box el-tablo" style={{marginBottom: '22px'}}>
                                                            <div className="label">Office</div>
                                                            <div className="lead">{merchant.office}</div>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-12">
                                                        <div className="element-box el-tablo" style={{marginBottom: '22px'}}>
                                                            <div className="label">Merchant ID</div>
                                                            <div className="lead">{merchant.merchant_code}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>                                

                                <div className="col-lg-6 b-l-lg">
                                    <div className="padded-lg">
                                        <div className="element-wrapper">
                                            <div className="element-box">
                                                <div className="m-b">
                                                    <div className="centered-header"><h6>Count &amp; Earnings</h6></div>
                                                    <div className="row">
                                                        <div className="col-4 b-r b-b relative">                                                            
                                                            <div className="el-tablo py-4 centered highlight bigger">
                                                                <div className="label">Total Referrals</div>
                                                                {merchant && 
                                                                <div className="value py-2 d-flex align-items-center justify-content-center" style={{ fontSize: '1.4rem' }}>
                                                                    <Naira/>{`${numeral(loans.length * lender.merchant_commission).format('0,0.00')}`}
                                                                </div>}
                                                            </div>
                                                            <span className="d-flex align-items-center justify-content-center merchant-value">{loans.length}</span>                                                            
                                                        </div>
                                                        <div className="col-4 b-r b-b relative">                                                            
                                                            <div className="el-tablo py-4 centered highlight bigger">
                                                                <div className="label">Approved Referrals</div>
                                                                {merchant && 
                                                                <div className="value py-2 d-flex align-items-center justify-content-center" style={{ fontSize: '1.4rem' }}>
                                                                    <Naira/>{`${numeral(approvedLoans.length * lender.merchant_commission).format('0,0.00')}`}
                                                                </div>}                                                                
                                                            </div>
                                                            <span className="d-flex align-items-center justify-content-center merchant-value">{approvedLoans.length}</span>                                                            
                                                        </div>
                                                        <div className="col-4 b-b relative">     
                                                            <div className="el-tablo py-4 centered highlight bigger">                                                                
                                                                <div className="label">Paid Referrals</div>
                                                                {merchant && 
                                                                <div className="value py-2 d-flex align-items-center justify-content-center" style={{ fontSize: '1.4rem' }}>
                                                                    <Naira/>{`${numeral(paidAmount).format('0,0.00')}`}
                                                                </div>}                                                                
                                                            </div>     
                                                            <span className="d-flex align-items-center justify-content-center merchant-value">{merchant.paid_count}</span>
                                                        </div>
                                                    </div>
                                                    <div className="row">
                                                        <div className="col-12 b-b">                                                           
                                                            <div className="el-tablo py-4 centered highlight bigger">
                                                                <div className="label">Commission Balance</div>
                                                                <div className="value">
                                                                    <Naira/>{`${numeral(commission_balance).format('0,0.00')}`}
                                                                    {/* <i onClick={() => this.openEditModal()} className="icon-pencil cursor" style={{ fontSize: '20px', position: 'absolute', right: '11px', top: '11px' }}></i>  */}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {merchant && (
                                                    <div className="padded m-b">
                                                        {/* <div className="centered-header"><h6> Transaction Target</h6></div>
                                                        <div className="os-progress-bar primary">
                                                            <div className="bar-labels">
                                                                <div className="bar-label-left"><span>Progress</span><span className="positive">{merchant.count}</span></div>
                                                                <div className="bar-label-right"><span className="info">{merchant.count}/20</span></div>
                                                            </div>
                                                            <div className="bar-level-1" style={{width: '100%'}}>
                                                                <div className="bar-level-2" style={{width: `${percent3}%`}}>
                                                                    <div className="bar-level-3" style={{width: `${percent1}%`}}></div>
                                                                </div>
                                                            </div>
                                                        </div> */}
                                                        {user && (user.role === 'super' || user.role === 'admin') && (
                                                            <button
                                                                className="btn btn-teal"
                                                                disabled={submitting || approvedNotPaid.length < 1}
                                                                type="submit"
                                                                onClick={() => this.payMerchant(merchant.id)}>
                                                                {submitting ? <img src={loading} alt="" /> : 'Pay Merchant'}
                                                            </button>
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                        <div className="row">
                            
                            <div className="col-lg-12">
                                <div className="padded-lg">
                                    <div className="element-wrapper">
                                        <h6 className="element-header">Transaction Statistics</h6>
                                        <div className="element-box">
                                            <div className="table-responsive">
                                                <table className="table table-striped">
                                                    <thead>
                                                    <tr>
                                                        <th>Employee Name</th>
                                                        <th>Loan ID</th>
                                                        <th>Ippis</th>
                                                        <th>Phone</th>
                                                        <th>Amount</th>
                                                        <th>Commission Amount</th>
                                                        <th>Loan Status</th>
                                                        <th>Commission Status</th>
                                                        <th>Approved Date</th>
                                                        <th className="text-center">Date</th>
                                                        <td colSpan="2"></td>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    {slicedLoans.map(l => {
                                                        const verified = l && l.verified === 1;
                                                        const isDisbursed = l && l.disbursed === 1;
                                                        const approved = l ? l.approved : -1;                                                        
									                    
                                                        const loan_status = verified > 0 ? ( approved > 0 ? (isDisbursed ?                                                             
                                                            <Tooltip placement="top" title="Disbursed"><div className="status-pill green"/></Tooltip>
                                                            : 
                                                            <Tooltip placement="top" title="Approved"><div className="status-pill yellow"/></Tooltip>
                                                            ) : 
                                                            <Tooltip placement="top" title="Verified"><div className="status-pill purple"/></Tooltip> 
                                                            ) : 
                                                            <Tooltip placement="top" title="Not Approved"><div className="status-pill dark"/></Tooltip>;
                                                        
                                                        return (
                                                            <tr key={l.id}>
                                                                <td>{l.user && l.user.name}</td>
                                                                <td>
                                                                <a onClick={() => this.showUserProfile(l.user.id)} className="cursor link">{l.id}</a>
                                                                </td>
                                                                <td>{l.user && l.user.ippis ? l.user.ippis : '-'}</td>
                                                                <td>{l.user && l.user.phone}</td>
                                                                <td>{`₦${numeral(l.amount).format('0,0.00')}`}</td>
                                                                <td>{`₦${numeral(lender.merchant_commission).format('0,0.00')}`}</td>
                                                                <td className='text-center'>{loan_status}</td>
                                                                <td className='text-center'>
                                                                    {l.paid == 0 ? <Tooltip placement="top" title="Not Paid"><div className="status-pill red"/></Tooltip> : <Tooltip placement="top" title="Paid"><div className="status-pill green"/></Tooltip>}
                                                                </td>
                                                                <td className='text-center'>{l.approved_at ? moment(l.approved_at).format('D.MMM.YYYY') : '-' }</td>
                                                                <td className='text-center'>{moment(l.created_at).format('D.MMM.YYYY')}</td>
                                                                <td>
                                                                {approved > 0 && l.paid == 0 && user && (user.role === 'super' || user.role === 'admin') &&
                                                                <button 
                                                                    disabled={submitting || submittingSingle} 
                                                                    onClick={() => this.singlePayMerchant(l.id, merchant.id, user.id)} 
                                                                    className="btn btn-sm btn-outline-primary cursor">
                                                                        { submittingSingle && selectedLoan == l.id ? <i className="fa fa-spinner fa-spin"></i> : 'Pay' }                                                                                                                                                
                                                                </button>}
                                                                </td>
                                                            </tr>
                                                        )
                                                    })}
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div className="pagination pagination-center mt-4">
                                                <Pagination
                                                    current={currentPage}
                                                    pageSize={pageSize}
                                                    total={totalPage}
                                                    showTotal={total => `Total ${total} loans`}
                                                    itemRender={itemRender}
                                                    onChange={this.handleChange}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                </div>
                <Modal
					isOpen={editModalIsOpen}
					onRequestClose={this.closeEditModal}
					contentLabel="Create Merchant"
					shouldCloseOnOverlayClick={false}
					overlayClassName="modal-dialog modal-sm"
					className="modal-content"
				>
					<EditCommission closeEditModal={() => this.closeEditModal()} amount={merchant && merchant.commission_balance} />
				</Modal>
				{editModalIsOpen ? <div className="modal-backdrop fade show" /> : ''}
            </div>
        );
    }
}

const showProfile = (data) => {
	return { type: SHOW_PROFILE, data };
};

const mapStateToProps = state => {
    return {
        merchants: state.merchant.list,
        user: state.user.user,
        lender: state.lender.profile,
    }
}


export default connect(mapStateToProps, { toggleMenu, updateMerchant, doNotify, showProfile })(ViewMerchant);