<?php

namespace Database\Seeders;

use App\Models\Loan;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class FixLoans extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $loans = Loan::all();
        foreach ($loans as $loan){
            $l = Loan::find($loan->id);
            if($l){
                if($l->approved_at != null && $l->disbursed_at != null){
                    $l->start_date = Carbon::parse($l->approved_at)->endOfMonth()->addDays(1)->startOfMonth()->format('Y-m-d H:i:s');
                    $l->end_date = Carbon::parse($l->approved_at)->addMonths($l->tenure)->endOfMonth()->format('Y-m-d H:i:s');
                }
                else {
                    $l->start_date = NULL;
                    $l->end_date = NULL;
                }
                $l->save();
            }
        }
    }
}
