/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import { withRouter } from 'react-router-dom';

import { baseURL, httpRequest, resetPasswordAPI, authKey } from '../config/constants';
import loading from '../assets/img/loading.gif';
import { doNotify } from '../actions/general';
import { setCurrentUser } from '../actions/user';
import { putSettings } from '../actions/settings';

const validate = values => {
    const errors = {}
    if (!values.password) {
        errors.password = 'Enter your password';
	}
	if (!values.password_confirmation) {
        errors.password_confirmation = 'Confirm your password';
    }
    return errors;
};

const renderField = ({input, id, icon, label, hideclass, type, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')} ${hideclass}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" />
        <div className={`pre-icon os-icon ${icon}`}></div>
        {touched &&
            ((error && <span className="help-block">{error}</span>) ||
                (warning && <span className="help-block">{warning}</span>))}
    </div>
);

class ResetPswdForm extends Component {
    constructor(props) {
        super(props);
        this.resetPassword = this.resetPassword.bind(this);
    }

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	resetPassword = async data => {
		try {
            const { user_email } = this.props;
		    const token = this.props.history.location.pathname.split('/').pop();
            const response = await httpRequest(`${baseURL}${resetPasswordAPI}/${token}`, 'POST', false, {...data, email: user_email});
            this.props.setCurrentUser(response.user);
            localStorage.setItem(authKey, response.token);
            document.body.className = 'pages';
            this.props.putSettings(response.settings);
            this.notify('', 'you have reset your password!', 'success');
            this.props.reset('resetpswdform');
            this.props.history.push({ pathname: '/dashboard' });
        }
        catch (error) {
            const message = error.message || 'error, please try again';
            this.notify('', message, 'error');
            throw new SubmissionError({
                _error: message,
            });
        }
	};

    render() {
        const { handleSubmit, error, submitting, pristine } = this.props;
        return (
            <div className="login-box">
                <div className="auth-box-w intro-description">
                    <h4 className="auth-header">Change Password</h4>
                    {error && <div className="alert alert-danger" dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                    <form onSubmit={handleSubmit(this.resetPassword)} autoComplete="off">
                        <Field
                            name="password"
                            type="password"
                            component={renderField}
                            label="Password"
                            icon="os-icon-lock"
                        />
						<Field
                            name="password_confirmation"
                            type="password"
                            component={renderField}
                            label="Confirm Password"
                            icon="os-icon-lock"
                        />
                        <div className="buttons-w">
                            <button className="btn btn-primary" disabled={pristine || submitting} type="submit">
                                {submitting? <img src={loading} alt=""/> : 'Save Password'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    }
}

ResetPswdForm = reduxForm({
    form: 'resetpswdform',
    validate
})(ResetPswdForm);

const mapStateToProps = (state, ownProps) => {
    return {
        user_email: state.user.user_email,
    }
};

export default withRouter(connect(mapStateToProps, { reset, doNotify, setCurrentUser, putSettings })(ResetPswdForm));
