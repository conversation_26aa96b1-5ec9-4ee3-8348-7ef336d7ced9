import React, { Component } from 'react';

import CreateUser from '../components/CreateUser';
import EditUser from '../components/EditUser';
import UserList from '../components/UserList';

class AdminUsers extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			edit_user: null,
		};
	}

	editUser = user => {
		this.setState({ edit_user: null }, () => {
			this.setState({ edit_user: user });
		});

		window.scrollTo(0, 0);
	};

	render() {
		const { edit_user } = this.state;
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="row">
						<div className="col-lg-12">
							<div className="padded-lg" style={{ paddingRight: 0, paddingLeft: 0 }}>
								{edit_user ? (
									<EditUser editUser={this.editUser} edit_user={edit_user} />
								) : (
									<CreateUser />
								)}
							</div>
						</div>
						<div className="col-lg-12">
							<div className="padded-lg" style={{ paddingRight: 0, paddingLeft: 0 }}>
								<UserList editUser={this.editUser} />
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}

export default AdminUsers;
