<?php
/**
 * Created by PhpStorm.
 * User: emnity
 * Date: 11/30/17
 * Time: 9:57 PM
 */

namespace App\Services;

use Mail;

class MailHandler
{
    private $name = 'FastCash';
    private $email = '<EMAIL>';

    public function sendScheduleMail($user, $file){
        $mailer = Mail::send('email.schedule', ['user' => $user], function ($m) use ($user, $file) {
            $m->from($this->email, $user->sender);
            $m->to($user->email, $user->to)->subject('Loan Schedule for '.$user->mda);
            $m->attach($file);
        });
        return $mailer;
    }

    public function sendMail($mail){
        if($mail->to_email != null && $mail->to_email != '') {
            $mailer = Mail::send($mail->template, ['mail' => $mail], function ($m) use ($mail) {
                $m->from($mail->from_email, $mail->from_name);
                $m->to($mail->to_email, ucwords(strtolower($mail->to_name)))->subject($mail->subject);
                if(isset($mail->hasCC) && $mail->hasCC){
                    $m->cc($mail->cc_email);
                }
            });
            return $mailer;
        }
        return null;
    }

    public function sendMailAttach($mail){
        $files = $mail->attachments;
        $mailer = Mail::send($mail->template, ['mail' => $mail], function ($m) use ($mail, $files) {
            $m->from($mail->from_email, $mail->from_name);
            $m->to($mail->to_email, ucwords(strtolower($mail->to_name)))->subject($mail->subject);
            if(isset($mail->hasCC) && $mail->hasCC){
                $m->cc($mail->cc_email);
            }
            foreach ($files as $file) {
                $m->attach($mail->uri.$file->name);
            }
        });
        return $mailer;
    }

    public function sendMailAttachment($mail){
        $mailer = Mail::send($mail->template, ['mail' => $mail], function ($m) use ($mail) {
            $m->from($mail->from_email, $mail->from_name);
            $m->to($mail->to_email, ucwords(strtolower($mail->to_name)))->subject($mail->subject);
            $m->attach($mail->file);
        });
        return $mailer;
    }
}
