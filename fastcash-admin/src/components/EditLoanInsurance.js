import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';

import 'react-datepicker/dist/react-datepicker.css';

import { baseURL, httpRequest, loanAPI } from '../config/constants';
import loading from '../assets/img/loading.gif';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {}
    if (!values.insurance) {
        errors.insurance = 'Enter loan insurance';
    }
    if (!values.processing_fee) {
        errors.processing_fee = 'Enter loan processing fee';
    }
    return errors;
};

const renderField = ({input, id, label, type, icon, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label htmlFor={id}>{label}</label>
        <div className="input-group mb-2 mr-sm-2 mb-sm-0">
            <input {...input} placeholder={label} type={type} className="form-control" />
            <div className="input-group-addon">{icon}</div>
        </div>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class EditLoanInsurance extends Component {
    state = {
		submitting: false,
	};

	close = () => {
		this.props.reset('editloan');
		this.props.doHide();
	}
	
	saveLoan = async (data) => {
		try {
			const { user, loan, doHide, saveLoanInsurance } = this.props;

			this.setState({ submitting: true });

			const datum = { ...data, staff_id: user.id };
			const url = `${baseURL}${loanAPI}/${loan.id}/update-insurance`;
			const rs = await httpRequest(url, 'POST', true, datum);
			saveLoanInsurance(rs);
			this.setState({ submitting: false });
			doHide();
		} catch (error) {
			const message = error.message || 'could not save loan';
			this.notify('', message, 'error');
			this.setState({ submitting: false });
			throw new SubmissionError({
				_error: message,
			});
		}
	};

	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	render() {
        const { handleSubmit, error, pristine } = this.props;
        const { submitting } = this.state;
		return (
			<div className="element-box p-3 m-0" style={{width: '240px'}}>
                <form onSubmit={handleSubmit(this.saveLoan)}>
					{error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}/>}
					<div className="row">
						<div className="col-sm-12">
							<Field
								name="insurance"
								id="insurance"
								type="number"
								component={renderField}
								label="Insurance"
								icon="%"
							/>
						</div>
						<div className="col-sm-12">
							<Field
								name="processing_fee"
								id="processing_fee"
								type="number"
								component={renderField}
								label="Processing Fee"
								icon="%"
							/>
						</div>
					</div>
                    <div className="text-center">
                        <button className="btn btn-primary" disabled={pristine || submitting} type="submit">
                            {submitting? <img src={loading} alt=""/> : 'Save Loan'}
                        </button>
                        <button className="btn btn-default ml-4" onClick={this.close} type="button"> Cancel</button>
                    </div>
                </form>
            </div>
		);
	}
}

EditLoanInsurance = reduxForm({
    form: 'editloan',
    validate,
})(EditLoanInsurance);

const mapStateToProps = (state, ownProps) => {
    return {
        initialValues: {
            insurance: ownProps.loan.insurance,
            processing_fee: ownProps.loan.processing_fee,
		},
		user: state.user.user,
    }
}

export default connect(mapStateToProps, { reset, doNotify })(EditLoanInsurance);
