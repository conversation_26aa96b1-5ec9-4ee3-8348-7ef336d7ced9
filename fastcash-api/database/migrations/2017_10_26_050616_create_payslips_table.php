<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatePayslipsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payslips', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('name')->nullable();
            $table->string('ippis')->nullable()->index('ippis');
            $table->string('employer')->nullable();
            $table->string('designation')->nullable();
            $table->string('gender')->nullable();
            $table->date('first_appointment')->nullable();
            $table->date('birth_date')->nullable();
            $table->date('retire_expected_at')->nullable();
            $table->string('salary_bank')->nullable();
            $table->string('salary_bank_branch')->nullable();
            $table->string('salary_account_number')->nullable();
            $table->string('account_pin')->nullable()->comment('last 4 digits of account number');
            $table->string('pfa_name')->nullable();
            $table->string('pension_pin')->nullable();
            $table->string('grade')->nullable();
            $table->string('location')->nullable();
            $table->string('step')->nullable();
            $table->integer('month')->nullable();
            $table->integer('year')->nullable();
            $table->string('tax_state')->nullable();
            $table->string('trade_union')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payslips');
    }
}
