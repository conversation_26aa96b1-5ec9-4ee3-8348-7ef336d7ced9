import * as types from './types';
import { httpRequest, baseURL, merchantAPI } from '../config/constants';

export const removeMerchant = id => {
	return async dispatch => {
		try {
			await httpRequest(`${baseURL}${merchantAPI}/${id}`, 'DELETE', true);

			dispatch({
				type: types.DELETE_MERCHANT,
				payload: id,
			});
		}
		catch (error) {
			console.log(error);
		}
	};
};

export const loadMerchants = merchants => {
	return {
		type: types.LOAD_MERCHANTS,
		payload: merchants,
	};
};

export const addMerchant = merchant => {
	return {
		type: types.ADD_MERCHANT,
		payload: merchant,
	};
};

export const updateMerchant = merchant => {
	return {
		type: types.UPDATE_MERCHANT,
		payload: merchant,
	};
};
