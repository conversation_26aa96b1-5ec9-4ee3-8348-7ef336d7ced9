<?php

namespace App\Http\Controllers\Api\V1;

use App\Exports\ExportLoan;
use App\Models\Accountno;
use App\Models\Setting;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use App\Models\Withdraw;
use App\Http\Controllers\Controller;
use App\Models\Lender;
use App\Services\MailHandler;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Log;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;

class WithdrawController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $limit = $request->has('pagesize') && $request->input('pagesize') != '' ? $request->input('pagesize') : 10;
            $page = $request->has('page') && $request->input('page') != '' ? $request->input('page') : 1;
            $skip = ($page - 1) * $limit;

            $lender_id = $request->has('lender_id') ? $request->input('lender_id') : '';
            $status = $request->has('status') ? $request->input('status') : '';

            // if ($lender_id == '' || $lender_id == 1) {
            //     $withdrawals = Withdraw::withTrashed()->skip($skip)->take($limit)->orderBy('approved', 'asc')->orderBy('created_at', 'desc')->get();

            //     $count = Withdraw::withTrashed()->count();
            // } else {
            //     $withdrawals = Withdraw::withTrashed()->where('lender_id', $lender_id)->skip($skip)->take($limit)->orderBy('approved', 'asc')->orderBy('created_at', 'desc')->get();

            //     $count = Withdraw::withTrashed()->where('lender_id', $lender_id)->count();
            // }

            $from = $request->has('from') && $request->input('from') != '' ? $request->input('from') : '';
            $to = $request->has('to') && $request->input('to') != '' ? $request->input('to') : '';

            $withdrawals = (object)null;
            $count = 0;

            $_from = $from == '' ? '' : Carbon::createFromFormat('d-m-Y', $from)->toDateTimeString();
            $_to = $to == '' ? '' : Carbon::createFromFormat('d-m-Y', $to)->toDateTimeString();

            if (empty($lender_id) && empty($status) && empty($from)) {
                $withdrawals = Withdraw::withTrashed()->skip($skip)->take($limit)->orderBy('approved', 'asc')->orderBy('created_at', 'desc')->get();
                $count = Withdraw::withTrashed()->count();
            } else if (empty($lender_id) && empty($status) && !empty($from)) {
                $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('approved', 'asc')->orderBy('created_at', 'desc')->get();
                $count = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->count();
            } else if (empty($lender_id) && !empty($status) && empty($from)) {
                if ($status == 'approved') {
                    $withdrawals = Withdraw::withTrashed()->where('approved', 1)->skip($skip)->take($limit)->orderBy('approved_at', 'desc')->get();
                    $count = Withdraw::withTrashed()->where('approved', 1)->count();
                } else if ($status == 'declined') {
                    $withdrawals = Withdraw::withTrashed()->where('declined_by', 1)->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    $count = Withdraw::withTrashed()->where('declined_by', 1)->count();
                } else {
                    $withdrawals = Withdraw::withTrashed()->whereNull('deleted_at')->where('approved', 0)->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    $count = Withdraw::withTrashed()->whereNull('deleted_at')->where('approved', 0)->count();
                }
            } else if (!empty($lender_id) && !empty($status) && !empty($from)) {
                if ($status == 'approved') {
                    $withdrawals = Withdraw::withTrashed()->where('approved', 1)->where('lender_id', $lender_id)->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('approved_at', 'desc')->get();
                    $count = Withdraw::withTrashed()->where('approved', 1)->where('lender_id', $lender_id)->whereBetween('created_at', [$_from, $_to])->count();
                } else if ($status == 'declined') {
                    $withdrawals = Withdraw::withTrashed()->where('declined_by', 1)->where('lender_id', $lender_id)->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    $count = Withdraw::withTrashed()->where('declined_by', 1)->where('lender_id', $lender_id)->whereBetween('created_at', [$_from, $_to])->count();
                } else {
                    $withdrawals = Withdraw::withTrashed()->whereNull('deleted_at')->where('approved', 0)->where('lender_id', $lender_id)->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    $count = Withdraw::withTrashed()->whereNull('deleted_at')->where('approved', 0)->where('lender_id', $lender_id)->whereBetween('created_at', [$_from, $_to])->count();
                }
            } else if (!empty($lender_id) && !empty($status) && empty($from)) {
                if ($status == 'approved') {
                    $withdrawals = Withdraw::withTrashed()->where('approved', 1)->where('lender_id', $lender_id)->skip($skip)->take($limit)->orderBy('approved_at', 'desc')->get();
                    $count = Withdraw::withTrashed()->where('approved', 1)->where('lender_id', $lender_id)->count();
                } else if ($status == 'declined') {
                    $withdrawals = Withdraw::withTrashed()->where('declined_by', 1)->where('lender_id', $lender_id)->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    $count = Withdraw::withTrashed()->where('declined_by', 1)->where('lender_id', $lender_id)->count();
                } else {
                    $withdrawals = Withdraw::withTrashed()->whereNull('deleted_at')->where('approved', 0)->where('lender_id', $lender_id)->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    $count = Withdraw::withTrashed()->whereNull('deleted_at')->where('approved', 0)->where('lender_id', $lender_id)->count();
                }
            } else if (empty($lender_id) && !empty($status) && !empty($from)) {
                if ($status == 'approved') {
                    $withdrawals = Withdraw::withTrashed()->where('approved', 1)->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('approved_at', 'desc')->get();
                    $count = Withdraw::withTrashed()->where('approved', 1)->whereBetween('created_at', [$_from, $_to])->count();
                } else if ($status == 'declined') {
                    $withdrawals = Withdraw::withTrashed()->where('declined_by', 1)->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    $count = Withdraw::withTrashed()->where('declined_by', 1)->whereBetween('created_at', [$_from, $_to])->count();
                } else {
                    $withdrawals = Withdraw::whereNull('deleted_at')->where('approved', 0)->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    $count = Withdraw::withTrashed()->whereNull('deleted_at')->where('approved', 0)->whereBetween('created_at', [$_from, $_to])->count();
                }
            } else if (!empty($lender_id) && empty($status) && !empty($from)) {
                $withdrawals = Withdraw::withTrashed()->where('lender_id', $lender_id)->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('approved', 'asc')->orderBy('created_at', 'desc')->get();
                $count = Withdraw::withTrashed()->where('lender_id', $lender_id)->whereBetween('created_at', [$_from, $_to])->count();
            } else if (!empty($lender_id) && empty($status) && empty($from)) {
                $withdrawals = Withdraw::withTrashed()->where('lender_id', $lender_id)->skip($skip)->take($limit)->orderBy('approved', 'asc')->orderBy('created_at', 'desc')->get();

                $count = Withdraw::withTrashed()->where('lender_id', $lender_id)->count();
            } else {
                $withdrawals = Withdraw::withTrashed()->skip($skip)->take($limit)->orderBy('approved', 'asc')->orderBy('created_at', 'desc')->get();
                $count = Withdraw::withTrashed()->count();
            }

            $withdrawals = $withdrawals->map(function ($item, $key) {
                $item->accountno = Accountno::where('id', $item->accountno_id)->first();
                $item->user = User::where('id', $item->user_id)->first();
                $item->lender = Lender::where('id', $item->lender_id)->first(['name']);
                return $item;
            });

            $lenders = Lender::orderBy('created_at', 'desc')->get();

            $result = (object)null;
            $result->total = $count;
            $result->size = $limit;
            $result->page = $page;
            $result->data = $withdrawals;
            $result->lenders = $lenders;
            $result->withdrawals = Withdraw::where('approved', 0)->count();

            return response()->json(compact('result'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not fetch withdrawals'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric',
            'bank_account' => 'required',
        ], [
            'amount.numeric' => 'amount should be a number'
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        DB::beginTransaction();
        try {
            $data = $request->all();

            $wallet = Wallet::where('user_id', $data['user_id'])->where('is_lender', 0)->where('paid', 1)->sum('amount');

            $pending = Withdraw::where('user_id', $data['user_id'])->where('approved', 0)->first();
            if ($pending) {
                return response()->json(['error' => 'you have a pending withdrawal request'], 500);
            }

            $check = Setting::where('name', 'withdrawal_limit')->where('lender_id', 1)->first();
            if ($wallet < $check->value) {
                return response()->json(['error' => 'you cannot withdraw less than NGN' . number_format($check->value, 0)], 500);
            }

            if ($data['amount'] > $wallet) {
                return response()->json(['error' => 'you have insufficient funds in your wallet to make this withdrawal'], 500);
            }

            $withdraw = Withdraw::create([
                'user_id' => $data['user_id'],
                'amount' => $data['amount'],
                'accountno_id' => $data['bank_account'],
                'lender_id' => $data['lender_id'],
            ]);

            $notify = doNotify($data['user_id'], 'withdraws', $withdraw->id, 'withdraw_request', 'success', null, $data['lender_id'], null);

            DB::commit();

            $user = User::where('id', $withdraw->user_id)->first(['id', 'name', 'email']);

            try {
                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $user->name);
                $mail->to_email = $user->email;
                $mail->template = 'email.withdrawal_request';
                $mail->subject = 'Withdrawal Request';
                $mail->login = env('MAIN_URL');
                $mail->amount = number_format($withdraw->amount, 2);
                $mail->to_user = true;

                $send = (new MailHandler())->sendMail($mail);

                $admin_mail = $mail;
                $admin_mail->to_name = 'FastCash Support';
                $admin_mail->to_email = '<EMAIL>';
                $admin_mail->login = '';
                $admin_mail->to_user = false;
                $admin_mail->user_name = str_replace(',', '', $user->name);

                $admin_send = (new MailHandler())->sendMail($admin_mail);
            } catch (\Exception $e) {
                Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            }

            $result = (object)null;
            $user->wallet = Wallet::where('user_id', $user->id)->where('approved', 1)->where('is_lender', 0)->where('paid', 1)->sum('amount');

            $requests = Wallet::where('user_id', $user->id)->where('approved', 0)->where('is_lender', 0)->count();
            $result->has_request = $requests > 0;

            $withdraw = Withdraw::where('id', $withdraw->id)->first();
            $withdraw->accountno = Accountno::where('id', $data['bank_account'])->first();

            return response()->json(['result' => $result, 'withdraw' => $withdraw], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not make withdrawal, try again later'], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        try {
            $limit = $request->has('pagesize') && $request->input('pagesize') != '' ? $request->input('pagesize') : 10;
            $page = $request->has('page') && $request->input('page') != '' ? $request->input('page') : 1;
            $skip = ($page - 1) * $limit;

            $withdrawals = Withdraw::withTrashed()->where('user_id', $id)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();
            $withdrawals = $withdrawals->map(function ($item, $key) {
                $item->accountno = Accountno::where('id', $item->accountno_id)->first();
                return $item;
            });

            $count = Withdraw::withTrashed()->where('user_id', $id)->count();

            $result = (object)null;
            $result->total = $count;
            $result->size = $limit;
            $result->page = $page;
            $result->data = $withdrawals;

            return response()->json(compact('result'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not fetch withdrawals'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * approve withdrawal.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function approve(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();

            $withdraw = Withdraw::where('id', $id)->first();

            $user = User::where('id', $withdraw->user_id)->first(['id', 'name', 'email', 'office_id']);

            $withdraw->approved = 1;
            $withdraw->approved_at = date('Y-m-d H:i:s');
            $withdraw->approved_by = $data['staff_id'];
            $withdraw->save();

            $notify = doNotify($user->id, 'withdraws', $id, 'withdraw_approve', 'success', null, $data['lender_id'], $data['staff_id']);

            $transaction = Transaction::create([
                'user_id' => $user->id,
                'office_id' => $user->office_id,
                'lender_id' => $data['lender_id'],
                'amount' => $withdraw->amount,
                'status' => 'Completed',
                'reference' => null,
                'transaction_flag' => 'withdrawal',
                'channel' => 'wallet-withdrawal',
                'approved' => 1,
                'approved_by' => $data['staff_id'],
                'approved_at' => date('Y-m-d H:i:s'),
            ]);

            $wallet = Wallet::create([
                'user_id' => $user->id,
                'category' => 'withdraw',
                'amount' => ($withdraw->amount * -1),
                'type' => 'debit',
                'lender_id' => $data['lender_id'],
                'transaction_id' => $transaction->id,
                'approved' => 1,
                'approved_by' => $data['staff_id'],
                'approved_at' => date('Y-m-d H:i:s'),
                'paid' => 1,
            ]);

            $notify_transaction = doNotify($user->id, 'transactions', $transaction->id, 'withdrawal_transaction', 'success', null, $data['lender_id'], $data['staff_id']);

            DB::commit();

            try {
                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $user->name);
                $mail->to_email = $user->email;
                $mail->template = 'email.withdrawal_approved';
                $mail->subject = 'Withdrawal Approved';
                $mail->login = env('MAIN_URL');
                $mail->amount = number_format($withdraw->amount, 2);

                $send = (new MailHandler())->sendMail($mail);
            } catch (\Exception $e) {
                Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            }

            return response()->json(compact('withdraw'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not approve withdrawal request'], 500);
        }
    }

    /**
     * decline withdrawal.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function decline(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();

            $withdraw = Withdraw::where('id', $id)->first();
            $withdraw->declined_by = $data['staff_id'];
            $withdraw->save();

            $user = User::where('id', $withdraw->user_id)->first(['id', 'name', 'email']);

            $notify = doNotify($withdraw->user_id, 'withdraws', $id, 'withdraw_decline', 'success', null, $data['lender_id'], $data['staff_id']);

            $withdraw->delete();

            DB::commit();

            try {
                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $user->name);
                $mail->to_email = $user->email;
                $mail->template = 'email.withdrawal_declined';
                $mail->subject = 'Withdrawal Declined';
                $mail->login = env('MAIN_URL');
                $mail->amount = number_format($withdraw->amount, 2);
                $mail->content = isset($data['message']) ? $data['message'] : '';

                $send = (new MailHandler())->sendMail($mail);
            } catch (\Exception $e) {
                Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            }

            return response()->json(compact('withdraw'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not decline withdrawal request'], 500);
        }
    }

    /**
     * Download withdrawal request.
     *
     * @param \Illuminate\Http\Request $request
     * @param string $slug
     * @return \Illuminate\Http\Response
     */
    public function export(Request $request)
    {
        $lender = $request->has('lender_id') ? $request->input('lender_id') : '';
        $status = $request->has('status') ? $request->input('status') : '';

        $from = $request->has('from') && $request->input('from') != '' ? $request->input('from') : '';
        $to = $request->has('to') && $request->input('to') != '' ? $request->input('to') : '';

        try {
            $report = null;

            $report = $this->getWithdrawalReport($lender, $status, $from, $to);
            if ($report) {
                $allData = json_decode(json_encode($report->data), true);

                $report = [];
                foreach ($allData as $item) {
                    $new_report = [];
                    foreach ($item as $key => $value) {
                        $new_report[ucwords(str_replace("_", " ", $key))] = strval($value);
                        unset($new_report[$key]);
                    }
                    $report[] = $new_report;
                }

                if (count($report) == 0) {
                    return response()->json(['message' => 'no data available'], 200);
                }

                $headings = getHeadings($report[0]);

                $filename = 'withdrawal-' . time();
                $filepath = 'reports/'.$filename.'.xlsx';

                Excel::store(new ExportLoan($report, $headings), $filepath, 'public');

                $file = env('APP_URL') . '/reports/' . $filename . '.xlsx';

                return response()->json(compact('file'), 200);
            }

            return response()->json(['error' => 'could not export requests'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not export withdrawal requests'], 500);
        }
    }

    public function getWithdrawalReport($lender, $status, $from, $to, $limit = 0, $skip = 0)
    {
        $data = [];
        $count = 0;

        $_from = $from == '' ? '' : Carbon::createFromFormat('d-m-Y', $from)->toDateTimeString();
        $_to = $to == '' ? '' : Carbon::createFromFormat('d-m-Y', $to)->toDateTimeString();

        if ($limit == 0) {
            if ($from == '' && $to == '') {
                if ($lender == '' && $status == '') {
                    $withdrawals = Withdraw::orderBy('approved', 'asc')->orderBy('created_at', 'desc')->get();
                } else if ($lender != '' && $status != '') {
                    if ($status == 'approved') {
                        $withdrawals = Withdraw::withTrashed()->where('approved', 1)->where('lender_id', $lender)->orderBy('approved', 'asc')->orderBy('created_at', 'desc')->get();
                    } else if ($status == 'declined') {
                        $withdrawals = Withdraw::withTrashed()->where('declined_by', 1)->where('lender_id', $lender)->orderBy('deleted_at', 'asc')->get();
                    } else {
                        $withdrawals = Withdraw::withTrashed()->whereNull('deleted_at')->where('approved', 0)->where('lender_id', $lender)->orderBy('deleted_at', 'asc')->get();
                    }
                } else if ($lender == '' && $status != '') {
                    if ($status == 'approved') {
                        $withdrawals = Withdraw::withTrashed()->where('approved', 1)->orderBy('approved', 'asc')->orderBy('created_at', 'desc')->get();
                    } else if ($status == 'declined') {
                        $withdrawals = Withdraw::withTrashed()->where('declined_by', 1)->orderBy('deleted_at', 'asc')->get();
                    } else {
                        $withdrawals = Withdraw::withTrashed()->whereNull('deleted_at')->where('approved', 0)->orderBy('deleted_at', 'asc')->get();
                    }
                } else if ($lender != '' && $status == '') {
                    $withdrawals = Withdraw::withTrashed()->where('lender_id', $lender)->orderBy('created_at', 'desc')->get();
                }
            } else {
                if ($lender == '' && $status == '') {
                    $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->orderBy('created_at', 'desc')->get();
                } else if ($lender != '' && $status != '') {
                    if ($status == 'approved') {
                        $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->where('approved', 1)->where('lender_id', $lender)->orderBy('created_at', 'desc')->get();
                    } else if ($status == 'declined') {
                        $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->where('declined_by', 1)->where('lender_id', $lender)->orderBy('created_at', 'desc')->get();
                    } else {
                        $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->whereNull('deleted_at')->where('approved', 0)->where('lender_id', $lender)->orderBy('deleted_at', 'asc')->get();
                    }
                } else if ($lender == '' && $status != '') {
                    if ($status == 'approved') {
                        $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->where('approved', 1)->orderBy('approved', 'asc')->orderBy('created_at', 'desc')->get();
                    } else if ($status == 'declined') {
                        $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->where('declined_by', 1)->orderBy('deleted_at', 'asc')->get();
                    } else {
                        $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->whereNull('deleted_at')->where('approved', 0)->orderBy('deleted_at', 'asc')->get();
                    }
                } else if ($lender != '' && $status == '') {
                    $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->where('lender_id', $lender)->orderBy('created_at', 'desc')->get();
                }
            }
        } else {
            if ($from == '' && $to == '') {
                if ($lender == '' && $status == '') {
                    $withdrawals = Withdraw::withTrashed()->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();
                } else if ($lender != '' && $status != '') {
                    if ($status == 'approved') {
                        $withdrawals = Withdraw::withTrashed()->where('approved', 1)->where('lender_id', $lender)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();
                    } else if ($status == 'declined') {
                        $withdrawals = Withdraw::withTrashed()->where('declined_by', 1)->where('lender_id', $lender)->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    } else {
                        $withdrawals = Withdraw::withTrashed()->whereNull('deleted_at')->where('approved', 0)->where('lender_id', $lender)->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    }
                } else if ($lender == '' && $status != '') {
                    if ($status == 'approved') {
                        $withdrawals = Withdraw::withTrashed()->where('approved', 1)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();
                    } else if ($status == 'declined') {
                        $withdrawals = Withdraw::withTrashed()->where('declined_by', 1)->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    } else {
                        $withdrawals = Withdraw::withTrashed()->whereNull('deleted_at')->where('approved', 0)->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    }
                } else if ($lender != '' && $status == '') {
                    $withdrawals = Withdraw::withTrashed()->where('lender_id', $lender)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();
                }
            } else {
                if ($lender == '' && $status == '') {
                    $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();
                } else if ($lender != '' && $status != '') {
                    if ($status == 'approved') {
                        $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->where('approved', 1)->where('lender_id', $lender)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();
                    } else if ($status == 'declined') {
                        $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->where('declined_by', 1)->where('lender_id', $lender)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();
                    } else {
                        $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->whereNull('deleted_at')->where('approved', 0)->where('lender_id', $lender)->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    }
                } else if ($lender == '' && $status != '') {
                    if ($status == 'approved') {
                        $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->where('approved', 1)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();
                    } else if ($status == 'declined') {
                        $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->where('declined_by', 1)->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    } else {
                        $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->whereNull('deleted_at')->where('approved', 0)->skip($skip)->take($limit)->orderBy('deleted_at', 'asc')->get();
                    }
                } else if ($lender != '' && $status == '') {
                    $withdrawals = Withdraw::withTrashed()->whereBetween('created_at', [$_from, $_to])->where('lender_id', $lender)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();
                }
            }
        }

        $i = 0;
        foreach ($withdrawals as $withdraw) {
            $i++;

            $user = User::where('id', $withdraw->user_id)->first(['name']);
            $lender = Lender::where('id', $withdraw->lender_id)->first(['name']);
            $accountNo = Accountno::where('id', $withdraw->accountno_id)->first(['bank_name', 'account_number']);
            $status = null;
            if ($withdraw->approved == 1) {
                $status = "Approved";
            } else if ($withdraw->declined_by == 1) {
                $status = "Declined";
            } else {
                $status = "Pending";
            }

            $item = (object)null;
            $item->sn = $i;
            $item->name = $user->name;
            $item->lender = $lender->name;
            $item->amount = $withdraw->amount;
            $item->bank_acc = $accountNo->bank_name . " - " . $accountNo->account_number;
            $item->status = $status;
            $item->request_date = Carbon::parse($withdraw->created_at)->format('Y-m-d H:i:s');

            $data[] = $item;
        }

        $result = (object)null;
        $result->count = $count;
        $result->data = $data;

        return $result;
    }
}
