<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropForeignKeysInLoansTable extends Migration
{
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropForeign(['accountno_id']);
            $table->dropForeign(['user_id']);
        });
    }

    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->foreign('accountno_id')->references('id')->on('accountnos');
            $table->foreign('user_id')->references('id')->on('users');
        });
    }
}

