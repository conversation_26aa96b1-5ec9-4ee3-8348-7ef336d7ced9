/* eslint eqeqeq: 0 */
import React from 'react';
import numeral from 'numeral';

const TabContent = ({ schedules, month, year }) => {
    const my_schedules = schedules.filter(s => s.month == month && s.year == year);
    return (
        <table className="table table-striped table-lightfont">
            <thead>
                <tr>
                    <th>S/N</th>
                    <th>IPPIS Number</th>
                    <th>Name</th>
                    <th>Acct NO</th>
                    <th><PERSON>an Amount</th>
                    <th>Interest</th>
                    <th>Outst. Loan Amt.</th>
                    <th>Monthly Dedux.</th>
                    <th>Tenure</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                </tr>
            </thead>
            <tbody>
                {(my_schedules.length == 0)? <tr><td colSpan="11"><div className="text-center">No Schedules Yet</div></td></tr> : (
                    my_schedules.map((ms, i) => {
                        return (
                            <tr key={i}>
                                <td>{(i+1)}</td>
                                <td>{(ms.ippis_number || ms.user.customer_id || ms.user.phone || '-')}</td>
                                <td>{ms.name}</td>
                                <td>{ms.account_number}</td>
                                <td>{`₦${numeral(ms.loan_amount).format('0,0.00')}`}</td>
                                <td>{`₦${numeral(ms.interest).format('0,0.00')}`}</td>
                                <td>{`₦${numeral(ms.outst_loan_amt).format('0,0.00')}`}</td>
                                <td>{`₦${numeral(ms.monthly_dedux).format('0,0.00')}`}</td>
                                <td>{ms.tenure}</td>
                                <td>{ms.start_date}</td>
                                <td>{ms.end_date}</td>
                            </tr>
                        );
                    })
                )}
            </tbody>
        </table>
    );
};

export default TabContent;