import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import { withRouter } from 'react-router-dom';

import { baseURL, httpRequest, changeEmailAPI } from '../config/constants';
import loading from '../assets/img/loading.gif';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {}
    if (!values.email) {
        errors.email = 'Enter your email address';
	}
    return errors;
};

const renderField = ({input, id, icon, label, hideclass, type, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')} ${hideclass}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" />
        <div className={`pre-icon os-icon ${icon}`}></div>
        {touched &&
            ((error && <span className="help-block">{error}</span>) ||
                (warning && <span className="help-block">{warning}</span>))}
    </div>
);

class ChangeEmail extends Component {
    state = {
        vsuccess: '',
    };

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	changeEmail = async data => {
		const { user_id, user_email } = this.props;
        this.setState({ vsuccess: '', submitting: true });
		try {
            const details = { ...data, user_id, user_email };
            await httpRequest(`${baseURL}${changeEmailAPI}`, 'POST', false, details);
            this.setState({ vsuccess: 'Please check your email to verify and activate your account', submitting: false });
            this.props.reset('resendemail');
        }
        catch (error) {
            const message = error.message || 'error, please try again';
            this.notify('', message, 'error');
            this.setState({ submitting: false });
            throw new SubmissionError({
                _error: message,
            });
        }
	};

    render() {
        const { handleSubmit, error, submitting, pristine } = this.props;
        const { vsuccess } = this.state;
        return (
            <div className="login-box">
                <div className="auth-box-w intro-description">
                    <h4 className="auth-header">Send Activation Email</h4>
                    {error && <div className="alert alert-danger" dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                    {vsuccess !== '' && <div className="alert alert-success" dangerouslySetInnerHTML={{__html: `<strong>Success!</strong> ${vsuccess}`}}></div>}
                    <form onSubmit={handleSubmit(this.changeEmail)} autoComplete="off">
                        <Field
                            name="email"
                            type="email"
                            component={renderField}
                            label="Email Address"
                            icon="os-icon-email-2-at"
                        />
                        <div className="buttons-w">
                            <button className="btn btn-primary" disabled={pristine || submitting} type="submit">
                                {submitting? <img src={loading} alt=""/> : 'Send Email'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    }
}

ChangeEmail = reduxForm({
    form: 'resendemail',
    validate
})(ChangeEmail);

const mapStateToProps = (state, ownProps) => {
	return {
		user_id: state.user.user_id,
		user_email: state.user.user_email,
	}
}

export default withRouter(connect(mapStateToProps, { reset, doNotify })(ChangeEmail));
