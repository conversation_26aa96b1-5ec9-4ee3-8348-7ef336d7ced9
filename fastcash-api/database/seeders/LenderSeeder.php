<?php

namespace Database\Seeders;

use App\Models\Lender;
use App\Models\State;
use Illuminate\Database\Seeder;

class LenderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $state = State::where('name', 'FCT Abuja')->first();
        $lender = Lender::create([
            'name' => 'Consumer Micro Finance Bank',
            'email' => '<EMAIL>',
            'address' => NULL,
            'state_id' => $state->id,
            'phone_number' => '***********',
            'bank_code_id' => NULL,
            'account_number' => NULL,
            'lender_code' => '0001',
            'next_of_kin' => NULL,
        ]);
    }
}
