/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import Modal from 'react-modal';
import moment from 'moment';
import { Link } from 'react-router-dom';
import numeral from 'numeral';

import { loadMerchants, removeMerchant } from '../actions/merchant';
import CreateMerchant from '../components/CreateMerchant';
import GetUser from '../components/GetUser';
import { httpRequest, baseURL, merchantAPI } from '../config/constants';
import { doNotify } from '../actions/general';
import { startBlock, stopBlock } from '../actions/ui-block';

class Merchants extends Component {
	state = {
		modalIsOpen: false,
		modalFindIsOpen: false,
	};

	componentWillMount() {
		Modal.setAppElement('body');
	}

	componentDidMount() {
		this.fetch();
	}

	fetch = async () => {
		try {
			this.props.startBlock();
			const { lender } = this.props;
			const url = `${baseURL}${merchantAPI}?lender=${lender.id}`;
			const rs = await httpRequest(url, 'GET', true);
			this.props.loadMerchants(rs.merchants);
			this.props.stopBlock();
		} catch (e) {
			console.log(e.message);
			this.props.stopBlock();
			this.props.doNotify({ message: 'could not load merchants', level: 'error' });
		}
	};

	openModal = () => {
		document.body.className = 'pages modal-open';
		this.setState({ modalIsOpen: true });
	};

	openFindModal = () => {
		document.body.className = 'pages modal-open';
		this.setState({ modalFindIsOpen: true });
	};

	closeModal = () => {
		this.setState({ modalIsOpen: false });
		document.body.className = 'pages';
		this.fetch();
	};

	closeFindModal = () => {
		this.setState({ modalFindIsOpen: false });
		document.body.className = 'pages';
		this.fetch();
	};

	removeUser = (id) => {
		const action = window.confirm('Are you sure you want to delete merchant?') ? true : false;
		if (action) {
			this.props.removeMerchant(id);
		}
	};

	render() {
		const { merchants } = this.props;
		const { modalIsOpen, modalFindIsOpen } = this.state;

		return (
			<div className="content-i">
				<div className="content-box">
					<div className="element-wrapper">
						<div className="element-actions">
							<a onClick={() => this.openFindModal()} className="btn btn-primary btn-sm cursor" style={{ color: '#ffffff' }}>
								<i className="os-icon os-icon-search" />
								<span>Find User</span>
							</a>
							<span> | </span>
							<a onClick={() => this.openModal()} className="btn btn-primary btn-sm cursor" style={{ color: '#ffffff' }}>
								<i className="os-icon os-icon-ui-22" />
								<span>New Merchant</span>
							</a>
						</div>
						<h6 className="element-header">Manage Merchants</h6>
						<div className="element-box">
							<div className="table-responsive">
								<table className="table table-striped">
									<thead>
										<tr>
											<th>Name of Establishment</th>
											<th>Merchant's Name</th>
											<th>Merchant Code</th>
											<th>Wallet Balance</th>
											<th>Commission Balance</th>
											<th>Count</th>
											<th>Enabled</th>
											<th>Date Created</th>
											<th className="text-center" />
										</tr>
									</thead>
									<tbody>
										{merchants.map((mu, i) => {
											return (
												<tr key={i}>
													<td>{mu.office}</td>
													<td>{mu.user}</td>
													<td>{mu.merchant_code}</td>
													<td>{`₦${numeral(mu.wallet_balance).format('0,0.00')}`}</td>
													<td>{`₦${numeral(mu.commission_balance).format('0,0.00')}`}</td>
													<td>{mu.count}</td>
													<td>
														{mu.enabled == 1 ? (
															<span className="elabel elabel-success">
																Active
															</span>
														) : (
															<span className="elabel elabel-danger">
																Not Active
															</span>
														)}
													</td>
													<td>{moment(mu.created_at).format('DD.MMM.YYYY')}</td>
													<td className="row-actions">
														<Link to={`/merchants/${mu.merchant_code}`}>
															<i className="os-icon os-icon-arrow-2-up" />
														</Link>
														<a className="text-danger cursor" onClick={() => this.removeUser(mu.id)}>
															<i className="os-icon os-icon-database-remove" />
														</a>
													</td>
												</tr>
											);
										})}
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
				<Modal
					isOpen={modalFindIsOpen}
					onRequestClose={this.closeFindModal}
					contentLabel="Find User"
					shouldCloseOnOverlayClick={false}
					overlayClassName="modal-dialog modal-md"
					className="modal-content"
					portalClassName="ReactModalPortalr"
					bodyOpenClassName="ReactModal__Body--openr"
				>
					<GetUser closeModal={() => this.closeFindModal()} />
				</Modal>
				{modalFindIsOpen ? <div className="modal-backdrop fade show" /> : ''}
				<Modal
					isOpen={modalIsOpen}
					onRequestClose={this.closeModal}
					contentLabel="Create Merchant"
					shouldCloseOnOverlayClick={false}
					overlayClassName="modal-dialog modal-sm"
					className="modal-content"
				>
					<CreateMerchant closeModal={() => this.closeModal()} />
				</Modal>
				{modalIsOpen ? <div className="modal-backdrop fade show" /> : ''}
			</div>
		);
	}
}

const mapStateToProps = (state) => {
	return {
		merchants: state.merchant.list,
		user: state.user.user,
		lender: state.lender.profile,
	};
};

export default connect(mapStateToProps, {removeMerchant, loadMerchants, startBlock, stopBlock, doNotify })(Merchants);
