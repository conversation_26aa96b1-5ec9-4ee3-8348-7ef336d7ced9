<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Accountno;
use App\Models\BankCode;
use App\Models\Earning;
use App\Models\Lender;
use App\Models\Loan;
use App\Models\Merchant;
use App\Models\MerchantPay;
use App\Models\Notify;
use App\Models\Office;
use App\Models\Payslip;
use App\Models\PayslipUpload;
use App\Models\RemitaUser;
use App\Models\Role;
use App\Models\Setting;
use App\Models\Support;
use App\Models\SupportMail;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use App\Models\Withdraw;
use App\Services\MailHandler;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Log;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $limit = $request->has('pagesize') && $request->input('pagesize') != '' ? $request->input('pagesize') : 10;
            $page = $request->has('page') && $request->input('page') != '' ? $request->input('page') : 1;
            $skip = ($page - 1) * $limit;

            $lender = $request->has('lender') && $request->input('lender') != '' ? $request->input('lender') : '';

            $search = $request->has('search') && $request->input('search') != '' ? $request->input('search') : '';

            if ($lender != '') {
                $q = $request->has('q') && $request->input('q') != '' ? $request->input('q') : '';
                $o = $request->has('office') ? $request->input('office') : 0;

                if ($q == 'list') {
                    $office = Office::where('id', $o)->first();
                    if (!$office) {
                        return response()->json(['error' => 'could not find office'], 500);
                    }

                    $_loan_status = $request->has('loan_status') && $request->input('loan_status') != '' ? $request->input('loan_status') : '';

                    if ($search == '') {
                        $users = User::with('office', 'lender')->where('office_id', $office->id)->get();
                    } else {
                        $users = User::with('office', 'lender')
                            ->where('office_id', $office->id)
                            ->where(function ($query) use ($search) {
                                $query->where('phone', 'like', '%' . $search . '%')->orWhere('ippis', 'like', '%' . $search . '%')->orWhere('customer_id', 'like', '%' . $search . '%');
                            })
                            ->get();
                    }

                    $users = $users->map(function ($item, $key) {
                        $loan = Loan::with('accountno', 'lender')->where('id', $item->loan_id)->first();
                        if ($loan) {
                            $loan->user = User::with('office', 'lender', 'accountno')->where('id', $item->id)->first();
                            $loan->remita_user = RemitaUser::where('id', $loan->remita_user_id)->first();
                        }

                        $item->loan = $loan;
                        $item->loans = Loan::with('user', 'lender', 'remita_user')->where('user_id', $item->id)->withTrashed()->get();

                        $loans = Loan::where('user_id', $item->id)->where('disbursed', 1)->count();
                        $item->requires_core_bank_id = ($loans > 0);

                        $item->wallet = Wallet::where('user_id', $item->id)->where('approved', 1)->where('is_lender', 0)->where('paid', 1)->sum('amount');

                        return $item;
                    });

                    switch ($_loan_status) {
                        case 'verified':
                            $data = collect($users)->filter(function ($item, $key) {
                                return $item->loan != null && $item->loan->approved == 0 && $item->loan->verified == 1;
                            })->sortByDesc('loan')->values();
                            break;
                        case 'approved':
                            $data = collect($users)->filter(function ($item, $key) {
                                return $item->loan != null && $item->loan->approved == 1 && $item->loan->disbursed == 0;
                            })->sortByDesc('loan')->values();
                            break;
                        case 'disbursed':
                            $data = collect($users)->filter(function ($item, $key) {
                                return $item->loan != null && $item->loan->approved == 1 && $item->loan->disbursed == 1;
                            })->sortByDesc('loan')->values();
                            break;
                        case 'not-verified':
                            $data = collect($users)->filter(function ($item, $key) {
                                return $item->loan != null && $item->loan->verified == 0;
                            })->sortByDesc('loan')->values();
                            break;
                        case 'admin-created':
                            $data = collect($users)->filter(function ($item, $key) {
                                return $item->is_admin_created == 1;
                            })->sortByDesc('loan')->values();
                            break;
                        default:
                            $data = collect($users)->sortByDesc('loan')->values();
                            break;
                    }

                    $count_users = count($data);

                    $results = array_slice($data->toArray(), $skip, $limit);

                    $details = getFinancialOverview($lender, $lender, $office->id);

                    $result = (object)null;
                    $result->total = $count_users;
                    $result->size = $limit;
                    $result->page = $page;
                    $result->data = $results;
                    $result->analytics = $details;

                    return response()->json(compact('result'), 200);
                }

                if ($q == 'admin') {
                    $users = DB::table('users')
                        ->join('user_roles', 'user_roles.user_id', '=', 'users.id')
                        ->join('roles', 'roles.id', '=', 'user_roles.role_id')
                        ->where('users.lender_id', $lender)
                        ->where('users.username', '!=', 'kuwchih')
                        ->where(function ($query) {
                            $query->where('roles.name', '<>', 'user')
                                ->where('roles.name', '<>', 'merchant')
                                ->where('roles.name', '<>', 'u-merchant');
                        })
                        ->select('users.*', 'roles.title AS role_category', 'roles.name AS role_name')
                        ->orderBy('created_at', 'ASC')
                        ->get();

                    return response()->json(compact('users'), 200);
                }

                if ($q == 'customers') {
                    $office_id = $request->has('office') && $request->input('office') != '' ? $request->input('office') : '0';
                    $loan_status = $request->has('status') && $request->input('status') != '' ? $request->input('status') : '';

                    if ($lender == 0) {
                        if ($office_id == 0) {
                            switch ($loan_status) {
                                case 'no-loans':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->whereNull('loan_id')
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->whereNull('loan_id')
                                        ->count();
                                    break;
                                case 'not-verified':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 0)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 0)
                                        ->count();
                                    break;
                                case 'verified':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 1)->where('loan_approved', 0)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 1)->where('loan_approved', 0)
                                        ->count();
                                    break;
                                case 'approved':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_approved', 1)->where('loan_disbursed', 0)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_approved', 1)->where('loan_disbursed', 0)
                                        ->count();
                                    break;
                                case 'disbursed':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_disbursed', 1)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_disbursed', 1)
                                        ->count();
                                    break;
                                case 'admin-created':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('is_admin_created', 1)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('is_admin_created', 1)
                                        ->count();
                                    break;
                                case '':
                                default:
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->count();
                                    break;
                            }
                        } else {
                            switch ($loan_status) {
                                case 'no-loans':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->whereNull('loan_id')->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->whereNull('loan_id')->where('office_id', $office_id)
                                        ->count();
                                    break;
                                case 'not-verified':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 0)->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 0)->where('office_id', $office_id)
                                        ->count();
                                    break;
                                case 'verified':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 1)->where('loan_approved', 0)->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 1)->where('loan_approved', 0)->where('office_id', $office_id)
                                        ->count();
                                    break;
                                case 'approved':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_approved', 1)->where('loan_disbursed', 0)->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_approved', 1)->where('loan_disbursed', 0)->where('office_id', $office_id)
                                        ->count();
                                    break;
                                case 'disbursed':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_disbursed', 1)->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_disbursed', 1)->where('office_id', $office_id)
                                        ->count();
                                    break;
                                case 'admin-created':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('is_admin_created', 1)->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('is_admin_created', 1)->where('office_id', $office_id)
                                        ->count();
                                    break;
                                case '':
                                default:
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('office_id', $office_id)
                                        ->count();
                                    break;
                            }
                        }

                        $all_users = DB::table('customers')->where(function ($query) {
                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                        })->get(['office_id']);

                    } else {
                        if ($office_id == 0) {
                            switch ($loan_status) {
                                case 'no-loans':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->whereNull('loan_id')->where('lender_id', $lender)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->whereNull('loan_id')->where('lender_id', $lender)
                                        ->count();
                                    break;
                                case 'not-verified':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 0)->where('lender_id', $lender)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 0)->where('lender_id', $lender)
                                        ->count();
                                    break;
                                case 'verified':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 1)->where('loan_approved', 0)->where('lender_id', $lender)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 1)->where('loan_approved', 0)->where('lender_id', $lender)
                                        ->count();
                                    break;
                                case 'approved':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_approved', 1)->where('loan_disbursed', 0)->where('lender_id', $lender)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_approved', 1)->where('loan_disbursed', 0)->where('lender_id', $lender)
                                        ->count();
                                    break;
                                case 'disbursed':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_disbursed', 1)->where('lender_id', $lender)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_disbursed', 1)->where('lender_id', $lender)
                                        ->count();
                                    break;
                                case 'admin-created':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('is_admin_created', 1)->where('lender_id', $lender)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('is_admin_created', 1)->where('lender_id', $lender)
                                        ->count();
                                    break;
                                case '':
                                default:
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('lender_id', $lender)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('lender_id', $lender)
                                        ->count();
                                    break;
                            }
                        } else {
                            switch ($loan_status) {
                                case 'no-loans':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->whereNull('loan_id')->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->whereNull('loan_id')->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->count();
                                    break;
                                case 'not-verified':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 0)->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 0)->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->count();
                                    break;
                                case 'verified':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 1)->where('loan_approved', 0)->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_verified', 1)->where('loan_approved', 0)->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->count();
                                    break;
                                case 'approved':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_approved', 1)->where('loan_disbursed', 0)->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_approved', 1)->where('loan_disbursed', 0)->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->count();
                                    break;
                                case 'disbursed':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_disbursed', 1)->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('loan_disbursed', 1)->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->count();
                                    break;
                                case 'admin-created':
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('is_admin_created', 1)->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('is_admin_created', 1)->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->count();
                                    break;
                                case '':
                                default:
                                    $users = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->orderBy('created_at', 'desc')->skip($skip)->take($limit)
                                        ->get();

                                    $count = DB::table('customers')
                                        ->where(function ($query) {
                                            $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                                        })
                                        ->where('lender_id', $lender)->where('office_id', $office_id)
                                        ->count();
                                    break;
                            }
                        }

                        $all_users = DB::table('customers')
                            ->where(function ($query) {
                                $query->where('role_name', 'user')->orWhere('role_name', 'u-merchant');
                            })
                            ->where('lender_id', $lender)
                            ->get(['office_id']);
                    }

                    $users = $users->map(function ($item, $key) {
                        $item->lender = Lender::where('id', $item->lender_id)->first();
                        $item->office = Office::where('id', $item->office_id)->first();

                        $loan = Loan::with('accountno', 'lender')->where('id', $item->loan_id)->first();
                        if ($loan) {
                            $loan->user = User::with('office', 'lender', 'accountno')->where('id', $item->id)->first();
                            $loan->remita_user = RemitaUser::where('id', $loan->remita_user_id)->first();
                        }

                        $item->loan = $loan;
                        $item->loans = Loan::with('user', 'lender', 'remita_user')->where('user_id', $item->id)->withTrashed()->get();

                        $loans = Loan::where('user_id', $item->id)->where('disbursed', 1)->count();
                        $item->requires_core_bank_id = ($loans > 0);

                        return $item;
                    });

                    $office_ids = [];
                    foreach ($all_users as $user) {
                        $office_ids[] = $user->office_id;
                    }
                    $offices = Office::find($office_ids);

                    $result = (object)null;
                    $result->total = $count;
                    $result->size = $limit;
                    $result->page = $page;
                    $result->data = $users;
                    $result->offices = $offices;

                    return response()->json(compact('result'), 200);
                }

                $users = User::where('lender_id', $lender)
                    ->where(function ($query) use ($search) {
                        $query->where('phone', 'like', '%' . $search . '%')
                            ->orWhere('ippis', 'like', '%' . $search . '%')
                            ->orWhere('customer_id', 'like', '%' . $search . '%')
                            ->orWhere('name', 'like', '%' . $search . '%');
                    })
                    ->orderBy('name', 'ASC')->get();

                return response()->json(compact('users'), 200);
            }

            return response()->json(['error' => 'could not fetch users'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not fetch users'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'firstname' => 'required',
            'lastname' => 'required',
            'gender' => 'required',
            'email' => 'required|email|unique:users',
            'phone' => 'required|unique:users',
            'role' => 'required',
            'lender_id' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }
        try {
            $data = $request->all();
            $username = generateUsername($data['firstname'], $data['lastname']);
            $password = generatePassword(6);

            $user = User::create([
                'name' => $data['firstname'] . ' ' . $data['lastname'],
                'username' => $username,
                'password' => bcrypt($password),
                'email' => $data['email'],
                'phone' => $data['phone'],
                'gender' => $data['gender'],
                'enabled' => 1,
                'lender_id' => $data['lender_id'],
            ]);
            $user->roles()->attach(Role::find($data['role']));

            try {
                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $user->name);
                $mail->to_email = $user->email;
                $mail->template = 'email.new_admin';
                $mail->username = $user->username;
                $mail->login = env('ADMIN_URL') . '/signin';
                $mail->subject = 'Account Created!';
                $mail->role_category = $user->role_category;
                $mail->securepwd = $password;

                $sendmail = (new MailHandler())->sendMail($mail);

            } catch (\Exception $e) {
                Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            }

            $notify = doNotify($user->id, 'users', $user->id, 'admin_user_create', 'success', null, $data['lender_id'], $data['user_id']);
            return response()->json(compact('user'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not create user, try again later'], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, $id)
    {
        try {
            $platform = $request->input('platform');

            $user = User::with('loan', 'lender', 'accountno')->find($id);

            if ($user && $platform != '') {
                $lender = Lender::where('id', $user->lender_id)->first();

                if ($lender) {
                    if ($user->role == 'admin' || $user->role == 'super') {
                        if ($user->enabled == 0) {
                            return response()->json(['error' => 'your account has been disabled, please contact support'], 500);
                        }

                        $details = getFinancialOverview($lender->id, $lender->id);

                        $banks = BankCode::whereRaw('id = (SELECT MIN(id) FROM bank_codes t2 WHERE bank_codes.code = t2.code)')->orderBy('name')->get(['id', 'name', 'code']);

                        $lenders = Lender::get(['id', 'name']);

                        $roles = Role::where('name', '<>', 'user')->where('name', '<>', 'super')->where('name', '<>', 'merchant')->where('name', '<>', 'u-merchant')->get();

                        $slips = PayslipUpload::where('lender_id', $lender->id)->orderBy('year', 'desc')->orderBy('month', 'desc')->orderBy('created_at', 'desc')->get();

                        $mails = SupportMail::where('user_id', $user->id)->where('lender_id', $lender->id)->orderBy('created_at', 'desc')->get();
                        $mails = $mails->map(function ($item, $key) {
                            $item->user = User::where('id', $item->user_id)->first(['id', 'name', 'email']);
                            $item->receiver = User::where('id', $item->receiver_id)->first(['id', 'name', 'email']);
                            return $item;
                        });

                        $unread_support = Support::where('lender_id', $lender->id)->where('top_query', 1)->where('status', 0)->count();

                        // all un approved transactions
                        $unapproved_transactions = Transaction::where('approved', '<', 1)->whereNull('description')->count();

                        // all liquidation requests
                        $unliquidated_loans = Loan::where('liquidated', 1)->where('liquidate_approve', 0)->count();

                        // all withdrawal requests
                        $withdrawals = Withdraw::where('approved', 0)->count();

                        // all loan cancel requests
                        $cancel_requests = Loan::where('cancel_request', -1)->count();

                        $wallet_debit_requests = User::where('debit_request', 1)->count();

                        $settings = Setting::where('lender_id', 1)->get();

                        return response()->json(['user' => $user, 'lender' => $lender, 'loan_details' => $details, 'slips' => $slips, 'mails' => $mails, 'banks' => $banks, 'lenders' => $lenders, 'roles' => $roles, 'settings' => $settings, 'unread_support' => $unread_support, 'unapproved_transactions' => $unapproved_transactions, 'unliquidated_loans' => $unliquidated_loans, 'withdrawals' => $withdrawals, 'cancel_requests' => $cancel_requests, 'wallet_debit_requests' => $wallet_debit_requests], 200);
                    } else if ($platform == 'merchant' && ($user->role == 'merchant' || $user->role == 'u-merchant')) {
                        $merchant = Merchant::where('user_id', $user->id)->first();
                        if ($merchant) {
                            $office = Office::find($merchant->office_id);

                            $merchant->user = $user->name;
                            $merchant->office = ($office ? $office->name : null);
                            $merchant->count = MerchantPay::where('merchant_id', $merchant->id)->where('paid', 0)->count();

                            $user->merchant_code = $merchant->merchant_code;
                            $user->merchant = $merchant;

                            $loans = Loan::where('merchant_code', $merchant->merchant_code)->get(['id', 'user_id', 'amount', 'created_at']);
                            $loans = $loans->map(function ($item, $key) {
                                $item->user = User::where('id', $item->user_id)->first(['id', 'name', 'email']);
                                return $item;
                            });

                            $settings = Setting::where('lender_id', 1)->get();

                            return response()->json(['user' => $user, 'loans' => $loans, 'settings' => $settings], 200);
                        }
                    } else if ($platform == 'user' && ($user->role == 'user' || $user->role == 'u-merchant')) {
                        $user = loadUserData($user);

                        $settings = Setting::where('lender_id', 1)->get();

                        return response()->json(['user' => $user, 'settings' => $settings], 200);
                    }
                }
            }

            return response()->json(['error' => 'user not found'], 422);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'network error, try again'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function enable(Request $request, $id)
    {
        try {
            $data = $request->all();

            $user = User::withTrashed()->where('id', $id)->first();
            $user->restore();

            try {
                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $user->name);
                $mail->to_email = $user->email;
                $mail->template = 'email.restore_user';
                $mail->username = $user->username;
                $mail->login = env('ADMIN_URL') . '/signin';
                $mail->subject = 'Account Enabled';
                $mail->role_category = $user->role_category;

                $sendmail = (new MailHandler())->sendMail($mail);

            } catch (\Exception $e) {
                Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            }

            $notify = doNotify($user->id, 'users', $user->id, 'admin_user_restore', 'success', null, $user->lender_id, $data['user_id']);
            return response()->json(compact('user'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not enable user, try again later'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'firstname' => 'required',
            'lastname' => 'required',
            'email' => 'required|email',
            'gender' => 'required',
            'phone' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }
        try {
            $data = $request->all();

            $user = User::withTrashed()->where('id', $id)->first();
            if ($user->trashed()) {
                $user->restore();
            }

            if ($user) {
                $user->name = $data['firstname'] . ' ' . $data['lastname'];
                $user->email = $data['email'];
                $user->phone = $data['phone'];
                $user->gender = $data['gender'];
                $user->save();

                if (isset($data['role']) && $data['role'] != null && $data['role'] != '') {
                    $role = DB::table('user_roles')->where('user_id', $user->id)->update(['role_id' => $data['role']]);
                }

                $notify = doNotify($user->id, 'users', $user->id, 'admin_user_update', 'success', null, $user->lender_id, $data['user_id']);
                return response()->json(compact('user'), 200);
            }

            return response()->json(['error' => 'could not find user, try again later'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not save user, try again later'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function updateProfile(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'lga' => 'required',
            'state' => 'required',
            'gender' => 'required',
            'address1' => 'required',
            'nok_firstname' => 'required',
            'nok_lastname' => 'required',
            'nok_relationship' => 'required',
            'nok_phone' => 'required',
            'nok_address1' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        try {
            $data = $request->all();

            $user = User::where('id', $id)->first();

            if ($user) {
                if ($data['email'] != $user->email) {
                    $email_exists = User::where('email', $data['email'])->first();
                    if ($email_exists) {
                        return response()->json(['error' => 'email is already been used'], 500);
                    }

                    $user->email = $data['email'];
                }

                if (isset($data['bvn']) && $data['bvn'] != $user->bvn && ($user->bvn == '' || $user->bvn == null)) {
                    $bvn_exists = User::where('bvn', $data['bvn'])->first();
                    if ($bvn_exists) {
                        return response()->json(['error' => 'bvn is already been used by ' . $bvn_exists->name], 500);
                    }

                    if (strlen($data['bvn']) == 11 && is_numeric($data['bvn'])) {
                        $user->bvn = $data['bvn'];

                        $accounts = Accountno::where('user_id', $user->id)->get();
                        foreach ($accounts as $account) {
                            $item = Accountno::where('id', $account->id)->first();
                            if ($item) {
                                $item->bvn = $user->bvn;
                                $item->save();
                            }
                        }
                    } else {
                        return response()->json(['error' => 'bvn should be a number and 11 digits'], 500);
                    }
                }

                if (isset($data['employer'])) {
                    $user->employer = $data['employer'];
                }

                $user->gender = $data['gender'];
                $user->state_of_origin = $data['state'];
                $user->lga_of_origin = $data['lga'];
                $user->address1 = $data['address1'];
                $user->nok_firstname = $data['nok_firstname'];
                $user->nok_lastname = $data['nok_lastname'];
                $user->nok_relationship = $data['nok_relationship'];
                $user->nok_phone = $data['nok_phone'];
                $user->nok_address1 = $data['nok_address1'];
                $user->save();

                $notify = doNotify($user->id, 'users', $user->id, 'user_updated', 'success', null, $user->lender_id);

                return response()->json(compact('user'), 200);
            }

            return response()->json(['error' => 'could not find user'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not save user, try again later'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function updateCustomer(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'ippis' => 'required',
            'bvn' => 'required',
            'phone' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }
        try {
            $data = $request->all();

            $user = User::where('id', $id)->first();

            $bvn = $user->bvn;
            $email = $user->email;

            if ($user) {
                if ($data['email'] != $user->email) {
                    $email_exists = User::where('email', $data['email'])->first();
                    if ($email_exists) {
                        return response()->json(['error' => 'email already exists for another customer'], 500);
                    }
                }

                if ($data['bvn'] != $user->bvn) {
                    $bvn_exists = User::where('bvn', $data['bvn'])->first();
                    if ($bvn_exists) {
                        return response()->json(['error' => 'bvn already exists for another customer named ' . $bvn_exists->name], 500);
                    }
                }

                if ($data['phone'] != $user->phone) {
                    $phone_exists = User::where('phone', $data['phone'])->first();
                    if ($phone_exists) {
                        return response()->json(['error' => 'phone number already exists for another customer named ' . $phone_exists->name], 500);
                    }
                }

                $payslip = Payslip::where('id', $user->payslip_id)->first();

                if ($user->employer == $data['employer']) {
                    $user_office = Office::where('id', $user->office_id)->first();

                    if ($user_office) {
                        $office = $user_office;
                    } else {
                        $office = getOffice($user, $payslip, $data);
                    }
                } else {
                    $office = getOffice($user, $payslip, $data);
                }

                $user->ippis = $data['ippis'];
                $user->bvn = $data['bvn'];
                $user->employer = $data['employer'];
                $user->phone = $data['phone'];
                $user->email = $data['email'];
                $user->office_id = ($office ? $office->id : null);
                $user->save();

                if ($bvn != $data['bvn'] || $email != $data['email']) {
                    $loan = Loan::where('id', $user->loan_id)->first();
                    if ($loan) {
                        $loan->oletter = null;
                        $loan->save();
                    }
                }

                $bank_accounts = Accountno::where('user_id', $user->id)->get();
                foreach ($bank_accounts as $account) {
                    $bank_account = Accountno::where('id', $account->id)->first();
                    if ($bank_account) {
                        $bank_account->bvn = $data['bvn'];
                        $bank_account->save();
                    }
                }

                $notify = doNotify($user->id, 'users', $user->id, 'admin_customer_update', 'success', null, $user->lender_id, $data['user_id']);
                return response()->json(compact('user'), 200);
            }

            return response()->json(['error' => 'could not find user, try again later'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not save user, try again later'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $data = $request->all();
            $user = User::find($id);

            $notify = doNotify($user->id, 'users', $user->id, 'admin_user_delete', 'success', null, $user->lender_id, $data['user_id']);

            $user->delete();

            return response()->json(['data' => $id], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'network error, try again'], 500);
        }
    }

    public function search(Request $request)
    {
        try {
            $q = '';
            if ($request->has('q') && $request->input('q') != '') {
                $q = $request->input('q');
            }

            $users = [];

            $lender = $request->has('lender') ? $request->input('lender') : '';
            if ($lender != '') {
                if ($lender == env('SUPER_LENDER')) {
                    $users = User::where(function ($query) use ($q) {
                        $query->where('name', 'like', '%' . $q . '%')->orWhere('phone', 'like', '%' . $q . '%')->orWhere('ippis', 'like', '%' . $q . '%')->orWhere('customer_id', 'like', '%' . $q . '%')->orWhere('email', 'like', '%' . $q . '%');
                    })->get(['id', 'name', 'phone', 'office_id']);
                } else {
                    $users = User::where('lender_id', $lender)
                        ->where(function ($query) use ($q) {
                            $query->where('name', 'like', '%' . $q . '%')->orWhere('phone', 'like', '%' . $q . '%')->orWhere('ippis', 'like', '%' . $q . '%')->orWhere('customer_id', 'like', '%' . $q . '%');
                        })->get(['id', 'name', 'phone', 'office_id']);
                }

                $users = $users->map(function ($item, $key) {
                    $office = Office::where('id', $item->office_id)->first();
                    $item->office_name = $office ? $office->name : '';
                    return $item;
                })->filter(function ($item, $key) {
                    return $item->role == 'user' || $item->role == 'u-merchant';
                })->flatten()->all();
            }

            return response()->json(compact('users'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            $users = [];
            return response()->json(compact('users'), 500);
        }
    }

    public function profile($id)
    {
        try {
            $user = User::with('accountno')->where('id', $id)->first();

            $user->office = Office::find($user->office_id);

            $loan = Loan::where('id', $user->loan_id)->first();
            if ($loan) {
                $loan->remita_user = RemitaUser::where('id', $loan->remita_user_id)->first();
            }

            $user->loan = $loan;

            $loans = Loan::with('user', 'lender', 'remita_user')->where('user_id', $id)->withTrashed()->orderBy('created_at', 'desc')->get();

            $loans = $loans->map(function ($item, $key) {
                $item->transactions = Transaction::with('loan:id,is_topup,liquidate_approve')->where('loan_id', $item->id)->where('approved', 1)->orderByRaw('description = "Loan Taken" desc')->orderBy('created_at')->get();

                return $item;
            });

            $user->loans = $loans;

            $user->payslip = Payslip::where('id', $user->payslip)->first(['id', 'designation', 'first_appointment', 'retire_expected_at']);

            $user->account = Accountno::where('user_id', $id)->first();
            $user->lender = Lender::where('id', $user->lender_id)->first(['id', 'name']);
            $user->earnings = $user->platform == 'ippis' ?
                DB::table('earnings')
                    ->select(DB::raw('DISTINCT ippis, month, year, net_earning, platform, gross_earning, auth_code, created_at'))
                    ->where('ippis', $user->ippis)->where('platform', $user->platform)->where('net_earning', '!=', '0.00')
                    ->orderBy('year', 'desc')->orderBy('month', 'desc')
                    ->take(12)->get()
                :
                DB::table('earnings')
                    ->select(DB::raw('DISTINCT customer_id, month, year, net_earning, platform, gross_earning, auth_code, created_at'))
                    ->where('customer_id', $user->customer_id)->where('platform', $user->platform)->where('net_earning', '!=', '0.00')
                    ->orderBy('year', 'desc')->orderBy('month', 'desc')
                    ->take(12)->get();

            $user->wallet = Wallet::where('user_id', $user->id)->where('approved', 1)->where('is_lender', 0)->where('paid', 1)->sum('amount');

            return response()->json(compact('user'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            $user = null;
            return response()->json(compact('user'), 500);
        }
    }

    public function transactions($id)
    {
        try {
            // group transactions by loan
            $loans = Loan::where('user_id', $id)->orderBy('created_at', 'desc')->get(['id']);

            $transactions = [];
            foreach ($loans as $loan) {
                $loanTransactions = Transaction::with('user')->where('loan_id', $loan->id)->orderByRaw('description = "Loan Taken" desc')->orderBy('created_at', 'asc')->get();
                $loanTransactions = $loanTransactions->map(function ($item, $key) {
                    $item->loan = Loan::where('id', $item->loan_id)->first();

                    return $item;
                });

                $transaction = (object)null;
                $transaction->loan = Loan::with('lender')->where('id', $loan->id)->first();
                $transaction->transactions = $loanTransactions;

                $transactions[] = $transaction;
            }

            $wallet = Wallet::where('user_id', $id)->where('approved', 1)->where('is_lender', 0)->where('paid', 1)->sum('amount');

            return response()->json(['transactions' => $transactions, 'wallet' => $wallet], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not load transactions'], 500);
        }
    }

    public function changeLender(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $input = $request->all();

            $new_lender = Lender::where('id', $input['lender_id'])->first(['id', 'name']);
            $user = User::where('id', $id)->first();

            if ($new_lender && $user && ($new_lender->id == $user->lender_id)) {
                return response()->json(compact('user'), 200);
            }

            $office = Office::where('id', $user->office_id)->first();

            $new_office = Office::where('name', $office->name)->where('lender_id', $new_lender->id)->first();
            if (!$new_office) {
                $new_office = Office::create([
                    'name' => $office->name,
                    'lender_id' => $new_lender->id,
                ]);
            }

            $user->lender_id = $new_lender->id;
            $user->office_id = $new_office->id;
            $user->save();

            $loan = Loan::where('id', $user->loan_id)->first();
            if ($loan) {
                $loan->lender_id = $new_lender->id;
                $loan->office_id = $new_office->id;
                $loan->save();

                $upd_notify = Notify::where('category', 'loans')->where('action_id', 1)->where('user_id', $user->id)->where('category_id', $loan->id)->update(['lender_id' => $new_lender->id]);
            }

            $notify = doNotify($user->id, 'users', $user->id, 'change_lender', 'success', null, $user->lender_id, $input['staff_id']);

            DB::commit();
            return response()->json(compact('user'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not change lender platform for user'], 500);
        }
    }

    /**
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function changePassword(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'old_password' => 'required',
            'password' => 'required|confirmed',
            'password_confirmation' => 'required',
        ], [
            'password.confirmed' => 'your passwords does not match'
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 422);
        }

        try {
            $input = $request->all();
            $user = User::where('id', $id)->first();

            if (!Hash::check($input['old_password'], $user->password)) {
                return response()->json(['error' => 'your old password is incorrect'], 422);
            }

            if ($user) {
                $user->password = bcrypt($input['password_confirmation']);
                $user->save();

                return response()->json(compact('user'), 200);
            }

            return response()->json(['error' => 'user not found'], 422);

        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not change password'], 422);
        }
    }

    /**
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function setCoreBank(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'core_bank_id' => 'required',
        ], [
            'core_bank_id.required' => 'enter the core bank id'
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 422);
        }

        try {
            $input = $request->all();
            $loans = Loan::where('user_id', $id)->where('approved', 1)->count();

            if ($loans == 0) {
                return response()->json(['error' => 'user has not taken a loan yet'], 422);
            }

            $user = User::where('id', $id)->first();
            if ($user) {
                $user->core_bank_id = $input['core_bank_id'];
                $user->save();

                return response()->json(compact('user'), 200);
            }

            return response()->json(['error' => 'user not found'], 422);

        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not update user'], 422);
        }
    }

    /**
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function enableCustomer(Request $request, $id)
    {
        try {
            $input = $request->all();

            $user = User::where('id', $id)->first();
            $user->enabled = 1;
            $user->save();

            $notify = doNotify($user->id, 'users', $user->id, 'enable_user', 'success', null, $user->lender_id, $input['staff']);

            return response()->json(['result' => 1], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not enable customer'], 422);
        }
    }

    /**
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function disableCustomer(Request $request, $id)
    {
        try {
            $input = $request->all();

            $user = User::where('id', $id)->first();
            $user->enabled = 0;
            $user->save();

            $notify = doNotify($user->id, 'users', $user->id, 'disable_user', 'success', null, $user->lender_id, $input['staff']);

            return response()->json(['result' => 1], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not enable customer'], 422);
        }
    }

    /**
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function snooze(Request $request, $id)
    {
        try {
            $input = $request->all();

            $user = User::where('id', $id)->first();
            $user->is_snoozed = 1;
            $user->snoozed_at = date('Y-m-d H:i:s');
            $user->save();

            $notify = doNotify($user->id, 'users', $user->id, 'snooze_user', 'success', null, $user->lender_id, $input['staff_id']);

            return response()->json(compact('user'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not snooze customer'], 422);
        }
    }

    /**
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function unsnooze(Request $request, $id)
    {
        try {
            $input = $request->all();

            $user = User::where('id', $id)->first();
            $user->is_snoozed = 0;
            $user->snoozed_at = null;
            $user->save();

            $notify = doNotify($user->id, 'users', $user->id, 'un_snooze_user', 'success', null, $user->lender_id, $input['staff_id']);

            return response()->json(compact('user'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not remove snooze from customer'], 422);
        }
    }

    /**
     * @return array
     */
    public function checkSnoozed()
    {
        DB::beginTransaction();
        try {
            $users = [];
            $settings = Setting::where('name', 'snooze_period')->first();
            $snoozed = User::where('is_snoozed', 1)->get();
            foreach ($snoozed as $u) {
                $user = User::where('id', $u->id)->first();
                $passed = Carbon::parse($user->snoozed_at)->addDays($settings->value)->diffInDays(Carbon::now());
                if ($passed <= 0) {
                    $user->is_snoozed = 0;
                    $user->snoozed_at = null;
                    $user->save();

                    $users[] = $user->id;

                    $notify = doNotify($user->id, 'users', $user->id, 'un_snooze_user', 'success', null, $user->lender_id, null);
                }
            }

            DB::commit();

            return ['unsnoozed' => $users];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return null;
        }
    }

    /**
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateCustomerId(Request $request, int $id)
    {
        DB::beginTransaction();
        try {
            $user = User::where('id', $id)->first();
            if($user && $user->customer_id){
                $user->customer_id = $request->customer_id;
                $user->save();

                $remita_user = RemitaUser::where('user_id', $user->id)->first();
                if($remita_user){
                    $remita_data = json_decode($remita_user->data, true);
                    $remita_data['data']['customerId'] = $request->customer_id;

                    $remita_user->data = json_encode($remita_data);
                    $remita_user->save();
                }

                $_ = Earning::where('customer_id', $request->old_customer_id)->update(['customer_id' => $request->customer_id]);

                DB::commit();

                return response()->json(compact('user'), 200);
            }

            return response()->json(['error' => 'customer id not found'], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not save customer id'], 422);
        }
    }
}
