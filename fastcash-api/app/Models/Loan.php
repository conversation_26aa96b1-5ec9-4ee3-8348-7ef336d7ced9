<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Loan extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    public function user()
    {
        return $this->belongsTo('App\Models\User');
    }

    public function office()
    {
        return $this->belongsTo('App\Models\Office');
    }

    public function accountno()
    {
        return $this->belongsTo('App\Models\Accountno');
    }

    public function lender()
    {
        return $this->belongsTo('App\Models\Lender');
    }

    public function remita_user()
    {
        return $this->belongsTo('App\Models\RemitaUser');
    }

    public function transaction()
    {
        return $this->hasOne('App\Models\Transaction');
    }
}
