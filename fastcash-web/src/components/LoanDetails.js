import React from 'react';
import numeral from 'numeral';

const LoanDetails = ({ netEarning, amount, myMonthlyDeductions, monthlyDeductions, eligibleAmt, tenure, platform, topup, loan, myDisburseAmount }) => {
    return (
        <div className="element-wrapper">
            {platform !== 'sme' && (
                <div className="element-content">
                    <div className="row">
                        <div className="col-sm-6">
                            <div className="element-box el-tablo">
                                <div className="label">NET EARNINGS</div>
                                <div className="value">{`₦${numeral(netEarning).format('0,0.00')}`}</div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            {topup && <h6 className="element-header">Current Loan</h6>}
            {topup && (
                <div className="element-content">
                    <div className="row">
                        <div className="col-sm-6">
                            <div className="element-box el-tablo">
                                <div className="label">Outstanding Loan Amount ({loan.tenure - loan.total_paid} Months)</div>
                                <div className="value">{`₦${numeral(loan.balance_amount).format('0,0.00')}`}</div>
                            </div>
                        </div>
                        <div className="col-sm-6">
                            <div className="element-box el-tablo">
                                <div className="label">Tenure Left</div>
                                <div className="value">{`${loan.tenure - loan.total_paid} Months`}</div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            <h6 className="element-header">{topup ? 'Topup Loan' : 'New Loan Application'}</h6>
            <div className="element-content">
                <div className="row">
                    <div className="col-sm-6">
                        <div className="element-box el-tablo">
                            <div className="label">Eligible Amount ({tenure} Months)</div>
                            <div className="value">{`₦${numeral(eligibleAmt).format('0,0.00')}`}</div>
                        </div>
                    </div>
                    <div className="col-sm-6">
                        {platform !== 'sme' && (
                            <div className="element-box el-tablo">
                                <div className="label">Eligible Deduction Per Month ({tenure} Months)</div>
                                <div className="value">{`₦${numeral(monthlyDeductions).format('0,0.00')}`}</div>
                            </div>
                        )}
                    </div>
                    <div className="col-sm-6">
                        <div className="element-box el-tablo">
                            <div className="label">Desired Loan Amount</div>
                            <div className="value">{`₦${numeral(amount).format('0,0.00')}`}</div>
                        </div>
                    </div>
                    <div className="col-sm-6">
                        <div className="element-box el-tablo">
                            <div className="label">Desired Deduction Per Month</div>
                            <div className="value">{`₦${numeral(myMonthlyDeductions).format('0,0.00')}`}</div>
                        </div>
                    </div>
                </div>
            </div>
            {topup && (
                <div className="element-content">
                    <div className="row">
                        <div className="col-sm-6">
                            <div className="element-box el-tablo">
                                <div className="label">Disburse Amount ({tenure} Months)</div>
                                <div className="value">{`₦${numeral(myDisburseAmount).format('0,0.00')}`}</div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default LoanDetails;