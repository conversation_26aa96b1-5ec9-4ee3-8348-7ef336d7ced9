<?php

namespace Database\Seeders;

use App\Models\RemitaTransaction;
use App\Models\Transaction;
use Illuminate\Database\Seeder;

class UpdRemitaBankRef extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $transactions = Transaction::whereNotNull('remita_transaction_id')->where('approved', '!=', 0)->get();
        foreach ($transactions as $transaction) {
            $remita = RemitaTransaction::where('id', $transaction->remita_transaction_id)->whereNull('bank_repayment_ref')->first();
            if($remita){
                $remita->bank_repayment_ref = $transaction->repayment_reference;
                $remita->save();
            }
        }
    }
}
