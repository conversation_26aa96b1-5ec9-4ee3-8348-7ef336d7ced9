import React, { Component } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';
import { Tab, Ta<PERSON>, <PERSON>b<PERSON>ist, TabPanel } from 'react-tabs';
import qs from 'querystring';

import { toggleMenu } from '../actions/user';
import { loadSchedules, unLoadSchedules, setSchedules } from '../actions/schedule';
import TabContent from '../components/TabContent';
import BreadCrumbs from '../components/BreadCrumbs';
import { startBlock, stopBlock } from '../actions/ui-block';

class ViewSchedule extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
            key: moment().format('M') - 1,
            month: moment().format('MMMM'),
            year: '',
            page: '',
            officeName: '',
        };
	}
	
	componentWillMount() {
		this.props.toggleMenu(false);
	}
	
    componentDidMount() {
		const { location, match, lender } = this.props;
		const query = qs.parse(location.search.replace('?', ''));
        const year = query && query.z ? query.z : moment().format('YYYY');
        this.setState({ year });
        this.props.startBlock();
        this.props.loadSchedules(match.params.id, year, lender.id).then((response) => {
                this.props.setSchedules(response.schedules);
                this.props.stopBlock();
            })
            .catch(error => {
                this.props.stopBlock();
                console.log(error);
            });
        
		if(query && query.p && query.office) {
			this.setState({ page: query.p, officeName: query.office });
		}
    }

    componentWillUnmount() {
        this.props.unLoadSchedules();
        this.props.toggleMenu(true);
    }

    handleSelect = k => {
        this.setState({ month: moment().month(k).format('MMMM') });
	};
	
	switchYear = (e) => {
		const { location, match, lender } = this.props;
		const { officeName } = this.state;
		this.props.unLoadSchedules();
		const year = e.target.value;
        this.setState({ year });
        this.props.startBlock();
		this.props.loadSchedules(match.params.id, year, lender.id).then((response) => {
                this.props.setSchedules(response.schedules);
                this.props.stopBlock();
                this.props.history.push(`${location.pathname}?z=${year}&office=${officeName}`);
            })
            .catch(error => {
                this.props.stopBlock();
                console.log(error);
            });
	}

    render() {
        const { schedules } = this.props;
        const { month, year, key, page, officeName } = this.state;

        return (
            <div>
                <BreadCrumbs url={`/offices?p=${page}`} />
                <div className="content-i">
                    <div className="content-box">
                        <div className="element-wrapper">
                            <h6 className="element-header">{`Schedule for ${officeName}`}</h6>
							<div className="row">
                                <div className="col-sm-12">
                                    <div className="element-wrapper">
                                        <div className="element-box" style={{marginBottom: '0',display: 'flex',justifyContent: 'space-between',alignItems: 'center',}}>
                                            <div>
                                                <div className="highlight bigger">
                                                    <div className="lead">{`${month} ${year}`}</div>
                                                </div>
                                                <div className="label">{officeName}</div>
                                            </div>
                                            <div>
                                                <div className="lead" style={{display: 'flex',whiteSpace: 'nowrap',alignItems: 'center',}}>
                                                    <span style={{marginRight: '12px', fontSize: '1rem',}}>Select Year</span>
                                                    <select className="form-control" onChange={this.switchYear} defaultValue={year}>
                                                        <option value="2020">2020</option>
                                                        <option value="2019">2019</option>
                                                        <option value="2018">2018</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="row">
                                <div className="col-sm-12">
                                    <div className="element-box">
                                        <Tabs defaultIndex={JSON.parse(key)} onSelect={this.handleSelect} className="os-tabs-w" selectedTabClassName="active" selectedTabPanelClassName="active">
                                            <div className="os-tabs-controls">
                                                <TabList className="nav nav-tabs smaller">
                                                    {[ ...Array(12).keys() ].map(m => {
                                                        return (
                                                            <Tab key={m} className="nav-item cursor">
                                                                <span className="nav-link">{moment().month(m).format('MMMM')}</span>
                                                            </Tab>
                                                        )
                                                    })}
                                                </TabList>
                                            </div>
                                            <div className="tab-content">
                                                {[ ...Array(12).keys() ].map(m => {
                                                    return (
                                                        <TabPanel className="tab-pane" key={m}>
                                                            <TabContent 
                                                                schedules={schedules} 
                                                                month={parseInt(m, 10) +1}
                                                                year={year}
                                                            />
                                                        </TabPanel>
                                                    )
                                                })}
                                            </div>
                                        </Tabs>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

const mapStateToProps = (state, ownProps) => {
    return {
        schedules: state.schedules,
        lender: state.lender.profile,
    }
};

export default connect(mapStateToProps, { toggleMenu, loadSchedules, unLoadSchedules, setSchedules, startBlock, stopBlock })(ViewSchedule);