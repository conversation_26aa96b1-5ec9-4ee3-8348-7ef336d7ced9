/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component, Fragment } from 'react';
import moment from 'moment';
import numeral from 'numeral';

import company1 from '../assets/img/company1.png';
import { currency } from '../config/constants';

class HistoryItem extends Component {
	state = {
		collapsed: true,
	};

	toggle = () => {
		this.setState({ collapsed: !this.state.collapsed });
	}

	render() {
		const { loan, current_loan, declined, liquidated } = this.props;
		const { collapsed } = this.state;
		const sign = '+';
		const transactions = loan.transactions;
		const userCancelled = loan.cancel_request === 1;
		return (
			<Fragment>
				<tr>
					<td>{!declined && !userCancelled && <div role="button" tabIndex="0" className={`row-expand-icon ${collapsed ? 'row-collapsed' : 'row-expanded'}`} onClick={this.toggle}/>}</td>
					<td className="cell-with-media"><img alt="" src={company1} style={{height: '25px'}}/><span>{`${current_loan && current_loan.id === loan.id ? (loan.is_topup === 1 ? 'Current Topup-Loan' : 'Current Loan') : (loan.is_topup === 1 ? 'Topup-Loan' : 'Loan')} Request`}</span> {userCancelled && <span className="badge badge-danger text-white ml-2">User cancelled</span>}</td>
					<td className="text-center"><a className="badge badge-success text-white">{loan.platform}</a></td>
					<td className="text-center bolder nowrap"><span className="text-success">{`${sign} ₦${numeral(loan.amount).format('0,0.00')}`}</span></td>
					<td>{`${loan.tenure} Months`}</td>
					<td><span>{moment(loan.created_at).format('DD.MMM.YYYY')}</span></td>
					<td className="text-center"><a className={`badge badge-${!declined  && !userCancelled ? (liquidated ? 'info' : 'success') : 'danger'} text-white`}>{declined || userCancelled ? 'declined' : (liquidated ? 'ended' : 'active')}</a></td>
				</tr>
				{!collapsed && (
					<tr>
						<td/>
						<td colSpan="6">
							<table className="table table-striped">
								<thead>
								<tr>
									<th>Description</th>
									<th>Amount Paid</th>
									<th>Outst Amount</th>
									<th>Date</th>
									<th className="text-center">Status</th>
								</tr>
								</thead>
								<tbody>
									{transactions.filter(t => t.approved === 1).map((d, i) => {
										const amount = d.description === 'Loan Taken' ? parseFloat(d.amount) - parseFloat(d.interest) : d.amount;
										return (
											<tr key={i}>
												<td>{`${d.loan.is_topup === 1 ? 'Topup' : ''} ${(d.description || 'Pending Deduction').replace('REMITA ', '')}`}</td>
												<td>{currency(amount)}</td>
												<td>{currency(d.outst_amount)}</td>
												<td nowrap="nowrap">{moment(d.created_at).format('DD-MMM-YYYY')}</td>
												<td className="text-center"><div className={`badge badge-${(d.loan.liquidate_approve === 0 && d.description === 'Loan Liquidated') || d.approved === 0 ? 'warning' : (d.status === 'Completed' ? 'success' : 'warning')} text-white`}>
												{(d.loan.liquidate_approve === 0 && d.description === 'Loan Liquidated') || d.approved === 0 ? 'Pending' : d.status}
												</div></td>
											</tr>
										)
									})}
									{transactions.length === 0 && (
										<tr><td colSpan="5">No transactions</td></tr>
									)}
								</tbody>
							</table>
						</td>
					</tr>
				)}
			</Fragment>
		);
	}
}

export default HistoryItem;
