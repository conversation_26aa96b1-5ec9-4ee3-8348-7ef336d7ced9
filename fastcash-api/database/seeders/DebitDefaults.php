<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\PaymentDate;
use App\Models\Wallet;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DebitDefaults extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            DB::beginTransaction();

            // find default loans
            $default_loans = DB::table('payment_dates')
                ->join('loans', 'loans.id', '=', 'payment_dates.loan_id')
                ->where('payment_dates.paid', 0)->where('payment_dates.payment_date', '<', Carbon::now()->format('Y-m-d H:i:s'))->whereNull('loans.deleted_at')->where('loans.disbursed', 1)
                ->select('payment_dates.loan_id')
                ->groupBy('payment_dates.loan_id')
                ->get();

            $debits = [];
            // find number of defaults per loan
            foreach ($default_loans as $item) {
                $defaults = PaymentDate::with('loan')->where('loan_id', $item->loan_id)->where('paid', 0)->where('payment_date', '<', Carbon::now()->format('Y-m-d H:i:s'))->get();

                $rs = (object)null;
                $rs->count = count($defaults);
                $rs->loan_id = $item->loan_id;
                $rs->pid = $defaults;

                $debits[] = $rs;
            }

            info(json_encode($debits));

            $repayments = [];
            // apply debit for number of defaults
            foreach ($debits as $item) {
                $loan = Loan::where('id', $item->loan_id)->first();

                for ($i=0; $i < $item->count; $i++) {
                    $wallet = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'debit',
                        'amount' => $loan->monthly_deduction * -1,
                        'approved' => 1,
                        'approved_by' => null,
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'paid' => 0,
                        'payment_date' => $item->pid[$i]->payment_date,
                    ]);

                    $repayments[] = $wallet->id;
                }
            }

            DB::commit();

            info(json_encode(['repayments' => $repayments]));
        } catch (\Exception $e){
            DB::rollBack();
            Log::error($e->getMessage());
        }
    }
}
