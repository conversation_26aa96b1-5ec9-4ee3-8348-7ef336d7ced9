/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { <PERSON>, withRouter } from 'react-router-dom';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';

import { baseURL, httpRequest, forgotPasswordAPI } from '../config/constants';
import loading from '../assets/img/loading.gif';
import logoBig from '../assets/img/fc-logo.png';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {}
    if (!values.email) {
        errors.email = 'Enter your email';
    }
    return errors;
};

const renderField = ({input, id, icon, label, hideclass, type, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')} ${hideclass}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" />
        <div className={`pre-icon os-icon ${icon}`}></div>
        {touched &&
            ((error && <span className="help-block">{error}</span>) ||
                (warning && <span className="help-block">{warning}</span>))}
    </div>
);

class ForgotPassword extends Component {
	constructor(props, context) {
		super(props, context);
		this.recoverPassword = this.recoverPassword.bind(this);
	}

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	recoverPassword = async data => {
		try {
            const details = {
                ...data,
                role: 'admin',
                platform: 'admin',
                source: 'admin',
            };
            await httpRequest(`${baseURL}${forgotPasswordAPI}`, 'POST', false, details);
            this.notify('', 'Check your email to reset your password!', 'success');
            this.props.reset('forgot-password');
        }
        catch (error) {
            const message = error.message || 'error, please try again';
            this.notify('', message, 'error');
            throw new SubmissionError({
                _error: message,
            });
        }
	};

	componentDidMount() {
		document.body.className = 'auth-wrapper pages';
    }
    
    componentWillUnmount() {
        document.body.className = 'pages';
    }

    render() {
        const { handleSubmit, error, submitting, pristine } = this.props;
        return (
            <div className="all-wrapper menu-side with-pattern">
                <div className="auth-box-wh">
                    <div className="logo-w" style={{padding: '10%'}}>
                        <Link to="/"><img alt="logo" src={logoBig} style={{width: '50%'}}/></Link>
                    </div>
                    <h4 className="auth-header">Reset Password</h4>
                    {error && <div className="alert alert-danger" dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                    <form onSubmit={handleSubmit(this.recoverPassword)} autoComplete="off">
                        <Field
                            name="email"
                            id="email"
                            type="email"
                            component={renderField}
                            label="Email address"
                            icon="os-icon-user-male-circle"
                        />
                        <div>
                            <Link className="btn btn-link" to="/">Remembered password? Sign in</Link>
                        </div>
                        <div className="buttons-w">
                            <button className="btn btn-primary" type="submit" disabled={pristine || submitting}>
                                {submitting? <img src={loading} alt=""/> : 'Send Recovery Mail'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    }
}

ForgotPassword = reduxForm({
    form: 'forgot-password',
    validate
})(ForgotPassword);

export default withRouter(connect(null, { reset, doNotify })(ForgotPassword));