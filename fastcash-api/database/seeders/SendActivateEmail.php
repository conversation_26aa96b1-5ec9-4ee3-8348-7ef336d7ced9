<?php

namespace Database\Seeders;

use App\Models\User;
use App\Services\MailHandler;
use Illuminate\Database\Seeder;

class SendActivateEmail extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $users = User::where('verified', 0)->get();

            foreach ($users as $user) {
                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $user->name);
                $mail->to_email = $user->email;
                $mail->login = env('MAIN_URL');
                $mail->platform = $user->platform;

                if ($user->verified == 0) {
                    $mail->template = 'email.verify_account';
                    $mail->subject = 'Verify Your Email';
                    $mail->activation_url = env('MAIN_URL') . '/activate/' . $user->email_token;
                } else {
                    $mail->template = 'email.new_customer';
                    $mail->subject = 'Welcome to FastCash';
                    $mail->ippis = $user->ippis ?? '';
                    $mail->phone = $user->phone;
                }

                $send = (new MailHandler())->sendMail($mail);
            }
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
        }
    }
}
