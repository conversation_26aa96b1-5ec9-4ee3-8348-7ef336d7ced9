import React, { Component } from 'react';
import { connect } from 'react-redux';
import { reduxForm } from 'redux-form';

import loading from '../assets/img/loading.gif';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {}
    if (!values.amount) {
        errors.amount = 'Enter the amount';
    }
    
    return errors;
};

class EditCommission extends Component {
    constructor(props, context) {
        super(props, context);
        
        this.state = {
            submitting: false,
            editAmount: props.amount
        };
    }
    
    async componentDidMount() {
        
    }
    
    editCommission = () => {
        
        this.setState({ submitting: true })
        setTimeout(() => {
            this.setState({ submitting: false })            
        }, 4000);
    }

    onChangeAmount = (e) => {
        console.log(e.target.value);
        this.setState({ editAmount: e.target.value })
    }
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

    render() {
        const { closeEditModal, handleSubmit } = this.props;
        const { submitting, editAmount } = this.state;
        
        return (
            <div>
                <div className="modal-header faded smaller">
                    <div className="modal-title">
                        <span>Agent Commission</span>
                    </div>
                    <button aria-label="Close" className="close" onClick={closeEditModal} type="button"><span aria-hidden="true"> ×</span></button>
                </div>
                <form onSubmit={handleSubmit(this.editCommission)}>
                    <div className="modal-body">
                        
                        <div className="row">                            
                            <div className="col-12">
                                <div className='form-group'>
                                    <label>Commission Amount</label>
                                    <input  type='number' value={editAmount} onChange={this.onChangeAmount} placeholder="Enter Amount" className="form-control" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="modal-footer buttons-on-left">
                        <button className="btn btn-teal" disabled={submitting} type="submit">
                            {submitting? <img src={loading} alt=""/> : 'Update'}
                        </button>
                        <button className="btn btn-link cursor" onClick={closeEditModal} type="button"> Cancel</button>
                    </div>
                </form>
            </div>
        );
    }
}

EditCommission = reduxForm({
    form: 'editcommission',
    validate,
})(EditCommission);

export default connect(null, { doNotify })(EditCommission);
