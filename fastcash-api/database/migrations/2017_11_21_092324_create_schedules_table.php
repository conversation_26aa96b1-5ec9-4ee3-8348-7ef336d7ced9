<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSchedulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('schedules', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('loan_id')->unsigned();
            $table->string('ippis_number')->index('ippis_number');
            $table->string('name');
            $table->bigInteger('office_id')->unsigned()->nullable();
            $table->string('account_number');
            $table->decimal('loan_amount', 50, 2);
            $table->decimal('outst_loan_amt', 50,2);
            $table->decimal('interest', 50, 2);
            $table->decimal('monthly_dedux', 50, 2);
            $table->decimal('default_amount', 50, 2)->default(0);
            $table->integer('tenure');
            $table->string('start_date');
            $table->string('end_date');
            $table->tinyInteger('month');
            $table->string('month_name');
            $table->string('year');
            $table->tinyInteger('type')->default(2);
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('loan_id')->references('id')->on('loans')->onDelete('cascade');
            $table->foreign('office_id')->references('id')->on('offices')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('schedules');
    }
}
