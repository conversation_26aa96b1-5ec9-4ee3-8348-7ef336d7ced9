/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint eqeqeq: 0 */
import React, { Component, Fragment } from 'react';
import moment from 'moment';
import Tooltip from 'antd/lib/tooltip';
import Pagination from 'antd/lib/pagination';

import { httpRequest, baseURL, transactionAPI, paginate, currency, formatDate } from '../config/constants';
import profile_bg1 from '../assets/img/profile_bg1.jpg';

import Payslip from './Payslip';
import LoanHistory from './LoanHistory';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 5;

class LoanProfile extends Component {state = {
		transactions: [],
		currentPage: 1,
		totalPage: 0,
		netEarnings: [],
	};

	async componentDidMount() {
		const { loan } = this.props;
		if (loan) {
			const earnings = loan ? JSON.parse(loan.net_earnings) : [];
			const filteredEarnings = paginate(earnings, pageSize, 1);
			this.setState({
				totalPage: earnings.length,
				netEarnings: filteredEarnings,
			});

			try {
				const url = `${baseURL}${transactionAPI}/${loan.id}/loan`;
				const rs = await httpRequest(url, 'GET', true);
				this.setState({ transactions: rs.transactions });
			} catch (error) {
				console.log(error);
			}
		}
	}

	onChange = (pageNumber) => {
		const { loan } = this.props;
		const earnings = loan ? JSON.parse(loan.net_earnings) : [];
		const filteredEarnings = paginate(earnings, pageSize, pageNumber);
		this.setState({
			currentPage: pageNumber,
			totalPage: earnings.length,
			netEarnings: filteredEarnings,
		});
	};

	render() {
		const { closeModal, user, loan } = this.props;
		const { transactions, currentPage, totalPage, netEarnings } = this.state;
		const repayments = loan ? loan.total_deduction : '0.00';
		const mrepayments = loan ? loan.monthly_deduction : '0.00';
		const loan_amount = loan ? loan.amount : '0.00';
		const disbursed_loan_amount = loan ? loan.disburse_amount : '0.00';
		const disbursed_amount = disbursed_loan_amount > 0 ? disbursed_loan_amount : loan_amount;
        return (
            <div>
                {user && (
					<div className="element-box" style={{padding: '1.5rem 0.4rem'}}>
						<div className="col-sm-12">
							<div className="row" style={{border: 'hidden'}}>
								<div className="col-sm-3">
									<div className="user-profile compact">
										<div className="up-head-w" style={{backgroundImage: 'url('+profile_bg1+')'}}>
											<div className="up-main-info">
												<h2 className="up-header">{user.name}</h2>
											</div>
											<svg className="decor" width="842px" height="219px" viewBox="0 0 842 219"
												preserveAspectRatio="xMaxYMax meet" version="1.1"
												xmlns="http://www.w3.org/2000/svg"
												xmlnsXlink="http://www.w3.org/1999/xlink">
												<g transform="translate(-381.000000, -362.000000)" fill="FFFFFF">
													<path className="decor-path"
														d="M1223,362 L1223,581 L381,581 C868.912802,575.666667 1149.57947,502.666667 1223,362 Z"></path>
												</g>
											</svg>
										</div>
										<div className="up-contents">
											<div className="element-wrapper">
												<h6 className="element-header" style={{marginTop: '1.5rem'}}>&nbsp;</h6>
												<div className="element-box-tp">
													{loan && <Payslip user={user} />}
												</div>
											</div>
										</div>
									</div>
								</div>
								<div className="col-sm-9 loan-detail">
									<div className="row">
										<div className="col-sm-4">
											<div className="element-box el-tablo">
												<div className="label">Monthly Repayment</div>
												{mrepayments && (
													<div className="value">{currency(mrepayments)}</div>
												)}
											</div>
										</div>
										<div className="col-sm-4">
											<div className="element-box el-tablo">
												<div className="label">Total Repayment</div>
												{repayments && (
													<div className="value">{currency(repayments)}</div>
												)}
											</div>
										</div>
										<div className="col-sm-4">
											<div className="element-box el-tablo">
												<div className="label">Tenure</div>
												{loan && (
													<div className="value">{`${loan.tenure} Month(s)`}</div>
												)}
											</div>
										</div>
									</div>
									{loan && (
										<div className="row">
											<div className="col-sm-4">
												<div className="element-box el-tablo">
													<div className="label">Net Earning</div>
													{loan && (
														<div className="value">{currency(loan.net_earning)}</div>
													)}
												</div>
											</div>
											<div className="col-sm-4">
												<div className="element-box el-tablo">
													<div className="label">Monthly Principal</div>
														<div className="value">{currency(loan.monthly_principal)}</div>
												</div>
											</div>
											<div className="col-sm-4">
												<div className="element-box el-tablo">
													<div className="label">Monthly Interest</div>
														<div className="value">{currency(loan.monthly_interest)}</div>
												</div>
											</div>
										</div>
									)}
									<div className="row">
										<div className="col-sm-4">
											<div className="element-box el-tablo">
												<div className="label">Requested Loan Amount</div>
												<div className="value">{currency(loan_amount)}</div>
											</div>
										</div>
										{loan && loan.accountno && (
											<Fragment>
												<div className="col-sm-4">
													<div className="element-box el-tablo flex">
														<div>
															<div className="label">Account Number</div>
															<div className="value">{loan.accountno.account_number}</div>
														</div>
														{loan.accountno.verified === 0 ? (
															<i className="fa fa-exclamation-triangle text-danger text-md"/>
														) : (
															<i className="fa fa-check-circle text-success text-md"/>
														)}
													</div>
												</div>
												<div className="col-sm-4">
													<div className="element-box el-tablo">
														<div className="label">Bank Name</div>
														<div className="value">{loan.accountno.bank_name}</div>
													</div>
												</div>
											</Fragment>
										)}
									</div>
									{loan&&<div className="row">
										<div className="col-sm-4">
											<div className="element-box el-tablo">
												<div className="label">Loan Application Method</div>
												<div className="value text-uppercase">{loan.method && loan.method !== ''  ? loan.method : 'Web'}</div>
											</div>
										</div>
										<div className="col-sm-4">
											<div className="element-box el-tablo">
												<div className="label">Loan Consent</div>
												<div className="value text-uppercase">{loan.has_consented === 1 ? moment(loan.consented_at).format('DD-MMM-YYYY h:mm a') : '--'}</div>
											</div>
										</div>
										<div className="col-sm-4">
											<div className="element-box el-tablo">
												<div className="label">Loan Consent Method</div>
												{!loan.consent_otp && !loan.has_consented ? <div className="value text-uppercase">Offer Letter</div> : <div className="value text-uppercase">{loan.has_consented === 1 ? 'OTP' : '--'}</div>}
											</div>
										</div>
									</div>}
									<div className="row">
										<div className="col-sm-4">
											<div className="element-box el-tablo">
												<div className="label">Disburse Amount</div>
												<div className="value">{currency(disbursed_amount)}</div>
											</div>
										</div>
									</div>
									{user.platform === 'sme' ? (
										user.payslip && <div className="row">
											<div className="col-sm-12">
												<h6 className="element-header">Eligible Amount</h6>
												<div className="table-responsive">
													<table className="table table-striped">
														<thead>
														<tr>
															<th>Date</th>
															<th>Turnover</th>
															<th>Eligible Amount</th>
														</tr>
														</thead>
														<tbody>
															<tr>
																<td>{moment(user.payslip.created_at).format('MMM-YYYY')}</td>
																<td>{currency(user.payslip.turnover)}</td>
																<td>{currency(user.payslip.eligible_amount)}</td>
															</tr>
														</tbody>
													</table>
												</div>
											</div>
										</div>
									) : (
										<div className="row">
											<div className="col-sm-12">
												<h6 className="element-header">Earnings</h6>
												<div className="table-responsive">
													<table className="table table-striped">
														<thead>
														<tr>
															<th>Date</th>
															<th>Gross Earning</th>
															<th>Net Earning</th>
														</tr>
														</thead>
														<tbody>
															{netEarnings.map((n, i) => {
																return (
																	<tr key={i}>
																		<td>{moment(`1-${n.month}-${n.year}`, "D-M-YYYY").format('MMM-YYYY')}</td>
																		<td>{currency(n.gross_earning)}</td>
																		<td>{currency(n.net_earning)}</td>
																	</tr>
																)
															})}
															{totalPage === 0 && (
																<tr><td colSpan="3">No earnings found</td></tr>
															)}
														</tbody>
													</table>
												</div>
												<div className="pagination pagination-center mt-4">
													<Pagination
														current={currentPage}
														pageSize={pageSize}
														total={totalPage}
														showTotal={total => `Total ${total} earnings`}
														itemRender={itemRender}
														onChange={this.onChange}
													/>
												</div>
											</div>
										</div>
									)}
									<LoanHistory
										loans={user.loans ? user.loans.filter(l => l.id === loan.id) : [loan]}
										title="Loan"
										current_loan={loan}
										user={user}
									/>
									<div className="row">
										<div className="col-sm-12">
											<h6 className="element-header">Loan Repayments</h6>
											<div className="table-responsive">
												<table className="table table-striped">
													<thead>
													<tr>
														<th>Description</th>
														<th>Source</th>
														<th>Amount</th>
														<th>Outst Amount</th>
														<th>Date</th>
														<th className="text-center">Status</th>
													</tr>
													</thead>
													<tbody>
														{transactions.map(d => {
															const amount = d.description === 'Loan Taken' ? parseFloat(d.amount) - parseFloat(d.interest) : d.amount;
															return (
																<tr key={d.id} className={d.deleted_at ? 'tr-danger' : (d.approved < 0 ? 'tr-warning' : '')}>
																	<td>
																		{`${d.loan.is_topup === 1 ? 'Topup' : ''} ${d.description || 'Pending  Deduction'}`}
																		{d.channel === 'staff-repayment' && (
																			<Tooltip title="Staff Repayment"><span className="text-primary ml-1" style={{fontSize: '17px'}}><i className="fa fa-info-circle"/></span></Tooltip>
																		)}
																		{d.channel === 'credit-wallet' && (
																			<Tooltip title="Staff Credit To Wallet"><span className="text-primary ml-1" style={{fontSize: '17px'}}><i className="fa fa-info-circle"/></span></Tooltip>
																		)}
																		{d.channel === 'wallet-repayments' && (
																			<Tooltip title="Repayment From Wallet"><span className="text-primary ml-1" style={{fontSize: '17px'}}><i className="fa fa-info-circle"/></span></Tooltip>
																		)}
																		{d.channel === 'credit-wallet-repayment' && (
																			<Tooltip title="Split Repayment"><span className="text-primary ml-1" style={{fontSize: '17px'}}><i className="fa fa-info-circle"/></span></Tooltip>
																		)}
																		{d.repayment_reference && d.repayment_reference !== '0' && <br/>}
																		{d.repayment_reference && d.repayment_reference !== '0' && (
																			<span className="bold smaller">{`Repayment Ref: ${d.repayment_reference || '-'}`}</span>
																		)}
																	</td>
																	<td className="ucase">{d.repayment_source || '-'}</td>
																	<td>{currency(amount)}</td>
																	<td>{currency(d.outst_amount)}</td>
																	<td nowrap="nowrap">{moment(d.created_at).format('DD-MMM-YYYY h:mm a')}</td>
																	<td className="text-center"><Tooltip placement="top" title={d.deleted_at ? `declined on ${formatDate(d.deleted_at)}` : ((d.loan.liquidate_approve === 0 && d.description === 'Loan Liquidated') || d.approved === 0 ? 'Pending Approval' : d.status)}><div className={`status-pill ${(d.loan.liquidate_approve === 0 && d.description === 'Loan Liquidated') || d.approved === 0 ? 'yellow' : (d.status === 'Completed' ? 'green' : 'yellow')}`}/></Tooltip></td>
																</tr>
															)
														})}
														{transactions.length == 0 && (
															<tr><td colSpan="6">No repayments made</td></tr>
														)}
													</tbody>
												</table>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				)}
                <div className="modal-footer">
                    <button className="btn btn-teal" onClick={closeModal} type="button"> Close</button>
                </div>
            </div>
        );
    }
}

export default LoanProfile;
