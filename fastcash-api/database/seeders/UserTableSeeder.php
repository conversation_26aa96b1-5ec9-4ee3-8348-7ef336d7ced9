<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;

class UserTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $user = new User();
        $user->username = 'supera';
        $user->name = 'Consumer MFB';
        $user->email = '<EMAIL>';
        $user->password = bcrypt('secret');
        $user->phone = '+2347046346454';
        $user->save();

        $user->roles()->save(Role::where('name', 'super')->first());
    }
}
