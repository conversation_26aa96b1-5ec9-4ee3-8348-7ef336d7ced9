<?php

namespace App\Http\Controllers\Api\V1;

use App\Exports\ExportLoan;
use App\Models\Lender;
use App\Models\Loan;
use App\Models\PaymentDate;
use App\Models\RemitaTransaction;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use DB;
use Log;
use Maatwebsite\Excel\Facades\Excel;

class WalletController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $limit = $request->has('pagesize') && $request->input('pagesize') != '' ? $request->input('pagesize') : 10;
            $page = $request->has('page') && $request->input('page') != '' ? $request->input('page') : 1;
            $skip = ($page - 1) * $limit;

            $lender = $request->has('lender') ? $request->input('lender') : '';

            $user_id = $request->has('user_id') ? $request->input('user_id') : '';
            $type = $request->has('type') ? $request->input('type') : 'default';

            $result = (object)null;

            if($type == 'debit-requests'){
                $wallets = User::where('debit_request', 1)->get();
                $count = User::where('debit_request', 1)->count();

                $wallets = $wallets->map(function ($item, $key) {
                    $item->wallet = Wallet::where('user_id', $item->id)->where('is_lender', 0)->where('approved', 1)->where('paid', 1)->sum('amount');
                    return $item;
                });


                $result->total = $count;
                $result->size = $limit;
                $result->page = $page;
            }else if($type == 'default'){
                if ($user_id != '') {
                    if($type == 'default'){
                        $all_wallets = Wallet::where('user_id', $user_id)->where('paid', 1)->where('is_lender', 0)->orderBy('updated_at', 'ASC')->orderBy('type', 'ASC')->get();

                        $amount = 0;
                        $all_wallets = $all_wallets->map(function ($item, $key) use (&$amount) {
                            $amount += $item->amount;
                            $item->new_amount = $amount;
                            return $item;
                        });

                        $wallets = Wallet::where('user_id', $user_id)->where('paid', 1)->where('is_lender', 0)->where('category', '!=', 'account-check')->skip($skip)->take($limit)->orderBy('updated_at', 'DESC')->orderBy('type', 'DESC')->get();

                        $wallets = $wallets->map(function ($item, $key) use ($all_wallets) {
                            $item->user = User::where('id', $item->user_id)->first(['id', 'name', 'email']);
                            $item->loan = Loan::where('id', $item->loan_id)->first(['id']);
                            $item->transaction = Transaction::where('id', $item->transaction_id)->first(['id', 'description', 'channel', 'repayment_date', 'approved_by']);
                            $item->single = collect($all_wallets)->firstWhere('id', $item->id);

                            return $item;
                        });

                        $count = Wallet::where('user_id', $user_id)->where('paid', 1)->where('is_lender', 0)->count();
                    }



                }
            }

            $result->data = $wallets;
            $result->count = $count;

            return response()->json(compact('result'), 200);

            // return response()->json(['error' => 'could not fetch wallet statement'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not fetch wallet statement'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        $data = $request->all();
        try {

            $balance = Wallet::where('lender_id', $data['lender_id'])->where(function ($query) {
                $query->whereNull('approved')->orWhere('approved', 1);
            })->where('is_lender', 1)->where('paid', 1)->sum('amount');

            if (($balance <= 0 || $data['amount'] > $balance) && $data['category'] == 'withdraw') {
                return response()->json(['error' => 'insufficient funds'], 500);
            }

            $wallet = Wallet::create([
                'user_id' => $data['user_id'],
                'category' => $data['category'],
                'amount' => $data['amount'],
                'type' => $data['type'],
                'lender_id' => $data['lender_id'],
                'approved' => $data['approved'],
                'approved_at' => $data['approved'] = 1 ? date('Y-m-d') : null,
                'approved_by' => $data['approved'] = 1 ? $data['user_id'] : null,
                'is_lender' => isset($data['is_lender']) ? $data['is_lender'] : 0,
                'paid' => $data['approved'] = 1 ? 1 : 0,
            ]);

            $notify = doNotify($data['user_id'], 'wallets', $wallet->id, $data['category'], 'success', null, $data['lender_id'], $data['user_id']);

            $details = getFinancialOverview($data['lender_id'], $data['lender_id']);

            return response()->json(compact('details'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not ' . $data['category'] . ' funds, try again later'], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function approve(Request $request, $id)
    {
        try {
            $data = $request->all();

            $wallet = Wallet::where('id', $id)->first();
            $wallet->paid = 1;
            $wallet->approved = 1;
            $wallet->approved_by = $data['staff'];
            $wallet->approved_at = date('Y-m-d H:i:s');
            $wallet->save();

            $notify = doNotify($data['staff'], 'wallets', $id, 'approve_' . $wallet->category, 'success', null, $wallet->lender_id, null);

            $details = getFinancialOverview($wallet->lender_id, $wallet->lender_id);

            return response()->json(['wallet' => $wallet, 'details' => $details], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not approve funds'], 500);
        }
    }

    /**
     * credit user wallet.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $loan_id
     * @return \Illuminate\Http\Response
     */
    public function credit(Request $request, $loan_id)
    {
        DB::beginTransaction();
        try {
            $validator = Validator::make($request->all(), [
                'amount' => 'required|numeric',
                'repayment_date' => 'required',
                'source' => 'required',
            ]);

            if ($validator->fails()) {
                $error = validatorMessage($validator->errors());
                return response()->json(['error' => $error], 500);
            }

            $data = $request->all();

            $loan = Loan::where('id', $loan_id)->first();
            if ($loan->disbursed == 0) {
                return response()->json(['error' => 'loan has not been disbursed'], 500);
            }

            $approved = json_decode($loan->approved_remita);
            $mandate_ref = $approved->data->mandateReference ?? '';

            $transaction = null;
            $remita = null;

            if (isset($data['repayment_reference'])) {
                $remita = RemitaTransaction::where('repayment_ref', $data['repayment_reference'])->where('mandate_reference', $mandate_ref)->first();
            }

            if ($remita) {
                $transaction = Transaction::where('remita_transaction_id', $remita->id)->where('approved', 0)->first();
            }

            if ($remita && $transaction) {
                $ref = null;

                $amount = $data['amount'];

                if ($loan->liquidate_approve == 0) {
                    $outAmount = calcOutstRemita($loan->id, $loan->total_deduction, $amount);

                    $pays = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)->get();
                    if (count($pays) == 1 && $outAmount <= 0) {
                        $merchantId = env('MERCHANT_ID');
                        $apiKey = env('API_KEY');
                        $apiToken = env('API_TOKEN');

                        $requestId = time() * 1000;
                        $apiHash = hash('sha512', $apiKey . $requestId . $apiToken);
                        $authorization = "remitaConsumerKey=" . $apiKey . ", remitaConsumerToken=" . $apiHash;

                        $body = [
                            "authorisationCode" => $loan->auth_code,
                            "customerId" => $loan->user->customer_id,
                            "mandateReference" => $mandate_ref,
                        ];

                        try {
                            $sl = (new Remita())->stopLoanCollection($merchantId, $apiKey, $requestId, $authorization, $body);

                            $status = $sl == null ? 'failed' : ($sl->responseCode == '00' ? 'success' : $sl->responseMsg);

                            logAPI('stop-loan', $loan->user->phone, $status);

                            if ($sl != null) {
                                info('stop loan:-> ' . json_encode($sl));
                                if ($sl->responseCode != '00') {
                                    Log::error('TransactionController.php Line 756 stop loan:-> ' . json_encode($sl));
                                } else {
                                    $ref = json_encode($sl);
                                }
                            }
                        } catch (\Exception $e) {
                            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                        }
                    }

                    // update
                    $remita->bank_repayment_ref = $data['repayment_reference'];
                    $remita->save();

                    // calculate month
                    $month = date('F');
                    $pay = PaymentDate::where('paid', 0)->where('loan_id', $loan->id)->first();
                    if ($pay) {
                        $month = Carbon::createFromFormat('Y-m-d H:i:s', $pay->payment_date)->format('F');
                    }

                    $transaction->amount = $amount;
                    $transaction->amount_paid = $transaction->amount;
                    $transaction->approved = 1;
                    $transaction->approved_by = $data['staff_id'];
                    $transaction->approved_at = date('Y-m-d H:i:s');
                    $transaction->description = 'REMITA Deduction for the Month of ' . $month;
                    $transaction->transaction_flag = 'deduction';
                    $transaction->status = 'Completed';
                    $transaction->outst_amount = $outAmount;
                    $transaction->repayment_date = $data['repayment_date'];
                    $transaction->reference = $ref;
                    $transaction->save();

                    $w_debit = Wallet::where('user_id', $loan->user_id)->where('loan_id', $loan->id)->where('type', 'debit')->where('category', 'repay_loan')->whereNull('transaction_id')->first();
                    if ($w_debit) {
                        $w_debit->transaction_id = $transaction->id;
                        $w_debit->amount = $data['amount'] * -1;
                        $w_debit->approved_by = $data['staff_id'];
                        $w_debit->paid = 1;
                        $w_debit->save();
                    } else {
                        // debit user account
                        $w_debit = Wallet::create([
                            'user_id' => $loan->user_id,
                            'loan_id' => $loan->id,
                            'category' => 'repay_loan',
                            'type' => 'debit',
                            'amount' => $data['amount'] * -1,
                            'transaction_id' => $transaction->id,
                            'approved' => 1,
                            'approved_by' => 1,
                            'approved_at' => date('Y-m-d H:i:s'),
                            'lender_id' => $loan->lender_id,
                            'is_lender' => 0,
                            'payment_date' => $pay ? $pay->payment_date : null,
                            'paid' => 1,
                        ]);
                    }

                    // credit user wallet to repay loan
                    $wallet1 = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'credit',
                        'amount' => $data['amount'],
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $data['staff_id'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'paid' => 1,
                    ]);

                    // credit lender
                    $wallet2 = Wallet::create([
                        'user_id' => $data['staff_id'],
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'credit',
                        'amount' => $data['amount'],
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $data['staff_id'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 1,
                        'paid' => 1,
                    ]);

                    $notify = doNotify($w_debit->user_id, 'wallets', $w_debit->id, 'repay_loan', 'success', null, $w_debit->lender_id, $data['staff_id']);

                    $liquidated = liquidateLoan($loan->id);

                    if ($pay) {
                        $pay->paid = 1;
                        $pay->paid_at = date('Y-m-d H:i:s');
                        $pay->transaction_id = $transaction->id;
                        $pay->save();
                    }

                    $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'repayment_approved', 'success', null, $loan->lender_id, $data['staff_id']);
                }
                else {
                    $transaction->approved = 1;
                    $transaction->approved_by = $data['staff_id'];
                    $transaction->approved_at = date('Y-m-d H:i:s');
                    $transaction->description = 'Excess Repayment';
                    $transaction->status = 'Completed';
                    $transaction->outst_amount = 0.00;
                    $transaction->channel = 'credit-wallet';
                    $transaction->transaction_flag = 'credit-wallet';
                    $transaction->repayment_date = $data['repayment_date'];
                    $transaction->save();

                    $w_debit = Wallet::where('user_id', $loan->user_id)->where('loan_id', $loan->id)->where('type', 'debit')->where('category', 'repay_loan')->whereNull('transaction_id')->delete();

                    // credit user wallet to repay over deduction
                    $wallet1 = Wallet::create([
                        'user_id' => $transaction->user_id,
                        'loan_id' => $transaction->loan_id,
                        'category' => 'repay_deduction',
                        'type' => 'credit',
                        'amount' => $data['amount'],
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $data['staff_id'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $transaction->lender_id,
                        'is_lender' => 0,
                        'paid' => 1,
                    ]);

                    // credit lender
                    $wallet2 = Wallet::create([
                        'user_id' => $data['staff_id'],
                        'loan_id' => $transaction->loan_id,
                        'category' => 'repay_deduction',
                        'type' => 'credit',
                        'amount' => $data['amount'],
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $data['staff_id'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $transaction->lender_id,
                        'is_lender' => 1,
                        'paid' => 1,
                    ]);

                    // debit lender
                    $wallet2 = Wallet::create([
                        'user_id' => $data['staff_id'],
                        'loan_id' => $transaction->loan_id,
                        'category' => 'repay_deduction',
                        'type' => 'debit',
                        'amount' => $data['amount'] * -1,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $data['staff_id'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $transaction->lender_id,
                        'is_lender' => 1,
                        'paid' => 1,
                    ]);

                    $notify = doNotify($loan->user_id, 'wallets', $wallet1->id, 'credit_wallet', 'success', null, $wallet1->lender_id, $data['staff_id']);
                }
            } else {
                if (strtolower($data['source']) == 'remita') {
                    DB::rollBack();
                    return response()->json(['error' => 'invalid repayment reference'], 500);
                }

                $transaction = Transaction::create([
                    'user_id' => $loan->user_id,
                    'loan_id' => $loan->id,
                    'office_id' => $loan->office_id,
                    'lender_id' => $loan->lender_id,
                    'source' => strtoupper($loan->platform),
                    'amount' => $data['amount'],
                    'status' => 'Completed',
                    'reference' => null,
                    'channel' => 'credit-wallet',
                    'transaction_flag' => 'credit-wallet',
                    'approved' => 1,
                    'repayment_source' => $data['source'],
                    'uploaded_by' => $data['staff_id'],
                    'repayment_date' => $data['repayment_date'],
                    'principal' => $loan->monthly_principal,
                    'interest' => $loan->monthly_interest,
                    'outst_amount' => 0.00,
                    'description' => 'Excess Repayment'
                ]);

                // credit user wallet to repay over deduction
                $wallet1 = Wallet::create([
                    'user_id' => $transaction->user_id,
                    'loan_id' => $transaction->loan_id,
                    'category' => 'repay_deduction',
                    'type' => 'credit',
                    'amount' => $data['amount'],
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $data['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $transaction->lender_id,
                    'is_lender' => 0,
                    'paid' => 1,
                ]);

                // credit lender
                $wallet2 = Wallet::create([
                    'user_id' => $data['staff_id'],
                    'loan_id' => $transaction->loan_id,
                    'category' => 'repay_deduction',
                    'type' => 'credit',
                    'amount' => $data['amount'],
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $data['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $transaction->lender_id,
                    'is_lender' => 1,
                    'paid' => 1,
                ]);

                // debit lender
                $wallet2 = Wallet::create([
                    'user_id' => $data['staff_id'],
                    'loan_id' => $transaction->loan_id,
                    'category' => 'repay_deduction',
                    'type' => 'debit',
                    'amount' => $data['amount'] * -1,
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $data['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $transaction->lender_id,
                    'is_lender' => 1,
                    'paid' => 1,
                ]);

                $notify = doNotify($loan->user_id, 'wallets', $wallet1->id, 'credit_wallet', 'success', null, $wallet1->lender_id, $data['staff_id']);
            }

            DB::commit();

            $wallet_amount = Wallet::where('user_id', $loan->user_id)->where('is_lender', 0)->where('approved', 1)->where('paid', 1)->sum('amount');

            DB::beginTransaction();
            // check if user has an active loan
            $user = User::where('id', $loan->user_id)->first(['id', 'loan_id']);
            $user_lender = User::where('lender_id', $user->lender_id)->whereNull('ippis')->whereNotNull('username')->first();
            if ($user->loan_id && $wallet_amount > 0) {
                error_log('active loan found');
                $current_loan = Loan::where('id', $user->loan_id)->where('disbursed', 1)->first();
                if ($current_loan) {
                    error_log('loan found:' . $current_loan->id);

                    $date = Carbon::now()->endOfMonth()->format('Y-m-d H:i:s');
                    error_log('date:' . $date);

                    // check for default or current debit
                    $not_paid = PaymentDate::where('loan_id', $current_loan->id)->where('paid', 0)->where('payment_date', '<=', $date)->count();
                    error_log('not repaid: ' . $not_paid);

                    if ($not_paid > 0) {
                        $pay = PaymentDate::where('loan_id', $current_loan->id)->where('paid', 0)->where('payment_date', '<=', $date)->first();
                        if ($pay) {
                            $payment_amount = $wallet_amount >= $current_loan->monthly_deduction ? $current_loan->monthly_deduction : $wallet_amount;

                            $month = Carbon::createFromFormat('Y-m-d H:i:s', $pay->payment_date)->format('F');

                            $repay = Transaction::create([
                                'user_id' => $current_loan->user_id,
                                'loan_id' => $current_loan->id,
                                'office_id' => $current_loan->office_id,
                                'lender_id' => $current_loan->lender_id,
                                'source' => strtoupper($current_loan->platform),
                                'amount' => $payment_amount,
                                'status' => 'Completed',
                                'reference' => null,
                                'channel' => 'wallet-repayments',
                                'transaction_flag' => 'deduction',
                                'approved' => 1,
                                'repayment_source' => $transaction->repayment_source,
                                'uploaded_by' => $data['staff_id'],
                                'repayment_date' => date('Y-m-d H:i:s'),
                                'principal' => $payment_amount - $current_loan->monthly_interest,
                                'interest' => $current_loan->monthly_interest,
                                'outst_amount' => calcOutstRemita($current_loan->id, $current_loan->total_deduction, $payment_amount),
                                'description' => strtoupper($current_loan->platform) . ' Rpmt From Wallet for the Month of ' . $month,
                                'approved_at' => date('Y-m-d H:i:s')
                            ]);

                            // check for pending debit
                            $w_debit = Wallet::where('user_id', $current_loan->user_id)->where('loan_id', $current_loan->id)->where('type', 'debit')->where('category', 'repay_loan')->whereNull('transaction_id')->first();
                            if ($w_debit) {
                                $w_debit->transaction_id = $repay->id;
                                $w_debit->amount = $payment_amount * -1;
                                $w_debit->approved_by = $data['staff_id'];
                                $w_debit->paid = 1;
                                $w_debit->save();
                            } else {
                                // debit user account
                                $w_debit = Wallet::create([
                                    'user_id' => $current_loan->user_id,
                                    'loan_id' => $current_loan->id,
                                    'category' => 'repay_loan',
                                    'type' => 'debit',
                                    'amount' => $payment_amount * -1,
                                    'transaction_id' => $repay->id,
                                    'approved' => 1,
                                    'approved_by' => 1,
                                    'approved_at' => date('Y-m-d H:i:s'),
                                    'lender_id' => $current_loan->lender_id,
                                    'is_lender' => 0,
                                    'payment_date' => $pay->payment_date,
                                    'paid' => 1,
                                ]);
                            }

                            // credit lender
                            $wallet2 = Wallet::create([
                                'user_id' => $user_lender ? $user_lender->id : $data['staff_id'],
                                'loan_id' => $current_loan->id,
                                'category' => 'repay_loan',
                                'type' => 'credit',
                                'amount' => $payment_amount,
                                'transaction_id' => $repay->id,
                                'approved' => 1,
                                'approved_by' => $data['staff_id'],
                                'approved_at' => date('Y-m-d H:i:s'),
                                'lender_id' => $current_loan->lender_id,
                                'is_lender' => 1,
                                'paid' => 1,
                            ]);

                            $notify = doNotify($w_debit->user_id, 'wallets', $w_debit->id, 'repay_loan', 'success', null, $w_debit->lender_id, $data['staff_id']);

                            $pay->paid = 1;
                            $pay->paid_at = date('Y-m-d H:i:s');
                            $pay->transaction_id = $repay->id;
                            $pay->save();

                            $liquidated = liquidateLoan($current_loan->id);

                            $notify = doNotify($repay->user_id, 'transactions', $repay->id, 'wallet_repayment', 'success', null, $current_loan->lender_id, $data['staff_id']);
                        }
                    }
                }
            }

            DB::commit();

            $amount = Wallet::where('user_id', $loan->user_id)->where('is_lender', 0)->where('approved', 1)->where('paid', 1)->sum('amount');

            // group transactions by loan
            $_transactions = Transaction::with('user')->where('user_id', $loan->user_id)->orderBy('created_at')->get();
            $_transactions = $_transactions->map(function ($item, $key) {
                $item->loan = Loan::where('id', $item->loan_id)->first();

                return $item;
            });

            $grouped = collect($_transactions)->groupBy('loan_id');
            $keys = array_keys($grouped->toArray());
            rsort($keys);

            $transactions = [];
            for ($i = 0; $i < count($keys); $i++) {
                $transaction = (object)null;
                $transaction->loan = Loan::with('lender')->where('id', $keys[$i])->first();
                $transaction->transactions = $grouped[$keys[$i]];

                $transactions[] = $transaction;
            }

            return response()->json(['amount' => $amount, 'transactions' => $transactions], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not credit wallet'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $data = $request->all();

            $wallet = Wallet::where('id', $id)->first();
            $wallet->deleted_by = $data['staff'];
            $wallet->save();

            $notify = doNotify($data['staff'], 'wallets', $id, 'decline_' . $wallet->category, 'success', null, $wallet->lender_id, null);

            $wallet->delete();

            $details = getFinancialOverview($wallet->lender_id, $wallet->lender_id);

            return response()->json(['wallet' => $wallet, 'details' => $details], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not decline transaction'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @return array
     */
    public function loanRepayment()
    {

        DB::beginTransaction();
        try {
            $date = Carbon::now()->endOfMonth()->format('Y-m-d H:i:s');
            $all_payment_dates = PaymentDate::where('payment_date', $date)->get();

            $repayments = [];
            foreach ($all_payment_dates as $pd) {
                $loan = Loan::where('id', $pd->loan_id)->where('liquidated', 0)->where('liquidate_approve', 0)->first();

                if ($loan) {
                    if ($pd->transaction_id) {
                        $transaction = Transaction::where('id', $pd->transaction_id)->where('transaction_flag', 'deduction')->where('approved', 1)->first();

                        if ($transaction) {
                            // debit user account
                            $wallet = Wallet::create([
                                'user_id' => $loan->user_id,
                                'loan_id' => $loan->id,
                                'category' => 'repay_loan',
                                'type' => 'debit',
                                'amount' => $transaction->amount * -1,
                                'transaction_id' => $pd->transaction_id,
                                'approved' => 1,
                                'approved_by' => 1,
                                'approved_at' => date('Y-m-d H:i:s'),
                                'lender_id' => $loan->lender_id,
                                'is_lender' => 0,
                                'payment_date' => $pd->payment_date,
                                'paid' => 1,
                            ]);

                            $repayments[] = $wallet->id;

                            // credit user wallet to repay loan
                            $wallet1 = Wallet::create([
                                'user_id' => $loan->user_id,
                                'loan_id' => $loan->id,
                                'category' => 'repay_loan',
                                'type' => 'credit',
                                'amount' => $transaction->amount,
                                'transaction_id' => $transaction->id,
                                'approved' => 1,
                                'approved_by' => $transaction->approved_by,
                                'approved_at' => date('Y-m-d H:i:s'),
                                'lender_id' => $loan->lender_id,
                                'is_lender' => 0,
                                'paid' => 1,
                            ]);

                            // credit lender
                            $wallet2 = Wallet::create([
                                'user_id' => $loan->user_id,
                                'loan_id' => $loan->id,
                                'category' => 'repay_loan',
                                'type' => 'credit',
                                'amount' => $transaction->amount,
                                'transaction_id' => $transaction->id,
                                'approved' => 1,
                                'approved_by' => $transaction->approved_by,
                                'approved_at' => date('Y-m-d H:i:s'),
                                'lender_id' => $loan->lender_id,
                                'is_lender' => 1,
                                'paid' => 1,
                            ]);

                            $notify = doNotify($wallet->user_id, 'wallets', $wallet->id, 'repay_loan', 'success', null, $wallet->lender_id, 1);
                        }
                    } else {
                        // debit user account
                        $wallet = Wallet::create([
                            'user_id' => $loan->user_id,
                            'loan_id' => $loan->id,
                            'category' => 'repay_loan',
                            'type' => 'debit',
                            'amount' => $loan->monthly_deduction * -1,
                            'approved' => 1,
                            'approved_by' => 1,
                            'approved_at' => date('Y-m-d H:i:s'),
                            'lender_id' => $loan->lender_id,
                            'is_lender' => 0,
                        ]);

                        $repayments[] = $wallet->id;
                    }

                } else {
                    info('loan not found: ' . json_encode($pd));
                }
            }

            $paid = [];

            //$today = Carbon::now()->format('Y-m-d H:i:s');
            //$wallets = Wallet::where('is_lender', 0)->where('approved', 1)->where('category', 'repay_loan')->where('type', 'debit')->where('paid', 0)->where('payment_date', '<', $today)->get();
            //foreach ($wallets as $item) {
            //$wallet = Wallet::where('id', $item->id)->first();
            //$wallet->paid = 1;
            //$wallet->save();

            //$paid[] = $wallet->id;
            //}

            $payments = ['repayments' => $repayments, 'paid' => $paid];
            info(json_encode($payments));

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return null;
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     *
     * @return array
     */
    public function exportCredit(Request $request)
    {
        try {
            $transactions = Transaction::where('channel', 'credit-wallet')->where('approved', 1)->where('repayment_source', $request->source)->get();

            $data = [];
            $i = 0;
            foreach ($transactions as $transaction) {
                $i++;

                $user = User::where('id', $transaction->user_id)->first();
                $lender = Lender::where('id', $user->lender_id)->first();

                $item = (object)null;
                $item->sn = $i;
                $item->name = $user->name;
                $item->lender = $lender->name;
                $item->phone = $user->phone;
                $item->ippis = $user->ippis;
                $item->amount = $transaction->amount;

                $data[] = $item;
            }

            $allData = json_decode(json_encode($data), true);
            $report = [];
            foreach ($allData as $item) {
                $new_report = [];
                foreach ($item as $key => $value) {
                    $new_report[ucwords(str_replace("_", " ", $key))] = $value;
                    unset($new_report[$key]);
                }
                $report[] = $new_report;
            }

            $headings = getHeadings($report[0]);

            $filename = time();
            $filepath = 'reports/' . $filename . '.xlsx';

            Excel::store(new ExportLoan($report, $headings), $filepath, 'public');

            $file = env('APP_URL') . '/reports/' . $filename . '.xlsx';

            return response()->json(['file' => $file], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json($e, 500);
        }
    }

    /**
     * Admin wallet withdrawal request
     */
    public function debitRequest(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'amount' => ['required'],
            'reason' => ['required'],
        ], [
            'reason.required' => 'Please provide the reason for this request',
            'amount.required' => 'Please enter an amount for this request'
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        DB::beginTransaction();

        try {
            $data = $request->all();

            $user = User::find($id);
            if (!$user) {
                return response()->json(['error' => 'User does not exist'], 500);
            }

            if($user->debit_request === 1){
                return response()->json(['error' => 'There is already a pending request'], 500);
            }

            $user->debit_request = 1;
            $user->debit_request_reason = $data['reason'];
            $user->debit_amount = $data['amount'];
            $user->debit_request_by = $data['staff_id'];
            $user->debit_request_at =date('Y-m-d H:i:s');
            $user->save();

            DB::commit();

            $notify = doNotify($user->id, 'user', $user->id, 'debit_wallet_request', 'success', $data['reason'], $user->lender_id, $data['staff_id']);

            return response()->json(compact('user'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not submit wallet debit request, try again later'], 500);
        }


    }

    /**
     * Approve admin wallet withdrawal requests.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function approveDebitRequest(Request $request, $id)
    {
        DB::beginTransaction();
        try{
            $data = $request->all();

            $user = User::find($id);
            if (!$user) {
                return response()->json(['error' => 'User does not exist'], 500);
            }

            $transaction = Transaction::create([
                'user_id' => $user->id,
                'loan_id' => null,
                'office_id' => $user->office_id,
                'source' => strtoupper($user->platform),
                'amount' => $user->debit_amount,
                'interest' => 0,
                'principal' => 0,
                'outst_amount' => 0,
                'transaction_flag' => 'withdrawal',
                'status' => 'Completed',
                'description' => $user->debit_request_reason,
                'lender_id' => $user->lender_id,
                'approved' => 1,
                'approved_by' => $data['staff_id'],
                'approved_at' => date('Y-m-d H:i:s'),
            ]);

            // debit user wallet
            $w1 = Wallet::create([
                'user_id' => $user->id,
                'category' => 'admin-wallet-debit',
                'type' => 'debit',
                'amount' => (-1 * $user->debit_amount),
                'transaction_id' => $transaction->id,
                'approved' => 1,
                'approved_by' => $data['staff_id'],
                'approved_at' => date('Y-m-d H:i:s'),
                'lender_id' => $user->lender_id,
                'is_lender' => 0,
                'paid' => 1,
            ]);

            $user->debit_request = 0;
            $user->debit_amount = null;
            $user->debit_request_reason = null;
            $user->debit_request_by = null;
            $user->debit_request_at = null;
            $user->save();

            DB::commit();

            $notify = doNotify($user->user_id, 'user', $user->id, 'debit_wallet_approve', 'success', null, $user->lender_id, $data['staff_id']);

            $wallet_debit_requests = User::where('debit_request', 1)->count();

            return response()->json(['user' => $user, 'wallet_debit_requests' => $wallet_debit_requests], 200);
        }catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not approve wallet debit request, try again later'], 500);
        }
    }

    /**
     * Decline admin wallet withdrawal requests.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function declineDebitRequest(Request $request, $id)
    {
        DB::beginTransaction();
        try{
            $data = $request->all();

            $user = User::find($id);
            if (!$user) {
                return response()->json(['error' => 'User does not exist'], 500);
            }

            $user->debit_request = 0;
            $user->debit_amount = null;
            $user->debit_request_reason = null;
            $user->debit_request_by = null;
            $user->debit_request_at = null;
            $user->save();

            DB::commit();

            $notify = doNotify($user->user_id, 'user', $user->id, 'debit_wallet_decline', 'success', null, $user->lender_id, $data['staff_id']);

            $wallet_debit_requests = User::where('debit_request', 1)->count();

            return response()->json(['user' => $user, 'wallet_debit_requests' => $wallet_debit_requests], 200);
        }catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not approve wallet debit request, try again later'], 500);
        }
    }


}
