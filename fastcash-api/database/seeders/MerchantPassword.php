<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MerchantPassword extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::beginTransaction();
        try {
            $users = User::withTrashed()->whereNotNull('username')->get();
            foreach ($users as $item) {
                $user = User::withTrashed()->where('id', $item->id)->first();
                if($user && $user->role == 'merchant') {
                    $user->merchant_password = $item->password;
                    $user->password = NULL;
                    $user->save();
                }
            }

            DB::commit();
        } catch (\Exception $e){
            DB::rollBack();
            Log::error($e->getMessage());
        }
    }
}
