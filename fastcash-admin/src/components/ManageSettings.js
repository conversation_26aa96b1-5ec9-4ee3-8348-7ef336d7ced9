import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, SubmissionError } from 'redux-form';
import DatePicker from 'react-datepicker';
import moment from 'moment';

import { doNotify } from '../actions/general';
import { baseURL, httpRequest, settingsAPI, settingKey } from '../config/constants';
import { putSettings } from '../actions/settings';
import { startBlock, stopBlock } from '../actions/ui-block';
import 'react-datepicker/dist/react-datepicker.css';

const validate = values => {
    const errors = {}
    if (!values.stop_loan_date) {
        errors.stop_loan_date = 'Select stop loan date';
	}
    if (!values.multifund_expire) {
        errors.multifund_expire = 'Enter multi funding expiration period';
	}
    if (!values.snooze_period) {
        errors.snooze_period = 'Enter snooze period';
	}
    if (!values.split_loan) {
        errors.split_loan = 'Enter number of loans to split into';
	}
    if (!values.consent_template) {
        errors.consent_template = 'Enter template for loan consent';
	}
    return errors;
};

const renderField = ({input, id, label, type, meta: {touched, error, warning}}) => (
	<div className={`form-group row ${(touched && error && 'has-error')}`}>
		<label className="col-sm-5 col-form-label text-right" htmlFor={id}>{label}</label>
		<div className="col-sm-7">
			<input {...input} placeholder={label} type={type} className="form-control" />
			{touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
		</div>
	</div>
);

const renderTAField = ({input, id, label, meta: {touched, error, warning}}) => (
    <div className={`form-group row ${(touched && error && 'has-error')}`}>
        <label className="col-sm-5 col-form-label text-right" htmlFor={id}>{label}</label>
		<div className="col-sm-7">
			<textarea {...input} placeholder={label} className="form-control" rows="4" />
			{touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
		</div>
    </div>
);

const renderAppendField = ({input, id, label, type, append, meta: {touched, error, warning}}) => (
	<div className={`form-group row ${(touched && error && 'has-error')}`}>
		<label className="col-sm-5 col-form-label text-right" htmlFor={id}>{label}</label>
		<div className="col-sm-7">
			<div className="input-group">
				<input {...input} placeholder={label} type={type} className="form-control" />
				<div className="input-group-append">
					<div className="input-group-text">{append}</div>
				</div>
			</div>
			{touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
		</div>
	</div>
);

class ManageSettings extends Component {
	constructor(props, context) {
		super(props, context);
        this.state = {
			sld_error: '',
			stopLoanDate: (props.stop_loan_date) ? moment(props.stop_loan_date, "YYYY-MM-DD") : null ,
		};
		this.handleChangeSLD = this.handleChangeSLD.bind(this);
    }
	
	saveSettings = async data => {
		this.props.startBlock();
		try {
			const { lender } = this.props;
			const { stopLoanDate } = this.state;
			const details = { ...data, stop_loan_date: stopLoanDate.format("YYYY-MM-DD"), lender_id: lender.id };
			const settings = await httpRequest(`${baseURL}${settingsAPI}`, 'POST', true, details);
			localStorage.setItem(settingKey, JSON.stringify(settings));
			this.props.putSettings(settings);
			this.props.stopBlock();
			this.notify('', 'settings saved!', 'success');
		}
		catch (error) {
			this.props.stopBlock();
			const message = error.message || 'could not save settings';
			this.notify('', message, 'error');
			throw new SubmissionError({
				_error: message,
			});
		}
	};
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	handleChangeSLD = date => this.setState({ stopLoanDate: date });
	
	render() {
		const { handleSubmit, error } = this.props;
		const { stopLoanDate, sld_error } = this.state;
		return (
			<div className="element-box">
				{error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
				<form onSubmit={handleSubmit(this.saveSettings)}>
					<Field
						name="multifund_expire"
						id="multifund_expire"
						type="number"
						component={renderAppendField}
						label="Multi-Fund Expiration Period"
						append="Days"
					/>
					<Field
						name="snooze_period"
						id="snooze_period"
						type="number"
						component={renderAppendField}
						label="User Snooze Period"
						append="Days"
					/>
					<Field
						name="split_loan"
						id="split_loan"
						component={renderField}
						label="Number of Loan Split"
						type="number"
					/>
					<div className={`form-group row ${(sld_error !== '' && 'has-error')}`}>
						<label className="col-sm-5 col-form-label text-right">Stop Loan Date</label>
						<div className="col-sm-7">
							<DatePicker
								selected={stopLoanDate}
								onChange={this.handleChangeSLD}
								dateFormat="Do"
								placeholderText="Click to select date"
								className="form-control white-bg"
								readOnly={false}
							/>
							{sld_error !== '' && <span className="help-block form-text with-errors form-control-feedback">{sld_error}</span>}
						</div>
					</div>
					<Field
						name="consent_template"
						id="consent_template"
						component={renderTAField}
						label="Template for loan consent"
					/>
					<div className="form-buttons-w">
						<button className="btn btn-primary" type="submit">Save Changes</button>
					</div>
				</form>
			</div>
		);
	}
}

ManageSettings = reduxForm({
    form: 'savesettings',
    validate,
})(ManageSettings);

const mapStateToProps = (state, ownProps) => {
	const settings = state.settings;
	
	return {
		stop_loan_date: settings.stop_loan_date,
		initialValues: {
            split_loan: settings.split_loan,
            multifund_expire: settings.multifund_expire,
            snooze_period: settings.snooze_period,
            consent_template: settings.consent_template,
		},
		lender: state.lender.profile,
	}
};

export default connect(mapStateToProps, { putSettings, startBlock, stopBlock, doNotify })(ManageSettings);
