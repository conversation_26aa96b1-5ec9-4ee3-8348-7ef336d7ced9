/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react';
import moment from 'moment';

import company1 from '../assets/img/company1.png';
import { currency } from '../config/constants';

const Deductions = ({ description, date, source, amount, status, is_topup, liquidate_approve, approved }) => {
    const sign = description === 'Loan Taken' ? '+' : '-';
    return (
        <tr>
            <td className="cell-with-media"><img alt="" src={company1} style={{height: '25px'}}/><span>{(is_topup && description === 'Loan Taken' ? 'Loan Topup' : description).replace('REMITA ', '')}</span></td>
            <td><span>{moment(date).format('DD.MMM.YYYY')}</span><span className="smaller lighter">{moment(date).format('h:mma')}</span></td>
            <td className="text-center"><a className="badge badge-success text-white">{source}</a></td>
            <td className="text-right bolder nowrap"><span className="text-success">{`${sign} ${currency(amount)}`}</span></td>
            <td className="nowrap">
                <span className={`status-pill smaller ${(liquidate_approve === 0 && description === 'Loan Liquidated') || approved === 0 ? 'yellow' : (status === 'Completed' ? 'green' : 'yellow')}`}/><span>{(liquidate_approve === 0 && description === 'Loan Liquidated') || approved === 0 ? 'Pending Approval' : status}</span>
				{/* <Tooltip placement="top" title={status}></Tooltip> */}
			</td>
        </tr>
    );
};

export default Deductions;