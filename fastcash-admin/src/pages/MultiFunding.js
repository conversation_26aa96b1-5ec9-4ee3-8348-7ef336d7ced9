/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import Pagination from 'antd/lib/pagination';
import qs from 'querystring';
import $ from 'jquery';

import ListHOC from '../container/ListHOC';
import { httpRequest, baseURL, loanAPI, multiFundsAPI } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import LoanData from '../components/LoanData';
import { notifyDone } from '../actions/general';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 12;

class MultiFunding extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			currentPage: 1,
			totalPage: 0,
			loans: [],
			offices: [],
			office: 0,
			visible: false,
			visibleRepays: false,
			loanID: null,
			amount: '',
			funding: false,
			visibleFunders: false,
		};
	}

	componentDidMount() {
		const query = qs.parse(this.props.location.search.replace('?', ''));
		const page = query && query.p ? parseInt(query.p, 10) : 1;
		this.fetch(page, 0);
	}

	fetch = async (pageNumber, office) => {
		try {
			this.props.startBlock();
			const { lender } = this.props;
			const rs = await httpRequest(`${baseURL}${loanAPI}?page=${pageNumber}&pagesize=${pageSize}&category=multi-fund&q=admin&lender=${lender.id}&office=${office}`, 'GET', true);
            const result = rs.result;
			this.setState({ currentPage: pageNumber, totalPage: result.total, loans: result.data, offices: result.offices });
			this.props.stopBlock();
			this.props.notifyDone(false);
            window.scrollTo(0, 0);
		} catch (e) {
			this.props.stopBlock();
			this.props.doNotify({ message: 'could not load loans', level: 'error' });
		}
	};

	onChange = pageNumber => {
		const { office } = this.state;
		this.fetch(pageNumber, office);
	};

	onSelect = e => {
		$(e.currentTarget).blur();
		const office = e.target.value;
		this.setState({ office: parseInt(office, 10) });
		this.fetch(1, office);
	};

	componentWillUpdate(nextProps, nextState) {
		if (nextProps.notifdone) {
			this.fetch(this.state.currentPage, this.state.office);
		}
	}

	doHide = () => {
		this.setState({ visible: false, visibleRepays: false, visibleFunders: false, loanID: null });
	};

	showFundLoan = (status, loan) => {
		this.setState({ visible: status, loanID: loan.id });
	};

	showRepayments = (status, loan) => {
		this.setState({ visibleRepays: status, loanID: loan.id });
	};

	showFunders = (status, loan) => {
		this.setState({ visibleFunders: status, loanID: loan.id });
	};

	onChangeAmount = e => {
		this.setState({ amount: e.target.value });
	};

	startFundLoan = async (e, loan, amount) => {
		e.preventDefault();
		const { user } = this.props;
		if(parseFloat(amount) > parseFloat(loan.amount)) {
			this.props.doNotify({ message: 'the amount you are funding is above the loan amount', level: 'error' });
			return;
		}
		this.setState({ funding: true });
		try {
			const data = { loan_id: loan.id, lender_id: user.lender_id, amount, user_id: user.id };
			const rs = await httpRequest(`${baseURL}${multiFundsAPI}`, 'POST', true, data);
			this.props.setLoanAnalytics(rs.loan_details);
			this.props.doNotify({ message: 'loan funded', level: 'success' });
			this.setState({ funding: false, visible: false, loanID: null });
			setTimeout(() => {
				this.fetch(this.state.currentPage, this.state.office);
			}, 1000);
		} catch (e) {
			this.setState({ funding: false });
			const message = e.message || 'could not fund loan!';
			this.props.doNotify({ message, level: 'error' });
		}
	};

	stopFundLoan = id => async () => {
		const action = window.confirm('Are you sure you want to remove your funds?') ? true : false;
		if(action) {
			const { user } = this.props;
			try {
				const data = { staff: user.id };
				const rs = await httpRequest(`${baseURL}${multiFundsAPI}/${id}`, 'DELETE', true, data);
				this.props.setLoanAnalytics(rs.loan_details);
				this.props.doNotify({ message: 'loan funding removed', level: 'success' });
				setTimeout(() => {
					this.fetch(this.state.currentPage, this.state.office);
				}, 1000);
			} catch (e) {
				const message = e.message || 'could not remove funding!';
				this.props.doNotify({ message, level: 'error' });
			}
		}
	};

	render() {
		const { currentPage, totalPage, loans, offices, visible, visibleRepays, loanID, amount, funding, visibleFunders } = this.state;
		const { showUserProfile, user, doApproveLoan, openModal, doVerifyLoan, doMultiFund, bypass, verifyPayment, disburse, sendConsentSMS, doSendMail } = this.props;
		return (
			<div className="element-wrapper">
				<h6 className="element-header">Multi-Fund Loans</h6>
				<div className="element-box">
					<div className="row">
						<label className="col-sm-1 col-form-label">Filter by: </label>
						<label className="col-sm-1 col-form-label text-right justify-content-end" htmlFor="">Office: </label>
						<div className="col-sm-3">
							<select name="office" className="form-control" onChange={this.onSelect}>
								<option value="0">All Offices</option>
								{offices.map(o => <option key={o.id} value={o.id}>{o.name}</option>)}
							</select>
						</div>
					</div>
				</div>
				<div className="element-box">
					<div className="table-responsive" style={{overflowX: 'scroll',}}>
						<table className="table table-striped table-lightfont">
							<thead>
								<tr>
									<th/>
									<th>Name of Employee</th>
									<th>IPPIS/Customer ID</th>
									<th>Amount</th>
									<th>Fund Status</th>
									<th className="text-center">Amount Funded</th>
									<th>Repayment</th>
									<th>Tenure</th>
									<th>Loan Consent</th>
									<th>Request Date</th>
									<th>Expiration Date</th>
									<th>&nbsp;</th>
								</tr>
							</thead>
							<tbody>
								{loans.map((loan, i) => (
									<LoanData
										key={i}
										loan={loan}
										user={user}
										showUserProfile={showUserProfile}
										doVerifyLoan={doVerifyLoan}
										bypass={bypass}
										verifyPayment={verifyPayment}
										doApproveLoan={doApproveLoan}
										disburse={disburse}
										openModal={openModal}
										startFundLoan={this.startFundLoan}
										doMultiFund={doMultiFund}
										category="multi_fund"
										visible={visible}
										loanID={loanID}
										amount={amount}
										funding={funding}
										visibleRepays={visibleRepays}
										visibleFunders={visibleFunders}
										doHide={this.doHide}
										showFundLoan={this.showFundLoan}
										onChangeAmount={this.onChangeAmount}
										stopFundLoan={this.stopFundLoan}
										showRepayments={this.showRepayments}
										showFunders={this.showFunders}
										sendConsentSMS={sendConsentSMS}
										doSendMail={doSendMail}
									/>
								))}
							</tbody>
						</table>
					</div>
					<div className="pagination pagination-center mt-4">
						<Pagination
							current={currentPage}
							pageSize={pageSize}
							total={totalPage}
							showTotal={total => `Total ${total} loans`}
							itemRender={itemRender}
							onChange={this.onChange}
						/>
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
		notifdone: state.general.notifdone,
		lender: state.lender.profile,
	}
};

export default ListHOC(connect(mapStateToProps, { startBlock, stopBlock, notifyDone })(MultiFunding));
