<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBlacklistedNumbersTable extends Migration
{
    public function up()
    {
        Schema::create('black_listed_numbers', function (Blueprint $table) {
            $table->id();  
            $table->string('phone_number');
            $table->integer('repeat');
            $table->boolean('isBlacklisted');
            $table->timestamp('dateOfBlackListing')->nullable();
            $table->timestamp('todaysDate')->nullable();
            $table->timestamps();  
        });
    }

    public function down()
    {
        Schema::dropIfExists('black_listed_numbers');
    }
}

