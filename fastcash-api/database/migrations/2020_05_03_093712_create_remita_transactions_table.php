<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateRemitaTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('remita_transactions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->unsigned()->nullable();
            $table->string('customer_id')->nullable();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('employer_name')->nullable();
            $table->string('salary_account')->nullable();
            $table->string('loan_merchant')->nullable();
            $table->string('disbursement_account')->nullable();
            $table->string('repayment_credit_account')->nullable();
            $table->string('bvn')->nullable();
            $table->string('salary_bank_code')->nullable();
            $table->string('disbursement_account_bank_code')->nullable();
            $table->string('repayment_credit_bank_code')->nullable();
            $table->string('repayment_credit_bank_name')->nullable();
            $table->string('collection_start_date')->nullable();
            $table->string('disbursement_date')->nullable();
            $table->string('salary_payment_date')->nullable();
            $table->string('mandate_reference')->nullable();
            $table->string('last_salary_payment_date')->nullable();
            $table->string('transaction_ref')->nullable();
            $table->string('salary_payment_ref')->nullable();
            $table->string('repayment_ref')->nullable();
            $table->string('bank_repayment_ref')->nullable();
            $table->string('repayment_status')->nullable();
            $table->string('repayment_date')->nullable();
            $table->string('status_reason')->nullable();
            $table->decimal('loan_amount', 20,2)->default(0)->nullable();
            $table->decimal('interest_amount', 20,2)->default(0)->nullable();
            $table->decimal('repayment_amount', 20,2)->default(0)->nullable();
            $table->decimal('repayment_collection_fee', 20,2)->default(0)->nullable();
            $table->decimal('last_salary_credit_amount', 20,2)->default(0)->nullable();
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('remita_transactions');
    }
}
