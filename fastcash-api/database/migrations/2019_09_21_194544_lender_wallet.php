<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class LenderWallet extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('wallets', function (Blueprint $table) {
            $table->bigInteger('lender_id')->unsigned()->nullable()->after('amount');
            $table->tinyInteger('approved')->nullable()->after('amount');
            $table->bigInteger('approved_by')->unsigned()->nullable()->after('approved');
            $table->dateTime('approved_at')->nullable()->after('approved_by');

            $table->foreign('lender_id')->references('id')->on('lenders');
            $table->foreign('approved_by')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('wallets', function (Blueprint $table) {
            Schema::disableForeignKeyConstraints();
            $table->dropForeign(['lender_id', 'approved_by']);
            $table->dropColumn(['lender_id', 'approved', 'approved_by', 'approved_at']);
            Schema::enableForeignKeyConstraints();
        });
    }
}
