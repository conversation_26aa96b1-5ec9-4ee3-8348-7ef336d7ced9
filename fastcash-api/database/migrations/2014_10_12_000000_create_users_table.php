<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id('id');
            $table->string('name');
            $table->string('ippis')->nullable();
            $table->string('username')->nullable();
            $table->string('email')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable();
            $table->string('phone');
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->bigInteger('office_id')->nullable();
            $table->string('bvn')->nullable();
            $table->integer('state_of_origin')->nullable();
            $table->integer('lga_of_origin')->nullable();
            $table->string('address1')->nullable();
            $table->string('address2')->nullable();
            $table->string('nok_firstname')->nullable();
            $table->string('nok_lastname')->nullable();
            $table->string('nok_relationship')->nullable();
            $table->string('nok_phone')->nullable();
            $table->string('nok_address1')->nullable();
            $table->string('nok_address2')->nullable();
            $table->bigInteger('payslip_id')->unsigned()->nullable();
            $table->bigInteger('loan_id')->nullable();
            $table->tinyInteger('enabled')->default(1);
            $table->string('platform')->default('ippis');
            $table->string('customer_id')->nullable()->comment('remita customer id');
            $table->rememberToken();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
}
