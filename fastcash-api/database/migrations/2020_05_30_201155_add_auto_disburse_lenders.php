<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddAutoDisburseLenders extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('lenders', function (Blueprint $table) {
            $table->dropColumn(['disbursement_option']);
            $table->tinyInteger('auto_disburse')->default(0)->after('commission');
            $table->integer('merchant_commission')->default(0)->after('auto_disburse');
            $table->integer('min_loan_tenure')->default(1)->after('merchant_commission');
            $table->integer('max_loan_tenure')->default(1)->after('min_loan_tenure');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('lenders', function (Blueprint $table) {
            $table->dropColumn(['auto_disburse', 'merchant_commission', 'min_loan_tenure', 'max_loan_tenure']);
            $table->tinyInteger('disbursement_option')->default(0)->after('next_of_kin');
        });
    }
}
