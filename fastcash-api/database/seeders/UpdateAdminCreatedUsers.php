<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class UpdateAdminCreatedUsers extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $loans = Loan::where('admin_request', 1)->get();
            foreach ($loans as $loan){
                $user = User::find($loan->user_id);
                if($user) {
                    $user->is_admin_created = 1;
                    $user->save();
                }
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }
    }
}
