<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\PaymentDate;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class FixPayment extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $transactions = Transaction::where('channel', 'staff-repayment')->get();
            foreach ($transactions as $transaction) {
                $loan = Loan::where('id', $transaction->loan_id)->first();

                $pays = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)->get();
                if(count($pays) == 0) {
                    Log::debug('loans has been fully repaid: loan_id => '.$loan->id);
                } else {
                    $pay = PaymentDate::where('paid', 0)->where('loan_id', $loan->id)->first();
                    if($pay){
                        $pay->paid = 1;
                        $pay->paid_at = date('Y-m-d H:i:s');
                        $pay->transaction_id = $transaction->id;
                        $pay->save();

                        $month = Carbon::createFromFormat('Y-m-d H:i:s', $pay->payment_date)->format('F');

                        $t = Transaction::find($transaction->id);
                        $t->description = 'Deduction for the Month of '.$month;
                        $t->save();
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error($e->getLine().' '.$e->getMessage().' '.$e->getFile());
        }
    }
}
