import React from 'react';
import moment from 'moment';

import { currency } from '../config/constants';

const RepaymentSchedule = ({ repayments }) => {
	return (
		<div className="row">
			<div className="col-sm-12">
				<div className="element-wrapper">
					<h5 className="element-header">Loan Repayment Schedule</h5>
					<div className="element-box-tp">
						<div className="table-responsive">
							<table className="table table-padded">
								<thead>
									<tr>
										<th>Amount</th>
										<th>Repayment Date</th>
										<th className="text-center">Status</th>
									</tr>
								</thead>
								<tbody>
									{repayments.map((item, i) => {
										return (
											<tr key={i}>
												<td>{currency(item.monthly_deduction)}</td>
												<td>{moment(item.payment_date_approved || item.payment_date).format('DD.MMM.YYYY')}</td>
												<td className="text-center">
													<span className={`badge badge-${item.paid === 0 ? 'warning' : 'success text-white'}`}>
														{item.paid === 0 ? 'Not Paid' : 'Paid'}
													</span>
												</td>
											</tr>
										);
									})}
									{repayments.length === 0 && (
										<tr>
											<td colSpan="5">No repayments available</td>
										</tr>
									)}
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default RepaymentSchedule;
