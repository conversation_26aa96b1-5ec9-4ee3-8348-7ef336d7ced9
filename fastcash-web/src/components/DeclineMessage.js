import React from 'react';

import loading from '../assets/img/loading.gif';

const DeclineMessage = ({ doHide, cancelLoanRequest, cancelReason, otherReason, onChangeOthers, onChangeReason, submitting }) => {
	return (
		<div className="element-box p-3 m-0">
			<form onSubmit={(e) => cancelLoanRequest(e)}>
				<div className="row">
					<div className="col-sm-12">
						<div className="form-group">
							<label>Reasons</label>
							<select
								value={cancelReason}
								onChange={(e) => onChangeReason(e)}
								className="form-control"
							>
								<option value="">Specify a reason</option>
								<option value="Dissatisfied with offer">
									Dissatisfied with offer
								</option>
								<option value="Not interested">Not interested</option>
								<option value="others">Others</option>
							</select>
						</div>
						{cancelReason === 'others' && (
							<div className="form-group">
								<label>Specify</label>
								<textarea
									name="reason"
									placeholder="Reason for decline"
									type="text"
									className="form-control"
									onChange={(e) => onChangeOthers(e)}
									rows="3"
									value={otherReason}
								></textarea>
							</div>
						)}
					</div>
				</div>
				<div className="text-center">
					<button
						className="mr-2 btn btn-primary"
						disabled={submitting}
						type="submit"
					>
						{submitting ? <img src={loading} alt="" /> : 'Proceed'}
					</button>
					<button className="btn btn-default" type="button" onClick={doHide}>
						Cancel
					</button>
				</div>
			</form>
		</div>
	);
};

export default DeclineMessage;
