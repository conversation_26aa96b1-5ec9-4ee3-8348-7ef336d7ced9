<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateEarningsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('earnings', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('ippis')->nullable()->index('ippis');
            $table->string('customer_id')->nullable()->index('customer_id');
            $table->integer('month')->nullable();
            $table->integer('year')->nullable();
            $table->decimal('gross_earning', 50, 2)->default('0.00');
            $table->decimal('gross_deduction', 50, 2)->default('0.00');
            $table->decimal('net_earning', 50, 2)->default('0.00');
            $table->string('platform')->default('ippis');
            $table->string('auth_code')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('earnings');
    }
}
