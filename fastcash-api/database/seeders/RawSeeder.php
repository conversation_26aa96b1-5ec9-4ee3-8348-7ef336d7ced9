<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RawSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('ALTER TABLE `users` CHANGE `state_of_origin` `state_of_origin` INT(11) UNSIGNED NULL DEFAULT NULL');
        DB::statement('ALTER TABLE `users` CHANGE `lga_of_origin` `lga_of_origin` INT(11) UNSIGNED NULL DEFAULT NULL');
        DB::statement('ALTER TABLE `users` CHANGE `office_id` `office_id` BIGINT(20) UNSIGNED NULL DEFAULT NULL');
        DB::statement('ALTER TABLE `users` CHANGE `loan_id` `loan_id` BIGINT(20) UNSIGNED NULL DEFAULT NULL');
    }
}
