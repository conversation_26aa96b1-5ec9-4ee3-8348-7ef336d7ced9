import React, { Component } from 'react';
import { Redirect } from 'react-router-dom';
import { connect } from 'react-redux';

import { signOut } from '../actions/user';
import { authKey, settingKey, lenderKey } from '../config/constants';

class Logout extends Component {
    componentWillMount() {
        localStorage.removeItem(authKey);
        localStorage.removeItem(settingKey);
        localStorage.removeItem(lenderKey);
        this.props.signOut();
    }
    
    render() {
        return (
            <Redirect to='/' />
        );
    }
}

export default connect(null, { signOut })(Logout);