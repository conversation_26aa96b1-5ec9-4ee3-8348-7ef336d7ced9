<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\PaymentDate;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class UpdatePaymentSchedule extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $loans = Loan::whereNotNull('start_date')->whereNotNull('end_date')->get();
            foreach ($loans as $loan) {
                $start_date = Carbon::createFromFormat('Y-m-d H:i:s', $loan->start_date);
                $end_date = Carbon::createFromFormat('Y-m-d H:i:s', $loan->end_date);

                $dates = monthsBtwDates($start_date, $end_date, true);

                $transactions = Transaction::where('loan_id', $loan->id)->where('transaction_flag', 'deduction')->get();

                for ($i=0; $i<count($dates); $i++) {
                    $schedule = PaymentDate::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'start_date' => $loan->start_date,
                        'end_date' => $loan->end_date,
                        'tenure' => $loan->tenure,
                        'monthly_deduction' => $loan->monthly_deduction,
                        'transaction_id' => isset($transactions[$i]) ? $transactions[$i]->id : NULL,
                        'payment_date' => $dates[$i],
                        'paid' => isset($transactions[$i]) ? 1 : 0,
                        'paid_at' => isset($transactions[$i]) ? $transactions[$i]->created_at : NULL,
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::error($e->getLine().' '.$e->getMessage().' '.$e->getFile());
        }
    }
}
