import * as types from '../actions/types';

const INITIAL_STATE = {
	profile: null,
	edit: null,
	list: [],
};

const lender = (state = INITIAL_STATE, action) => {
	switch (action.type) {
		case types.LOAD_LENDERS:
			return { ...state, list: [...action.payload] };
		case types.ADD_LENDER:
			return { ...state, list: [...state.list, { id: action.payload.id, name: action.payload.name }] };
		case types.SET_LENDER_PROFILE:
			return { ...state, profile: action.payload };
		case types.EDIT_LENDER_PROFILE:
			return { ...state, edit: action.payload };
		case types.SIGN_OUT:
			return { ...state, ...INITIAL_STATE };
		default:
			return state;
	}
};

export default lender;
