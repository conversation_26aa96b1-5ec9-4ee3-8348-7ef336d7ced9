<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\PaymentDate;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateLiquidatedLoans extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::beginTransaction();
        try {
            $loans = Loan::where('liquidated', 0)->where('disbursed', 1)->get();
            foreach ($loans as $item) {
                $loan = Loan::find($item->id);
                $payments = PaymentDate::where('loan_id', $item->id)->where('paid', 0)->count();
                if($loan && $payments == 0) {
                    info('loan worked on: '.$loan->id);
                    $liquidated = liquidateLoan($loan->id);
                }
            }

            DB::commit();

        } catch (\Exception $e){
            DB::rollBack();
            Log::error($e->getMessage());
        }
    }
}
