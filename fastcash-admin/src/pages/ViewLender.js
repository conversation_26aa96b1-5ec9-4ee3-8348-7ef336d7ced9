import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import BreadCrumbs from '../components/BreadCrumbs';
import Loading from '../components/Loading';
import { baseURL, lenderAPI, httpRequest } from '../config/constants';
import { doNotify } from '../actions/general';
import { approveDeposit, declineDeposit } from '../actions/lender';
import { startBlock, stopBlock } from '../actions/ui-block';
import { setLoanAnalytics } from '../actions/loan';
import LenderTransactions from '../components/LenderTransactions';
import LenderDetails from '../components/LenderDetails';

class ViewLender extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			lender: null,
			fetching: true,
		};
	}

	componentDidMount() {
		const { match, lenders } = this.props;
		const lender = lenders.find(l => l.id === parseInt(match.params.id, 10));
		this.setState({ lender });
		this.fetchLender(lender);
	}

	fetchLender = async lender => {
		this.setState({ fetching: true });
		try {
			const rs = await httpRequest(`${baseURL}${lenderAPI}/${lender.id}`, 'GET', true);
			this.setState({
				fetching: false,
				lender: { ...this.state.lender, ...rs.lender },
			});
		} catch (error) {
			const message = error.message || 'error, failed loading lender profile';
			this.props.doNotify({ message, level: 'error', title: '' });
			this.setState({ fetching: false });
		}
	};

	approve = id => () => {
		this.props.startBlock();
		const { user } = this.props;
		const data = { staff: user.id, id };
		this.props
			.approveDeposit(data)
			.then(rs => {
				if (rs.wallet.lender_id === user.lender_id) {
					this.props.setLoanAnalytics(rs.details);
				}
				this.props.doNotify({ message: 'deposit approved!', level: 'success', title: '' });
				this.fetchLender(this.state.lender);
				this.props.stopBlock();
			})
			.catch(error => {
				this.props.stopBlock();
				const message = error.message || 'error please try again';
				this.props.doNotify({ message, level: 'error', title: '' });
			});
	};

	decline = id => () => {
		const action = window.confirm('Are you sure you want to decline transaction?') ? true : false;
		if (action) {
			this.props.startBlock();
			const { user } = this.props;
			const data = { staff: user.id, id };
			this.props
				.declineDeposit(data)
				.then(rs => {
					if (rs.wallet.lender_id === user.lender_id) {
						this.props.setLoanAnalytics(rs.details);
					}
					this.props.doNotify({ message: 'deposit declined!', level: 'success', title: '' });
					this.fetchLender(this.state.lender);
					this.props.stopBlock();
				})
				.catch(error => {
					this.props.stopBlock();
					const message = error.message || 'error please try again';
					this.props.doNotify({ message, level: 'error', title: '' });
				});
		}
	};

	render() {
		const { fetching, lender } = this.state;
		const { user } = this.props;
		return (
			<div>
				{user && user.role_name === 'super' && <BreadCrumbs url={`/lenders`} />}
				<div className="content-i">
					<div className="content-box">
						<div className="row">
							<div className="col-sm-5">
								{lender && (
									<LenderDetails
										fetching={fetching}
										lender={lender}
									/>
								)}
							</div>
							<div className="col-sm-7">
								{lender && (
									<div className="element-wrapper">
										<div className="element-actions">
											<Link
												to="/settings?category=withdraw"
												className="btn btn-info btn-sm cursor"
												style={{ color: '#ffffff' }}>
												<i className="os-icon os-icon-cancel-circle" />
												<span>Make Withdrawal</span>
											</Link>
											<Link
												to="/settings?category=deposit"
												className="btn btn-primary btn-sm cursor"
												style={{ color: '#ffffff' }}>
												<i className="os-icon os-icon-ui-22" />
												<span>Deposit Capital</span>
											</Link>
										</div>
										<h6 className="element-header">Transactions</h6>
										<div className="element-box" style={{padding: '1.5rem  10px'}}>
											{fetching && <Loading />}
											{!fetching && (
												<LenderTransactions
													user={user}
													lender={lender}
													approve={this.approve}
													decline={this.decline}
												/>
											)}
										</div>
									</div>
								)}
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		lenders: state.lender.list,
		user: state.user.user,
	};
};

export default connect(
	mapStateToProps,
	{ doNotify, approveDeposit, startBlock, stopBlock, setLoanAnalytics, declineDeposit },
)(ViewLender);
