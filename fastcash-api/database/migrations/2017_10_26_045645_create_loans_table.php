<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLoansTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loans', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('user_id');
            $table->bigInteger('office_id')->nullable();
            $table->integer('accountno_id');
            $table->decimal('amount', 50, 2);
            $table->decimal('monthly_deduction', 50, 2);
            $table->decimal('total_deduction', 50, 2);
            $table->integer('tenure');
            $table->decimal('net_earning', 20, 2)->default('0.00');
            $table->integer('interest_rate')->default(0);
            $table->string('merchant_code')->nullable();
            $table->tinyInteger('topup')->default(0);
            $table->tinyInteger('liquidated')->default(0);
            $table->date('liquidated_at')->nullable();
            $table->string('transaction_data')->nullable();
            $table->string('transaction_tx')->nullable();
            $table->tinyInteger('status')->default(0);

            $table->decimal('monthly_principal', 20, 2)->default('0.00');
            $table->decimal('monthly_interest', 20, 2)->default('0.00');
            $table->dateTime('end_date')->nullable();
            $table->dateTime('topup_date')->nullable();

            $table->tinyInteger('verified')->default(0);
            $table->bigInteger('verified_by')->nullable();
            $table->dateTime('verified_at')->nullable();
            
            $table->tinyInteger('approved')->default(0);
            $table->bigInteger('approved_by')->nullable();
            $table->dateTime('approved_at')->nullable();
            $table->text('approved_remita')->nullable()->comment('remita loan notification response');
            $table->text('disbursement')->nullable()->comment('remita loan disburment response');
            $table->text('payment_status')->nullable()->comment('remita loan disburment payment status');

            $table->text('net_earnings')->comment('earnings used in json format');
            $table->string('auth_code')->nullable();
            $table->string('platform');

            $table->string('oletter')->nullable();
            $table->bigInteger('deleted_by')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loans');
    }
}
