<?php

namespace App\Http\Controllers\Api\V1;

use App\Exports\ExportLoan;
use App\Models\Lender;
use App\Models\Loan;
use App\Models\Merchant;
use App\Models\Office;
use App\Models\RemitaTransaction;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Wallet;
use Log;
use Maatwebsite\Excel\Facades\Excel;

class ReportController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @param string $slug
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, $slug)
    {
        $lender = $request->has('lender') && $request->input('lender') != '' ? $request->input('lender') : 0;
        $limit = $request->has('pagesize') && $request->input('pagesize') != '' ? $request->input('pagesize') : 10;
        $page = $request->has('page') && $request->input('page') != '' ? $request->input('page') : 1;
        $skip = ($page - 1) * $limit;

        $from = $request->has('from') && $request->input('from') != '' ? $request->input('from') : '';
        $to = $request->has('to') && $request->input('to') != '' ? $request->input('to') : '';

        try {
            $report = null;

            switch ($slug) {
                case 'repayment':
                    $report = $this->fetchRepayment($lender, $from, $to, $limit, $skip);
                    break;
                case 'loan':
                    $report = $this->fetchLoans($lender, $from, $to, $limit, $skip);
                    break;
                case 'disbursed-loan':
                    $report = $this->fetchDisbursedLoans($lender, $from, $to, $limit, $skip);
                    break;
                case 'wallet':
                    $report = $this->fetchWallets($lender, $from, $to, $limit, $skip);
                    break;
                case 'credit-wallet':
                    $report = $this->fetchCreditWallet($lender, $from, $to, $limit, $skip);
                    break;
                default:
                    break;
            }

            if ($report) {
                $result = (object)null;
                $result->total = $report->count;
                $result->size = $limit;
                $result->page = $page;
                $result->data = $report->data;
                $result->skip = $skip;

                return response()->json(compact('result'), 200);
            }

            return response()->json(['error' => 'could not fetch ' . $slug . ' report'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not fetch ' . $slug . ' report'], 500);
        }
    }

    private function fetchRepayment($lender, $from, $to, $limit = 0, $skip = 0)
    {
        $data = [];
        $count = 0;

        $_from = $from == '' ? '' : Carbon::createFromFormat('d-m-Y', $from)->toDateTimeString();
        $_to = $to == '' ? '' : Carbon::createFromFormat('d-m-Y', $to)->toDateTimeString();

        if ($limit == 0) {
            if ($from == '' && $to == '') {
                if ($lender == 0) {
                    $transactions = Transaction::where('approved', 1)->where(function ($query) {
                        $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                    })->orderBy('created_at', 'desc')->get(['id', 'loan_id', 'user_id', 'office_id', 'repayment_reference', 'amount', 'repayment_date', 'lender_id', 'remita_transaction_id', 'repayment_source', 'created_at', 'principal', 'interest']);
                } else {
                    $transactions = Transaction::where('approved', 1)->where(function ($query) {
                        $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                    })->where('lender_id', $lender)->orderBy('created_at', 'desc')->get(['id', 'loan_id', 'user_id', 'office_id', 'repayment_reference', 'amount', 'repayment_date', 'lender_id', 'remita_transaction_id', 'repayment_source', 'created_at', 'principal', 'interest']);
                }
            } else {
                if ($lender == 0) {
                    $transactions = Transaction::where('approved', 1)->where(function ($query) {
                        $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                    })->whereBetween('repayment_date', [$_from, $_to])->orderBy('created_at', 'desc')->get(['id', 'loan_id', 'user_id', 'office_id', 'repayment_reference', 'amount', 'repayment_date', 'lender_id', 'remita_transaction_id', 'repayment_source', 'created_at', 'principal', 'interest']);
                } else {
                    $transactions = Transaction::where('approved', 1)->where(function ($query) {
                        $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                    })->whereBetween('repayment_date', [$_from, $_to])->where('lender_id', $lender)->orderBy('created_at', 'desc')->get(['id', 'loan_id', 'user_id', 'office_id', 'repayment_reference', 'amount', 'repayment_date', 'lender_id', 'remita_transaction_id', 'repayment_source', 'created_at', 'principal', 'interest']);
                }
            }
        } else {
            if ($from == '' && $to == '') {
                if ($lender == 0) {
                    $transactions = Transaction::where('approved', 1)->where(function ($query) {
                        $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                    })->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get(['id', 'loan_id', 'user_id', 'office_id', 'repayment_reference', 'amount', 'repayment_date', 'lender_id', 'remita_transaction_id', 'repayment_source', 'created_at', 'principal', 'interest']);

                    $count = Transaction::where('approved', 1)->where(function ($query) {
                        $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                    })->count();
                } else {
                    $transactions = Transaction::where('approved', 1)->where(function ($query) {
                        $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                    })->where('lender_id', $lender)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get(['id', 'loan_id', 'user_id', 'office_id', 'repayment_reference', 'amount', 'repayment_date', 'lender_id', 'remita_transaction_id', 'repayment_source', 'created_at', 'principal', 'interest']);

                    $count = Transaction::where('approved', 1)->where(function ($query) {
                        $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                    })->where('lender_id', $lender)->count();
                }
            } else {
                if ($lender == 0) {
                    $transactions = Transaction::where('approved', 1)->where(function ($query) {
                        $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                    })->whereBetween('repayment_date', [$_from, $_to])->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get(['id', 'loan_id', 'user_id', 'office_id', 'repayment_reference', 'amount', 'repayment_date', 'lender_id', 'remita_transaction_id', 'repayment_source', 'created_at', 'principal', 'interest']);

                    $count = Transaction::where('approved', 1)->where(function ($query) {
                        $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                    })->whereBetween('repayment_date', [$_from, $_to])->count();
                } else {
                    $transactions = Transaction::where('approved', 1)->where(function ($query) {
                        $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                    })->whereBetween('repayment_date', [$_from, $_to])->where('lender_id', $lender)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get(['id', 'loan_id', 'user_id', 'office_id', 'repayment_reference', 'amount', 'repayment_date', 'lender_id', 'remita_transaction_id', 'repayment_source', 'created_at', 'principal', 'interest']);

                    $count = Transaction::where('approved', 1)->where(function ($query) {
                        $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                    })->whereBetween('repayment_date', [$_from, $_to])->where('lender_id', $lender)->count();
                }
            }
        }

        $i = 0;
        foreach ($transactions as $transaction) {
            $i++;

            $loan = Loan::where('id', $transaction->loan_id)->first();
            $user = User::where('id', $transaction->user_id)->first(['ippis', 'name', 'phone', 'employer', 'core_bank_id']);
            $office = Office::where('id', $transaction->office_id)->first(['name']);
            $lender = Lender::where('id', $transaction->lender_id)->first(['name']);

            if ($office) {
                $office_name = $office->name;
                if ($office->name == 'Remita') {
                    $remita = RemitaTransaction::where('id', $transaction->remita_transaction_id)->first();
                    if ($remita) {
                        $office_name = $remita->employer_name;
                    } else {
                        $office_name = $user->employer;
                    }
                }
            } else {
                $office_name = $user->employer;
            }

            $approved = json_decode($loan->approved_remita);
            $mandate_ref = $approved == null ? '-' : $approved->data->mandateReference;

            $item = (object)null;
            $item->sn = $i;
            $item->name = $user->name;
            $item->core_bank_id = $user->core_bank_id;
            $item->ippis = $user->ippis;
            $item->phone = $user->phone;
            $item->employer = $office_name;
            $item->mandate_ref = $mandate_ref;
            $item->loan_id = $transaction->loan_id;
            $item->lender = $lender->name;
            $item->amount = $loan->amount;
            $item->disburse_amount = $loan->disburse_amount;
            $item->is_topup = $loan->is_topup;
            $item->tenure = $loan->tenure;
            $item->monthly_principal = $loan->monthly_principal;
            $item->monthly_interest = $loan->monthly_interest;
            $item->disbursed_date = $loan->disbursed_at;
            $item->repayment_date = $transaction->repayment_date;
            $item->repayment_amount = $transaction->amount;
            $item->repayment_principal = $transaction->principal;
            $item->repayment_interest = $transaction->interest;
            $item->repayment_source = $transaction->repayment_source;
            $item->repayment_reference = $transaction->repayment_reference;
            $item->status = $loan->liquidate_approve == 0 ? 'Active' : 'Liquidated';
            $item->loan_application_method = ($loan->method ?? '') != '' ? $loan->method : 'Web';
            $item->acceptance_method = $loan->has_consented == 1 ? 'OTP' : '-';
            $item->acceptance_date = $loan->has_consented == 1 ? Carbon::createFromFormat('Y-m-d H:i:s', $loan->consented_at)->format('d-M-Y h:ia') : '-';

            $data[] = $item;
        }

        $result = (object)null;
        $result->count = $count;
        $result->data = $data;

        return $result;
    }

    private function fetchLoans($lender, $from, $to, $limit = 0, $skip = 0)
    {
        $data = [];
        $count = 0;

        $_from = $from == '' ? '' : Carbon::createFromFormat('d-m-Y', $from)->toDateTimeString();
        $_to = $to == '' ? '' : Carbon::createFromFormat('d-m-Y', $to)->toDateTimeString();

        if ($limit == 0) {
            if ($from == '' && $to == '') {
                if ($lender == '0') {
                    $loans = Loan::where('status', 1)->where('disbursed', 1)->orderBy('created_at', 'desc')->get();
                } else {
                    $loans = Loan::where('status', 1)->where('disbursed', 1)->where('lender_id', $lender)->orderBy('created_at', 'desc')->get();
                }
            } else {
                if ($lender == '0') {
                    $loans = Loan::where('status', 1)->where('disbursed', 1)->whereBetween('created_at', [$_from, $_to])->orderBy('created_at', 'desc')->get();
                } else {
                    $loans = Loan::where('status', 1)->where('disbursed', 1)->where('lender_id', $lender)->whereBetween('created_at', [$_from, $_to])->orderBy('created_at', 'desc')->get();
                }
            }
        } else {
            if ($from == '' && $to == '') {
                if ($lender == '0') {
                    $loans = Loan::where('status', 1)->where('disbursed', 1)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();

                    $count = Loan::where('status', 1)->where('disbursed', 1)->count();
                } else {
                    $loans = Loan::where('status', 1)->where('disbursed', 1)->where('lender_id', $lender)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();

                    $count = Loan::where('status', 1)->where('disbursed', 1)->where('lender_id', $lender)->count();
                }
            } else {
                if ($lender == '0') {
                    $loans = Loan::where('status', 1)->where('disbursed', 1)->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();

                    $count = Loan::where('status', 1)->where('disbursed', 1)->whereBetween('created_at', [$_from, $_to])->count();
                } else {
                    $loans = Loan::where('status', 1)->where('disbursed', 1)->where('lender_id', $lender)->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();

                    $count = Loan::where('status', 1)->where('disbursed', 1)->where('lender_id', $lender)->whereBetween('created_at', [$_from, $_to])->count();
                }
            }
        }

        $i = 0;
        foreach ($loans as $loan) {
            $i++;

            $user = User::where('id', $loan->user_id)->first(['ippis', 'name', 'phone', 'office_id', 'employer', 'lender_id', 'core_bank_id']);
            $office = Office::where('id', $user->office_id)->first(['name']);
            $lender = Lender::where('id', $user->lender_id)->first(['name']);

            if ($office) {
                $office_name = $office->name;
                if ($office->name == 'Remita') {
                    $office_name = $user->employer;
                }
            } else {
                $office_name = $user->employer;
            }

            $approved = json_decode($loan->approved_remita);
            $mandate_ref = $approved == null ? '-' : $approved->data->mandateReference;

            $item = (object)null;
            $item->sn = $i;
            $item->name = $user->name;
            $item->core_bank_id = $user->core_bank_id;
            $item->ippis = $user->ippis;
            $item->phone = $user->phone;
            $item->employer = $office_name;
            $item->mandate_ref = $mandate_ref;
            $item->loan_id = $loan->id;
            $item->lender = $lender->name;
            $item->amount = $loan->amount;
            $item->disburse_amount = $loan->disburse_amount;
            $item->is_topup = $loan->is_topup;
            $item->tenure = $loan->tenure;
            $item->monthly_principal = $loan->monthly_principal;
            $item->monthly_interest = $loan->monthly_interest;
            $item->request_date = Carbon::parse($loan->created_at)->format('Y-m-d H:i:s');
            $item->approved_date = $loan->approved_at;
            $item->disbursed_date = $loan->disbursed_at;
            $item->status = $loan->liquidate_approve == 0 ? 'Active' : 'Liquidated';
            $item->loan_application_method = ($loan->method ?? '') != '' ? $loan->method : 'Web';
            $item->acceptance_method = $loan->has_consented == 1 ? 'OTP' : '-';
            $item->acceptance_date = $loan->has_consented == 1 ? Carbon::createFromFormat('Y-m-d H:i:s', $loan->consented_at)->format('d-M-Y h:ia') : '-';

            $data[] = $item;
        }

        $result = (object)null;
        $result->count = $count;
        $result->data = $data;

        return $result;
    }

    private function fetchDisbursedLoans($lender, $from, $to, $limit = 0, $skip = 0)
    {
        $data = [];
        $count = 0;

        $_from = $from == '' ? '' : Carbon::createFromFormat('d-m-Y', $from)->toDateTimeString();
        $_to = $to == '' ? '' : Carbon::createFromFormat('d-m-Y', $to)->toDateTimeString();

        if ($limit == 0) {
            if ($from == '' && $to == '') {
                if ($lender == '0') {
                    $loans = Loan::where('disbursed', 1)->orderBy('created_at', 'desc')->get();
                } else {
                    $loans = Loan::where('disbursed', 1)->where('lender_id', $lender)->orderBy('created_at', 'desc')->get();
                }
            } else {
                if ($lender == '0') {
                    $loans = Loan::where('disbursed', 1)->whereBetween('created_at', [$_from, $_to])->orderBy('created_at', 'desc')->get();
                } else {
                    $loans = Loan::where('disbursed', 1)->where('lender_id', $lender)->whereBetween('created_at', [$_from, $_to])->orderBy('created_at', 'desc')->get();
                }
            }
        } else {
            if ($from == '' && $to == '') {
                if ($lender == '0') {
                    $loans = Loan::where('disbursed', 1)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();

                    $count = Loan::where('disbursed', 1)->count();
                } else {
                    $loans = Loan::where('disbursed', 1)->where('lender_id', $lender)->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();

                    $count = Loan::where('disbursed', 1)->where('lender_id', $lender)->count();
                }
            } else {
                if ($lender == '0') {
                    $loans = Loan::where('disbursed', 1)->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();

                    $count = Loan::where('disbursed', 1)->whereBetween('created_at', [$_from, $_to])->count();
                } else {
                    $loans = Loan::where('disbursed', 1)->where('lender_id', $lender)->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();

                    $count = Loan::where('disbursed', 1)->where('lender_id', $lender)->whereBetween('created_at', [$_from, $_to])->count();
                }
            }
        }

        $i = 0;
        foreach ($loans as $loan) {
            $i++;

            $user = User::where('id', $loan->user_id)->first(['ippis', 'name', 'phone', 'office_id', 'employer', 'lender_id', 'core_bank_id']);
            $office = Office::where('id', $user->office_id)->first(['name']);
            $lender = Lender::where('id', $user->lender_id)->first(['name']);

            if ($office) {
                $office_name = $office->name;
                if ($office->name == 'Remita') {
                    $office_name = $user->employer;
                }
            } else {
                $office_name = $user->employer;
            }

            $approved = json_decode($loan->approved_remita);
            $mandate_ref = $approved == null ? '-' : $approved->data->mandateReference;

            $item = (object)null;
            $item->sn = $i;
            $item->name = $user->name;
            $item->core_bank_id = $user->core_bank_id;
            $item->ippis = $user->ippis;
            $item->phone = $user->phone;
            $item->employer = $office_name;
            $item->mandate_ref = $mandate_ref;
            $item->loan_id = $loan->id;
            $item->lender = $lender->name;
            $item->amount = $loan->amount;
            $item->disburse_amount = $loan->disburse_amount;
            $item->is_topup = $loan->is_topup;
            $item->tenure = $loan->tenure;
            $item->monthly_principal = $loan->monthly_principal;
            $item->monthly_interest = $loan->monthly_interest;
            $item->request_date = Carbon::parse($loan->created_at)->format('Y-m-d H:i:s');
            $item->approved_date = $loan->approved_at;
            $item->disbursed_date = $loan->disbursed_at;
            $item->start_date = Carbon::parse($loan->start_date)->format('Y-m-d H:i:s');
            $item->end_date = Carbon::parse($loan->end_date)->format('Y-m-d H:i:s');
            $item->platform = $loan->platform;
            $item->status = $loan->liquidate_approve == 0 ? 'Active' : 'Liquidated';
            $item->loan_application_method = ($loan->method ?? '') != '' ? $loan->method : 'Web';
            $item->acceptance_method = $loan->has_consented == 1 ? 'OTP' : '-';
            $item->acceptance_date = $loan->has_consented == 1 ? Carbon::createFromFormat('Y-m-d H:i:s', $loan->consented_at)->format('d-M-Y h:ia') : '-';

            $data[] = $item;
        }

        $result = (object)null;
        $result->count = $count;
        $result->data = $data;

        return $result;
    }

    private function fetchUsers($lender, $from, $to, $limit = 0, $skip = 0)
    {
        $data = [];
        $count = 0;

        $users = User::get(['id', 'name', 'ippis', 'office_id', 'phone', 'lender_id', 'email', 'address1', 'core_bank_id', 'loan_id']);

        $i = 0;
        foreach ($users as $user) {
            $i++;

            $office = Office::where('id', $user->office_id)->first(['name']);
            $lender = Lender::where('id', $user->lender_id)->first(['name']);

            if ($office) {
                $office_name = $office->name;
                if ($office->name == 'Remita') {
                    $office_name = $user->employer;
                }
            } else {
                $office_name = $user->employer;
            }

            $merchant = Merchant::where('user_id', $user->id)->first();
            $current = Loan::where('id', $user->loan_id)->first();

            $loans_taken = Loan::where('user_id', $user->id)->count();

            $item = (object)null;
            $item->sn = $i;
            $item->employee_name = $user->name;
            $item->employer = $office_name;
            $item->lender = $lender->name;
            $item->ippis_number = $user->ippis;
            $item->phone_number = $user->phone;
            $item->email = $user->email;
            $item->address = $user->address1;
            $item->corebank_id = $user->core_bank_id;
            $item->merchant = $merchant ? $merchant->merchant_code : '-';
            $item->number_of_loans_taken = $loans_taken;
            $item->current_loan_status = $current ? 'Has Loan' : 'No Loan';
            $item->loan_request_date = $current ? Carbon::parse($user->created_at)->format('Y-m-d H:i:s') : '-';
            $item->loan_amount = $current ? $current->amount : '-';
            $item->loan_source = $current ? $current->platform : '-';
            $item->disbursement_status = $current ? ($current->disbursed === 1 ? 'Disbursed' : 'Not Disbursed') : '-';
            $item->loan_start_date = $current ? Carbon::parse($user->start_date)->format('Y-m-d H:i:s') : '-';
            $item->loan_end_date = $current ? Carbon::parse($user->end_date)->format('Y-m-d H:i:s') : '-';
            $item->tenure = $current ? $current->tenure : '-';
            $item->repayment_amount = $current ? $current->monthly_deduction : '-';
            $item->monthly_principal = $current ? $current->monthly_principal : '-';
            $item->monthly_interest = $current ? $current->monthly_interest : '-';
            $item->wallet_balance = Wallet::where('user_id', $user->id)->where('approved', 1)->where('is_lender', 0)->where('paid', 1)->sum('amount');
            $item->loan_application_method = ($current->method ?? '') != '' ? $current->method : 'Web';
            $item->acceptance_method = ($current->has_consented ?? '') == 1 ? 'OTP' : '-';
            $item->acceptance_date = ($current->has_consented ?? '') == 1 ? Carbon::createFromFormat('Y-m-d H:i:s', $current->consented_at)->format('d-M-Y h:ia') : '-';

            $data[] = $item;
        }

        $result = (object)null;
        $result->count = $count;
        $result->data = $data;

        return $result;
    }

    private function fetchWallets($lender, $from, $to, $limit = 0, $skip = 0)
    {
        $data = [];

        $_from = $from == '' ? '' : Carbon::createFromFormat('d-m-Y', $from)->toDateTimeString();
        $_to = $to == '' ? '' : Carbon::createFromFormat('d-m-Y', $to)->toDateTimeString();

        $query = Wallet::query()->where('approved', 1)->where('is_lender', 0);

        if ($from != '' && $to != '') {
            $query->whereBetween('created_at', [$_from, $_to]);
        }

        if ($lender != '0') {
            $query->where('lender_id', $lender);
        }

        $count = $query->count();

        if ($limit != 0) {
            $query->skip($skip)->take($limit);
        }

        $wallets = $query->orderBy('created_at', 'desc')->get();

        $i = 0;
        foreach ($wallets as $wallet) {
            $i++;

            $_lender = Lender::where('id', $wallet->lender_id)->first(['id', 'name']);

            $transaction = $wallet->transaction_id ? Transaction::where('id', $wallet->transaction_id)->first(['repayment_date', 'repayment_source', 'repayment_reference', 'amount', 'principal', 'interest', 'created_at', 'description']) : null;

            $user = User::where('id', $wallet->user_id)->first(['ippis', 'name', 'phone', 'core_bank_id', 'employer']);

            $loan = $wallet->loan_id ? Loan::where('id', $wallet->loan_id)->first() : null;

            $mandate_ref = $loan ? json_decode($loan->approved_remita) : null;

            $item = (object)null;
            $item->sn = $i;
            $item->name = $user->name;
            $item->core_bank_id = $user->core_bank_id;
            $item->ippis = $user->ippis;
            $item->phone = $user->phone;
            $item->employer = $user->employer;
            $item->mandate_ref = $mandate_ref ? $mandate_ref->data->mandateReference : '';
            $item->loan_id = $loan->id ?? '';
            $item->loan_amount = $loan->amount ?? '';
            $item->disburse_amount = $loan->disburse_amount ?? '';
            $item->is_topup = $loan->is_topup ?? '';
            $item->tenure = $loan->tenure ?? '';
            $item->disburse_date = $loan->disbursed_at ?? '';
            $item->monthly_principal = $loan->monthly_principal ?? '';
            $item->monthly_interest = $loan->monthly_interest ?? '';
            $item->liquidated = $loan->liquidated ?? '';
            $item->loan_status = $loan->status ?? '';
            $item->lender = $_lender->name;

            $item->repayment_source = $transaction->repayment_source ?? '';
            $item->repayment_date = $transaction->repayment_date ?? '';
            $item->amount = $transaction->amount ?? '';
            $item->source = $transaction->source ?? '';
            $item->repayment_principal = $transaction->principal ?? '';
            $item->repayment_reference = $transaction->repayment_reference ?? '';
            $item->repayment_interest = $transaction->interest ?? '';
            $item->description = $transaction->description ?? '';

            $item->status = $wallet->paid == 1 ? 'Paid' : 'Not Paid';
            $item->transaction_date = Carbon::parse($wallet->created_at)->format('Y-m-d H:i:s');
            $item->type = $wallet->type;
            $item->loan_application_method = ($loan->method ?? '') != '' ? $loan->method : 'Web';
            $item->acceptance_method = ($loan->has_consented ?? '') == 1 ? 'OTP' : '-';
            $item->acceptance_date = ($loan->has_consented ?? '') == 1 ? Carbon::createFromFormat('Y-m-d H:i:s', $loan->consented_at)->format('d-M-Y h:ia') : '-';

            $data[] = $item;
        }

        $result = (object)null;
        $result->count = $count;
        $result->data = $data;

        return $result;
    }

    private function fetchCreditWallet($lender, $from, $to, $limit = 0, $skip = 0)
    {
        $data = [];

        $_from = $from == '' ? '' : Carbon::createFromFormat('d-m-Y', $from)->toDateTimeString();
        $_to = $to == '' ? '' : Carbon::createFromFormat('d-m-Y', $to)->toDateTimeString();

        $query = Transaction::query()->where('approved', 1)->where('channel', 'credit-wallet');

        if ($from != '' && $to != '') {
            $query->whereBetween('repayment_date', [$_from, $_to]);
        }

        if ($lender != 0) {
            $query->where('lender_id', $lender);
        }

        $count = $query->count();

        if ($limit != 0) {
            $query->skip($skip)->take($limit);
        }

        $transactions = $query->orderBy('created_at', 'desc')->get(['id', 'loan_id', 'user_id', 'office_id', 'repayment_reference', 'amount', 'repayment_date', 'lender_id', 'remita_transaction_id', 'repayment_source', 'created_at', 'principal', 'interest']);

        $i = 0;
        foreach ($transactions as $transaction) {
            if ($transaction) {
                $i++;
                info('transaction: ' . json_encode($transaction));

                $loan = Loan::where('id', $transaction->loan_id)->first();
                $user = User::where('id', $transaction->user_id)->first(['ippis', 'name', 'phone', 'employer', 'core_bank_id']);
                $office = Office::where('id', $transaction->office_id)->first(['name']);
                $lender = Lender::where('id', $transaction->lender_id)->first(['name']);


                if ($office) {
                    $office_name = $office->name;
                    if ($office->name == 'Remita') {
                        $remita = RemitaTransaction::where('id', $transaction->remita_transaction_id)->first();
                        if ($remita) {
                            $office_name = $remita->employer_name;
                        } else {
                            $office_name = $user->employer;
                        }
                    }
                } else {
                    $office_name = $user->employer;
                }

                $approved = json_decode($loan->approved_remita);
                $mandate_ref = $approved == null ? '-' : $approved->data->mandateReference;

                $item = (object)null;
                $item->sn = $i;
                $item->name = $user->name;
                $item->core_bank_id = $user->core_bank_id;
                $item->ippis = $user->ippis;
                $item->phone = $user->phone;
                $item->employer = $office_name;
                $item->mandate_ref = $mandate_ref;
                $item->loan_id = $transaction->loan_id;
                $item->lender = $lender->name;
                $item->amount = $loan->amount;
                $item->disburse_amount = $loan->disburse_amount;
                $item->is_topup = $loan->is_topup;
                $item->tenure = $loan->tenure;
                $item->monthly_principal = $loan->monthly_principal;
                $item->monthly_interest = $loan->monthly_interest;
                $item->disbursed_date = $loan->disbursed_at;
                $item->repayment_date = $transaction->repayment_date;
                $item->repayment_amount = $transaction->amount;
                $item->repayment_principal = $transaction->principal;
                $item->repayment_interest = $transaction->interest;
                $item->repayment_source = $transaction->repayment_source;
                $item->repayment_reference = $transaction->repayment_reference;
                $item->status = $loan->liquidate_approve == 0 ? 'Active' : 'Liquidated';
                $item->loan_application_method = ($loan->method ?? '') != '' ? $loan->method : 'Web';
                $item->acceptance_method = $loan->has_consented == 1 ? 'OTP' : '-';
                $item->acceptance_date = $loan->has_consented == 1 ? Carbon::createFromFormat('Y-m-d H:i:s', $loan->consented_at)->format('d-M-Y h:ia') : '-';

                $data[] = $item;
            }
        }

        $result = (object)null;
        $result->count = $count;
        $result->data = $data;

        return $result;
    }

    /**
     * Download remita report.
     *
     * @param \Illuminate\Http\Request $request
     * @param string $slug
     * @return \Illuminate\Http\Response
     */
    public function export(Request $request, $slug)
    {
        $lender = $request->has('lender') && $request->input('lender') != '' ? $request->input('lender') : '0';

        $from = $request->has('from') && $request->input('from') != '' ? $request->input('from') : '';
        $to = $request->has('to') && $request->input('to') != '' ? $request->input('to') : '';

        try {
            $report = null;

            switch ($slug) {
                case 'repayment':
                    $report = $this->fetchRepayment($lender, $from, $to);
                    break;
                case 'loan':
                    $report = $this->fetchLoans($lender, $from, $to);
                    break;
                case 'disbursed-loan':
                    $report = $this->fetchDisbursedLoans($lender, $from, $to);
                    break;
                case 'users-no-loan':
                    $report = $this->fetchUsers($lender, $from, $to);
                    break;
                case 'credit-wallet':
                    $report = $this->fetchCreditWallet($lender, $from, $to);
                    break;
                default:
                    break;
            }

            if ($report) {
                $allData = json_decode(json_encode($report->data), true);

                $report = [];
                foreach ($allData as $item) {
                    $new_report = [];
                    foreach ($item as $key => $value) {
                        $new_report[ucwords(str_replace("_", " ", $key))] = $value;
                        unset($new_report[$key]);
                    }
                    $report[] = $new_report;
                }

                if (count($report) == 0) {
                    return response()->json(['message' => 'no data available'], 200);
                }

                $headings = getHeadings($report[0]);

                $filename = $slug . '-' . time();
                $filepath = 'reports/'.$filename.'.xlsx';

                Excel::store(new ExportLoan($report, $headings), $filepath, 'public');

                $file = env('APP_URL') . '/reports/' . $filename . '.xlsx';
                return response()->json(compact('file'), 200);
            }

            return response()->json(['error' => 'could not export report'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not export ' . $slug . ' report'], 500);
        }
    }
}
