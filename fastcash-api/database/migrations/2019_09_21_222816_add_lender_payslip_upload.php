<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddLenderPayslipUpload extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('payslip_uploads', function (Blueprint $table) {
            $table->bigInteger('lender_id')->unsigned()->nullable()->after('year');

            $table->foreign('lender_id')->references('id')->on('lenders');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('payslip_uploads', function (Blueprint $table) {
            Schema::disableForeignKeyConstraints();
            $table->dropForeign(['lender_id']);
            $table->dropColumn(['lender_id']);
            Schema::enableForeignKeyConstraints();
        });
    }
}
