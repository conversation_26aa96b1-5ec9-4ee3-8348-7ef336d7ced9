/* eslint eqeqeq: 0 */
import fetch from 'isomorphic-fetch';
// eslint-disable-next-line
import { polyfill } from 'es6-promise';
import JwtDecode from "jwt-decode";

export const authKey = 'fcm--token';
export const settingKey = 'fcm--stumps';

const debug = process.env.REACT_APP_DEBUG;
// console.log(debug);
export const rootURL = debug == 'true' ? process.env.REACT_APP_DEBUG_API : process.env.REACT_APP_LIVE_API;

export const baseURL = `${rootURL}/v1`;

export const settingsAPI = '/settings';
export const userAPI = '/users';
export const loginAPI = '/auth/signin-merchant';
export const forgotPasswordAPI = '/auth/forgot-password';
export const resetPasswordAPI = '/auth/merchant/reset-password';

export const getUserID = () => {
    const date = new Date();
    const token = localStorage.getItem(authKey);
    if(token){
        const decoded = JwtDecode(token);
        const userid = decoded.sub;
        if(decoded.exp < date.getTime()){
            return userid;
        }
    }
    return null;
}

const defaultHeaders = {
    Accept: 'application/json',
    'Content-Type': 'application/json',
};

const headers = (auth = false) => {
    if (auth) {
		const jwt = `Bearer ${localStorage.getItem(authKey)}`;
		return { ...defaultHeaders, Authorization: jwt };
	}

	return { ...defaultHeaders };
}

export const checkStatus = async response => {
    if (response.ok) {
        return response;
    } else {
        if(response.statusText=='Unauthorized'){
            localStorage.removeItem(authKey);
            localStorage.removeItem(settingKey);
            window.location.reload(true);
            return;
        }

        const message = await response.text();
		const err = JSON.parse(message);
		throw Object.freeze({ message: err.error });
    }
}

const parseJSON = response => {
    return response.json();
}

export const httpRequest = (url, method, auth, data) => {
    const body = JSON.stringify(data);
    return fetch(url, {
            method: method,
            headers: headers(auth),
            body: body,
        })
        .then(checkStatus)
        .then(parseJSON);
}
