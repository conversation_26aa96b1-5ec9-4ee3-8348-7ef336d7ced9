/* eslint eqeqeq: 0 */
import * as types from '../actions/types';

const INITIAL_STATE = {
	user: null,
	loggedIn: false,
	show_menu: true,
	preloading: true,
};

const user = (state = INITIAL_STATE, action) => {
	switch (action.type) {
		case types.START_PRELOADING:
			return { ...state, preloading: true };
		case types.STOP_PRELOADING:
			return { ...state, preloading: false };
		case types.CHANGE_USER_DATA:
			return { ...state, user: action.user, loggedIn: action.status };
		case types.SIGN_OUT:
			return { ...state, ...INITIAL_STATE, preloading: false };
		case types.TOGGLE_MENU:
			return { ...state, show_menu: action.payload };
		default:
			return state;
	}
};

export default user;
