import React, { Component } from 'react';
import { Link, Switch, Route } from 'react-router-dom';
import Loadable from 'react-loadable';
import { connect } from 'react-redux';
import moment from 'moment';
import truncate from 'lodash.truncate';

import Loading from '../components/Loading';
import BreadCrumbs from '../components/BreadCrumbs';
import { toggleMenu } from '../actions/user';
import { pickMail } from '../actions/mail';

const SendMail = Loadable({ loader: () => import('./SendMail'), loading: Loading });
const SentMail = Loadable({ loader: () => import('./SentMail'), loading: Loading });
const SavedMail = Loadable({ loader: () => import('./SavedMail'), loading: Loading });

class Mail extends Component {
	componentDidMount() {
		this.props.toggleMenu(false);
	}

	componentWillUnmount() {
		this.props.toggleMenu(true);
	}

	selectMail = mail => () => {
		this.props.pickMail(mail);
	}

	render() {
		const { location, match, mails, mail } = this.props;
		const path = location.pathname.split('/').pop();
		const status = path === 'draft' ? 0 : 1;
		return (
			<div>
				<BreadCrumbs url="/dashboard" />
				<div className="content-i">
					<div className="content-box">
						<div className="app-email-w">
							<div className="app-email-i">
								<div className="ae-side-menu">
									<ul className="ae-main-menu">
										<li className={path === 'compose' ? 'active' : ''}>
											<Link to="/mail/compose">
												<i className="os-icon os-icon-phone-21"/>
												<span>Composer</span>
											</Link>
										</li>
										<li className={path === 'sent' ? 'active' : ''}>
											<Link to="/mail/sent">
												<i className="os-icon os-icon-ui-92"/>
												<span>Sent</span>
											</Link>
										</li>
										{/* <li className={path === 'draft' ? 'active' : ''}>
											<Link to="/mail/draft">
												<i className="os-icon os-icon-documents-03"/>
												<span>Draft</span>
											</Link>
										</li> */}
									</ul>
								</div>
								{path !== 'compose' && (
									<div className="ae-list-w">
										<div className="ae-list ps ps--theme_default ps--active-y">
											{mails.filter(m => m.status === status).map((m, i) => {
												return (
													<div className={`ae-item ${(mail && mail.id === m.id) ? 'active' : ''}`} onClick={this.selectMail(m)} key={i}>
														<div className="aei-content">
															<div className="aei-timestamp">{moment(m.created_at).format('D.MMM.YYYY h:ma')}</div>
															<h6 className="aei-title">{m.receiver.name}</h6>
															<div className="aei-sub-title">{m.subject}</div>
															<div className="aei-text">{truncate(m.message_plain, { 'length': 70, })}</div>
														</div>
													</div>
												);
											})}
											{mails.filter(m => m.status === status).length === 0 && (
												<div className="ae-item">
													<div className="aei-content">
														<div className="aei-text">No Mails Found!</div>
													</div>
												</div>
											)}
										</div>
									</div>
								)}
								<div className="ae-content-w">
									<div className="ae-content">
										<Switch>
											<Route path={`${match.url}/compose`} component={SendMail} />
											<Route path={`${match.url}/sent`} component={SentMail} />
											<Route path={`${match.url}/draft`} component={SavedMail} />
										</Switch>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		mails: state.mail.list,
		mail: state.mail.mail,
	}
};

export default connect(
	mapStateToProps,
	{ toggleMenu, pickMail },
)(Mail);
