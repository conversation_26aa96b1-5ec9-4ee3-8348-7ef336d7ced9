import React, { Component } from 'react';
import { Link } from 'react-router-dom';
import $ from 'jquery';

import logo from '../assets/img/fc-logo.png';
import Footer from '../components/Footer';
import ResetPswdForm from '../components/ResetPswdForm';

class ResetPassword extends Component {
    constructor(props) {
		super(props);
        this.toggleMobileMenu = this.toggleMobileMenu.bind(this);
    }

    toggleMobileMenu = (e) => {
        e.preventDefault();
        $(this.refs.mmenu).toggleClass('show-mobile');
    };

    render() {
        return (
            <div className="wrapper-front">
                <div className="desktop-menu menu-top-f menu-activated-on-hover">
                    <div className="menu-top-i os-container">
                        <div className="logo-w">
                            <Link className="logo" to="/">
                                <img alt="logo" src={logo} style={{width:"200px"}}/>
                            </Link>
                        </div>
                        <ul className="main-menu">
                            <li className="active"><Link to="/">Home</Link></li>
                        </ul>
                    </div>
                    <div className="mobile-menu-w">
                        <div className="mobile-menu-holder color-scheme-dark" ref='mmenu'>
                            <ul className="mobile-menu">
                                <li className="active"><Link to="/">Home</Link></li>
                            </ul>
                        </div>
                        <div className="mobile-menu-i">
                            <div className="mobile-logo-w">
                                <Link className="logo" to="/">
                                    <img alt="logo" src={logo} style={{width:"150px"}}/>
                                </Link>
                            </div>
                            <div className="mobile-menu-trigger" onClick={this.toggleMobileMenu}>
                                <i className="os-icon os-icon-hamburger-menu-1"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="intro-w layout-v1" id="sectionIntro">
                    <div className="os-container">
                        <div className="intro-i">
                            <div className="intro-description">
                                <div className="intro-text">Welcome to FastCash. The fastest and most secure Lending platform for employees under the Federal Civil Service of the Federal Republic of Nigeria.
                                </div>
                            </div>
                            <div className="intro-media"/>
                            <ResetPswdForm />
                        </div>
                    </div>
                </div>
                <Footer/>
            </div>
        );
    }
}

export default ResetPassword;