<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddTransactionMultifund extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('multi_funds', function (Blueprint $table) {
            $table->bigInteger('mf_transaction_id')->unsigned()->after('percentage');

            $table->foreign('mf_transaction_id')->references('id')->on('multi_fund_transactions');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('multi_funds', function (Blueprint $table) {
            $table->dropForeign(['mf_transaction_id']);
            $table->dropColumn(['mf_transaction_id']);
        });
    }
}
