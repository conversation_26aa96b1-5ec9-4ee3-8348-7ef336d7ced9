import React from 'react';
import { Link } from 'react-router-dom';

const ReportMenu = () => {
	return (
		<div className="desktop-lmenu menu-side-v2-w menu-activated-on-hover flying-menu">
			<ul className="main-menu">
				<li className="menu-sub-header">
					<span>Reports</span>
				</li>
				<li>
					<Link to="/report/loans">
						<div className="icon-w">
							<div className="os-icon os-icon-delivery-box-2" />
						</div>
						<span>Loans</span>
					</Link>
				</li>
				<li>
					<Link to="/report/maturing-loans">
						<div className="icon-w">
							<div className="os-icon os-icon-delivery-box-2" />
						</div>
						<span>Maturing Loans</span>
					</Link>
				</li>
				<li>
					<Link to="/report/maturing-loans-defaulting">
						<div className="icon-w">
							<div className="os-icon os-icon-delivery-box-2" />
						</div>
						<span>Maturing Loans (Defaulting)</span>
					</Link>
				</li>
				<li>
					<Link to="/report/loan-due">
						<div className="icon-w">
							<div className="os-icon os-icon-delivery-box-2" />
						</div>
						<span>Loan Due</span>
					</Link>
				</li>
				<li>
					<Link to="/report/loan-balance">
						<div className="icon-w">
							<div className="os-icon os-icon-delivery-box-2" />
						</div>
						<span>Loan Balance</span>
					</Link>
				</li>
				<li>
					<Link to="/report/expected-repayments">
						<div className="icon-w">
							<div className="os-icon os-icon-delivery-box-2" />
						</div>
						<span>Expected Repayments</span>
					</Link>
				</li>
				<li>
					<Link to="/report/disbursed-loans">
						<div className="icon-w">
							<div className="os-icon os-icon-delivery-box-2" />
						</div>
						<span>Disbursed Loans</span>
					</Link>
				</li>
				<li>
					<Link to="/report/interest-earned">
						<div className="icon-w">
							<div className="os-icon os-icon-delivery-box-2" />
						</div>
						<span>Interest Earned</span>
					</Link>
				</li>
				<li>
					<Link to="/report/agent-earning">
						<div className="icon-w">
							<div className="os-icon os-icon-delivery-box-2" />
						</div>
						<span>Agent Earning</span>
					</Link>
				</li>
			</ul>
		</div>
	);
};

export default ReportMenu;
