<?php

namespace Database\Seeders;

use App\Models\Accountno;
use App\Models\BankCode;
use App\Models\Loan;
use App\Models\Payslip;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class FixAccountNos extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $users = [];

            $accountnos = Accountno::all();

            foreach ($accountnos as $item){
                $account = Accountno::where('id', $item->id)->first();
                if($account){
                    $user = User::where('id', $account->user_id)->first();
                    if($user) {
                        if($user->bvn != $account->bvn || $user->phone != $account->phone) {
                            $users[] = ['account' => $account->id, 'bvn' => $user->bvn, 'abvn' => $account->bvn, 'phone' => $user->phone, 'aphone' => $account->phone];
                        }

                        $account->bvn = $user->bvn;
                        $account->phone = $user->phone;
                    }

                    $bank = BankCode::where('code', $account->bank_code)->orWhere('name', 'LIKE', '%'.$account->bank_name.'%')->first();

                    if($bank) {
                        $account->bank_code = $bank->code;
                        $account->bank_code_id = $bank->id;
                    }

                    $account->save();
                }
            }

            info('updated accounts: '.json_encode($users));

            $accounts = $accounts_u = [];

            $loans = Loan::withTrashed()->get();
            foreach ($loans as $loan) {
                $account = Accountno::where('id', $loan->accountno_id)->first();
                if($account == null) {
                    $user = User::where('id', $loan->user_id)->first();

                    $payslip = Payslip::where('id', $user->payslip_id)->first();

                    $account_number = $payslip ? $payslip->salary_account_number : 0;

                    $bankname = $payslip ? str_replace('- ', '', $payslip->salary_bank) : null;
                    $bank = $bankname ? BankCode::where('name', $bankname)->first() : null;
                    info('bank name: ' . $bankname . ', bank: ' . json_encode($bank));

                    $bank_name = $bank ? $bank->name : null;
                    $bank_code = $bank ? $bank->code : null;
                    $bank_id = $bank ? $bank->id : null;

                    $actno = Accountno::create([
                        'id' => $loan->accountno_id,
                        'user_id' => $user->id,
                        'phone' => $user->phone,
                        'bvn' => $user->bvn,
                        'account_number' => $account_number,
                        'verified' => 1,
                        'bank_name' => $bank_name,
                        'bank_code' => $bank_code,
                        'bank_code_id' => $bank_id,
                    ]);

                    $accounts[] = $actno->id;

                } else if($account && ($account->account_number == 0 || $account->account_number == null)) {
                    $user = User::where('id', $loan->user_id)->first();

                    $payslip = Payslip::where('id', $user->payslip_id)->first();

                    $account_number = $payslip ? $payslip->salary_account_number : 0;

                    $bankname = $payslip ? str_replace('- ', '', $payslip->salary_bank) : null;
                    $bank = $bankname ? BankCode::where('name', $bankname)->first() : null;
                    info('bank name: ' . $bankname . ', bank: ' . json_encode($bank));

                    $bank_name = $bank ? $bank->name : null;
                    $bank_code = $bank ? $bank->code : null;
                    $bank_id = $bank ? $bank->id : null;

                    $account->account_number = $account_number;
                    $account->bank_name = $bank_name;
                    $account->bank_code = $bank_code;
                    $account->bank_code_id = $bank_id;
                    $account->save();

                    $accounts_u[] = $account->id;
                }
            }

            info('created accounts: '.json_encode($accounts));
            info('updated accounts: '.json_encode($accounts_u));
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }
    }
}
