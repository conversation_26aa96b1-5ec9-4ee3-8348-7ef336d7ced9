import React from 'react';
import numeral from 'numeral';

const LoanDetails = ({ netEarning, amount, myMonthlyDeductions, monthlyDeductions, eligibleAmt, tenure, platform, is_topup, loan, myDisburseAmount }) => {
    return (
        <div className="element-wrapper">
            {is_topup && <h6 className="element-header mt-0">Current Loan</h6>}
            {is_topup && (
                <div className="element-content">
                    <div className="row">
                        <div className="col-sm-6">
                            <div className="element-box el-tablo">
                                <div className="label">Outstanding Loan Amount ({loan.tenure - loan.total_paid} Months)</div>
                                <div className="value">{`₦${numeral((parseFloat(loan.monthly_principal) * (loan.tenure - loan.total_paid)) + parseFloat(loan.monthly_interest)).format('0,0.00')}`}</div>
                            </div>
                        </div>
                        <div className="col-sm-6">
                            <div className="element-box el-tablo">
                                <div className="label">Tenure Left</div>
                                <div className="value">{`${loan.tenure - loan.total_paid} Months`}</div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            <h6 className="element-header mt-0">{is_topup ? 'Topup Loan' : 'New Loan Application'}</h6>
            {platform !== 'sme' && (
                <div className="element-content">
                    <div className="row">
                        <div className="col-sm-6">
                            <div className="element-box el-tablo">
                                <div className="label">NET EARNINGS</div>
                                <div className="value">{`₦${numeral(netEarning).format('0,0.00')}`}</div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            <div className="element-content">
                <div className="row">
                    <div className="col-sm-6">
                        <div className="element-box el-tablo">
                            <div className="label">Eligible Amount ({tenure} Months)</div>
                            <div className="value">{`₦${numeral(eligibleAmt).format('0,0.00')}`}</div>
                        </div>
                    </div>
                    <div className="col-sm-6">
                        {platform !== 'sme' && (
                            <div className="element-box el-tablo">
                                <div className="label">Eligible Deduction Per Month ({tenure} Months)</div>
                                <div className="value">{`₦${numeral(monthlyDeductions).format('0,0.00')}`}</div>
                            </div>
                        )}
                    </div>
                    <div className="col-sm-6">
                        <div className="element-box el-tablo">
                            <div className="label">Desired Loan Amount</div>
                            <div className="value">{`₦${numeral(amount).format('0,0.00')}`}</div>
                        </div>
                    </div>
                    <div className="col-sm-6">
                        <div className="element-box el-tablo">
                            <div className="label">Desired Deduction Per Month</div>
                            <div className="value">{`₦${numeral(myMonthlyDeductions).format('0,0.00')}`}</div>
                        </div>
                    </div>
                </div>
            </div>
            {is_topup && (
                <div className="element-content">
                    <div className="row">
                        <div className="col-sm-6">
                            <div className="element-box el-tablo">
                                <div className="label">Disburse Amount ({tenure} Months)</div>
                                <div className="value">{`₦${numeral(myDisburseAmount).format('0,0.00')}`}</div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default LoanDetails;