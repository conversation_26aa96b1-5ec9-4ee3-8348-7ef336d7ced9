{"name": "fastcash", "version": "0.1.0", "private": true, "scripts": {"start": "react-scripts start", "build": "yarn clean && react-scripts build", "clean": "sudo rm -rf build", "test": "react-scripts test --env=jsdom", "eject": "react-scripts eject"}, "dependencies": {"antd": "^3.8.2", "font-awesome": "^4.7.0", "jquery": "^3.2.1", "jwt-decode": "^2.2.0", "lodash.startcase": "^4.4.0", "moment": "^2.22.1", "numeral": "^2.0.6", "react": "^16.0.0", "react-block-ui": "^1.1.1", "react-dom": "^16.0.0", "react-loadable": "^5.5.0", "react-notification-system": "^0.2.17", "react-redux": "^5.0.6", "react-router-dom": "^4.2.2", "react-router-redux": "^4.0.8", "react-scripts": "1.0.14", "redux": "^3.7.2", "redux-form": "^7.1.2", "redux-thunk": "^2.2.0", "simple-line-icons": "^2.4.1"}, "devDependencies": {"@types/lodash.startcase": "^4.4.6", "redux-logger": "^3.0.6"}}