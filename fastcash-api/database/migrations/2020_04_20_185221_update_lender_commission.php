<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateLenderCommission extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            Schema::disableForeignKeyConstraints();
            $table->dropColumn(['lender_interest_rate', 'commission']);
            Schema::enableForeignKeyConstraints();

            $table->integer('commission_percent')->nullable()->after('monthly_interest');
            $table->integer('interest_percent')->nullable()->after('commission_percent');
            $table->decimal('total_commission', 20, 2)->nullable()->after('monthly_commission');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->integer('lender_interest_rate')->nullable()->after('interest_rate');
            $table->decimal('commission', 20,2)->nullable()->after('monthly_interest');

            Schema::disableForeignKeyConstraints();
            $table->dropColumn(['commission_percent', 'interest_percent', 'total_commission']);
            Schema::enableForeignKeyConstraints();
        });
    }
}
