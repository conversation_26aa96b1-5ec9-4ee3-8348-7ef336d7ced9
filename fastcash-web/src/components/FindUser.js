import React, { Component } from 'react';
import { connect } from 'react-redux';

class FindUser extends Component {
	state = {
		errorMessage: '',
		phone: '',
		accountNumber: '',
		bankId: '',
	};
	
    async componentDidMount() {
		this.setState({ phone: this.props.user.phone });
	}

	cancel = e => {
		e.preventDefault();
		this.setState({ phone: '', accountNumber: '', bankId: '', errorMessage: '' });
	};

	onChange = e => {
		const phone = e.target.value;
		this.setState({ phone });
	};

	onChangeAcn = e => {
		const account = e.target.value;
		this.setState({ accountNumber: account, bankId: '' });
	};

	onSelectBank = e => {
		const bank_id = e.target.value;
		this.setState({ bankId: bank_id });
	};

	render() {
		const { banks } = this.props;
		const { errorMessage, phone, accountNumber, bankId } = this.state;
		return (
			<div className="element-box" style={{marginBottom: '0px'}}>
				{errorMessage !== '' && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${errorMessage}`}}></div>}
				<form onSubmit={(e) => this.props.onFindUser(e, accountNumber, bankId)}>
					<div className={`row ${(errorMessage !== '' && 'has-error')}`}>
						<label className="col-sm-2 col-form-label" htmlFor="phone">Phone number</label>
						<div className="col-sm-4">
							<input name="phone" placeholder="Enter phone number or ippis" type="text" className="form-control" value={phone} onChange={this.onChange} disabled={true} />
						</div>
					</div>
					<div className={`row ${(errorMessage !== '' && 'has-error')}`} style={{marginTop: '8px'}}>
						<label className="col-sm-2 col-form-label" htmlFor="account_number">Salary Account number</label>
						<div className="col-sm-4">
							<input name="account_number" placeholder="Enter account number" type="number" className="form-control" value={accountNumber} onChange={this.onChangeAcn} />
						</div>
						<div className="col-sm-3">
							<select name="bank_id" className="form-control" onChange={this.onSelectBank} value={Number(bankId)}>
								<option value="">Select Salary Bank</option>
								{banks.map(b => <option value={b.id} key={b.id}>{b.name}</option>)}
							</select>
						</div>
						<div className="col-sm-3">
							<button className="btn btn-primary" type="submit">Check Eligibility</button>
						</div>
					</div>
				</form>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		banks: state.general.banks,
	}
};

export default connect(mapStateToProps)(FindUser);
