import React, { Component } from 'react';

class SavedMail extends Component {
	render() {
		return (
			<div className="aec-full-message-w show-pack">
				<div className="aec-full-message">
					<div className="message-head">
						<div className="user-w">
							<div className="user-name">
								<h6 className="user-title"><PERSON></h6>
								<div className="user-role">
									Account Manager<span>&lt; <EMAIL> &gt;</span>
								</div>
							</div>
						</div>
						<div className="message-info">
							January 12th, 2017
							<br />
							1:24pm
						</div>
					</div>
					<div className="message-content">
						Hi <PERSON>,
						<br />
						<br />
						When the equation, first to ability the forwards, the a but travelling, outlines sentinels bad
						expand to goodness. Behind if have at the even I and how work, completely deference who boss
						actually designer; Monstrous with geared from far and these, morals, phase rome; Class. Called
						get amidst of next.
						<br />
						<br />
						Regards,
						<br />
						<PERSON>
						{/* <div className="message-attachments">
							<div className="attachments-heading">Attachments</div>
							<div className="attachments-docs">
								<a href="#">
									<i className="os-icon os-icon-ui-51" />
									<span>Excel Document</span>
								</a>
								<a href="#">
									<i className="os-icon os-icon-documents-07" />
									<span>Image File</span>
								</a>
							</div>
						</div> */}
					</div>
				</div>
			</div>
		);
	}
}

export default SavedMail;
