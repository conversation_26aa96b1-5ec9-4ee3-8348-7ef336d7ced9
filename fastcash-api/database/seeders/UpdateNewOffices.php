<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\MultiFund;
use App\Models\Transaction;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateNewOffices extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::beginTransaction();
        try {
            $transactions = Transaction::withTrashed()->get();
            foreach ($transactions as $item) {
                $loan = Loan::withTrashed()->where('id', $item->loan_id)->first();
                if($loan) {
                    $transaction = Transaction::withTrashed()->where('id', $item->id)->first();
                    $transaction->office_id = $loan->office_id;
                    $transaction->save();
                }
            }

            $multi_funds = MultiFund::withTrashed()->get();
            foreach ($multi_funds as $item) {
                $loan = Loan::withTrashed()->where('id', $item->loan_id)->first();
                if($loan) {
                    $fund = MultiFund::withTrashed()->where('id', $item->id)->first();
                    $fund->office_id = $loan->office_id;
                    $fund->save();
                }
            }

            DB::commit();

        } catch (\Exception $e){
            DB::rollBack();
            Log::error($e->getMessage());
        }
    }
}
