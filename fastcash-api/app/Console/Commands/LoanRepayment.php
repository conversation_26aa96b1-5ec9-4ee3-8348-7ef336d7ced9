<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\V1\WalletController;
use Illuminate\Console\Command;

class LoanRepayment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'loan:repayment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debit wallets for loan repayment this month';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $repayment = app(WalletController::class)->loanRepayment();
        $this->info(json_encode($repayment));
    }
}
