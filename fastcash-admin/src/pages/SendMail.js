/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { EditorState, convertToRaw } from 'draft-js';
import { Editor } from 'react-draft-wysiwyg';
import draftToHtml from 'draftjs-to-html';
import { connect } from 'react-redux';
import truncate from 'lodash.truncate';
import Autosuggest from 'react-autosuggest';

import { httpRequest2, baseURL, uploadDocAPI, httpRequest, supportMailAPI, userAPI } from '../config/constants';
import { doNotify } from '../actions/general';
import { addMail } from '../actions/mail';
import { startBlock, stopBlock } from '../actions/ui-block';
import loading from '../assets/img/loading-2.gif';

const renderSuggestion = suggestion => {
	const ippis = suggestion.ippis ? `- ${suggestion.ippis} ` : '';
	return <div>{`${suggestion.phone} ${ippis}- ${suggestion.name}`}</div>;
}

class SendMail extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			editorState: EditorState.createEmpty(),
			files: [],
			value: '',
			suggestions: [],
			users: [],
			subject: null,
			searching: false,
			hasSearched: false,
		};
	}

	onEditorStateChange = editorState => {
		this.setState({ editorState });
	};

	doSendMail = () => {
		window.scrollTo(0, 0);
		const { editorState, files, users, subject } = this.state;
		const { profile, lender } = this.props;
		if (users.length === 0) {
			this.notify('', 'please select user', 'error');
		}
		const message = draftToHtml(convertToRaw(editorState.getCurrentContent()));
		if (subject) {
			const data = {
				user_id: profile.id,
				receivers: users,
				subject,
				message,
				attachments: files,
				send: 'mail',
				lender_id: lender.id,
			};
			this.props.startBlock();
			return httpRequest(`${baseURL}${supportMailAPI}`, 'POST', true, data)
				.then(response => {
					this.setState({
						users: [],
						editorState: EditorState.createEmpty(),
						subject: null,
						files: [],
						value: '',
					});
					this.props.addMail(response.mails);
					this.props.stopBlock();
					this.notify('', 'Mail sent!', 'success');
				})
				.catch(error => {
					this.props.stopBlock();
					if (error.message) {
						this.notify('', error.message, 'error');
					} else {
						this.notify('', 'Error, could not send mail', 'error');
					}
				});
		} else {
			this.notify('', 'please enter subject', 'error');
		}
	};

	onChange = async e => {
		const file = e.target.files[0];
		if (file) {
			this.props.startBlock();

			let formData = new FormData();
			formData.append('file', file);
			formData.append('name', file.name);

			return httpRequest2(`${baseURL}${uploadDocAPI}`, 'POST', formData)
				.then(response => {
					this.setState({ files: [...this.state.files, response.result] });
					this.props.stopBlock();
				})
				.catch(error => {
					this.props.stopBlock();
					if (error.message) {
						this.notify('', error.message, 'error');
					} else {
						this.notify('', 'Error, could not upload file', 'error');
					}
				});
		}
	};

	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	remove = file => () => {
		this.setState({ files: [...this.state.files.filter(f => f.name !== file.name)] });
	};

	onChangeSuggestion = (event, { newValue }) => {
		this.setState({ value: newValue });
	};

	getUsers = async search => {
		const { lender } = this.props;
		return await httpRequest(`${baseURL}${userAPI}?lender=${lender.id}&search=${search}`, 'GET', true);
	};

	getSuggestions = async value => {
		const inputValue = value.trim().toLowerCase();
		const inputLength = inputValue.length;

		if (inputLength <= 3) {
			this.setState({ hasSearched: false });
			return [];
		}

		this.setState({ searching: !this.state.searching });

		const response = await this.getUsers(inputValue);
		if (response && response.users) {
			this.setState({ hasSearched: true });
			return response.users;
		}

		return [];
	};

	onSuggestionsFetchRequested = async ({ value }) => {
		const { searching, suggestions } = this.state;
		const _suggestions = searching ? suggestions : await this.getSuggestions(value);
		this.setState({ searching: false }, () => {
			this.setState({ suggestions: _suggestions });
		});
	};

	onSuggestionsClearRequested = () => {
		this.setState({ suggestions: [], hasSearched: false });
	};

	getSuggestionValue = suggestion => {
		const u = this.state.users.find(u => u.id === suggestion.id);
		if(!u) {
			this.setState({ users: [...this.state.users, { id: suggestion.id, name: suggestion.name, email: suggestion.email }], hasSearched: false });
		}
		return '';
	};

	removeUser = (user) => () => {
		this.setState({ users: [...this.state.users.filter(u => u.id !== user.id)], hasSearched: false });
	};

	onChangeSubject = e => {
		this.setState({ subject: e.target.value });
	};

	render() {
		let uploadAttachment;
		const { editorState, files, suggestions, value, users, subject, searching, hasSearched } = this.state;
		const inputProps = {
			placeholder: 'Type a Phone Number or IPPIS Number or Customer ID',
			onChange: this.onChangeSuggestion,
			value: value,
		};

		return (
			<div className="aec-reply">
				<div className="reply-header">
					{users.length > 0 && <h5><small>Send Mail to: </small> {users.map(user => <span className="badge badge-primary badge-light" key={user.id}>{user.name} &lt; {user.email} &gt; <i onClick={this.removeUser(user)} className="os-icon os-icon-ui-15 text-danger ml-1 pointer" /></span>)}</h5>}
					<div className="row no-gutters mt-0 mb-3 mt-4">
						<div className="col-md-6">
							<label className="lighter">Search</label>
							<div className="flex flex-row">
								<Autosuggest
									suggestions={suggestions}
									onSuggestionsFetchRequested={this.onSuggestionsFetchRequested}
									onSuggestionsClearRequested={this.onSuggestionsClearRequested}
									getSuggestionValue={(suggestion) => this.getSuggestionValue(suggestion)}
									renderSuggestion={renderSuggestion}
									inputProps={inputProps}
								/>
								{searching && (
									<div>
										<img src={loading} alt="" style={{width: '24px',marginTop: '6px', marginLeft: '-36px'}} />
									</div>
								)}
							</div>
							{suggestions.length <= 0 && hasSearched && (
								<div className="alert alert-danger aec-alert m-0">no users found!</div>
							)}
						</div>
					</div>
					<div className="row no-gutters mt-2">
						<div className="col-md-6">
							<div className="form-group">
								<label className="lighter">Subject</label>
								<input name="subject" placeholder="Enter subject" type="text" className="form-control" onChange={this.onChangeSubject} value={subject || ''} />
							</div>
						</div>
					</div>
				</div>
				<div className="message-editor">
					<Editor
						editorState={editorState}
						wrapperClassName="mail-wrapper"
						editorClassName="mail-editor"
						onEditorStateChange={this.onEditorStateChange}
						toolbar={{
							options: ['inline', 'blockType', 'fontSize'],
							inline: {
								options: ['bold', 'italic', 'underline'],
								bold: { className: 'bordered-option-classname' },
								italic: { className: 'bordered-option-classname' },
								underline: { className: 'bordered-option-classname' },
							},
							blockType: {
								className: 'bordered-option-classname',
							},
							fontSize: {
								className: 'bordered-option-classname',
							},
						}}
					/>
				</div>
				<div className="buttons-w">
					<div className="actions-left">
						<input className="hidden" onClick={(e) => { e.target.value = null; }} type="file" ref={(el) => { uploadAttachment = el; }} onChange={(e) => this.onChange(e)} />
						<a className="btn btn-link" onClick={() => { uploadAttachment.click() }}>
							<i className="os-icon os-icon-ui-51"/>
							<span>Add Attachment</span>
						</a>
						<div className="message-attachments">
							<div className="attachments-docs">
								{files.map((file, i) => {
									return (
										<a key={i} className="badge badge-info-inverted badge-light ml-2">
											<span>{truncate(file.name, { 'length': 20, })}</span>
											<i onClick={this.remove(file)} className="os-icon os-icon-ui-15 text-danger ml-2" />
										</a>
									);
								})}
							</div>
						</div>
					</div>
					<div className="actions-right"  onClick={this.doSendMail}>
						<a className="btn btn-success">
							<i className="os-icon os-icon-mail-18"/>
							<span>Send Message</span>
						</a>
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		profile: state.user.user,
		lender: state.lender.profile,
	}
};

export default connect(mapStateToProps, { doNotify, startBlock, stopBlock, addMail })(SendMail);
