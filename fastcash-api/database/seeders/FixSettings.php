<?php

namespace Database\Seeders;

use App\Models\Lender;
use App\Models\Setting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class FixSettings extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $lenders = Lender::all();
            foreach ($lenders as $item){
                $lender = Lender::where('id', $item->id)->first();

                if($lender){
                    $max_lt = Setting::where('name', 'max_loan_tenure')->where('lender_id', $lender->id)->first();
                    $mc = Setting::where('name', 'merchant_commission')->where('lender_id', $lender->id)->first();

                    $lender->auto_disburse = 0;
                    $lender->merchant_commission = $mc ? $mc->value : 0;
                    $lender->min_loan_tenure = 1;
                    $lender->max_loan_tenure = $max_lt ? $max_lt->value : 1;
                    $lender->save();
                }
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }
    }
}
