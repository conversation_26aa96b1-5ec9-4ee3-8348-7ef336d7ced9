<?php

namespace Database\Seeders;

use App\Models\Earning;
use Illuminate\Database\Seeder;

class EarningTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $earning = new Earning();
        $earning->ippis = '351239';
        $earning->month = '9';
        $earning->year = '2017';
        $earning->gross_earning = '247227.33';
        $earning->gross_deduction = '45778.74';
        $earning->net_earning = '201448.59';
        $earning->save();

        $earning = new Earning();
        $earning->ippis = '235401';
        $earning->month = '9';
        $earning->year = '2017';
        $earning->gross_earning = '126589.92';
        $earning->gross_deduction = '21437.07';
        $earning->net_earning = '105152.85';
        $earning->save();
        $earning->save();

        $earning = new Earning();
        $earning->ippis = '235101';
        $earning->month = '9';
        $earning->year = '2017';
        $earning->gross_earning = '145261.17';
        $earning->gross_deduction = '24278.54';
        $earning->net_earning = '120982.63';
        $earning->save();

        $earning = new Earning();
        $earning->ippis = '260553';
        $earning->month = '9';
        $earning->year = '2017';
        $earning->gross_earning = '76441.50';
        $earning->gross_deduction = '32843.43';
        $earning->net_earning = '43598.07';
        $earning->save();
    }
}
