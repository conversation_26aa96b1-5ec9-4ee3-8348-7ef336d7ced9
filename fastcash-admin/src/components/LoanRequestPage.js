import React, { Component } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';

import { calculateLoan, setTenure, setEarnings, doRefresh, calculateSMELoan, setRemitaID } from '../actions/loan';
import { httpRequest, baseURL, remitaLoanHistoryRequestAPI, dateLimit } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import LoanForm from './LoanForm';
import LoanDetails from './LoanDetails';
import loader from '../assets/img/loader.gif';

class LoanRequestPage extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
            eligible: 1,
            current: null,
            type: '',
            title: '',
			message: '',
			error_msg: '',
			platform: '',
        };
    }
    
    async componentDidMount() {
		this.setState({ error_msg: '' });
		const { tenure, payslip, current, earnings, user, lender, eligibilityGap, loan_platform } = this.props;
		//console.log(earnings);
        const ir = lender.interest_rate;
		const eg = eligibilityGap;
        const date_diff = payslip ? moment(payslip.retire_expected_at).diff(Date.now(), 'years') : 0;
		const eligibility = date_diff - parseInt(eg, 10);
        if(eligibility <= 0 && user && loan_platform === 'ippis'){
            this.setState({ eligible: 0 });
        }
		this.setState({ current });

		let dates = [];
		for (let d=1; d<=parseInt(dateLimit, 10); d++) {
			dates = [ ...dates, { date: moment().subtract(d, 'month').format('M-YYYY') } ];
		}
		
		let ippisNewEarnings = [];
		for (let i = 0; i < earnings.length; i++) {
			const earning = earnings[i];
			const found = ippisNewEarnings.find(e => e.month === earning.month && e.year === earning.year && parseFloat(e.net_earning) === parseFloat(earning.net_earning));
			if(!found){
				ippisNewEarnings = [...ippisNewEarnings, earning];
			}
		}
		const ippisFilteredEarnings = ippisNewEarnings.filter(e => {
			return dates.find(d => d.date === `${e.month}-${e.year}`) && parseInt(e.net_earning, 10) > 0;
		});
		ippisFilteredEarnings.sort((a, b) => b.year - a.year || b.month - a.month);
		const newEarnings = ippisFilteredEarnings.slice(0, 3);

		if(!current){
			this.props.startBlock();
			this.props.doRefresh(true);

			if(loan_platform === 'sme') {
				this.props.setEarnings([]);
				this.setState({ error_msg: '', platform: 'sme' });
				this.props.stopBlock();
				
				this.props.calculateSMELoan(payslip.eligible_amount || 0);
				this.props.doRefresh(false);
			} else if(loan_platform === 'ippis') {
				if(newEarnings.length >= 1){
					this.props.setEarnings(newEarnings);
					this.setState({ error_msg: '', platform: 'ippis' });
					this.props.stopBlock();
					
					const map_earnings = newEarnings.map(r => r.net_earning);
					const net_earning = Math.min( ...map_earnings );
					this.props.calculateLoan(net_earning, tenure, ir, 0.33);
					this.props.doRefresh(false);
				} else {
					this.setState({ error_msg: 'net earnings not available' });
					this.props.stopBlock();
					this.props.doRefresh(false);
				}
			} else {
				const remitaData = { phone: user.phone };
				
				try {
					const url = `${baseURL}${remitaLoanHistoryRequestAPI}`;
					const response = await httpRequest(url, 'POST', true, remitaData);
					
					let remitaNewEarnings = [];
					for (let i = 0; i < response.earnings.length; i++) {
						const earning = response.earnings[i];
						const found = remitaNewEarnings.find(e => e.month === earning.month && e.year === earning.year && parseFloat(e.net_earning) === parseFloat(earning.net_earning));
						if(!found){
							remitaNewEarnings = [...remitaNewEarnings, earning];
						}
					}

					const remitaFilteredEarnings = remitaNewEarnings.filter(e => {
						return dates.find(d => d.date === `${e.month}-${e.year}`) && parseInt(e.net_earning, 10) > 0;
					});
					remitaFilteredEarnings.sort((a, b) => b.year - a.year || b.month - a.month);
					
					const netEarnings = remitaFilteredEarnings.slice(0, 3);
					
					this.props.setEarnings(netEarnings);
					this.setState({ error_msg: '', platform: 'remita' });
					this.props.stopBlock();

					const map_earnings = netEarnings.map(r => r.net_earning);
					const total_net_earning = map_earnings.reduce((total, amount) => parseFloat(amount) + total, 0);
					const net_earning = (total_net_earning / 3).toFixed(2);
					
					this.props.calculateLoan(net_earning, tenure, ir, 0.4);
					this.props.doRefresh(false);
					this.props.setRemitaID(response.remita_id);
				} catch(error){
					const message = error.message || 'could not retrieve your net earnings';
					this.setState({ error_msg: message });
					this.props.stopBlock();
					this.props.doRefresh(false);
				}
			}
		}
    }

    componentWillUnmount = () => {
		this.props.setTenure(1);
    };
    
    render() {
        const { user, myAmount, myMonthlyDeductions, monthlyDeductions, netEarning, eligibleAmt, tenure, bankAccounts, stopLoanDate, refresh } = this.props;
		const { eligible, current, error_msg, platform } = this.state;
		const accountNumbers = bankAccounts.length > 0 ? bankAccounts.map(a => a.bank_name ? ({ id: a.id, value: `${a.account_number} - ${a.bank_name}` }) : ({ id: a.id, value: a.account_number })) : [];
		const monthLegible = moment(stopLoanDate).date() < moment().date();
        return refresh ? (
			<div className="content-i"><div className="content-box" style={{textAlign:'center'}}><img src={loader} alt=""/></div></div>
        ) : (
			<div className="content-i">
                <div className="content-box p-0">
					{
						error_msg !== '' ? (
							<div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>{error_msg}</div>
						) : (
							<div>
								{eligible === 0 ? (
									<div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>you are not legible to take any loans at this moment</div>
								) : ''}
								{
									eligible === 1 && (
										monthLegible ? (
											<div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>{`you are not legible to take any loans at this moment, please try again on or before the ${moment(stopLoanDate).format('Do')} of next month.`}</div>
										) : (
											current ? (
												<div className="alert alert-warning" style={{margin: '0', marginBottom: '15px'}}>you cant take a loan at this moment because you have a current loan running</div>
											) :  (
												<div className="row">
													<div className="col-sm-6">
														<LoanDetails
															amount={myAmount}
															myMonthlyDeductions={myMonthlyDeductions}
															monthlyDeductions={monthlyDeductions}
															netEarning={netEarning}
															eligibleAmt={eligibleAmt}
															tenure={tenure}
															platform={platform}
															is_topup={false}
															section='create'
														/>
													</div>
													{(current === null && eligible === 1) ? (
														<div className="col-sm-6">
															<div className="element-wrapper">
																<div className="element-wrapper">
																	<LoanForm
																		user={user}
																		tenure={tenure}
																		netEarning={netEarning}
																		eligibleAmt={eligibleAmt}
																		accountNumbers={accountNumbers}
																		bankAccounts={bankAccounts}
																		platform={platform}
																		topup={0}
																		old_loan={null}
																	/>
																</div>
															</div>
														</div>
													) : '' }
												</div>
											)
										)
									)
								}
							</div>
						)
					}
                </div>
            </div>
		);
    }
}

const mapStateToProps = (state, ownProps) => {
    return {
        user: ownProps.user,
        myAmount: state.loan.myAmount,
        myMonthlyDeductions: state.loan.myMonthlyDeductions,
        monthlyDeductions: state.loan.monthlyDeductions,
        netEarning: state.loan.netEarning,
        eligibleAmt: state.loan.eligibleAmt,
        tenure: state.loan.tenure,
		refresh: state.loan.refresh,
		stopLoanDate: state.settings.stop_loan_date,
		eligibilityGap: state.settings.eligibility_gap,
		earnings: ownProps.earnings,
		bankAccounts: ownProps.bank_accounts,
        payslip: ownProps.payslip,
        current: ownProps.loan,
        lender: state.lender.profile,
    }
};

export default connect(mapStateToProps, { calculateLoan, setTenure, setEarnings, startBlock, stopBlock, doRefresh, calculateSMELoan, setRemitaID })(LoanRequestPage);
