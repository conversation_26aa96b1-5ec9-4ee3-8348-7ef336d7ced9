/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';

import { calculateLoan, setTenure, setEarnings, doRefresh, calculateSMELoan, setRemitaID } from '../actions/loan';
import { httpRequest, baseURL, remitaLoanHistoryAPI, dateLimit } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import LoanForm from '../components/LoanForm';
import LoanDetails from '../components/LoanDetails';
import loader from '../assets/img/loader.gif';

class LoanTopup extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
            eligible: 1,
            current: null,
            done: 0,
            type: '',
            title: '',
			message: '',
			error_msg: '',
			platform: '',
        };

        this.loanMessage = this.loanMessage.bind(this);
    }
    
    async componentDidMount() {
		const { tenure, payslip, current, lender, earnings, user } = this.props;
		
		if(user.is_snoozed === 1) {
			this.setState({ error_msg: 'you are not allowed to request a loan at this time.' });
			return;
		}
		
		this.setState({ error_msg: '' });
        const ir = lender.interest_rate;
		
		this.setState({ current, eligible: current.topup_eligible });

		let dates = [];
		for (let d=1; d<=parseInt(dateLimit, 10); d++) {
			dates = [...dates, { date: moment().subtract(d, 'month').format('M-YYYY') }];
		}
		
		let ippisNewEarnings = [];
		for (let i = 0; i < earnings.length; i++) {
			const earning = earnings[i];
			const found = ippisNewEarnings.find(e => e.month === earning.month && e.year === earning.year && parseFloat(e.net_earning) === parseFloat(earning.net_earning));
			if(!found){
				ippisNewEarnings = [...ippisNewEarnings, earning];
			}
		}
		const ippisFilteredEarnings = ippisNewEarnings.filter(e => {
			return dates.find(d => d.date === `${e.month}-${e.year}`) && parseInt(e.net_earning, 10) > 0;
		});
		ippisFilteredEarnings.sort((a, b) => b.year - a.year || b.month - a.month);
		const newEarnings = ippisFilteredEarnings.slice(0, 3);

		if(current){
			this.props.startBlock();
			this.props.doRefresh(true);
			if(current.platform === 'sme'){
				this.props.setEarnings([]);
				this.setState({ error_msg: '', platform: 'sme' });
				this.props.stopBlock();

				this.props.calculateSMELoan(payslip.eligible_amount || 0);
				this.props.doRefresh(false);
			}
			else {
				if(newEarnings.length >= 1 && current.platform === 'ippis'){
					this.props.setEarnings(newEarnings);
					this.setState({ error_msg: '', platform: newEarnings[0].platform });
					this.props.stopBlock();
					
					const map_earnings = newEarnings.map(r => r.net_earning);
					const net_earning = Math.min( ...map_earnings );
					this.props.calculateLoan(net_earning, tenure, ir, 0.33);
					this.props.doRefresh(false);
				}
				else {
					const remitaData = { phone: user.phone };
					
					try {
						const response = await httpRequest(`${baseURL}${remitaLoanHistoryAPI}`, 'POST', true, remitaData);
						let remitaNewEarnings = [];
						for (let i = 0; i < response.earnings.length; i++) {
							const earning = response.earnings[i];
							const found = remitaNewEarnings.find(e => e.month === earning.month && e.year === earning.year && parseFloat(e.net_earning) === parseFloat(earning.net_earning));
							if(!found){
								remitaNewEarnings = [...remitaNewEarnings, earning];
							}
						}
						const remitaFilteredEarnings = remitaNewEarnings.filter(e => {
							return dates.find(d => d.date === `${e.month}-${e.year}`) && parseInt(e.net_earning, 10) > 0;
						});
						remitaFilteredEarnings.sort((a, b) => b.year - a.year || b.month - a.month);
						
						const netEarnings = remitaFilteredEarnings.slice(0, 3);

						this.props.setEarnings(netEarnings);
						this.setState({ error_msg: '', platform: 'remita' });
						this.props.stopBlock();

						const map_earnings = netEarnings.map(r => r.net_earning);
						const total_net_earning = map_earnings.reduce((total, amount) => parseFloat(amount) + total, 0);
						const net_earning = (total_net_earning / 3).toFixed(2);
						this.props.calculateLoan(net_earning, tenure, ir, 0.4);
						this.props.doRefresh(false);
						this.props.setRemitaID(response.remita_id);
					} catch (error) {
						const message = error.message || 'could not retrieve your net earnings';
						this.setState({ error_msg: message });
						this.props.stopBlock();
						this.props.doRefresh(false);
					}
				}
			}
		}
    }

    componentWillUnmount = () => {
		const { maxLoanTenure } = this.props;
		this.props.setTenure(parseInt(maxLoanTenure, 10));
    };

    loanMessage = (type, title, message) => {
        this.setState({ done: 1, type, title, message });
    };
    
    render() {
        const { user, myAmount, myMonthlyDeductions, monthlyDeductions, netEarning, eligibleAmt, tenure, bankAccounts, stopLoanDate, refresh, myDisburseAmount } = this.props;
		const { eligible, current, done, type, title, message, error_msg, platform } = this.state;
		const monthLegible = moment(stopLoanDate).date() < moment().date();
        return (
			refresh ? <div className="content-i"><div className="content-box" style={{textAlign:'center'}}><img src={loader} alt=""/></div></div> :
            <div className="content-i">
                <div className="content-box">
					{(
						error_msg !== '' ? (
							<div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>{error_msg}</div>
						) : 
						<div>
							{done === 1 && (
								<div className="element-box">
									<div className="element-box-content">
										<div className={`alert alert-${type}`} role="alert">
											<h4 className="alert-heading">{title}</h4>
											<p>{message}</p>
											<p className="mb-0"></p>
										</div>
									</div>
								</div>
							)}
							{eligible === 0 ? (
                                <div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>you are not legible to topup your loan at this moment.</div>
                            ) 
                            : (
                                monthLegible ? 
                                    (<div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}}>{`you are not legible to take any loans at this moment, please try again on or before the ${moment(stopLoanDate).format('Do')} of next month.`}</div>) 
                                    : 
                                    (current ?
                                        (done === 0 && (
                                            <div className="row">
                                                <div className="col-sm-8">
                                                    <LoanDetails
                                                        amount={myAmount}
                                                        myMonthlyDeductions={myMonthlyDeductions}
                                                        monthlyDeductions={monthlyDeductions}
                                                        netEarning={netEarning}
                                                        eligibleAmt={eligibleAmt}
                                                        tenure={tenure}
                                                        platform={platform}
                                                        topup={true}
                                                        loan={current}
                                                        myDisburseAmount={myDisburseAmount}
                                                    />
                                                </div>
                                                {(current && eligible == 1)? (
                                                    <div className="col-sm-4">
                                                        <div className="element-wrapper">
                                                            <div className="element-wrapper">
																<LoanForm
																	user={user}
																	tenure={tenure}
																	netEarning={netEarning}
																	eligibleAmt={eligibleAmt}
																	loanMessage={(type, title, message) =>
																		this.loanMessage(type, title, message)
																	}
																	bankAccounts={bankAccounts}
																	platform={platform}
																	topup={1}
																	old_loan={current ? current.id : null}
																	currentLoan={current}
																	balance_amount={
																		current ? current.balance_amount : 0
																	}
																/>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ) : '' }
                                            </div>
                                        )) : (
                                            <div className="alert alert-warning" style={{margin: '0', marginBottom: '15px'}}>you cant topup your loan at this moment because you dont have a current loan running</div>
                                        )
                                    )
                            )}
						</div>
					)}
                </div>
            </div>
        );
    }
}

const mapStateToProps = (state, ownProps) => {
	const lender = state.user.lender;

    return {
        user: state.user.user,
        myAmount: state.loan.myAmount,
        myDisburseAmount: state.loan.myDisburseAmount,
        myMonthlyDeductions: state.loan.myMonthlyDeductions,
        monthlyDeductions: state.loan.monthlyDeductions,
        netEarning: state.loan.netEarning,
        eligibleAmt: state.loan.eligibleAmt,
        tenure: state.loan.tenure,
        current: state.loan.current,
        payslip: state.user.payslip,
		earnings: state.user.earnings,
		bankAccounts: state.user.bank_accounts,
		refresh: state.loan.refresh,
		stopLoanDate: state.settings.stop_loan_date,
		maxLoanTenure: lender.max_loan_tenure,
        lender: state.user.lender,
    }
};

export default connect(mapStateToProps, { calculateLoan, setTenure, setEarnings, startBlock, stopBlock, doRefresh, calculateSMELoan, setRemitaID })(LoanTopup);