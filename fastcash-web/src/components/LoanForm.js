/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, change, formValueSelector, untouch } from 'redux-form';
import Modal from 'react-modal';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import range from 'lodash.range';

import loading from '../assets/img/loading.gif';
import { calculateNewLoan, setTenure, calculateLoan, setDisburseLoan } from '../actions/loan';
import { currency } from '../config/constants';
import OfferLetter from './OfferLetter';
import AddbankAccount from './AddbankAccount';

const validate = values => {
    const errors = {};
    if (!values.amount) {
        errors.amount = 'Enter amount';
    }
    if (values.amount < 10000) {
        errors.amount = 'You should enter an amount greater than 10,000.00';
    }
    if (!values.tenure) {
        errors.type = 'Select tenure';
    }
    if (!values.account_number) {
        errors.type = 'Select account number';
    }
    return errors;
};

const renderIGField = ({input, id, label, type, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label className="lighter" htmlFor={id}>{label}</label>
        <div className="input-group mb-2 mr-sm-2 mb-sm-0">
            <input {...input} placeholder={label} type={type} className="form-control" />
            <div className="input-group-addon">NGN</div>
        </div>
        {touched && error && <span className="help-block">{error}</span>}
    </div>
);

const renderField = ({input, id, label, type, disabled, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label className="lighter" htmlFor={id}>{label}</label>
        <div className="input-group mb-2 mr-sm-2 mb-sm-0">
            <input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
        </div>
        {touched && error && <span className="help-block">{error}</span>}
    </div>
);

const renderSelectField = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label className="lighter">{label}</label>
        <select {...input} className="form-control">
            {data.map(t => <option value={t} key={t}>{t}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

const renderSelectFieldA = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label className="lighter">{label}</label>
        <select {...input} className="form-control">
            {data.map((t, i) => <option value={t.id} key={i}>{t.value}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class LoanForm extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
            tenure: props.tenure,
            modalIsOpen: false,
            loanData: null,
			errMessage: '',
            modalIsOpenAC: false,
            editProfile: false,
        };

        this.openModal = this.openModal.bind(this);
        this.afterOpenModal = this.afterOpenModal.bind(this);
        this.closeModal = this.closeModal.bind(this);
        this.updateErrorMessage = this.updateErrorMessage.bind(this);
		this.loanCompleted = this.loanCompleted.bind(this);
		this.addBankAct = this.addBankAct.bind(this);
    }
	
	componentDidMount() {
        Modal.setAppElement('body');
	}
    
    openModal() {
        this.setState({ modalIsOpen: true });
    }
    
    afterOpenModal() {
    }

    closeModal = () => {
        this.setState({ modalIsOpen: false, modalIsOpenAC: false });
        document.body.className="pages";
    }

    loanCompleted = () => {
        this.closeModal();
    };
    
    updateLoanAmount = e => {
        const { balance_amount, topup, lender } = this.props;
        const { tenure } = this.state;
        const amount = e.target.value || 0;
        this.props.calculateNewLoan(parseFloat(amount), tenure, lender.interest_rate, lender);
        if(topup === 1){
            const disburseAmount = parseFloat(amount) - parseFloat(balance_amount);
            const _disburseAmount = disburseAmount <= 0 ? 0 : disburseAmount;
            this.props.setDisburseLoan(_disburseAmount.toFixed(2));
        }
    };

    updateLoanTenure = e => {
        const tenure = e.target.value;
        this.setState({ tenure });
        const { netEarning, platform, lender } = this.props;
        this.props.setTenure(tenure);
        if(platform !== 'sme') {
            const percent = platform === 'ippis' ? 0.33 : 0.4;
            this.props.calculateLoan(netEarning, tenure, lender.interest_rate, percent);
        }
        this.props.calculateNewLoan(0, tenure, lender.interest_rate, lender);
        this.props.setDisburseLoan(0);
        this.props.dispatch(change('takeloan', 'amount', ''));
        this.props.dispatch(untouch('takeloan', 'amount'));
        this.setState({ editProfile: false });
        this.updateErrorMessage('');
    };

    updateErrorMessage = message => {
        this.setState({ errMessage: message });
        if(message != ''){
            this.closeModal();
        }
    };

    updateFocus = e => {
        this.setState({ editProfile: false });
        this.updateErrorMessage('');
    }

    takeLoan = data => {
        const { eligibleAmt, user, myMonthlyDeductions, netEarning, lender, monthlyInterest, monthlyPrincipal, earnings, platform, topup, old_loan, myDisburseAmount, monthlyCommission, balance_amount, remita_id } = this.props;
        if(parseFloat(data.amount) > eligibleAmt){
            this.updateErrorMessage(`Error, you cannot take a loan above your eligible amount of ${currency(eligibleAmt)}`);
            return;
        }
        if(topup === 1 && myDisburseAmount === 0){
            this.updateErrorMessage('Error, your topup amount should be greater than your outstanding amount');
            return;
        }
        if(user && (!user.bvn || (user.bvn && user.bvn === '') || !user.state_of_origin || !user.lga_of_origin || !user.email)){
            this.setState({ editProfile: true });
            this.updateErrorMessage('Please edit and complete your profile details before you continue');
            return;
        }
        this.setState({ editProfile: false });
        this.updateErrorMessage('');
        const status = 0;
        const details = { ...data, user_id: user.id, monthly_deduction: myMonthlyDeductions, status, net_earning: netEarning, net_earnings: JSON.stringify(earnings), interest_rate: lender.interest_rate, monthly_interest: monthlyInterest, monthly_principal: monthlyPrincipal, platform, auth_code: earnings.length > 0 ? earnings[0].auth_code : 0, topup, old_loan, disburse_amount: myDisburseAmount, monthly_commission: monthlyCommission, admin_request: 0, balance_amount, remita_id };
        // console.log(details);
        this.setState({ loanData: details });
        document.body.className="pages modal-open";
        this.openModal();
        return;
	};
	
	addBankAct = () => {
		document.body.className="pages modal-open";
		this.setState({ modalIsOpenAC: true });
	};
    
    render() {
        const { submitting, pristine, reset, handleSubmit, user, bankAccounts, minLoanTenure, maxLoanTenure } = this.props;
        const { loanData, modalIsOpen, errMessage, modalIsOpenAC, editProfile } = this.state;
		const _maxLoanTenure = parseInt(maxLoanTenure, 10) + 1;
		const tenures = range(minLoanTenure, _maxLoanTenure);
        return (
            <div className="element-box">
                {errMessage && <div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${errMessage}`}}></div>}
                {editProfile && (
                    <div className="text-center mb-2">
                        <Link to={`/edit-profile/${user.id}`} className="text-danger"><i className="fa fa-edit"/> Edit Profile</Link>
                    </div>
                )}
                <form onSubmit={handleSubmit(this.takeLoan)} autoComplete="off">
                    <h5 className="element-box-header">Loan Request Form</h5>
                    <div className="row">
                        <div className="col-sm-6">
                            <Field
                                name="tenure"
                                component={renderSelectField}
                                label="Repayment Tenure"
                                data={tenures}
                                onChange={this.updateLoanTenure.bind(this)}
                            />
                        </div>
                        <div className="col-sm-6">
                            <Field
                                name="amount"
                                id="amount"
                                type="number"
                                component={renderIGField}
                                label="Desired Amount"
                                onChange={this.updateLoanAmount.bind(this)}
                                onFocus={this.updateFocus.bind(this)}
                            />
                        </div>
                    </div>
					<div className="row">
						<div className="col-sm-6">
                            <Field
                                name="account_number"
                                component={renderSelectFieldA}
                                data={bankAccounts}
                                label="Account Number"
                            />
						</div>
                        {/* {bankAccounts.length < 2 && (
                            <div className="col-sm-6">
                                <button type="button" className="btn btn-primary" style={{marginTop: 30}} onClick={this.addBankAct}>
                                    <i className="os-icon os-icon-ui-22" style={{marginRight: 0}}/>
                                    <span>Add Bank A/C</span>
                                </button>
                            </div>
                        )}
					</div>
                    <div className="row"> */}
                        <div className="col-sm-6">
                            <Field
                                name="merchant_code"
                                id="merchant_code"
                                type="text"
                                component={renderField}
                                label="Merchant Code (Optional)"
                            />
                        </div>
                    </div>
                    <div className="form-buttons-w text-right compact">
                        <button className="btn btn-grey" type="button" onClick={reset}>
                            <span>Clear</span>
                        </button>
                        <button className="mr-2 btn btn-primary" disabled={pristine || submitting} type="submit">
                            {submitting? <img src={loading} alt=""/> : 'Apply'}
                        </button>
                    </div>
                </form>
				{modalIsOpen && (
                    <Modal
						isOpen={modalIsOpen}
						onAfterOpen={this.afterOpenModal}
						onRequestClose={this.closeModal}
						contentLabel="Offer Letter"
						shouldCloseOnOverlayClick={false}
						overlayClassName="modal-dialog modal-ol"
						className="modal-content"
					>
						<OfferLetter
							loanData={loanData}
							closeModal={() => this.closeModal()}
							user={user}
							updateError={(msg) => this.updateErrorMessage(msg)}
							completed={() => this.loanCompleted()}
							loanMessage={(type, title, message) => this.props.loanMessage(type, title, message)}
						/>
					</Modal>
                )}
				{modalIsOpenAC && (
                    <Modal
						isOpen={modalIsOpenAC}
						onAfterOpen={this.afterOpenModal}
						onRequestClose={this.closeModal}
						contentLabel="Add Alternative Bank Account"
						shouldCloseOnOverlayClick={false}
						overlayClassName="modal-dialog modal-sm"
						className="modal-content"
					>
						<AddbankAccount
							closeModal={() => this.closeModal()}
							user={user}
							updateError={(msg) => this.updateErrorMessage(msg)}
							completed={() => this.loanCompleted()}
						/>
					</Modal>
                )}
                {modalIsOpen? (<div className="modal-backdrop fade show"/>) : ''}
                {modalIsOpenAC? (<div className="modal-backdrop fade show"/>) : ''}
            </div>
        );
    }
}

LoanForm = reduxForm({
    form: 'takeloan',
    validate,
})(LoanForm);

const selector = formValueSelector('takeloan');

const mapStateToProps = (state, ownProps) => {
    const amount = selector(state, 'amount');

	const bankAccounts =
		ownProps.bankAccounts.length > 0
			? ownProps.bankAccounts.map((a) =>
					a.bank_name
						? { id: a.id, value: `${a.account_number} - ${a.bank_name}` }
						: { id: a.id, value: a.account_number }
			  )
            : [];
    
    const lender = state.user.lender;

    return {
        amount: amount,
        myMonthlyDeductions: state.loan.myMonthlyDeductions,
		monthlyPrincipal: state.loan.monthlyPrincipal,
		monthlyInterest: state.loan.monthlyInterest,
		myDisburseAmount: state.loan.myDisburseAmount,
        initialValues: {
            tenure: ownProps.tenure,
			account_number: bankAccounts.length > 0 ? bankAccounts[0].id : null,
			acnum: "None"
        },
        bankAccounts,
		minLoanTenure: lender.min_loan_tenure,
		maxLoanTenure: lender.max_loan_tenure,
        earnings: state.loan.earnings,
        monthlyCommission: state.loan.monthlyCommission,
        lender: state.user.lender,
        remita_id: state.loan.remita_id,
    }
};

export default withRouter(connect(mapStateToProps, { calculateNewLoan, setTenure, calculateLoan, setDisburseLoan, reset, change, untouch })(LoanForm));
