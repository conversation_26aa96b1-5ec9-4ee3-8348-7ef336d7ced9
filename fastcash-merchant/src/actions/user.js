import * as types from './types';
import { setLenderProfile } from './lender';

export const toggleMenu = action => {
	return {
		type: types.TOGGLE_MENU,
		payload: action,
	};
};

export const startPreloading = () => {
	return {
		type: types.START_PRELOADING,
	};
};

export const stopPreloading = () => {
	return {
		type: types.STOP_PRELOADING,
	};
};

export const signOut = () => {
	return {
		type: types.SIGN_OUT,
	};
};

export const changeUserData = (user, status) => {
	const { lender, ...details } = user;

	return dispatch => {
		dispatch({
			type: types.CHANGE_USER_DATA,
			user: details,
			status,
		});

		dispatch(setLenderProfile(lender));
	};
};
