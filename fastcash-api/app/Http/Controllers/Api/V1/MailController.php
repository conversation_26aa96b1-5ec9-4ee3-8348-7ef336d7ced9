<?php

namespace App\Http\Controllers\Api\V1;

use App\Services\MailHandler;
use App\Models\SupportMail;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Log;
use Symfony\Component\HttpFoundation\File\Exception\FileException;

class MailController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $lender = $request->has('lender') ? $request->input('lender') : '';

            if ($lender != '') {
                $mails = SupportMail::where('lender_id', $lender)->orderBy('created_at', 'DESC')->get();
                $mails = $mails->map(function ($item, $key) {
                    $item->user = User::where('id', $item->user_id)->first(['id', 'name', 'email']);
                    $item->receiver = User::where('id', $item->receiver_id)->first(['id', 'name', 'email']);
                    return $item;
                });

                return response()->json(compact('mails'), 200);
            }

            return response()->json(['error' => 'could not fetch mails'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not fetch mails'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'receivers' => 'required',
            'subject' => 'required',
            'message' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        try {
            $input = $request->all();

            $mails = [];
            foreach ($input['receivers'] as $receiver) {
                $mail = SupportMail::create([
                    'user_id' => $input['user_id'],
                    'receiver_id' => $receiver['id'],
                    'subject' => $input['subject'],
                    'message' => $input['message'],
                    'message_plain' => strip_tags($input['message']),
                    'attachments' => json_encode($input['attachments']),
                    'status' => 0,
                    'lender_id' => $input['lender_id'],
                ]);

                if ($input['send'] == 'mail') {
                    $rmail = $this->send($mail);

                    $mail = SupportMail::where('id', $mail->id)->first();
                    if ($rmail != null) {
                        $mail->status = 1;
                        $mail->save();
                    }
                }

                $mail->user = User::where('id', $mail->user_id)->first(['id', 'name', 'email']);
                $mail->receiver = User::where('id', $mail->receiver_id)->first(['id', 'name', 'email']);

                $mails[] = $mail;
            }

            return response()->json(compact('mails'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not save mail, try again later'], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function send($data)
    {
        try {
            $user = User::where('id', $data->user_id)->first();
            $receiver = User::where('id', $data->receiver_id)->first();

            $files = json_decode($data->attachments);

            $mail = (object)null;
            $mail->from_name = $user->name;
            $mail->from_email = '<EMAIL>';
            $mail->to_name = str_replace(',', '', $receiver->name);
            $mail->to_email = $receiver->email;
            $mail->template = 'email.support';
            $mail->subject = $data->subject;
            $mail->content = $data->message;
            $mail->attachments = $files;
            $mail->uri = public_path() . '/attachment/';

            if ($receiver->role == 'user' || $receiver->role == 'u-merchant') {
                $mail->login = env('MAIN_URL');
            } else if ($receiver->role == 'admin' || $receiver->role == 'super') {
                $mail->login = env('ADMIN_URL');
            } else if ($receiver->role == 'merchant') {
                $mail->login = env('MERCHANT_URL');
            } else {
                $mail->login = '';
            }

            if (count($files) == 0) {
                $mail = (new MailHandler())->sendMail($mail);
            } else {
                $mail = (new MailHandler())->sendMailAttach($mail);
            }

            return $data;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return null;
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'subject' => 'required',
            'message' => 'required',
            'receiver_id' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        try {
            $data = $request->all();

            $mail = SupportMail::where('id', $id)->first();

            if ($mail) {
                $mail->subject = $data['subject'];
                $mail->message = $data['message'];
                $mail->receiver_id = $data['receiver_id'];
                $mail->attachments = json_encode($data['attachments']);
                $mail->status = $data['status'];
                $mail->save();

                return response()->json(compact('mail'), 200);
            }

            return response()->json(['error' => 'could not update mail, try again later'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not update mail, try again later'], 500);
        }
    }

    public function upload(Request $request)
    {
        try {
            $file = $request->file('file');

            $mime = explode('/', $file->getMimeType());
            $name = explode('.' . $file->extension(), $request->input('name'));
            $filename = str_replace(' ', '-', $name[0]) . '-' . substr(time(), -4, 4) . '.' . $file->extension();

            $file->move(public_path('attachment'), $filename);

            if ($file) {
                $result = (object)null;
                $result->name = $filename;
                $result->icon = 'os-icon-ui-51';
                $result->type = (isset($mime[1]) && $mime[1] == 'pdf' ? 'PDF' : ucfirst($mime[0])) . ' Document';

                return response()->json(compact('result'), 200);
            }

            return response()->json(['error' => 'File could not be uploaded'], 500);
        } catch (FileException $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'File could not be uploaded'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'File could not be uploaded'], 500);
        }
    }
}
