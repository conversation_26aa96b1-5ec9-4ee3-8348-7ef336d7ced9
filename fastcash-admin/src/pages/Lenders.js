import React, { Component } from 'react';
import { Switch, Route } from 'react-router-dom';
import Loadable from 'react-loadable';
import { connect } from 'react-redux';

import Loading from '../components/Loading';
import { lenderKey } from '../config/constants';
import { editLender } from '../actions/lender';

const ManageLenders = Loadable({ loader: () => import('./ManageLenders'), loading: Loading });
const ViewLender = Loadable({ loader: () => import('./ViewLender'), loading: Loading });
const CreateLender = Loadable({ loader: () => import('./CreateLender'), loading: Loading });
const EditLender = Loadable({ loader: () => import('./EditLender'), loading: Loading });
const ApprovedLoans = Loadable({ loader: () => import('./ApprovedLoans'), loading: Loading });

class Lenders extends Component {
	componentDidMount() {
		const lender = localStorage.getItem(lenderKey);
		if(lender){
			this.props.editLender(JSON.parse(lender));
		}
	}
	
	render() {
		const { match } = this.props;
		return (
			<Switch>
				<Route path={`${match.url}`} component={ManageLenders} exact />
				<Route path={`${match.url}/approved-loans`} component={ApprovedLoans} />
				<Route path={`${match.url}/view/:id`} component={ViewLender} />
				<Route path={`${match.url}/create`} component={CreateLender} />
				<Route path={`${match.url}/edit/:id`} component={EditLender} />
			</Switch>
		);
	}
}

export default connect(null, { editLender })(Lenders);
