import React from 'react';

import Deductions from './Deductions';

const LoanDeductions = ({ transactions }) => {
	return (
		<div className="row">
			<div className="col-sm-12">
				<div className="element-wrapper">
					<h5 className="element-header">Current Loan Repayments</h5>
					<div className="element-box-tp">
						<div className="table-responsive">
							<table className="table table-padded">
								<thead>
									<tr>
										<th>Description</th>
										<th>Date</th>
										<th className="text-center">Source</th>
										<th className="text-right">Amount</th>
										<th className="text-right">Status</th>
									</tr>
								</thead>
								<tbody>
									{transactions?.map((d, i) => {
										const amount =
											d.description === 'Loan Taken'
												? parseFloat(d.amount) - parseFloat(d.interest)
												: d.amount;
										return (
											<Deductions
												key={i}
												description={d.description}
												date={d.created_at}
												source={d.source}
												amount={amount}
												status={d.status}
												is_topup={d.loan.is_topup}
												liquidate_approve={d.loan.liquidate_approve}
												approved={d.approved}
											/>
										);
									})}
									{transactions?.length === 0 && (
										<tr>
											<td colSpan="5">No repayments/loan available</td>
										</tr>
									)}
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default LoanDeductions;
