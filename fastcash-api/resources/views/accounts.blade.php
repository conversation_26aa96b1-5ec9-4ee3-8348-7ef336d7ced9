<!doctype html>
<html lang="{{ app()->getLocale() }}">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>FastCash</title>

        <!-- Fonts -->
        <link href="https://fonts.googleapis.com/css?family=Raleway:100,600" rel="stylesheet" type="text/css">
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">

        <!-- Styles -->
        <style>
            html, body {
                background-color: #fff;
                color: #636b6f;
                font-family: 'Raleway', sans-serif;
                font-weight: 100;
                margin: 0;
            }

            .flex-center {
                align-items: center;
                display: flex;
                justify-content: center;
            }

            .position-ref {
                position: relative;
            }

            .top-right {
                position: absolute;
                right: 10px;
                top: 18px;
            }

            .content {
                text-align: center;
            }

            .title {
                font-size: 42px;
            }

            .links > a {
                color: #636b6f;
                padding: 0 25px;
                font-size: 12px;
                font-weight: 600;
                letter-spacing: .1rem;
                text-decoration: none;
                text-transform: uppercase;
            }

            .m-b-md {
                margin-bottom: 30px;
            }
        </style>
    </head>
    <body>
        <div class="flex-center position-ref">
            <div class="content">
                <div class="title m-b-md">
                    {{ count($bad) }} approved loans with wrong accounts
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <tr>
                            <th>S/N</th>
                            <th>Name</th>
                            <th>Platform</th>
                            <th>Date Registered</th>
                        </tr>
                        @foreach($accounts as $account)
                        <tr>
                            <td>{{ $loop->index + 1 }}</td>
                            <td>{{ $account->name }}</td>
                            <td>{{ $account->platform }}</td>
                            <td>{{ $account->created_at }}</td>
                        </tr>
                        <tr>
                            <td></td>
                            <td colspan="3">
                                <div class="p-2">
                                    <table class="table table-info table-sm">
                                        <tr>
                                            <th>Account Number</th>
                                            <th>Account Bank</th>
                                            {{-- <th>Date Added</th> --}}
                                            <th>Loan Date</th>
                                            <th>Is Salary Account</th>
                                            <th>Loan ID</th>
                                            <th>Amount</th>
                                            <th>Approved</th>
                                            <th>Disbursed</th>
                                            <th>Liquidated</th>
                                            <th>Status</th>
                                            <th></th>
                                        </tr>
                                        @foreach($account->info as $item)
                                            <tr>
                                                <td>{{ $item->account_number }}</td>
                                                <td>{{ $item->account_bank }}</td>
                                                {{-- <td>{{ $item->created_at }}</td> --}}
                                                <td>{{ $item->request_at }}</td>
                                                <td>{{ $item->is_salary_act }}</td>
                                                <td>{{ $item->loan_id }}</td>
                                                <td>{{ $item->loan_amount }}</td>
                                                <td>{{ $item->approved }}</td>
                                                <td>{{ $item->disbursed }}</td>
                                                <td>{{ $item->liquidated }}</td>
                                                <td><span style="font-weight:bold">{{ $item->status }}</span></td>
                                                <td>
                                                    @if($item->status == '-' && $item->is_salary_act == 'false')
                                                        <a href="/accounts?q={{ $item->id }}" class="btn btn-danger">del</a>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </table>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </table>
                </div>
            </div>
        </div>
        <script src="https://code.jquery.com/jquery-3.4.1.slim.min.js" integrity="sha384-J6qa4849blE2+poT4WnyKhv5vZF5SrPo0iEjwBvKU7imGFAV0wwj1yYfoRSJoZ+n" crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js" integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo" crossorigin="anonymous"></script>
        <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js" integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6" crossorigin="anonymous"></script>
    </body>
</html>
