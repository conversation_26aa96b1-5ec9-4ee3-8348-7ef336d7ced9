<?php
/**
 * Created by PhpStorm.
 * User: emnity
 * Date: 11/22/17
 * Time: 10:36 AM
 */

use App\Models\Accountno;
use App\Models\Action;
use App\Models\ApiCall;
use App\Models\BankCode;
use App\Models\Earning;
use App\Models\Lender;
use App\Services\MailHandler;
use App\Models\Loan;
use App\Models\Merchant;
use App\Models\MerchantPay;
use App\Models\Notify;
use App\Models\Office;
use App\Models\PaymentDate;
use App\Models\Payslip;
use App\Models\PayslipUpload;
use App\Models\RemitaTransaction;
use App\Models\RemitaUser;
use App\Models\Role;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use App\Models\Withdraw;
use App\Models\Support;
use App\Services\Remita;
use Carbon\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Smalot\PdfParser\Parser;

if (!function_exists('validatorMessage')) {
    /**
     * @param string $validator_errors
     *
     * @return string
     */
    function validatorMessage($validator_errors)
    {
        $error = collect($validator_errors)->values()->flatten()->implode(', ');
        $message = str_replace('.', '', strtolower($error));
        return $message;
    }
}

if (!function_exists('random')) {
    /**
     * @param string $validator_errors
     *
     * @return string
     */
    function random()
    {
        return (float) rand() / (float) getrandmax();
    }
}

if (!function_exists('randomPassword')) {
    /**
     * @param $numlenth
     * @param int $type
     * @return string
     */
    function randomPassword($numlenth, $type = 1)
    {
        $alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
        $number = '1234567890';

        $alphabetNum = ($type == 1) ? $alphabet : $number;
        $pass = array();
        $alphaLength = strlen($alphabetNum) - 1;
        for ($i = 0; $i < $numlenth; $i++) {
            $n = rand(0, $alphaLength);
            $pass[] = $alphabetNum[$n];
        }
        return implode($pass);
    }
}

if (!function_exists('generatePassword')) {
    /**
     * @param $numlength
     * @return string
     */
    function generatePassword($numlength)
    {
        return randomPassword($numlength);
    }
}

if (!function_exists('generateUsername')) {
    /**
     * @param $fname
     * @param $lastname
     * @return string
     */
    function generateUsername($fname, $lastname)
    {
        $firstname = str_shuffle(substr($fname, 0, 5));
        $username = strtolower($firstname . substr($lastname, 0, 2));
        $check = null;
        $i = 2;
        do {
            $check = User::where('username', $username)->first();
            if ($check) {
                $length = strlen($lastname);
                if ($i >= $length) {
                    $lname = substr($lastname, 0, 3) . $i;
                } else {
                    $lname = substr($lastname, 0, $i);
                }

                $username = strtolower($firstname . $lname);
                $i++;
            }
        } while ($check != null);
        return $username;
    }
}

if (!function_exists('generateMerchantCode')) {
    /**
     * @param $numlength
     * @return string
     */
    function generateMerchantCode($numlength)
    {
        $code = randomPassword($numlength, 0);
        $check = null;
        do {
            $check = Merchant::where('merchant_code', $code)->first();
            if ($check) {
                $code = randomPassword(8, 0);
            }
        } while ($check != null);
        return $code;
    }
}

if (!function_exists('fixArray')) {
    /**
     * @param $array
     * @return mixed
     */
    function fixArray($array)
    {
        $arr[str_replace(" ", "_", preg_replace('/\s\s+/', ' ', trim(strtolower($array[0]))))] = (isset($array[1]) ? $array[1] : '');
        return $arr;
    }
}

if (!function_exists('formatPayslipDate')) {
    /**
     * @param $old_date
     * @return string
     */
    function formatPayslipDate($old_date)
    {
        $date = Carbon::parse($old_date)->format('Y-m-d');
        return $date;
    }
}

if (!function_exists('removeComma')) {
    /**
     * @param $amount
     * @return mixed
     */
    function removeComma($amount)
    {
        return str_replace(",", "", $amount);
    }
}

if (!function_exists('parsePayslip')) {
    /**
     * @param $file
     * @return array
     * @throws Exception
     */
    function parsePayslip($file)
    {
        $parser = new Parser();
        $pdf = $parser->parseFile($file);
        $pages = $pdf->getPages();

        $details = [];
        foreach ($pages as $page) {
            $texts = explode("\n", $page->getText());
            $text = array();
            foreach ($texts as $t) {
                $text[] = fixArray(explode(":", str_replace("\t", "", $t)));
            }
            $details[] = $text;
        }
        return $details;
    }
}

if (!function_exists('parseData')) {
    /**
     * @param $data
     * @param $month
     * @param $year
     * @return array
     */
    function parseData($data, $lender_id, $month, $year)
    {
        DB::beginTransaction();
        try {
            $ministry = [];
            $psmonth = [];
            $psyear = [];
            $insertIDs = [];
            $updateIDs = [];

            foreach ($data as $detail) {
                $payslip = new Payslip;
                for ($i = 0; $i < count($detail); $i++) {
                    $payslip->salary_bank_branch = null;
                    $payslip->tax_state = null;

                    if (trim(array_keys($detail[$i])[0]) == 'employee_name') {
                        $payslip->name = (isset($detail[$i]['employee_name']) ? $detail[$i]['employee_name'] : null);
                    }

                    $payslip_month = null;
                    $payslip_year = null;

                    if (isset($detail[4])) {
                        $payslip_date = array_keys($detail[4])[0];
                        $split_date = explode('-', $payslip_date);
                        $payslip_month = isset($split_date[0]) ? date_parse($split_date[0])['month'] : null;
                        $payslip_year = isset($split_date[1]) ? $split_date[1] : null;
                    }

                    $payslip->month = $payslip_month;
                    $payslip->year = $payslip_year;

                    $psmonth[] = $payslip_month;
                    $psyear[] = $payslip_year;

                    if (array_keys($detail[$i])[0] == 'ministry') {
                        if (isset($detail[$i]['ministry'])) {
                            $payslip->employer = $detail[$i]['ministry'];
                            $ministry[] = $detail[$i]['ministry'];
                        }
                    }

                    if (array_keys($detail[$i])[0] == 'ippis_number') {
                        $payslip->ippis = (isset($detail[$i]['ippis_number']) ? trim($detail[$i]['ippis_number']) : null);
                    }
                    if (array_keys($detail[$i])[0] == 'designation') {
                        $payslip->designation = (isset($detail[$i]['designation']) ? $detail[$i]['designation'] : null);
                    }

                    if (trim(array_keys($detail[$i])[0]) == 'gender') {
                        $payslip->gender = (isset($detail[$i]['gender']) ? $detail[$i]['gender'] : null);
                    }

                    if (array_keys($detail[$i])[0] == 'date_of_first_appt.') {
                        $payslip->first_appointment = (isset($detail[$i]['date_of_first_appt.']) ? formatPayslipDate($detail[$i]['date_of_first_appt.']) : null);
                    }
                    if (array_keys($detail[$i])[0] == 'date_of_birth') {
                        $payslip->birth_date = (isset($detail[$i]['date_of_birth']) ? formatPayslipDate($detail[$i]['date_of_birth']) : null);
                    }
                    if (array_keys($detail[$i])[0] == 'bank_name') {
                        $payslip->salary_bank = (isset($detail[$i]['bank_name']) ? $detail[$i]['bank_name'] : null);
                    }
                    if (array_keys($detail[$i])[0] == 'account_number') {
                        $payslip->salary_account_number = (isset($detail[$i]['account_number']) ? $detail[$i]['account_number'] : null);
                    }
                    if (array_keys($detail[$i])[0] == 'account_number') {
                        $payslip->account_pin = (isset($detail[$i]['account_number']) ? substr($detail[$i]['account_number'], -4) : null);
                    }
                    if (array_keys($detail[$i])[0] == 'pfa_name') {
                        $payslip->pfa_name = (isset($detail[$i]['pfa_name']) ? $detail[$i]['pfa_name'] : null);
                    }
                    if (array_keys($detail[$i])[0] == 'pension_pin') {
                        $payslip->pension_pin = (isset($detail[$i]['pension_pin']) ? $detail[$i]['pension_pin'] : null);
                    }
                    if (array_keys($detail[$i])[0] == 'grade') {
                        $payslip->grade = (isset($detail[$i]['grade']) ? $detail[$i]['grade'] : null);
                    }
                    if (array_keys($detail[$i])[0] == 'location') {
                        $payslip->location = (isset($detail[$i]['location']) ? $detail[$i]['location'] : null);
                    }
                    if (array_keys($detail[$i])[0] == 'step') {
                        $payslip->step = (isset($detail[$i]['step']) ? $detail[$i]['step'] : null);
                    }
                    if (array_keys($detail[$i])[0] == 'trade_union') {
                        $payslip->trade_union = (isset($detail[$i]['trade_union']) ? $detail[$i]['trade_union'] : null);
                    }
                }

                $payslip->lender_id = $lender_id;
                $payslip->platform = 'ippis';

                if (isset($payslip->ippis) && $payslip->ippis != null && $payslip->ippis != '' && isset($payslip->salary_account_number) && $payslip->salary_account_number != null && $payslip->salary_account_number != '') {
                    $oldpayslip = Payslip::where('ippis', $payslip->ippis)->whereNotNull('ippis')->first();
                    if ($oldpayslip) {
                        $oldpayslip->name = $payslip->name;
                        $oldpayslip->month = $payslip->month;
                        $oldpayslip->year = $payslip->year;
                        $oldpayslip->employer = $payslip->employer;
                        $oldpayslip->ippis = $payslip->ippis;
                        $oldpayslip->designation = $payslip->designation;
                        $oldpayslip->gender = $payslip->gender;
                        $oldpayslip->first_appointment = $payslip->first_appointment;
                        $oldpayslip->birth_date = $payslip->birth_date;
                        $oldpayslip->salary_bank = $payslip->salary_bank;
                        $oldpayslip->account_pin = $payslip->account_pin;
                        $oldpayslip->location = $payslip->location;
                        $oldpayslip->platform = $payslip->platform;
                        $oldpayslip->save();

                        $updateIDs[] = $oldpayslip->id;
                    } else {
                        $newpayslip = Payslip::create([
                            'name' => $payslip->name,
                            'month' => $payslip->month,
                            'year' => $payslip->year,
                            'employer' => $payslip->employer,
                            'ippis' => $payslip->ippis,
                            'designation' => $payslip->designation,
                            'gender' => $payslip->gender,
                            'first_appointment' => $payslip->first_appointment,
                            'birth_date' => $payslip->birth_date,
                            'salary_bank' => $payslip->salary_bank,
                            'salary_account_number' => $payslip->salary_account_number,
                            'account_pin' => $payslip->account_pin,
                            'location' => $payslip->location,
                            'lender_id' => $payslip->lender_id,
                            'platform' => $payslip->platform,
                        ]);

                        $insertIDs[] = $newpayslip->id;
                    }
                }

                $earning = new Earning;
                for ($j = 0; $j < count($detail); $j++) {
                    $earning->month = $payslip_month;
                    $earning->year = $payslip_year;

                    if (array_keys($detail[$j])[0] == 'ippis_number') {
                        $earning->ippis = (isset($detail[$j]['ippis_number']) ? trim($detail[$j]['ippis_number']) : null);
                    }
                    if (array_keys($detail[$j])[0] == 'total_gross_earnings') {
                        $earning->gross_earning = (isset($detail[$j]['total_gross_earnings']) ? removeComma($detail[$j]['total_gross_earnings']) : 0.00);
                    }
                    if (array_keys($detail[$j])[0] == 'total_gross_deductions') {
                        $earning->gross_deduction = (isset($detail[$j]['total_gross_deductions']) ? removeComma($detail[$j]['total_gross_deductions']) : 0.00);
                    }
                    if (array_keys($detail[$j])[0] == 'total_net_earnings') {
                        $earning->net_earning = (isset($detail[$j]['total_net_earnings']) ? removeComma($detail[$j]['total_net_earnings']) : 0.00);
                    }
                }

                if (isset($earning->ippis) && $earning->ippis != null && $earning->ippis != '' && isset($earning->net_earning) && $earning->net_earning != null && $earning->net_earning != '' && $earning->net_earning > 0) {
                    $earning->save();
                }
            }

            $ministry = array_unique($ministry);
            $ps_month = array_unique($psmonth);
            $ps_year = array_unique($psyear);

            if (count($ministry) > 0 && count($ps_month) > 0 && count($ps_year) > 0) {
                $slip = PayslipUpload::where('lender_id', $lender_id)->where('employer', $ministry[0])->where('month', $ps_month[0])->where('year', $ps_year[0])->first();
                if (!$slip) {
                    PayslipUpload::create([
                        'employer' => $ministry[0],
                        'month' => $ps_month[0],
                        'year' => $ps_year[0],
                        'lender_id' => $lender_id,
                    ]);
                }
            }

            DB::commit();
            return [$insertIDs, $ministry, $updateIDs];
        } catch (\Exception $e) {
            DB::rollback();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return [];
        }
    }
}

if (!function_exists('convertNumber')) {
    /**
     * @param $number
     * @return string
     */
    function convertNumber($number)
    {
        list($integer, $fraction) = explode(".", (string) $number);

        $output = "";

        if ($integer[0] == "-") {
            $output = "negative ";
            $integer = ltrim($integer, "-");
        } else if ($integer[0] == "+") {
            $output = "positive ";
            $integer = ltrim($integer, "+");
        }

        if ($integer[0] == "0") {
            $output .= "zero";
        } else {
            $integer = str_pad($integer, 36, "0", STR_PAD_LEFT);
            $group = rtrim(chunk_split($integer, 3, " "), " ");
            $groups = explode(" ", $group);

            $groups2 = array();
            foreach ($groups as $g) {
                $groups2[] = convertThreeDigit($g[0], $g[1], $g[2]);
            }

            for ($z = 0; $z < count($groups2); $z++) {
                if ($groups2[$z] != "") {
                    $output .= $groups2[$z] . convertGroup(11 - $z) . (
                        $z < 11
                        && !array_search('', array_slice($groups2, $z + 1, -1))
                        && $groups2[11] != ''
                        && $groups[11][0] == '0'
                        ? " and "
                        : ", "
                    );
                }
            }

            $output = rtrim($output, ", ");
        }

        if ($fraction > 0) {
            $output .= " point";
            for ($i = 0; $i < strlen($fraction); $i++) {
                $output .= " " . convertDigit($fraction[$i]);
            }
        }

        return $output;
    }
}

if (!function_exists('convertGroup')) {
    /**
     * @param $index
     * @return string
     */
    function convertGroup($index)
    {
        switch ($index) {
            case 11:
                return " decillion";
            case 10:
                return " nonillion";
            case 9:
                return " octillion";
            case 8:
                return " septillion";
            case 7:
                return " sextillion";
            case 6:
                return " quintrillion";
            case 5:
                return " quadrillion";
            case 4:
                return " trillion";
            case 3:
                return " billion";
            case 2:
                return " million";
            case 1:
                return " thousand";
            case 0:
                return "";
        }
    }
}

if (!function_exists('convertThreeDigit')) {
    /**
     * @param $digit1
     * @param $digit2
     * @param $digit3
     * @return string
     */
    function convertThreeDigit($digit1, $digit2, $digit3)
    {
        $buffer = "";

        if ($digit1 == "0" && $digit2 == "0" && $digit3 == "0") {
            return "";
        }

        if ($digit1 != "0") {
            $buffer .= convertDigit($digit1) . " hundred";
            if ($digit2 != "0" || $digit3 != "0") {
                $buffer .= " and ";
            }
        }

        if ($digit2 != "0") {
            $buffer .= convertTwoDigit($digit2, $digit3);
        } else if ($digit3 != "0") {
            $buffer .= convertDigit($digit3);
        }

        return $buffer;
    }
}

if (!function_exists('convertTwoDigit')) {
    /**
     * @param $digit1
     * @param $digit2
     * @return string
     */
    function convertTwoDigit($digit1, $digit2)
    {
        if ($digit2 == "0") {
            switch ($digit1) {
                case "1":
                    return "ten";
                case "2":
                    return "twenty";
                case "3":
                    return "thirty";
                case "4":
                    return "forty";
                case "5":
                    return "fifty";
                case "6":
                    return "sixty";
                case "7":
                    return "seventy";
                case "8":
                    return "eighty";
                case "9":
                    return "ninety";
            }
        } else if ($digit1 == "1") {
            switch ($digit2) {
                case "1":
                    return "eleven";
                case "2":
                    return "twelve";
                case "3":
                    return "thirteen";
                case "4":
                    return "fourteen";
                case "5":
                    return "fifteen";
                case "6":
                    return "sixteen";
                case "7":
                    return "seventeen";
                case "8":
                    return "eighteen";
                case "9":
                    return "nineteen";
            }
        } else {
            $temp = convertDigit($digit2);
            switch ($digit1) {
                case "2":
                    return "twenty-$temp";
                case "3":
                    return "thirty-$temp";
                case "4":
                    return "forty-$temp";
                case "5":
                    return "fifty-$temp";
                case "6":
                    return "sixty-$temp";
                case "7":
                    return "seventy-$temp";
                case "8":
                    return "eighty-$temp";
                case "9":
                    return "ninety-$temp";
            }
        }
    }
}

if (!function_exists('convertDigit')) {
    /**
     * @param $digit
     * @return string
     */
    function convertDigit($digit)
    {
        switch ($digit) {
            case "1":
                return "one";
            case "2":
                return "two";
            case "3":
                return "three";
            case "4":
                return "four";
            case "5":
                return "five";
            case "6":
                return "six";
            case "7":
                return "seven";
            case "8":
                return "eight";
            case "9":
                return "nine";
            case "0":
            default:
                return "zero";
        }
    }
}

if (!function_exists('calculateOutstanding')) {
    /**
     * @param $loan_id
     * @return float|int|mixed
     */
    function calculateOutstanding($loan_id, $amount_paid = 0)
    {
        $loan = Loan::find($loan_id);
        $total_amount = Transaction::where('loan_id', $loan_id)->where('transaction_flag', 'deduction')->where('approved', 1)->sum('amount');
        $total_paid = $total_amount + $amount_paid;
        $outst_amount = $loan->total_deduction - $total_paid;
        return $outst_amount;
    }
}

if (!function_exists('calcOutstRemita')) {
    /**
     * @param $loan_id int
     * @param $total_deduction float
     * @param $amount float
     *
     * @return float|int|mixed
     */
    function calcOutstRemita($loan_id, $total_deduction, $amount = 0)
    {
        $loan = Loan::where('id', $loan_id)->first();

        if ($loan->liquidate_approve == 1) {
            return 0;
        }

        $total_amount = Transaction::where('loan_id', $loan->id)->where('transaction_flag', 'deduction')->where('approved', 1)->sum('amount');
        $total = floatval($total_amount) + floatval($amount);
        $remains = $loan->total_deduction - floatval($total);

        return $remains;
    }
}

if (!function_exists('liquidateLoan')) {
    /**
     * @param $loan_id
     *
     * @return string|int
     */
    function liquidateLoan($loan_id)
    {
        $loan = Loan::find($loan_id);
        $total_amount = Transaction::where('loan_id', $loan_id)->where('transaction_flag', 'deduction')->where('approved', 1)->sum('amount');

        if (($loan->total_deduction - floatval($total_amount)) <= 0) {
            $loan->liquidated = 1;
            $loan->liquidated_at = date('Y-m-d H:i:s');
            $loan->liquidate_approve = 1;
            $loan->liquidate_approve_at = date('Y-m-d H:i:s');
            $loan->save();

            $user = User::where('id', $loan->user_id)->first();
            $user->loan_id = null;
            $user->save();

            try {
                $mail = (object) null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $user->name);
                $mail->to_email = $user->email;
                $mail->login = env('MAIN_URL');
                $mail->subject = 'Loan Liquidated';
                $mail->template = 'email.loan_liquidated';

                $mail = (new MailHandler())->sendMail($mail);
            } catch (\Exception $e) {
                Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            }

            return $user->id;
        }

        return null;
    }
}

if (!function_exists('getAction')) {
    /**
     * @param $name
     * @return mixed
     */
    function getAction($name)
    {
        $action = Action::where('name', $name)->first();
        return $action ? $action->id : null;
    }
}

if (!function_exists('doNotify')) {
    /**
     * @param $user_id
     * @param $cat
     * @param $cat_id
     * @param $action
     * @param $status
     * @param null $description
     * @return $this|\Illuminate\Database\Eloquent\Model|null
     */
    function doNotify($user_id, $cat, $cat_id, $action, $status = null, $description = null, $lender_id = null, $staff_id = null)
    {
        try {
            $notify = Notify::create([
                'user_id' => $user_id,
                'category' => $cat,
                'category_id' => $cat_id,
                'action_id' => getAction($action),
                'status' => $status,
                'lender_id' => $lender_id,
                'staff_id' => $staff_id,
                'description' => $description,
            ]);

            return $notify;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}


if (!function_exists('fetchRemitaHistory')) {
    /**
     * @param $authorisationCode
     * @param $body
     * @param $api
     * @param $live
     * @param $user
     * @return null|object
     */
    function fetchRemitaHistory($authorisationCode, $body, $api, $live, $user = null)
    {
        try {
            $merchantId = env("APP_DEBUG") ? env("DEBUG_MERCHANT_ID") : env('MERCHANT_ID');
            $apiKey = env("APP_DEBUG") ? env("DEBUG_API_KEY") : env('API_KEY');
            $apiToken = env("APP_DEBUG") ? env("DEBUG_API_TOKEN") : env('API_TOKEN');

            $requestId = time() * 1000;
            $apiHash = hash('sha512', $apiKey . $requestId . $apiToken);
            $authorization = "remitaConsumerKey=" . $apiKey . ", remitaConsumerToken=" . $apiHash;

            $lh = (new Remita())->getLoanHistory($api, $merchantId, $apiKey, $requestId, $authorization, $body);
            Log::error('loan history...............'. json_encode($lh));

            $result = (object) null;

            if ($lh == null || $lh == '') {
                $result->data = null;
                $result->errorMessage = 'fetch loan history failed';
                $result->responseCode = '';
                $result->status = 'failed';
            } else {
                if (isset($lh->responseCode)) {
                    if ($lh->responseCode == '00') {
                        try {
                            $lh->data->customerPhone = $user ? $user->phone : '';
                        } catch (\Exception $e) {
                            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                        }
    
                        if (!$live && $user) {
                            $remita_data = RemitaUser::updateOrCreate(
                                ['phone' => $user->phone],
                                ['data' => json_encode($lh), 'auth_code' => $authorisationCode, 'user_id' => $user->id]
                            );
                            $result->remita_id = $remita_data->id;
                        } else {
                            $result->remita_id = null;
                        }
    
                        $result->data = $lh;
                        $result->auth_code = $authorisationCode;
                        $result->status = 'success';
                    } else {
                        $result->data = null;
                        $result->errorMessage = isset($lh->responseMsg) ? $lh->responseMsg : 'fetch loan history failed';
                        $result->responseCode = isset($lh->responseCode) ? $lh->responseCode : '';
                        $result->status = isset($lh->responseMsg) ? $lh->responseMsg : 'failed';
                    }
                } else {
                    $result->data = null;
                    $result->errorMessage = 'fetch loan history failed';
                    $result->responseCode = '';
                    $result->status = 'failed';
                }
            }

            return $result;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}


if (!function_exists('fetchRemitaHistoryPhone')) {
    /**
     * @param $phone
     * @return null|object
     * @internal param $body
     */
    function fetchRemitaHistoryPhone($phone, $live = false)
    {
        try {
            $api = 'remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/salary/history/ph';

            $remita_data = RemitaUser::where('phone', $phone)->where('updated_at', '>', Carbon::now()->subDays(30))->first();
            $user = User::where('phone', $phone)->first();

            $result = (object) null;

            if ($remita_data && $live == false) {
                $result->data = json_decode($remita_data->data);
                $result->auth_code = $remita_data->auth_code;
                $result->remita_id = $remita_data->id;
            } else {
                $authorisationCode = floor(random() * 1101233);

                $body = [
                    "authorisationCode" => $authorisationCode,
                    "phoneNumber" => $phone,
                    "authorisationChannel" => "USSD"
                ];

                $result = fetchRemitaHistory($authorisationCode, $body, $api, $live, $user);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}

if (!function_exists('fetchRemitaHistoryAccount')) {
    /**
     * @param $account_number
     * @param $bank_code
     * @return null|object
     * @internal param $body
     */
    function fetchRemitaHistoryAccount($account_number, $bank_code, $live = false, $customer = null)
    {
        try {
            $api = 'remita/exapp/api/v1/send/api/loansvc/data/api/v2/payday/salary/history/provideCustomerDetails';

            $remita_data = null;
            $user = null;
            
            $bank_account = Accountno::where('account_number', $account_number)->where('bank_code', $bank_code)->where('status', 'primary')->first();
            if ($bank_account) {
                $remita_data = RemitaUser::where('user_id', $bank_account->user_id)->where('updated_at', '>', Carbon::now()->subDays(30))->first();
                $user = User::where('id', $bank_account->user_id)->first();
            } else {
                if ($customer != null) {
                    $user = User::where('id', $customer->id)->first();
                    if ($user) {
                        $bank = BankCode::where('code', $bank_code)->first();
                        if ($bank) {
                            createBankAccount($user->id, $account_number, $bank->name, $bank->code, 1, $user->phone, $bank->id, $user->bvn);
                        } else {
                            info($bank_code);
                        }
                    }
                }
            }

            $result = (object) null;

            if ($remita_data && $live == false) {
                $result->data = json_decode($remita_data->data);
                $result->auth_code = $remita_data->auth_code;
                $result->remita_id = $remita_data->id;
            } else {
                $authorisationCode = floor(random() * 1101233);

                $body = [
                    "authorisationCode" => $authorisationCode,
                    "accountNumber" => $account_number,
                    "bankCode" => $bank_code,
                    "authorisationChannel" => "USSD"
                ];

                $result = fetchRemitaHistory($authorisationCode, $body, $api, $live, $user);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}

if (!function_exists('fetchAccountNumberLoanHistory')) {
    /**
     * @param $phone
     * @return null|object
     * @internal param $body
     */
    function fetchAccountNumberLoanHistory($accountNumber, $bankCode, $live = false)
    {
        try {
            $remita_data = RemitaUser::where('account_number', $accountNumber)->where('bank_code', $bankCode)->where('updated_at', '>', Carbon::now()->subDays(30))->first();

            $result = (object) null;

            if ($remita_data && $live == false) {
                $result->data = json_decode($remita_data->data);
                $result->auth_code = $remita_data->auth_code;
                $result->remita_id = $remita_data->id;
            } else {
                $merchantId = env('MERCHANT_ID');
                $apiKey = env('API_KEY');
                $apiToken = env('API_TOKEN');

                $requestId = time() * 1000;
                $apiHash = hash('sha512', $apiKey . $requestId . $apiToken);
                $authorization = "remitaConsumerKey=" . $apiKey . ", remitaConsumerToken=" . $apiHash;

                $authorisationCode = floor(random() * 1101233);

                $body = [
                    "authorisationCode" => $authorisationCode,
                    "accountNumber" => $accountNumber,
                    "bankCode" => $bankCode,
                    "authorisationChannel" => "USSD"
                ];

                $lh = (new Remita())->getLoanHistoryAccountNumber($merchantId, $apiKey, $requestId, $authorization, $body);

                $status = $lh == null ? 'failed' : ($lh->responseCode == '00' ? 'success' : $lh->responseMsg);

                logAPI('loan-history', $accountNumber, $status);

                if ($lh != null && $lh->responseCode == '00') {
                    $user = User::where('bvn', $lh->data->bvn)->first();
                    try {
                        // $lh->data->customerPhone = $phone;
                    } catch (\Exception $e) {
                        Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                    }

                    if (!$live) {
                        $remita_data = RemitaUser::updateOrCreate(
                            ['phone' => $accountNumber],
                            ['data' => json_encode($lh), 'account_number' => $accountNumber, 'auth_code' => $authorisationCode, 'user_id' => $user ? $user->id : null, 'bank_code' => $bankCode]
                        );
                        $result->remita_id = $remita_data->id;
                    } else {
                        $result->remita_id = null;
                    }

                    $result->data = $lh;
                    $result->auth_code = $authorisationCode;
                } else {
                    $result->data = null;
                    $result->errorMessage = $lh != null ? $lh->responseMsg : 'fetch loan history failed';
                    $result->responseCode = $lh != null ? $lh->responseCode : '';
                    if ($lh != null && $lh->responseMsg != 'Customer not found') {
                        Log::error('Loan history error -> ' . json_encode($lh));
                    }
                }
            }

            return $result;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}

if (!function_exists('logAPI')) {
    /**
     * @param $api
     * @param $user
     * @param $status
     * @internal param $body
     */
    function logAPI($api, $user, $status)
    {
        try {
            ApiCall::create([
                'api' => $api,
                'user' => $user,
                'status' => $status,
            ]);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
        }
    }
}

if (!function_exists('bvnMatch')) {
    function bvnMatch($body)
    {
        try {
            $url = 'https://api.paystack.co';
            $key = env('APP_DEBUG') ? env('PAYSTACK_TEST_SECRET') : env('PAYSTACK_SECRET');

            $client = new Client([
                'base_uri' => $url
            ]);

            $response = $client->post('bvn/match', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $key,
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode($body),
                'debug' => false,
            ]);

            $body = $response->getBody();
            $stringBody = (string) $body;
            return json_decode($stringBody);
        } catch (ClientException $e) {
            $response = $e->getResponse();
            return json_decode((string) $response->getBody()->getContents());
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}

if (!function_exists('getTopupDate')) {
    /**
     * @param $disbursed_at
     * @param $tenure
     * @return null|string
     */
    function getTopupDate($disbursed_at, $tenure)
    {
        try {
            $days = Carbon::parse($disbursed_at)->addMonths($tenure)->diffInDays(Carbon::now());
            $topup_tenure_days = round($days / 2, 0, PHP_ROUND_HALF_UP);
            return Carbon::parse($disbursed_at)->addDays($topup_tenure_days)->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}

if (!function_exists('retrieveBankProfile')) {
    /**
     * @param $account_number
     * @return object
     */
    function retrieveBankProfile($account_number)
    {
        $profile = (object) null;
        $profile->bvn = "";
        $profile->phone = "";

        return $profile;
    }
}

if (!function_exists('checkSMSCredit')) {
    /**
     * @return null|string
     */
    function checkSMSCredit()
    {
        $debug = env("SMS_DEBUG");
        if ($debug) {
            $user = env("SMS_DEBUG_USERNAME");
            $pass = env("SMS_DEBUG_PASSWORD");
        } else {
            $user = env("SMS_USERNAME");
            $pass = env("SMS_PASSWORD");
        }

        try {
            $url = env("SMS_URL");
            $client = new Client([
                'base_uri' => $url
            ]);

            $response = $client->get('/api/credit/', [
                'query' => [
                    'user' => $user,
                    'pass' => $pass,
                ],
                'debug' => false,
            ]);

            $body = $response->getBody();
            $stringBody = (string) $body;
            return $stringBody;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}

if (!function_exists('sendSMS')) {
    /**
     * @param $message
     * @param $phone
     * @return null|string
     */
    function sendSMS($message, $phone)
    {
        try {
            $debug = (bool) env("SMS_DEBUG");
            if ($debug) {
                $user = env("SMS_DEBUG_USERNAME");
                $pass = env("SMS_DEBUG_PASSWORD");
            } else {
                $user = env("SMS_USERNAME");
                $pass = env("SMS_PASSWORD");
            }

            $from = env("SMS_FROM");
            $phone_number = '+234'.substr($phone, -10);
            
            $request = array(
                "user" => $user,
                "pass" => $pass,
                "from" => $from,
                "to" => $phone_number,
                "msg" => $message,
            );

            $params = array();
            foreach($request as $param => $value) {
                $params[] = "$param=$value";
            }
            
            $query = implode('&', $params);
            $url = env("SMS_URL").env("SMS_SEND_API");

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $query);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);

            $response = curl_exec($ch);
            curl_close($ch);

            $stringBody = (string) $response;
            return $stringBody;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}

if (!function_exists('getFinancialOverview')) {
    function getFinancialOverview($lender_id, $main_lender, $office_id = null)
    {
        try {
            // get lender
            $lender = Lender::where('id', $lender_id)->first();

            // investment capital
            $invested_capital = Wallet::where('lender_id', $lender_id)->where('is_lender', 1)->where('approved', 1)->where('category', 'deposit')->where('paid', 1)->sum('amount');

            // disbursed multi loans
            $multi_funds = [];
            if ($office_id) {
                $multi_funds_list = DB::table('multi_funds')
                    ->join('loans', 'loans.id', '=', 'multi_funds.loan_id')
                    ->whereNull('multi_funds.deleted_at')->whereNull('loans.deleted_at')->where('loans.fully_funded', 1)->where('loans.disbursed', 1)->where('multi_funds.lender_id', $lender_id)->where('multi_funds.office_id', $office_id)
                    ->select('multi_funds.amount')
                    ->get();
            } else {
                $multi_funds_list = DB::table('multi_funds')
                    ->join('loans', 'loans.id', '=', 'multi_funds.loan_id')
                    ->whereNull('multi_funds.deleted_at')->whereNull('loans.deleted_at')->where('loans.fully_funded', 1)->where('loans.disbursed', 1)->where('multi_funds.lender_id', $lender_id)
                    ->select('multi_funds.amount')
                    ->get();
            }
            foreach ($multi_funds_list as $fund) {
                $multi_funds[] = $fund->amount;
            }

            // active loans
            if ($office_id) {
                $active_loans = Loan::where('lender_id', $lender_id)->where('approved', 1)->where('liquidated', 0)->where('office_id', $office_id)->count();
            } else {
                $active_loans = Loan::where('lender_id', $lender_id)->where('approved', 1)->where('liquidated', 0)->count();
            }

            // disbursed principal and interest
            $disbursed_principal = [];
            $disbursed_interest = [];
            if ($office_id) {
                $all_disbursed_loans = Loan::where('disbursed', 1)->where('lender_id', $lender_id)->where('office_id', $office_id)->get();
            } else {
                $all_disbursed_loans = Loan::where('disbursed', 1)->where('lender_id', $lender_id)->get();
            }

            foreach ($all_disbursed_loans as $loan) {
                $principal = $loan->disburse_amount > 0 ? $loan->disburse_amount : $loan->amount;

                $disbursed_principal[] = $principal;
                $disbursed_interest[] = $loan->total_deduction - $principal;
            }

            $disbursed = array_sum($disbursed_principal) + array_sum($multi_funds);

            // default loans, principal and interest
            $default_loans = [];
            $default_principal = [];
            $default_interest = [];
            if ($office_id) {
                $all_default_loans = DB::table('payment_dates')
                    ->join('loans', 'loans.id', '=', 'payment_dates.loan_id')
                    ->where('payment_dates.paid', 0)->where('payment_dates.payment_date', '<', Carbon::now()->format('Y-m-d H:i:s'))->whereNull('loans.deleted_at')->where('loans.disbursed', 1)->where('loans.lender_id', $lender_id)->where('loans.office_id', $office_id)
                    ->select('loans.id', 'loans.monthly_principal', 'loans.monthly_interest')
                    ->get();
            } else {
                $all_default_loans = DB::table('payment_dates')
                    ->join('loans', 'loans.id', '=', 'payment_dates.loan_id')
                    ->where('payment_dates.paid', 0)->where('payment_dates.payment_date', '<', Carbon::now()->format('Y-m-d H:i:s'))->whereNull('loans.deleted_at')->where('loans.disbursed', 1)->where('loans.lender_id', $lender_id)
                    ->select('loans.id', 'loans.monthly_principal', 'loans.monthly_interest')
                    ->get();
            }
            foreach ($all_default_loans as $loan) {
                $default_loans[] = $loan->id;
                $default_principal[] = $loan->monthly_principal;
                $default_interest[] = $loan->monthly_interest;
            }

            $default_loans = array_values(array_unique($default_loans, SORT_REGULAR));

            // profit
            $profits = [];
            $repaid_principal = [];
            if ($office_id) {
                $all_paid = Transaction::where('approved', 1)->where('lender_id', $lender_id)->where('office_id', $office_id)->where(function ($query) {
                    $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                })->get(['interest', 'principal']);
            } else {
                $all_paid = Transaction::where('approved', 1)->where('lender_id', $lender_id)->where(function ($query) {
                    $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
                })->get(['interest', 'principal']);
            }

            foreach ($all_paid as $paid) {
                $profits[] = $paid->interest;
                $repaid_principal[] = $paid->principal;
            }

            $outstanding_principal = $disbursed - array_sum($repaid_principal);
            $outstanding_interest = array_sum($disbursed_interest) - array_sum($profits);

            // users
            if ($office_id) {
                $users = DB::table('users')
                    ->join('user_roles', 'user_roles.user_id', '=', 'users.id')
                    ->join('roles', 'user_roles.role_id', '=', 'roles.id')
                    ->where('users.lender_id', $lender_id)->where('roles.name', 'user')->where('users.office_id', $office_id)
                    ->count();
            } else {
                $users = DB::table('users')
                    ->join('user_roles', 'user_roles.user_id', '=', 'users.id')
                    ->join('roles', 'user_roles.role_id', '=', 'roles.id')
                    ->where('users.lender_id', $lender_id)->where('roles.name', 'user')
                    ->count();
            }

            // loan request this month
            if ($office_id) {
                $loan_request_this_month = Loan::where('lender_id', $lender_id)->whereYear('created_at', Carbon::now()->year)->whereMonth('created_at', Carbon::now()->month)->where('office_id', $office_id)->count();
            } else {
                $loan_request_this_month = Loan::where('lender_id', $lender_id)->whereYear('created_at', Carbon::now()->year)->whereMonth('created_at', Carbon::now()->month)->count();
            }

            // unverified and unapproved loans
            if ($office_id) {
                $loans_unverified = Loan::where('verified', 0)->where('approved', 0)->where('lender_id', $lender_id)->where('office_id', $office_id)->count();
                $loans_unapproved = Loan::where('verified', 1)->where('approved', 0)->where('lender_id', $lender_id)->where('office_id', $office_id)->count();
            } else {
                $loans_unverified = Loan::where('verified', 0)->where('approved', 0)->where('lender_id', $lender_id)->count();
                $loans_unapproved = Loan::where('verified', 1)->where('approved', 0)->where('lender_id', $lender_id)->count();
            }

            // approved loans for lenders
            if ($office_id) {
                $lloans_approved = Loan::where('lender_id', '!=', 1)->where('verified', 1)->where('approved', 1)->where('disbursed', 0)->where('office_id', $office_id)->count();
            } else {
                $lloans_approved = Loan::where('lender_id', '!=', 1)->where('verified', 1)->where('approved', 1)->where('disbursed', 0)->count();
            }

            // loans
            if ($office_id) {
                $loans_number = Loan::where('lender_id', $lender_id)->where('office_id', $office_id)->count();
            } else {
                $loans_number = Loan::where('lender_id', $lender_id)->count();
            }

            // number of transactions
            $transactions = Transaction::where('approved', 1)->where('lender_id', $lender_id)->count();
            $super_transactions = Transaction::where('approved', 1)->where('lender_id', $main_lender)->count();

            // number of multi funds
            $multi_funding = Loan::where('is_multifund', 1)->where('cancel_multifund', 0)->count();

            // users this month
            $beginning = Carbon::now()->startOfMonth()->format('Y-m-d');
            $end = Carbon::now()->endOfMonth()->format('Y-m-d');

            if ($office_id) {
                $users_this_month = DB::table('users')
                    ->join('user_roles', 'user_roles.user_id', '=', 'users.id')
                    ->join('roles', 'user_roles.role_id', '=', 'roles.id')
                    ->where('users.lender_id', $lender_id)->where('roles.name', 'user')->where('users.office_id', $office_id)
                    ->whereBetween('users.created_at', [$beginning, $end])
                    ->count();
            } else {
                $users_this_month = DB::table('users')
                    ->join('user_roles', 'user_roles.user_id', '=', 'users.id')
                    ->join('roles', 'user_roles.role_id', '=', 'roles.id')
                    ->where('users.lender_id', $lender_id)->where('roles.name', 'user')
                    ->whereBetween('users.created_at', [$beginning, $end])
                    ->count();
            }

            $balance = $invested_capital + array_sum($profits) - $outstanding_principal;

            // result
            $result = (object) null;
            $result->name = $lender->name;
            $result->lender_id = $lender->id;
            $result->invested_capital = $invested_capital;
            $result->principal_disbursed = $disbursed;
            $result->cummulative_profit = array_sum($profits);
            $result->payout_balance = $balance;
            $result->super_payout_balance = walletBalance($main_lender);
            $result->outstanding_principal = $outstanding_principal;
            $result->default_principal = array_sum($default_principal);
            $result->outstanding_interest = $outstanding_interest;
            $result->default_interest = array_sum($default_interest);
            $result->users = $users;
            $result->users_this_month = $users_this_month;
            $result->loans = $loans_number;
            $result->active_loans = $active_loans;
            $result->default_loans_count = count($default_loans);
            $result->loan_request = $loans_unverified;
            $result->loan_request_this_month = $loan_request_this_month;
            $result->loans_unverified = $loans_unverified;
            $result->loans_unapproved = $loans_unapproved;
            $result->repaid_principal = array_sum($repaid_principal);
            $result->lloans_approved = $lloans_approved;
            $result->transactions = $transactions;
            $result->super_transactions = $super_transactions;
            $result->multi_funding = $multi_funding;

            return $result;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile() . ': ' . $e->getTraceAsString());
            return null;
        }
    }
}

if (!function_exists('getOverallFinancialOverview')) {
    function getOverallFinancialOverview($main_lender)
    {
        try {
            // investment capital
            $invested_capital = Wallet::where('approved', 1)->where('is_lender', 1)->where('category', 'deposit')->where('paid', 1)->sum('amount');

            // active loans
            $active_loans = Loan::where('approved', 1)->where('liquidated', 0)->count();

            // disbursed principal and interest
            $disbursed_principal = [];
            $disbursed_interest = [];

            $all_disbursed_loans = Loan::where('disbursed', 1)->get();
            foreach ($all_disbursed_loans as $loan) {
                $principal = $loan->disburse_amount > 0 ? $loan->disburse_amount : $loan->amount;

                $disbursed_principal[] = $principal;
                $disbursed_interest[] = $loan->total_deduction - $principal;
            }

            $disbursed = array_sum($disbursed_principal);

            // default loans, principal and interest
            $default_loans = [];
            $default_principal = [];
            $default_interest = [];
            $all_default_loans = DB::table('payment_dates')
                ->join('loans', 'loans.id', '=', 'payment_dates.loan_id')
                ->where('payment_dates.paid', 0)->where('payment_dates.payment_date', '<', Carbon::now()->format('Y-m-d H:i:s'))->whereNull('loans.deleted_at')->where('loans.disbursed', 1)
                ->select('loans.id', 'loans.monthly_principal', 'loans.monthly_interest')
                ->get();
            foreach ($all_default_loans as $loan) {
                $default_loans[] = $loan->id;
                $default_principal[] = $loan->monthly_principal;
                $default_interest[] = $loan->monthly_interest;
            }

            $default_loans = array_values(array_unique($default_loans, SORT_REGULAR));

            // profit
            $profits = [];
            $repaid_principal = [];

            $all_paid = Transaction::where('approved', 1)->where(function ($query) {
                $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
            })->get(['interest', 'principal']);
            foreach ($all_paid as $paid) {
                $profits[] = $paid->interest;
                $repaid_principal[] = $paid->principal;
            }

            $outstanding_principal = $disbursed - array_sum($repaid_principal);
            $outstanding_interest = array_sum($disbursed_interest) - array_sum($profits);

            // users
            $users = DB::table('users')
                ->join('user_roles', 'user_roles.user_id', '=', 'users.id')
                ->join('roles', 'user_roles.role_id', '=', 'roles.id')
                ->where('roles.name', 'user')
                ->count();

            // loan request this month
            $loan_request_this_month = Loan::whereYear('created_at', Carbon::now()->year)->whereMonth('created_at', Carbon::now()->month)->count();

            // unverified and unapproved loans
            $loans_unverified = Loan::where('verified', 0)->where('approved', 0)->count();
            $loans_unapproved = Loan::where('verified', 1)->where('approved', 0)->count();

            // approved loans for lenders
            $lloans_approved = Loan::where('lender_id', '!=', 1)->where('verified', 1)->where('approved', 1)->where('disbursed', 0)->count();

            // loans
            $loans_number = Loan::count();

            // number of transactions
            $transactions = Transaction::where('approved', 1)->count();
            $super_transactions = Transaction::where('approved', 1)->where('lender_id', $main_lender)->count();

            // number of multi funds
            $multi_funding = Loan::where('is_multifund', 1)->where('cancel_multifund', 0)->count();

            $balance = $invested_capital + array_sum($profits) - $outstanding_principal;

            // result
            $result = (object) null;
            $result->name = 'Overall Summary';
            $result->lender_id = 0;
            $result->invested_capital = $invested_capital;
            $result->principal_disbursed = $disbursed;
            $result->cummulative_profit = array_sum($profits);
            $result->payout_balance = $balance;
            $result->super_payout_balance = walletBalance($main_lender);
            $result->outstanding_principal = $outstanding_principal;
            $result->default_principal = array_sum($default_principal);
            $result->outstanding_interest = $outstanding_interest;
            $result->default_interest = array_sum($default_interest);
            $result->users = $users;
            $result->loans = $loans_number;
            $result->active_loans = $active_loans;
            $result->default_loans_count = count($default_loans);
            $result->loan_request = $loans_unverified;
            $result->loan_request_this_month = $loan_request_this_month;
            $result->loans_unverified = $loans_unverified;
            $result->loans_unapproved = $loans_unapproved;
            $result->repaid_principal = array_sum($repaid_principal);
            $result->lloans_approved = $lloans_approved;
            $result->transactions = $transactions;
            $result->super_transactions = $super_transactions;
            $result->multi_funding = $multi_funding;

            return $result;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile() . ': ' . $e->getTraceAsString());
            return null;
        }
    }
}

if (!function_exists('getLoans')) {
    function getLoans($loanIds)
    {
        try {
            $loans = [];

            foreach ($loanIds as $id) {
                $loans[] = Loan::with('user', 'office')->where('id', $id)->first();
            }

            return $loans;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return 0;
        }
    }
}

if (!function_exists('getOfficeFinance')) {
    function getOfficeFinance($office, $lender)
    {
        $amount_disbursed = Loan::where('office_id', $office->id)->where('approved', 1)->where('disbursed', 1)->where('lender_id', $lender)->sum('amount');

        $all_paid = DB::table('payment_dates')
            ->join('transactions', 'transactions.id', '=', 'payment_dates.transaction_id')
            ->join('loans', 'loans.id', '=', 'payment_dates.loan_id')
            ->where('payment_dates.paid', 1)->whereNull('loans.deleted_at')->whereNull('transactions.deleted_at')->where('loans.disbursed', 1)->where('loans.office_id', $office->id)->where('transactions.approved', 1)
            ->select('transactions.interest', 'transactions.amount')
            ->get();

        $interest_paid = [];
        $principal_paid = [];

        foreach ($all_paid as $paid) {
            $interest_paid[] = $paid->interest;
            $principal_paid[] = $paid->amount - $paid->interest;
        }

        $result = (object) null;
        $result->amount_disbursed = $amount_disbursed;
        $result->interest_paid = $interest_paid;
        $result->principal_paid = $principal_paid;

        return $result;
    }
}

if (!function_exists('getBalance')) {
    function getBalance($lender_id)
    {
        try {
            // investment capital
            $capital = Wallet::where('lender_id', $lender_id)->where('is_lender', 1)->where('approved', 1)->where('category', 'deposit')->where('paid', 1)->sum('amount');

            // get amount disbursed
            $disbursed_principal = [];

            $all_disbursed_loans = Loan::where('lender_id', $lender_id)->where('approved', 1)->where('is_multifund', 0)->get();
            foreach ($all_disbursed_loans as $loan) {
                $principal = $loan->disburse_amount > 0 ? $loan->disburse_amount : $loan->amount;

                $disbursed_principal[] = $principal;
            }

            $disbursed = array_sum($disbursed_principal);

            // get disbursed amounts from `multi_funds`
            $multi_funds = [];
            $multi_funds_list = DB::table('multi_funds')
                ->join('loans', 'loans.id', '=', 'multi_funds.loan_id')
                ->whereNull('multi_funds.deleted_at')->whereNull('loans.deleted_at')->where('loans.fully_funded', 1)->where('loans.approved', 1)->where('multi_funds.lender_id', $lender_id)
                ->select('multi_funds.amount')
                ->get();
            foreach ($multi_funds_list as $fund) {
                $multi_funds[] = $fund->amount;
            }

            $profits = [];
            $repaid_principal = [];
            $all_paid = Transaction::where('approved', 1)->where('lender_id', $lender_id)->where(function ($query) {
                $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
            })->get(['interest', 'principal']);
            foreach ($all_paid as $paid) {
                $profits[] = $paid->interest;
                $repaid_principal[] = $paid->principal;
            }

            $outstanding_principal = $disbursed + array_sum($multi_funds) - array_sum($repaid_principal);
            $balance = $capital + array_sum($profits) - $outstanding_principal;

            return $balance;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return 0;
        }
    }
}

if (!function_exists('walletBalance')) {
    function walletBalance($lender_id)
    {
        try {
            $balance = Wallet::where('is_lender', 1)->where('lender_id', $lender_id)->where('approved', 1)->where('paid', 1)->sum('amount');

            return $balance;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return 0;
        }
    }
}

if (!function_exists('installRemita')) {
    function installRemita($lender_id)
    {
        try {
            $lender = Office::create([
                'name' => 'Remita',
                'topup' => 1,
                'interest_rate' => 4,
                'repaymentsrc_id' => 2,
                'paymentmode_id' => 1,
                'lender_id' => $lender_id,
            ]);

            return $lender;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}

if (!function_exists('monthsBtwDates')) {
    /**
     * @param date start_date
     * @param date end_date
     * @param boolean eom
     * @return array
     */
    function monthsBtwDates($start_date, $end_date, $eom)
    {
        try {
            $dates = [];

            $end = new DateTime($end_date->toDateTimeString());
            $interval = DateInterval::createFromDateString('1 month');
            $option = $eom ? 0 : 1;
            $period = new DatePeriod($start_date, $interval, $end, $option);

            foreach ($period as $dt) {
                if ($eom) {
                    $dates[] = Carbon::parse($dt)->endOfMonth()->format('Y-m-d H:i:s');
                } else {
                    $dates[] = Carbon::parse($dt)->format('Y-m-d H:i:s');
                }
            }

            return $dates;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return [];
        }
    }
}

if (!function_exists('completeLiquidate')) {
    function completeLiquidate($loan, $staff_id)
    {
        try {
            $loan->liquidate_approve = 1;
            $loan->liquidate_approve_at = date('Y-m-d H:i:s');
            $loan->liquidate_approve_by = $staff_id;
            $loan->save();

            $notify = doNotify($loan->user_id, 'loans', $loan->id, 'loan_liquidated', 'success', null, $loan->lender_id, $staff_id);

            $transaction = Transaction::where('loan_id', $loan->id)->where('status', 'Pending')->first();
            $transaction->status = 'Completed';
            $transaction->approved = 1;
            $transaction->approved_by = $staff_id;
            $transaction->approved_at = date('Y-m-d H:i:s');
            $transaction->save();

            // debit user wallet
            $wallet2 = Wallet::create([
                'user_id' => $loan->user_id,
                'loan_id' => $loan->id,
                'category' => 'liquidate-topup',
                'type' => 'debit',
                'amount' => $transaction->amount * -1,
                'transaction_id' => $transaction->id,
                'approved' => 1,
                'approved_by' => $staff_id,
                'approved_at' => date('Y-m-d H:i:s'),
                'lender_id' => $loan->lender_id,
                'is_lender' => 0,
                'paid' => 1,
            ]);

            // credit lender
            $wallet3 = Wallet::create([
                'user_id' => $loan->user_id,
                'loan_id' => $loan->id,
                'category' => 'liquidate-topup',
                'type' => 'credit',
                'amount' => $transaction->amount,
                'transaction_id' => $transaction->id,
                'approved' => 1,
                'approved_by' => $staff_id,
                'approved_at' => date('Y-m-d H:i:s'),
                'lender_id' => $loan->lender_id,
                'is_lender' => 1,
                'paid' => 1,
            ]);

            $pd = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)
                ->update(['paid' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'transaction_id' => $transaction->id]);

            return $loan;
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}

if (!function_exists('hasLoanEnded')) {
    function hasLoanEnded($loan)
    {
        $end_date = Carbon::createFromFormat('Y-m-d H:i:s', $loan->end_date);
        return Carbon::now()->greaterThan($end_date);
    }
}

if (!function_exists('repaymentEmail')) {
    function repaymentEmail($loan, $transaction)
    {
        $user = User::where('id', $loan->user_id)->first();

        $mail = (object) null;
        $mail->from_name = 'FastCash';
        $mail->from_email = '<EMAIL>';
        $mail->to_name = str_replace(',', '', $user->name);
        $mail->to_email = $user->email;
        $mail->template = 'email.repayment';
        $mail->login = env('MAIN_URL');
        $mail->subject = 'Loan Repayment';
        $mail->amount = number_format($transaction->amount, 2);

        $send = (new MailHandler())->sendMail($mail);

        return $send;
    }
}

if (!function_exists('getOffice')) {
    function getOffice($user, $payslip, $data)
    {
        if ($user->platform == 'remita') {
            $office = Office::where('lender_id', $user->lender_id)->where('name', 'Remita')->first();
        } else if ($user->platform == 'sme') {
            $office = Office::where('lender_id', $user->lender_id)->where('name', 'SME')->first();
        } else {
            if ($payslip) {
                $office = Office::where('name', 'LIKE', '%' . $payslip->employer . '%')->first();
            } else {
                $office = Office::where('name', 'LIKE', '%' . $data['employer'] . '%')->first();
                if (!$office) {
                    $office = Office::create([
                        'name' => $data['employer'],
                        'lender_id' => $user->lender_id,
                    ]);
                }
            }
        }

        return $office;
    }
}

if (!function_exists('repaymentEmail')) {
    function repaymentEmail($loan, $transaction)
    {
        $user = User::where('id', $loan->user_id)->first();

        $mail = (object) null;
        $mail->from_name = 'FastCash';
        $mail->from_email = '<EMAIL>';
        $mail->to_name = str_replace(',', '', $user->name);
        $mail->to_email = $user->email;
        $mail->template = 'email.repayment';
        $mail->login = env('MAIN_URL');
        $mail->subject = 'Loan Repayment';
        $mail->amount = number_format($transaction->amount, 2);

        $send = (new MailHandler())->sendMail($mail);

        return $send;
    }
}

if (!function_exists('getHeading')) {
    function getHeading($haystack, $needle)
    {
        $row = [];
        foreach ($haystack as $items) {
            foreach ($items as $key => $value) {
                $index = array_search($needle, $value, true);
                if ($index !== false) {
                    $row[$index] = $value;
                }
            }
        }

        return $row;
    }
}

if (!function_exists('getLoan')) {
    function getLoan($data)
    {
        $phone = strlen($data['phone_number']) < 11 ? '0' . $data['phone_number'] : $data['phone_number'];
        $user = User::where('phone', $phone)->orWhere('customer_id', $data['customer_id'])->first(['id']);
        if ($user) {
            $loans = Loan::where('user_id', $user->id)->where('status', 1)->where('platform', 'remita')->get();

            $loan = null;
            foreach ($loans as $l) {
                $approved_remita = json_decode($l->approved_remita);
                //info('user: '.json_encode($user).', loans: '.count($loans).', loan: '.json_encode($loan).', compare: '.$approved_remita->data->mandateReference .' == '. $data['mandate_reference']);
                if ($approved_remita->data->mandateReference == $data['mandate_reference']) {
                    $loan = $l;
                }
            }

            return $loan;
        }

        return null;
    }
}

if (!function_exists('parseReDate')) {
    function parseReDate($date)
    {
        $date_array = str_split($date, 2);
        return $date_array[0] . $date_array[1] . '-' . $date_array[2] . '-' . $date_array[3];
    }
}

if (!function_exists('loanData')) {
    function loanData($current_loan)
    {
        if ($current_loan) {
            $total_paid = PaymentDate::where('loan_id', $current_loan->id)->where('paid', 1)->count();
            $should_have_paid = round($current_loan->tenure / 2);
            $has_ended = $current_loan->disbursed == 1 ? hasLoanEnded($current_loan) : true;
            $eligible = $total_paid >= $should_have_paid && $has_ended == false ? 1 : 0;

            $current_loan->topup_eligible = $eligible;
            $current_loan->has_topup = Loan::where('topup', $current_loan->id)->first();
            $current_loan->total_paid = $total_paid;

            $transactions = Transaction::where('loan_id', $current_loan->id)->where('transaction_flag', 'deduction')->where('approved', 1)->get(['amount', 'principal', 'interest']);
            $t_amount = [];
            $amt_paid = [];
            foreach ($transactions as $item) {
                $t_amount[] = $item->amount - ($item->principal + $item->interest);
                $amt_paid[] = $item->amount;
            }
            $outstanding = array_sum($t_amount);

            $current_loan->outstanding_balance = $current_loan->total_deduction - array_sum($amt_paid);
            $current_loan->tenure_balance = PaymentDate::where('loan_id', $current_loan->id)->where('paid', 0)->count();

            $not_paid_default = PaymentDate::where('loan_id', $current_loan->id)->where('paid', 0)->whereDate('payment_date', '<', Carbon::now())->count();

            $excess = array_sum($amt_paid) - ($total_paid * ($current_loan->monthly_principal + $current_loan->monthly_interest));

            $default = $current_loan->disbursed == 1 && $has_ended ? $current_loan->total_deduction - array_sum($amt_paid) : $not_paid_default * ($current_loan->monthly_principal + $current_loan->monthly_interest) + $outstanding - $excess;

            $current_loan->default_amount = $default;
            $current_loan->default_amount_actual = getLoanBalance($current_loan);

            $_transactions = Transaction::with('user')->where('loan_id', $current_loan->id)->where('approved', 1)->orderByRaw('description = "Loan Taken" desc')->orderBy('created_at')->get();
            $_transactions = $_transactions->map(function ($item, $key) {
                $item->loan = Loan::where('id', $item->loan_id)->first();

                return $item;
            });
            $current_loan->transactions = $_transactions;
            $current_loan->repayments = PaymentDate::where('loan_id', $current_loan->id)->get();

            $current_loan->balance_amount = getLoanBalance($current_loan);
        }

        return $current_loan;
    }
}

if (!function_exists('loadUserData')) {
    function loadUserData($user)
    {
        $user->accountno = Accountno::where('user_id', $user->id)->get();
        $user->lender = Lender::where('id', $user->lender_id)->first();

        $user->earning = $user->platform == 'ippis' && $user->ippis ?
            DB::table('earnings')
                ->select(DB::raw('DISTINCT ippis, month, year, net_earning, platform, gross_earning, auth_code, created_at'))
                ->where('ippis', $user->ippis)->where('platform', 'ippis')->where('net_earning', '!=', '0.00')
                ->orderBy('year', 'desc')->orderBy('month', 'desc')
                ->take(12)->get()
            : [];
        // Log::error(json_encode($earning));

        $user->payslip = Payslip::where('id', $user->payslip_id)->first();

        $current_loan = Loan::where('id', $user->loan_id)->first();
        if (!$current_loan) {
            $pending = Loan::where('user_id', $user->id)->where('liquidated', 0)->get();
            if (count($pending)) {
                $current_loan = $pending[0];
            }
        }

        // Getting users pending support messages
        $supports = Support::where('user_id', $user->id)->where('top_query', 1)->where('status', 0)->get();
        $supports = $supports->map(function ($item, $key) {
            $item->replies = Support::where('reply_id', $item->reply_id)->orderBy('created_at', 'asc')->get();
            return $item;
        });

        $last = [];
        foreach ($supports as $key => $value) {
            $reply_count = count($value->replies);
            $lastReply = $value->replies[$reply_count - 1]; // getting the last reply
            if ($lastReply->read == 0) {
                $last[] = $lastReply;
            }
        }
        $user->pending_support = count($last);

        $user->current_loan = loanData($current_loan);
        $user->office = Office::find($user->office_id);
        $user->banks = BankCode::whereRaw('id = (SELECT MIN(id) FROM bank_codes t2 WHERE bank_codes.code = t2.code)')->orderBy('name')->get(['id', 'name', 'code']);
        $user->bank_accounts = Accountno::where('user_id', $user->id)->get();

        $user->loans = Loan::where('user_id', $user->id)->get();

        $merchant = Merchant::where('user_id', $user->id)->first(['id']);
        $user->merchant_id = $merchant ? $merchant->id : null;

        $user->wallet = Wallet::where('user_id', $user->id)->where('is_lender', 0)->where('approved', 1)->where('paid', 1)->sum('amount');

        $user->referrals = 0;

        $user->investment = 0;

        $requests = Withdraw::where('user_id', $user->id)->where('approved', 0)->count();
        $user->has_request = $requests > 0;

        return $user;
    }
}

if (!function_exists('getLoanBalance')) {
    function getLoanBalance($loan)
    {
        if (!$loan) {
            return 0;
        }

        if ($loan->disbursed == 0) {
            return 0;
        }

        $not_paid_default = DB::table('payment_dates')->where('loan_id', $loan->id)->where('paid', 0)->where('payment_date', '<', Carbon::now()->endOfMonth()->format('Y-m-d H:i:s'))->count();

        $this_month_paid = DB::table('payment_dates')->where('loan_id', $loan->id)->where('paid', 1)->where('payment_date', Carbon::now()->endOfMonth()->format('Y-m-d H:i:s'))->count();

        $paid = Transaction::where('loan_id', $loan->id)->where('approved', 1)->where('transaction_flag', 'deduction')->count();

        // part payments if found
        $transactions = Transaction::where('loan_id', $loan->id)->where('transaction_flag', 'deduction')->where('approved', 1)->get(['amount', 'principal', 'interest']);
        $t_amount = [];
        $amt_paid = [];
        foreach ($transactions as $item) {
            $t_amount[] = $item->amount - ($item->principal + $item->interest);
            $amt_paid[] = $item->amount;
        }
        $outstanding = array_sum($t_amount);
        $paid_amount = array_sum($amt_paid);

        error_log('--------------------------------------');
        if ($paid <= 0 && $not_paid_default <= 0) {
            $amount = $loan->amount + $loan->monthly_interest;
        } else {
            $default = $not_paid_default * ($loan->monthly_principal + $loan->monthly_interest);
            error_log('not_paid_default: ' . $not_paid_default);
            error_log('default: ' . $default);
            $remaining_tenure = $loan->tenure - $not_paid_default - $paid;
            error_log('remaining_tenure: ' . $loan->tenure . '-' . $not_paid_default . '-' . $paid);
            error_log('remaining_tenure: ' . $remaining_tenure);
            error_log('this_month_paid: ' . $this_month_paid);

            $interest = $this_month_paid > 0 ? 0 : $loan->monthly_interest;

            $balance = $default + ($remaining_tenure * $loan->monthly_principal) + $interest;
            error_log('def: ' . $balance);

            if ($paid > 0) {
                // has paid something
                $excess = $paid_amount - ($paid * ($loan->monthly_principal + $loan->monthly_interest));
                error_log('excess: ' . $excess);

                $amount = $remaining_tenure == 0 ? $loan->total_deduction - $paid_amount : $balance + $outstanding - $excess;
                error_log($balance . ' + ' . $outstanding . ' - ' . $excess);
                error_log($amount);
            } else {
                // has not paid anything yet
                $amount = $remaining_tenure == 0 ? $default : $balance;
            }

            // calculate default interests since loan ended
//            if(hasLoanEnded($loan)){
//                $end_date = Carbon::createFromFormat('Y-m-d H:i:s', $loan->end_date)->startOfMonth()->format('Y-m-d H:i:s');
//                $month_defaults = Carbon::now()->diffInMonths($end_date);
//                $amount = $amount + ($loan->monthly_interest * $month_defaults);
//            }
        }

        return round($amount, 2);
    }
}

if (!function_exists('getSavingsBalance')) {
    function getSavingsBalance($loan)
    {
        $amount = 0;

        return $amount;
    }
}

if (!function_exists('ussdLiquidateLoan')) {
    function ussdLiquidateLoan($loan_id, $payment_type, $amount)
    {
        $result = (object) null;
        try {
            $loan = Loan::where('id', $loan_id)->first();
            if ($loan) {
                $loan->transaction_data = null;  // data from payment
                $loan->transaction_tx = null;   // verify transaction
                $loan->liquidated = 1;
                $loan->liquidate_approve = 0;
                $loan->liquidated_at = date('Y-m-d H:i:s');
                $loan->save();

                $channel = $payment_type == 'card' ? 'flutterwave' : $payment_type;

                $transaction = Transaction::create([
                    'user_id' => $loan->user_id,
                    'loan_id' => $loan->id,
                    'office_id' => $loan->office_id,
                    'source' => strtoupper($loan->platform),
                    'amount' => $amount,
                    'interest' => $loan->monthly_interest,
                    'principal' => $amount - $loan->monthly_interest,
                    'outst_amount' => 0,
                    'status' => 'Pending',
                    'description' => 'Loan Liquidated',
                    'transaction_flag' => 'liquidate',
                    'lender_id' => $loan->lender_id,
                    'payment_type' => $payment_type,
                    'reference' => null,
                    'channel' => $channel,
                    'approved' => 0,
                    'repayment_date' => date('Y-m-d'),
                    'repayment_source' => $payment_type,
                ]);

                $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'loan_liquidated', 'success', 'ussd', $loan->lender_id, null);

                DB::commit();

                $user = User::where('id', $loan->user_id)->first();

                try {
                    $mail = (object) null;
                    $mail->from_name = 'FastCash';
                    $mail->from_email = '<EMAIL>';
                    $mail->to_name = str_replace(',', '', $user->name);
                    $mail->to_email = $user->email;
                    $mail->login = env('MAIN_URL');
                    $mail->subject = 'Loan Liquidate';
                    $mail->amount = number_format($amount, 2);

                    if ($payment_type == 'bank') {
                        $mail->template = 'email.liquidate_bank';
                    } else {
                        $mail->template = 'email.liquidate_card';
                    }

                    if ($loan->admin_request == 0) {
                        $send = (new MailHandler())->sendMail($mail);
                    }
                } catch (\Exception $e) {
                    Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                }

                $result->message = 'Your loan liquidation has been logged for approval, thanks.';
                $result->error = '';
            } else {
                $result->message = '';
                $result->error = 'Loan liquidation failed, try again later.';
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            $result->message = '';
            $result->error = 'Loan liquidation failed, try again later.';
        }

        return $result;
    }
}

if (!function_exists('dateFound')) {
    function dateFound($earning)
    {
        $dates = [];
        for ($d = 1; $d <= 6; $d++) {
            $dates[] = Carbon::now()->subMonths($d)->format('n-Y');
        }

        $item = null;
        foreach ($dates as $date) {
            if ($date == $earning->month . '-' . $earning->year && $earning->net_earning > 0) {
                $item = $earning;
            }
        }

        return $item;
    }
}

if (!function_exists('searchDuplicate')) {
    function searchDuplicate($earning, $array)
    {
        $found = null;
        foreach ($array as $item) {
            if ($item->month == $earning->month && $item->year == $earning->year && $item->net_earning == $earning->net_earning) {
                $found = $item;
            }
        }

        return $found ? null : $earning;
    }
}

if (!function_exists('getEarnings')) {
    function getEarnings($earnings)
    {
        $newEarnings = [];
        for ($i = 0; $i < count($earnings); $i++) {
            $newItem = searchDuplicate($earnings[$i], $newEarnings);
            if ($newItem) {
                $newEarnings[] = $newItem;
            }
        }

        $filteredEarnings = collect($newEarnings)->filter(function ($value, $key) {
            return dateFound($value);
        });

        $filtered = $filteredEarnings->all();
        return array_slice($filtered, 0, 3);
    }
}

if (!function_exists('getRemitaNetEarnings')) {
    function getRemitaNetEarnings($user, $platform)
    {
        try {
            $result = fetchRemitaHistoryPhone($user->phone);

            if ($result->data == null) {

                $account = Accountno::where('user_id', $user->id)->where('status', 'primary')->first();

                $accountNumber = $account->account_number;
                $bankCode = $account->bank_code;

                $result = fetchAccountNumberLoanHistory($accountNumber, $bankCode);

            }

            $lh = $result ? $result->data : null;

            $rs = (object) null;

            if ($lh != null && $lh->responseCode == '00') {
                $salaries = $lh->data->salaryPaymentDetails;
                $customerId = $lh->data->customerId;

                if ($customerId) {
                    foreach ($salaries as $earning) {
                        $d = explode('+', $earning->paymentDate);

                        $month = Carbon::parse($d[0])->format('n');
                        $year = Carbon::parse($d[0])->format('Y');

                        $earn = Earning::where('customer_id', $customerId)->where('month', $month)->where('year', $year)->where('net_earning', $earning->amount)->where('platform', $platform)->first();

                        if (!$earn) {
                            $earn = Earning::create([
                                'customer_id' => $customerId,
                                'month' => $month,
                                'year' => $year,
                                'net_earning' => $earning->amount,
                                'platform' => $platform,
                                'auth_code' => $result->auth_code,
                            ]);
                        }
                    }

                    $earnings = DB::table('earnings')
                        ->select(DB::raw('DISTINCT customer_id, month, year, net_earning, platform, gross_earning, auth_code, created_at'))
                        ->where('customer_id', $user->customer_id)->where('platform', $platform)->where('net_earning', '!=', '0.00')
                        ->orderBy('year', 'desc')->orderBy('month', 'desc')
                        ->take(12)->get();

                    $rs->earnings = getEarnings($earnings);
                    $rs->remita_id = $result->remita_id;

                    return $rs;
                }
            }

            return null;
        } catch (\Exception $e) {
            error_log($e->getMessage());
            return null;
        }
    }
}

if (!function_exists('getIppisNetEarnings')) {
    function getIppisNetEarnings($user, $platform)
    {
        if ($user->ippis) {
            $earnings = DB::table('earnings')
                ->select(DB::raw('DISTINCT ippis, month, year, net_earning, platform, gross_earning, auth_code, created_at'))
                ->where('ippis', $user->ippis)->where('platform', $user->platform)->where('net_earning', '!=', '0.00')
                ->orderBy('year', 'desc')->orderBy('month', 'desc')
                ->take(12)->get();

            return getEarnings($earnings);
        }

        return [];
    }
}

if (!function_exists('getNetEarning')) {
    function getNetEarning($earnings, $platform)
    {
        $net_earnings = [];
        foreach ($earnings as $earn) {
            $net_earnings[] = $earn->net_earning;
        }
        asort($net_earnings);

        $net_earning = $platform == 'ippis' ? $net_earnings[0] : round(array_sum($net_earnings) / count($net_earnings), 2);

        return $net_earning;
    }
}

if (!function_exists('getEligibleAmount')) {
    function getEligibleAmount($loan)
    {
        if ($loan->platform == 'sme') {
            return $loan->net_earning;
        }

        $percent = $loan->platform == 'ippis' ? 0.33 : 0.4;
        $monthly_repayment = round($loan->net_earning * $percent, 2);

        $t = 1 / $loan->tenure;
        $ir = $loan->interest_rate / 100;

        $eligible_amt = round(($monthly_repayment / ($t + $ir)), 2);

        return $eligible_amt;
    }
}

if (!function_exists('loanRequest')) {
    function loanRequest($data, $user, $lender)
    {
        DB::beginTransaction();

        try {
            $monthly_commission = $lender->commission > 0 ? ($lender->commission / 100) * $data->monthly_interest : 0.00;

            $account = Accountno::where('user_id', $user->id)->where('status', 'primary')->first();

            $loan = Loan::create([
                'user_id' => $user->id,
                'amount' => $data->amount,
                'disburse_amount' => $data->disburse_amount,
                'net_earning' => $data->net_earning,
                'interest_rate' => $data->interest_rate,
                'monthly_deduction' => $data->monthly_deduction,
                'total_deduction' => number_format(($data->monthly_deduction * $data->tenure), 2, ".", ""),
                'tenure' => $data->tenure,
                'status' => $data->status,
                'office_id' => $user->office_id,
                'accountno_id' => $account->id,
                'monthly_principal' => $data->monthly_principal,
                'monthly_interest' => $data->monthly_interest,
                'commission_percent' => $lender->commission,
                'interest_percent' => 100 - $lender->commission,
                'monthly_commission' => round($monthly_commission, 2),
                'total_commission' => round($monthly_commission * $data->tenure, 2),
                'net_earnings' => json_encode($data->net_earnings),
                'platform' => $data->platform,
                'auth_code' => $data->auth_code,
                'lender_id' => $user->lender_id,
                'is_topup' => $data->topup,
                'topup' => null,
                'method' => 'ussd',
                'remita_user_id' => $data->remita_id,
            ]);

            if ($data->topup == 1) {
                try {
                    $old__loan = Loan::find($data->old_loan);
                    $old__loan->topup = $loan->id;
                    $old__loan->topup_date = date('Y-m-d H:i:s');
                    $old__loan->liquidated = 1;
                    $old__loan->liquidated_at = date('Y-m-d H:i:s');
                    $old__loan->liquidate_approve = 0;
                    $old__loan->save();

                    $__tenure = PaymentDate::where('loan_id', $old__loan->id)->where('paid', 0)->count();
                    $__balance_amount = $data->balance_amount;

                    $transaction = Transaction::create([
                        'user_id' => $old__loan->user_id,
                        'loan_id' => $old__loan->id,
                        'office_id' => $old__loan->office_id,
                        'source' => strtoupper($old__loan->platform),
                        'amount' => $__balance_amount,
                        'interest' => $old__loan->monthly_interest,
                        'principal' => $__balance_amount - $old__loan->monthly_interest,
                        'outst_amount' => 0,
                        'status' => 'Pending',
                        'description' => 'Loan Liquidated by Topup',
                        'transaction_flag' => 'liquidate',
                        'channel' => 'loan-liquidated-topup',
                        'lender_id' => $old__loan->lender_id,
                        'repayment_date' => date('Y-m-d'),
                    ]);

                } catch (\Exception $e) {
                    Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
                }
            }

            try {
                $merchant = Merchant::where('merchant_code', $data->merchant_code)->first();
                if ($merchant != null) {
                    $loan->merchant_code = $merchant->merchant_code;
                    $loan->save();

                    MerchantPay::create([
                        'loan_id' => $loan->id,
                        'merchant_id' => $merchant->id,
                    ]);
                }
            } catch (\Exception $e) {
            }

            User::where('id', $user->id)->update(['loan_id' => $loan->id, 'bvn' => $data->bvn]);

            Accountno::where('user_id', $user->id)->update(['bvn' => $data->bvn]);

            //customer id not there
            $earnings = Earning::where('auth_code', $loan->auth_code)->where('platform', 'remita')->get();
            if (count($earnings) > 0) {
                $_user = User::find($loan->user_id);

                if ($_user && ($_user->customer_id == null || $_user->customer_id == '')) {
                    Log::debug('update customer id: ' . $earnings[0]->customer_id);
                    $_user->customer_id = $earnings[0]->customer_id;
                    $_user->save();
                }
            }

            $notify = doNotify($user->id, 'loans', $loan->id, 'loan_request_ussd', 'success', null, $user->lender_id, null);

            DB::commit();

            return $loan;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('this error is from create loan.. ' . $e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return null;//
        }
    }
}

if (!function_exists('findViaRemita')) {
    function findViaRemita($phone)
    {
        try {
            $res = fetchRemitaHistoryPhone($phone);

            $lh = $res && $res->data ? $res->data : null;

            if ($lh != null && $lh->responseCode == '00') {
                $result = [
                    'customer_id' => $lh->data->customerId,
                    'name' => $lh->data->customerName,
                    'bank_code' => $lh->data->bankCode,
                    'account_number' => $lh->data->accountNumber,
                    'salaries' => $lh->data->salaryPaymentDetails,
                    'auth_code' => $res->auth_code,
                    'bvn' => $lh->data->bvn ? $lh->data->bvn : null,
                    'company' => $lh->data->companyName ? $lh->data->companyName : null
                ];
                return $result;
            } else if ($res->responseCode == '7801') {
                return $res->responseCode;
            }
            ;

            return null;
        } catch (\Exception $e) {
            Log::debug($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}

if (!function_exists('findAccountNumberViaRemita')) {
    function findAccountNumberViaRemita($accountNumber, $bankCode)
    {
        try {

            $res = fetchAccountNumberLoanHistory($accountNumber, $bankCode);

            $lh = $res && $res->data ? $res->data : null;

            if ($lh != null && $lh->responseCode == '00') {
                $result = [
                    'customer_id' => $lh->data->customerId,
                    'name' => $lh->data->customerName,
                    'bank_code' => $lh->data->bankCode,
                    'account_number' => $lh->data->accountNumber,
                    'salaries' => $lh->data->salaryPaymentDetails,
                    'auth_code' => $res->auth_code,
                    'bvn' => $lh->data->bvn ? $lh->data->bvn : null,
                    'company' => $lh->data->companyName ? $lh->data->companyName : null
                ];
                return $result;
            }

            return null;
        } catch (\Exception $e) {
            Log::debug($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}

if (!function_exists('checkMerchantCode')) {
    function checkMerchantCode($code)
    {
        try {
            $merchant = Merchant::where('merchant_code', $code)->first();

            return $merchant ? $merchant->merchant_code : null;
        } catch (\Exception $e) {
            Log::debug($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}

if (!function_exists('findPayslip')) {
    function findPayslip($ippis)
    {
        $payslip = Payslip::where('ippis', $ippis)->orderBy('created_at', 'DESC')->first();
        if ($payslip) {
            $user = User::where('payslip_id', $payslip->id)->orWhere('ippis', $ippis)->first();
            if (!$user) {
                return $payslip;
            }
        }
        return null;
    }
}

if (!function_exists('validateEmail')) {
    function validateEmail($email)
    {
        $user = User::where('email', $email)->first();
        if ($user) {
            return null;
        }

        $validator = Validator::make(['email' => $email], [
            'email' => 'email'
        ]);

        if ($validator->fails()) {
            return null;
        }

        return $email;
    }
}

if (!function_exists('validateAccountNumber')) {
    function validateAccountNumber($accountNumber)
    {
        $validator = Validator::make(['accountNumber' => $accountNumber], [
            'accountNumber' => 'digits:10'
        ]);

        if ($validator->fails()) {
            return null;
        }

        return $accountNumber;
    }
}

if (!function_exists('validateAccountNumber')) {
    function validateAccountNumber($accountNumber)
    {
        $validator = Validator::make(['accountNumber' => $accountNumber], [
            'accountNumber' => 'digits:10'
        ]);

        if ($validator->fails()) {
            return null;
        }

        return $accountNumber;
    }
}

if (!function_exists('getBankNameBySerialNumber')) {
    function getBankNameBySerialNumber($serialNumber)
    {
        // Define an associative array of serial numbers and bank names
        $banks = [
            1 => 'First Bank',
            2 => 'Access (Diamond)',
            3 => 'Access',
            4 => 'Zenith Bank',
            5 => 'UBA',
            6 => 'Ecobank',
            7 => 'Polaris Bank',
            8 => 'FCMB',
            9 => 'Guaranty Trust Bank',
            10 => 'Fidelity Bank',
            12 => 'Stanbic',
            13 => 'Sterling Bank',
            14 => 'Union Bank',
            15 => 'Wema Bank',
            16 => 'STD CHARTD',
            17 => 'Citi Bank',
            18 => 'Providus Bank',
            19 => 'Parallex Bank',
            20 => 'Premium Trust Bank',
            23 => 'Unity Bank',
            24 => 'Keystone Bank',
            25 => 'Heritage Bank',
            26 => 'Aso Savings & Loans',
            27 => 'Jaiz Bank',
            28 => 'Jubilee Life Mortgage Bank',
            29 => 'Suntrust Bank',
            30 => 'Coronation Merchant Bank',
            32 => 'Rand Merchant Bank (RMB)',
            33 => 'Optimus Bank',
            34 => 'Prudential Bank',
            35 => 'Imperial Homes Mortgage Bank',
            36 => 'FSDH Merchant Bank',
            37 => 'Gateway Mortgage Bank',
            38 => 'Titan Trust Bank',
            39 => 'Taj Bank',
            40 => 'Globus Bank',
            42 => 'M36',
            43 => 'Hope PSB',
            44 => '9PSB',
            45 => 'Lotus Bank',
            46 => 'Signature Bank'
        ];

        // Check if the serial number exists in the array
        if (array_key_exists($serialNumber, $banks)) {
            return $banks[$serialNumber];
        } else {
            return null; // Return null if the serial number is invalid
        }
    }
}


if (!function_exists('validateBankCode')) {
    function validateBankCode($bankName)
    {
        $bankCodes = [
            'First Bank' => '011',
            'Access (Diamond)' => '063',
            'Access' => '044',
            'Zenith Bank' => '057',
            'UBA' => '033',
            'Ecobank' => '050',
            'Polaris Bank' => '076',
            'FCMB' => '214',
            'Guaranty Trust Bank' => '058',
            'Fidelity Bank' => '070',
            'Stanbic' => '221',
            'Sterling Bank' => '232',
            'Union Bank' => '032',
            'Wema Bank' => '035',
            'STD CHARTD' => '068',
            'Citi Bank' => '023',
            'Providus Bank' => '101',
            'Parallex Bank' => '104',
            'Premium Trust Bank' => '105',
            'Unity Bank' => '215',
            'Keystone Bank' => '082',
            'Heritage Bank' => '030',
            'Aso Savings & Loans' => '401',
            'Jaiz Bank' => '301',
            'Jubilee Life Mortgage Bank' => '402',
            'Suntrust Bank' => '100',
            'Coronation Merchant Bank' => '559',
            'Rand Merchant Bank (RMB)' => '308',
            'Optimus Bank' => '000036',
            'Prudential Bank' => '090108',
            'Imperial Homes Mortgage Bank' => '415',
            'FSDH Merchant Bank' => '400',
            'Gateway Mortgage Bank' => '403',
            'Titan Trust Bank' => '102',
            'Taj Bank' => '302',
            'Globus Bank' => '103',
            'M36' => '100035',
            'Hope PSB' => '120002',
            '9PSB' => '120001',
            'Lotus Bank' => '000029',
            'Signature Bank' => '106'
        ];

        return $bankCodes[$bankName] ?? null;
    }
}



if (!function_exists('createBankAccount')) {
    function createBankAccount($user_id, $account_number, $bank_name, $bank_code, $verified, $phone, $bank_id, $bvn)
    {
        $account = Accountno::where('account_number', $account_number)->where('user_id', $user_id)->first();
        if (!$account) {
            Accountno::create([
                'user_id' => $user_id,
                'account_number' => $account_number,
                'bank_name' => $bank_name,
                'bank_code' => $bank_code,
                'verified' => $verified,
                'phone' => $phone,
                'bank_code_id' => $bank_id,
                'bvn' => $bvn ? $bvn : null
            ]);
        } else {
            $account->account_number = $account_number;
            $account->bank_name = $bank_name;
            $account->bank_code = $bank_code;
            $account->bank_code_id = $bank_id;
            $account->save();
        }
    }
}

if (!function_exists('createUser')) {
    function createUser($input)
    {
        DB::beginTransaction();
        try {
            $payslip = Payslip::find($input->payslip_id);

            if ($input->platform == 'remita') {
                $office = Office::where('lender_id', $input->lender_id)->where('name', 'Remita')->first();
            } else if ($input->platform == 'sme') {
                $office = Office::where('lender_id', $input->lender_id)->where('name', 'SME')->first();
            } else {
                if ($payslip) {
                    $office = Office::where('name', 'LIKE', '%' . $payslip->employer . '%')->first();
                } else {
                    $office = Office::where('name', 'LIKE', '%' . $input->employer . '%')->first();
                    if (!$office) {
                        $office = Office::create([
                            'name' => $input->employer,
                            'lender_id' => $input->lender_id,
                        ]);
                    }
                }
            }

            $user = User::create([
                'password' => bcrypt($input->password),
                'name' => str_replace(',', '', $input->name),
                'ippis' => $payslip ? $payslip->ippis : null,
                'bvn' => $input->bvn ? $input->bvn : null,
                'email' => $input->email,
                'phone' => $input->phone,
                'payslip_id' => ($payslip ? $payslip->id : null),
                'office_id' => ($office ? $office->id : null),
                'platform' => $input->platform,
                'customer_id' => $input->customer_id,
                'lender_id' => $input->lender_id,
                'employer' => $input->employer,
                'is_admin_created' => 0,
                'verified' => 1,
                'method' => 'ussd',
            ]);
            $user->roles()->attach(Role::where('name', 'user')->first());

            if ($input->platform == 'remita') {
                $bank_code = $input->bankCode;
                $bank = BankCode::where('code', $bank_code)->first();

                $bank_name = $bank ? $bank->name : null;
                $bank_id = $bank ? $bank->id : null;

                createBankAccount($user->id, $input->accountNumber, $bank_name, $bank_code, 1, $input->phone, $bank_id, $input->bvn);
            } else {
                $bankname = str_replace('- ', '', $payslip->salary_bank);
                $bank = BankCode::where('name', $bankname)->first();
                info('bank name: ' . $bankname . ', bank: ' . json_encode($bank));

                $bank_code = $bank ? $bank->code : null;
                $bank_id = $bank ? $bank->id : null;

                createBankAccount($user->id, $payslip->salary_account_number, $payslip->salary_bank, $bank_code, 1, $input->phone, $bank_id, $input->bvn);
            }

            $notify = doNotify($user->id, 'users', $user->id, 'user_create', 'success', null, $user->lender_id, null);

            DB::commit();

            try {
                $mail = (object) null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $user->name);
                $mail->to_email = $user->email;
                $mail->template = 'email.new_customer';
                $mail->subject = 'Welcome to FastCash';
                $mail->login = env('MAIN_URL');
                $mail->platform = $user->platform;
                $mail->ippis = $user->ippis ?? '';
                $mail->phone = $user->phone;

                $send = (new MailHandler())->sendMail($mail);
            } catch (\Exception $e) {
                Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            }

            return $user;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::debug($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return null;
        }
    }
}

if (!function_exists('getHeadings')) {
    function getHeadings($report)
    {
        return array_keys($report);
    }
}

if (!function_exists('filterSupport')) {
    function filterSupport($status, $lender, $skip, $limit, $_from, $_to)
    {
        $filteredSupport = [];
        $openTickets = [];
        $closedTickets = [];

        if ($status == 'all') {
            $supports = Support::where('lender_id', $lender)->where('top_query', 1)
                ->orderBy('updated_at', 'desc')->get();

            foreach ($supports as $key => $ticket) {
                $data = (object) null;
                $data = $ticket;
                $data->replies = Support::with('user')->where('reply_id', $ticket->reply_id)->get();
                $data->user = User::where('id', $ticket->user_id)->first(['id', 'name']);
                $data->receiver = User::where('id', $ticket->receiver_id)->first(['id', 'name']);

                $lastIndex = count($data->replies) - 1;
                $lastReply = $data->replies[$lastIndex];
                if (strpos($lastReply->response, 'closed') === false) {
                    array_unshift($openTickets, $data);
                }
                if (strpos($lastReply->response, 'closed') !== false) {
                    array_unshift($closedTickets, $data);
                }
            }
            $filteredSupport = array_merge($openTickets, $closedTickets);
        } else if ($status == 'closed') {
            if ($_from == '') {
                $supports = Support::where('lender_id', $lender)->where('top_query', 1)->orderBy('created_at', 'desc')->get();
            } else {
                $supports = Support::where('lender_id', $lender)->where('top_query', 1)->whereBetween('created_at', [$_from, $_to])->orderBy('created_at', 'desc')->get();
            }

            foreach ($supports as $key => $value) {
                $data = (object) null;
                $data = Support::with('user')->where('reply_id', $value->reply_id)->orderBy('created_at', 'asc')->first();
                $data->replies = Support::with('user')->where('reply_id', $value->reply_id)->orderBy('created_at', 'asc')->get();
                $data->user = User::where('id', $value->user_id)->first(['id', 'name']);
                $data->receiver = User::where('id', $value->receiver_id)->first(['id', 'name']);
                $lastIndex = count($data->replies) - 1;
                $lastReply = $data->replies[$lastIndex];
                if (strpos($lastReply->response, $status) !== false) {
                    $filteredSupport[] = $data;
                }
            }
        } else if ($status == 'answered') {
            $myStatus = 'answered';
            if ($_from == '') {
                $supports = Support::where('lender_id', $lender)->where('response', 'like', '%' . $myStatus . '%')
                    ->orderBy('created_at', 'asc')
                    ->get();
            } else {
                $supports = Support::where('lender_id', $lender)->where('response', 'like', '%' . $myStatus . '%')->whereBetween('created_at', [$_from, $_to])
                    ->orderBy('created_at', 'asc')->get();
            }
            foreach ($supports as $key => $value) {
                $data = (object) null;
                $data = Support::with('user')->where('reply_id', $value->reply_id)->orderBy('created_at', 'asc')->first();
                $data->replies = Support::with('user')->where('reply_id', $value->reply_id)->where('lender_id', $value->lender_id)->orderBy('created_at', 'asc')->get();
                $data->user = User::where('id', $value->user_id)->first(['id', 'name']);
                $data->receiver = User::where('id', $value->receiver_id)->first(['id', 'name']);

                $lastIndex = count($data->replies) - 1;
                $lastReply = $data->replies[$lastIndex];
                if (strpos($lastReply->response, $myStatus) !== false) {
                    $filteredSupport[] = $data;
                }
            }
        } else if ($status == 'open') {
            $not = 'closed,closed';
            if ($_from == '') {
                $supports = Support::where('lender_id', $lender)
                    ->where('response', '<>', $not)
                    ->orderBy('created_at', 'asc')->get();
            } else {
                $supports = Support::where('lender_id', $lender)
                    ->where('response', '<>', $not)->whereBetween('created_at', [$_from, $_to])
                    // ->skip($skip)->take($limit)
                    ->orderBy('created_at', 'asc')->get();
            }

            foreach ($supports as $key => $value) {
                if ($value->top_query == 1) {
                    $data = (object) null;
                    $data = Support::with('user')->where('lender_id', $lender)->where('top_query', 1)->where('reply_id', $value->reply_id)->orderBy('created_at', 'asc')->first();

                    $data->replies = Support::with('user')->where('reply_id', $value->reply_id)->where('lender_id', $value->lender_id)->orderBy('created_at', 'asc')->get();
                    $data->user = User::where('id', $value->user_id)->first(['id', 'name']);
                    $data->receiver = User::where('id', $value->receiver_id)->first(['id', 'name']);

                    $lastIndex = count($data->replies) - 1; // lat index
                    $lastReply = $data->replies[$lastIndex]; // Get last reply for ticket
                    if (strpos($lastReply->response, $not) === false) { // if it doesn't contain "closed,closed" string push to array
                        $filteredSupport[] = $data;
                    }
                }
            }
        }
        // else if($status == 'awaiting-response'){
        //     $status = 'awaiting response';
        // }

        return $filteredSupport;
    }
}

if (!function_exists('parseTransaction')) {
    function parseTransaction($transaction, $repayment_ref, $transaction_amount, $transaction_date, $staff)
    {
        $approved_transactions = [];

        if ($transaction) {
            $remita = RemitaTransaction::where('id', $transaction->remita_transaction_id)->first();
            if ($remita) {
                $loan = Loan::where('id', $transaction->loan_id)->first();

                $pays = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)->get();

                // update
                $remita->bank_repayment_ref = $repayment_ref;
                $remita->save();

                if (count($pays) == 0 || $loan->liquidate_approve == 1) {
                    info('loans has been fully repaid: ' . $loan->id . ' transaction: ' . $transaction->id);

                    $transaction->approved = count($pays) == 0 ? -2 : -1;
                    $transaction->amount = $transaction_amount;
                    $transaction->repayment_date = $transaction_date;
                    $transaction->save();
                } else {
                    $ref = null;
                    $outAmount = calcOutstRemita($loan->id, $loan->total_deduction, $transaction_amount);

                    if (count($pays) == 1 && $outAmount <= 0) {
                        $merchantId = env('MERCHANT_ID');
                        $apiKey = env('API_KEY');
                        $apiToken = env('API_TOKEN');

                        $requestId = time() * 1000;
                        $apiHash = hash('sha512', $apiKey . $requestId . $apiToken);
                        $authorization = "remitaConsumerKey=" . $apiKey . ", remitaConsumerToken=" . $apiHash;
                        $approved = json_decode($loan->approved_remita);

                        $body = [
                            "authorisationCode" => $loan->auth_code,
                            "customerId" => $loan->user->customer_id,
                            "mandateReference" => $approved->data->mandateReference,
                        ];

                        try {
                            $sl = (new Remita())->stopLoanCollection($merchantId, $apiKey, $requestId, $authorization, $body);

                            $status = $sl == null ? 'failed' : ($sl->responseCode == '00' ? 'success' : $sl->responseMsg);

                            logAPI('stop-loan', $loan->user->phone, $status);

                            if ($sl != null) {
                                info('stop loan:-> ' . json_encode($sl));
                                if ($sl->responseCode != '00') {
                                    Log::error('RemitaTransController.php Line 203 stop loan error:-> ' . json_encode($sl));
                                } else {
                                    $ref = json_encode($sl);
                                }
                            }
                        } catch (\Exception $e) {
                            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                        }
                    }

                    // calculate month
                    $month = date('F');
                    $pay = PaymentDate::where('paid', 0)->where('loan_id', $loan->id)->first();
                    if ($pay) {
                        $month = Carbon::createFromFormat('Y-m-d H:i:s', $pay->payment_date)->format('F');
                    }

                    $transaction->amount = $transaction_amount;
                    $transaction->approved = 1;//$transaction_amount >= $loan->monthly_deduction ? 1 : 2;
                    $transaction->approved_by = $staff;
                    $transaction->approved_at = date('Y-m-d H:i:s');
                    $transaction->description = strtoupper('REMITA') . ' Deduction for the Month of ' . $month;
                    $transaction->status = 'Completed';
                    $transaction->principal = $transaction_amount - $loan->monthly_interest;
                    $transaction->interest = $loan->monthly_interest;
                    $transaction->outst_amount = $outAmount;
                    $transaction->reference = $ref;
                    $transaction->repayment_date = $transaction_date;
                    $transaction->save();

                    $w_debit = Wallet::where('user_id', $loan->user_id)->where('loan_id', $loan->id)->where('type', 'debit')->where('category', 'repay_loan')->whereNull('transaction_id')->first();
                    if ($w_debit) {
                        $w_debit->amount = $transaction_amount * -1;
                        $w_debit->transaction_id = $transaction->id;
                        $w_debit->approved_by = $staff;
                        $w_debit->paid = 1;
                        $w_debit->save();
                    } else {
                        // debit user account
                        $w_debit = Wallet::create([
                            'user_id' => $loan->user_id,
                            'loan_id' => $loan->id,
                            'category' => 'repay_loan',
                            'type' => 'debit',
                            'amount' => $transaction_amount * -1,
                            'transaction_id' => $transaction->id,
                            'approved' => 1,
                            'approved_by' => 1,
                            'approved_at' => date('Y-m-d H:i:s'),
                            'lender_id' => $loan->lender_id,
                            'is_lender' => 0,
                            'payment_date' => $pay->payment_date,
                            'paid' => 1,
                        ]);
                    }

                    // credit user wallet to repay loan
                    $wallet1 = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'credit',
                        'amount' => $transaction_amount,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $staff,
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'paid' => 1,
                    ]);

                    // credit lender
                    $wallet2 = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'credit',
                        'amount' => $transaction_amount,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $staff,
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 1,
                        'paid' => 1,
                    ]);

                    $notify = doNotify($w_debit->user_id, 'wallets', $w_debit->id, 'repay_loan', 'success', null, $w_debit->lender_id, $staff);

                    $liquidated = liquidateLoan($loan->id);

                    if ($pay) {
                        $pay->paid = 1;
                        $pay->paid_at = date('Y-m-d H:i:s');
                        $pay->transaction_id = $transaction->id;
                        $pay->save();
                    }

                    $approved_transactions[] = $transaction->id;

                    $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'repayment_approved', 'success', null, $loan->lender_id, $staff);
                }
            }
        }

        return $approved_transactions;
    }
}
