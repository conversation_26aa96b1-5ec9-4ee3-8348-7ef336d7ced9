import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';

import { doNotify, notifyDone } from '../actions/general';
import { baseURL, httpRequest, lenderAPI } from '../config/constants';
import loading from '../assets/img/loading.gif';
import all_states from '../assets/json/states';
import { addLender } from '../actions/lender';

const validate = values => {
	const errors = {};
	if (!values.name) {
		errors.name = 'Enter the lender name';
	}
	if (!values.email) {
		errors.email = 'Enter email address';
	}
	if (!values.address) {
		errors.address = 'Enter your address';
	}
	if (!values.phone) {
		errors.phone = 'Please give us the phone number';
	}
	if (!values.bank) {
		errors.bank = 'Select bank';
	}
	if (!values.state) {
		errors.state = 'Select state';
	}
	if (!values.account_number) {
		errors.account_number = 'Enter account number';
	}
	if (!values.next_of_kin) {
		errors.next_of_kin = 'Enter next of kin';
	}
	if (!values.fullname) {
		errors.fullname = 'Enter full name';
	}
	if (!values.username) {
		errors.username = 'Enter username';
	}
	if (!values.password) {
		errors.password = 'Enter password';
	}
	return errors;
};

const renderField = ({ input, id, label, type, disabled, meta: { touched, error } }) => (
	<div className={`form-group ${touched && error && 'has-error'}`}>
		<label htmlFor={id}>{label}</label>
		<input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
		{touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
	</div>
);

const renderSelectField = ({ input, label, data, meta: { touched, error } }) => (
	<div className={`form-group ${touched && error && 'has-error'}`}>
		<label>{label}</label>
		<select {...input} className="form-control">
			<option value="">{`Select ${label}`}</option>
			{data.map((c, i) => (
				<option value={c.id} key={i}>
					{c.name}
				</option>
			))}
		</select>
		{touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
	</div>
);

const renderAppendField = ({input, id, label, disabled, type, append, meta: {touched, error, warning}}) => (
	<div className={`form-group ${(touched && error && 'has-error')}`}>
		<label htmlFor={id}>{label}</label>
		<div className="input-group">
			<input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
			<div className="input-group-append">
				<div className="input-group-text">{append}</div>
			</div>
		</div>
		{touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
	</div>
);

class CreateLender extends Component {
	doCreateLender = async data => {
		const { user, lender } = this.props;
		try {
			const details = {
				...data,
				user_id: user.id,
				lender_id: lender.id,
				auto_disburse: data.auto_disburse ? 1 : 0,
			};
			const response = await httpRequest(`${baseURL}${lenderAPI}`, 'POST', true, details);
			this.props.addLender(response.lender);
			this.props.reset('new-lender');
			this.props.notifyDone(true);
			this.notify('', 'new lender profile created!', 'success');
		}
		catch (error) {
			const message = error.message || 'could not create lender profile';
			this.notify('', message, 'error');
			throw new SubmissionError({
				_error: message,
			});
		}
	};

	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	render() {
		const { handleSubmit, error, submitting, pristine, banks } = this.props;
		const states = all_states.map(({ state }) => ({ id: state.id, name: state.name }));
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="row">
						<div className="col-lg-12">
							<div className="padded-lg" style={{ paddingRight: 0, paddingLeft: 0 }}>
								<div className="element-wrapper">
									<h6 className="element-header">Create Lender Profile</h6>
									<div className="element-box">
										{error && (
											<div
												className="alert alert-danger"
												style={{ marginBottom: '15px' }}
												dangerouslySetInnerHTML={{
													__html: `<strong>Error!</strong> ${error}`,
												}}></div>
										)}
										<form onSubmit={handleSubmit(this.doCreateLender)}>
											<div className="row">
												<div className="col-sm-4">
													<Field
														name="name"
														id="name"
														type="text"
														component={renderField}
														label="Lender/Organization Name"
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="interest_rate"
														id="interest_rate"
														type="number"
														component={renderAppendField}
														label="Interest Rate"
														append="%"
														disabled={true}
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="commission"
														id="commission"
														type="number"
														component={renderAppendField}
														label="Commission"
														append="%"
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="email"
														id="email"
														type="email"
														component={renderField}
														label="Email"
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="phone"
														id="phone"
														type="text"
														component={renderField}
														label="Phone Number"
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="address"
														id="address"
														type="text"
														component={renderField}
														label="Address"
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="state"
														component={renderSelectField}
														label="State"
														data={states}
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="country"
														id="country"
														type="text"
														component={renderField}
														label="Country"
														disabled={true}
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="bank"
														component={renderSelectField}
														label="Bank"
														data={banks}
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="account_number"
														id="account_number"
														type="text"
														component={renderField}
														label="Account Number"
													/>
												</div>
												<div className="col-sm-4">
													<Field
														name="next_of_kin"
														id="next_of_kin"
														type="text"
														component={renderField}
														label="Next of Kin"
													/>
												</div>
											</div>
											<fieldset>
												<legend className="text-gray-dark">Loan Settings</legend>
												<div className="row">
													<div className="col-sm-4">
														<div className="form-group row">
															<label className="col-sm-5 col-form-label text-right" htmlFor="auto_disburse">Auto Disburse</label>
															<div className="col-sm-7">
																<div className="input-group">
																	<Field
																		name="auto_disburse"
																		id="auto_disburse"
																		component="input"
																		type="checkbox"
																		style={{marginTop: '8px'}}
																	/>
																</div>
															</div>
														</div>
													</div>
												</div>
											</fieldset>
											<fieldset>
												<legend className="text-gray-dark">Super Admin User Profile</legend>
												<div className="row">
													<div className="col-sm-4">
														<Field
															name="fullname"
															id="fullname"
															type="text"
															component={renderField}
															label="Full Name"
														/>
													</div>
													<div className="col-sm-4">
														<Field
															name="username"
															id="username"
															type="text"
															component={renderField}
															label="Username"
														/>
													</div>
													<div className="col-sm-4">
														<Field
															name="password"
															id="password"
															type="password"
															component={renderField}
															label="Password"
														/>
													</div>
												</div>
											</fieldset>
											<div className="form-buttons-w">
												<button
													className="btn btn-primary"
													disabled={pristine || submitting}
													type="submit">
													{submitting ? <img src={loading} alt="" /> : 'Create Profile'}
												</button>
											</div>
										</form>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}

CreateLender = reduxForm({
	form: 'new-lender',
	validate,
})(CreateLender);

const mapStateToProps = state => {
	const lender = state.lender.profile;
	return {
		initialValues: {
			bank: '',
			state: '',
			country: 'Nigeria',
			interest_rate: lender.interest_rate,
			auto_disburse: lender.auto_disburse === 1,
		},
		user: state.user.user,
		banks: state.general.banks,
		lender: state.lender.profile,
	};
};

export default connect(
	mapStateToProps,
	{ reset, doNotify, notifyDone, addLender },
)(CreateLender);
