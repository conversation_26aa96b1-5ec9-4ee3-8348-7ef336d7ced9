<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDebitWalletRequestToUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->tinyInteger('debit_request')->default(0)->after('remember_token');
            $table->mediumText('debit_request_reason')->nullable()->after('debit_request');
            $table->decimal('debit_amount', 20, 2)->nullable()->after('debit_request_reason');
            $table->dateTime('debit_request_at')->nullable()->after('debit_amount');
            $table->bigInteger('debit_request_by')->unsigned()->nullable()->after('debit_request_at');

            $table->foreign('debit_request_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['debit_request_by']);
            $table->dropColumn(['debit_request', 'debit_request_reason', 'debit_request_at', 'debit_request_by']);
        });
    }
}
