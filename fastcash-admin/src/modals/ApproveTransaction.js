import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import sha512 from 'crypto-js/sha512';

import loading from '../assets/img/loading.gif';
import { baseURL, httpRequest, transactionAPI, apiKey, apiToken, merchantId } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import { setLoanAnalytics } from '../actions/loan';
import { setUnapprovedTransactions } from '../actions/user';
import { doNotify } from '../actions/general';

const ApproveTransaction = ({ closeModal, transID, refetch, loan, customer }) => {
	const [submitting, setSubmitting] = useState(false);
	const [error, setError] = useState('');
	const [bypass, setBypass] = useState(false);

	const user = useSelector((state) => state.user.user);

	const dispatch = useDispatch();

	const approve = async () => {
		const action = window.confirm('Do you want to approve transaction?') ? true : false;
		if (action) {
			setError('');
			setSubmitting(true);
			dispatch(startBlock());

			let remitaData = {};

			if (loan && customer && loan.platform === 'remita') {
				const approved = JSON.parse(loan.approved_remita);
				if (approved) {
					const d = new Date();
					const requestId = d.getTime();
					const apiHash = sha512(`${apiKey}${requestId}${apiToken}`);
					const authorization = `remitaConsumerKey=${apiKey}, remitaConsumerToken=${apiHash}`;
					const body = {
						authorisationCode: loan.auth_code,
						customerId: customer.customer_id,
						mandateReference: approved.data.mandateReference,
					};
					remitaData = { merchantId, apiKey, requestId, authorization, body };
				}
			}

			try {
				const data = { ...remitaData, staff_id: user.id, bypass_remita: bypass ? 1 : 0 };
				const url = `${baseURL}${transactionAPI}/approve/${transID}`;
				const response = await httpRequest(url, 'PUT', true, data);
				dispatch(setLoanAnalytics(response.loan_details));
				dispatch(setUnapprovedTransactions(response.unapproved_transactions));
				notify('', 'Transaction Approved!', 'success');
				setSubmitting(false);
				dispatch(stopBlock());
				refetch();
				closeModal();
			} catch (e) {
				console.log(e);
				dispatch(stopBlock());
				const message = e.message || 'could not approve transaction';
				setSubmitting(false);
				setError(message);
			}
		}
	};

	const notify = (title, message, type) => {
		dispatch(doNotify({ message, level: type, title }));
	};

	const toggleBypass = () => setBypass(!bypass);

	return (
		<div>
			<div className="modal-header faded smaller">
				<div className="modal-title">
					<span>Approve Transactions</span>
				</div>
				<button aria-label="Close" className="close" onClick={closeModal} type="button"><span aria-hidden="true"> ×</span></button>
			</div>
			<div className="modal-body">
				{error !== '' && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}/>}
				<div className="row">
					<div className="col-md-12">
						<div className="form-check">
							<label className="form-check-label">
								<input type="checkbox" className="form-check-input" onChange={toggleBypass} checked={bypass}/> Bypass Remita Stop Loan Notification?
							</label>
						</div>
					</div>
				</div>
				<div className="modal-footer buttons-on-left">
					<button className="btn btn-teal" disabled={submitting} type="button" onClick={() => approve()}>
						{submitting ? <img src={loading} alt=""/> : 'Submit'}
					</button>
					<button className="btn btn-link cursor" onClick={closeModal} type="button"> Cancel</button>
				</div>
			</div>
		</div>
	);
};

export default ApproveTransaction;
