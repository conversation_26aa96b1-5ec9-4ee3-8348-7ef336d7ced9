import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';

import { baseURL, httpRequest, userAPI } from '../config/constants';
import { doNotify } from '../actions/general';
import loading from '../assets/img/loading.gif';
import all_states from '../assets/json/states';
import { updateUser } from '../actions/user';
import UserData from '../components/UserData';
import RemitaUserData from '../components/RemitaUserData';

const validate = values => {
    const errors = {};
    if (!values.phone) {
        errors.phone = 'Enter your phone number';
    }
    if (!values.email) {
        errors.email = 'Enter your email address';
    }
    if (!values.bvn) {
        errors.bvn = 'Enter your bvn number';
    }
    if (!values.state) {
        errors.state = 'Select your state of origin';
    }
    if (!values.lga) {
        errors.lga = 'Select your local govt area';
    }
    if (!values.address1) {
        errors.address1 = 'Enter your address';
    }
    if (!values.nok_firstname) {
        errors.nok_firstname = 'Enter next of kin\'s first name';
    }
    if (!values.nok_lastname) {
        errors.nok_lastname = 'Enter next of kin\'s last name';
    }
    if (!values.nok_relationship) {
        errors.nok_relationship = 'Enter your relationship with next of kin';
    }
    if (!values.nok_phone) {
        errors.nok_phone = 'Enter next of kin\'s phone number';
    }
    if (!values.nok_address1) {
        errors.nok_address1 = 'Enter next of kin\'s address';
    }
    if (!values.employer) {
        errors.employer = 'Enter employer office';
    }
    if (!values.gender) {
        errors.gender = 'Select your gender';
    }
    if (!values.agree) {
        errors.agree = 'You have not agreed to our terms and conditions';
    }
    return errors;
};

const genders = [
    {id: 'male', name: 'Male'},
    {id: 'female', name: 'Female'},
];

const renderField = ({input, id, label, type, disabled, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

const renderSelectField = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label>{label}</label>
        <select {...input} className="form-control">
            <option value="">{`Select ${label}`}</option>
            {data.map((c, i) => <option value={c.id} key={i}>{c.name}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class EditProfile extends Component {
	state = {
		submitting: false,
		lga: [],
		message: '',
	};

    componentDidMount() {
		const { user } = this.props;
		const stateOfOrigin = user.state_of_origin || '';
		if (stateOfOrigin) {
			this.changeLGA(stateOfOrigin);
		} else {
			this.changeLGA(1);
		}
    }
    
    updateLGA = e => {
        const id = e.target.value;
        this.changeLGA(id);
    }

    changeLGA = id => {
		const lga = all_states.filter((c, i) => c.state.id === parseInt(id, 10));
		if(lga){
			this.setState({ lga: lga[0].state.locals });
		}
    }

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};
    
    saveProfile = async data => {
		const { user, payslip } = this.props;
		this.setState({ message: '' });

        if(payslip && payslip.biometric !== data.bvn && user.platform === 'sme') {
            window.scrollTo(0, 0);
            const message = 'Error, incorrect bvn';
            this.notify('', message, 'error');
            throw new SubmissionError({
                _error: message,
            });
        }

        try {
			const url = `${baseURL}${userAPI}/profile/${user.id}`;
			const rs = await httpRequest(url, 'PUT', true, data);
			this.props.updateUser(rs.user);
			this.notify('', 'Profile saved.', 'success');
			this.setState({ message: 'Profile saved.' });
            window.scrollTo(0, 0);
        }
        catch (error) {
            window.scrollTo(0, 0);
            const info = error.message || 'Error, check your connection and try again';
            this.notify('', info, 'error');
            throw new SubmissionError({
                _error: info,
            });
        }
    };

    render() {
        const { handleSubmit, error, user, bvn, ippis, employer, phone, lender, location } = this.props;
		const { submitting, lga, message } = this.state;
		const states = all_states.map(({ state }) => ({ id: state.id, name: state.name }));
        return (
			<div className="content-i" style={{tableLayout: 'fixed', display: 'table', width: '100%'}}>
				<div className="content-box">
					<div className="element-box">
						{error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
						{message !== '' && <div className="alert alert-success" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Success!</strong> ${message}`}}></div>}
						<form onSubmit={handleSubmit(this.saveProfile)}>
							<div className="row">
								<div className="col-sm-6">
									<Field
										name="ippis"
										id="ippis"
										type="number"
										component={renderField}
										label="IPPIS Number"
										disabled={ippis !== ''}
									/>
								</div>
								<div className="col-sm-6">
									<Field
										name="employer"
										id="employer"
										type="text"
										component={renderField}
										label="Employer Office"
										disabled={employer !== ''}
									/>
								</div>
							</div>
							<div className="row">
								<div className="col-sm-6">
									<Field
										name="email"
										id="email"
										type="email"
										component={renderField}
										label="Email Address"
									/>
								</div>
								<div className="col-sm-6">
									<Field
										name="gender"
										component={renderSelectField}
										label="Gender"
										data={genders}
									/>
								</div>
							</div>
							<div className="row">
								<div className="col-sm-6">
									<Field
										name="phone"
										id="phone"
										type="text"
										component={renderField}
										label="Phone Number"
										disabled={phone !== ''}
									/>
								</div>
								<div className="col-sm-6">
									<Field
										name="bvn"
										id="bvn"
										type="text"
										component={renderField}
										label="Salary Account BVN Number"
										disabled={bvn !== ''}
									/>
								</div>
							</div>
							<div className="row">
								<div className="col-sm-6">
									<Field
										name="state"
										component={renderSelectField}
										label="State of Origin"
										data={states}
										onChange={this.updateLGA.bind(this)}
									/>
								</div>
								<div className="col-sm-6">
									<Field
										name="lga"
										component={renderSelectField}
										label="Local Govt Area"
										data={lga}
									/>
								</div>
							</div>
							<div className="row">
								<div className="col-sm-12">
									<Field
										name="address1"
										id="address1"
										type="text"
										component={renderField}
										label="Address"
									/>
								</div>
							</div>
							<fieldset className="form-group">
								<legend><span>Next of Kin</span></legend>
								<div className="row">
									<div className="col-sm-6">
										<Field
											name="nok_firstname"
											id="nok_firstname"
											type="text"
											component={renderField}
											label="First Name"
										/>
									</div>
									<div className="col-sm-6">
										<Field
											name="nok_lastname"
											id="nok_lastname"
											type="text"
											component={renderField}
											label="Last Name"
										/>
									</div>
								</div>
								<div className="row">
									<div className="col-sm-6">
										<Field
											name="nok_relationship"
											id="nok_relationship"
											type="text"
											component={renderField}
											label="Relationship"
										/>
									</div>
									<div className="col-sm-6">
										<Field
											name="nok_phone"
											id="nok_phone"
											type="text"
											component={renderField}
											label="Phone Number"
										/>
									</div>
								</div>
								<div className="row">
									<div className="col-sm-12">
										<Field
											name="nok_address1"
											id="nok_address1"
											type="text"
											component={renderField}
											label="Address"
										/>
									</div>
								</div>
							</fieldset>
							<div className="form-buttons-w">
								<button className="btn btn-primary" disabled={submitting} type="submit">
									{submitting? <img src={loading} alt=""/> : 'Save'}
								</button>
							</div>
						</form>
					</div>
				</div>
				{(user && (user.platform === 'ippis' || user.platform === 'sme')) && (
					<UserData user={user} lender={lender} location={location}/>
				)}
				{(user && user.platform === 'remita') && (
					<RemitaUserData user={user} lender={lender} location={location}/>
				)}
			</div>
        );
    }
}

EditProfile = reduxForm({
    form: 'edit-profile',
    validate
})(EditProfile);

const mapStateToProps = (state, ownProps) => {
	const user = state.user.user;

	return {
		initialValues: {
            ippis: user.ippis,
            employer: user.employer,
            email: user.email,
            gender: user.gender,
            phone: user.phone,
            bvn: user.bvn,
            state: user.state_of_origin,
            lga: user.lga_of_origin,
            address1: user.address1,
            nok_firstname: user.nok_firstname,
            nok_lastname: user.nok_lastname,
            nok_relationship: user.nok_relationship,
            nok_phone: user.nok_phone,
            nok_address1: user.nok_address1,
		},
		
		user: state.user.user,
		payslip: state.user.payslip,
		lender: state.lender.profile,

		bvn: user.bvn || '',
		ippis: user.ippis || '',
        employer: user.employer || '',
        phone: user.phone || '',
	}
};

export default connect(mapStateToProps, { reset, doNotify, updateUser })(EditProfile);
