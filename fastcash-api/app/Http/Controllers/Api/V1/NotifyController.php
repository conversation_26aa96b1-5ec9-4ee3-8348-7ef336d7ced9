<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Loan;
use App\Models\Office;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Log;
use DB;

class NotifyController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $lender = $request->has('lender') ? $request->input('lender') : '';

            if ($lender != '') {
                $notifications = DB::table('notifies')
                    ->join('loans', 'loans.id', '=', 'notifies.category_id')
                    ->where('notifies.category', 'loans')
                    ->where('notifies.action_id', 1)
                    ->where('loans.status', 0)
                    ->where('loans.cancel_request', -1) 
                    ->where('notifies.lender_id', $lender)
                    ->orderBy('notifies.created_at', 'desc')
                    ->select('notifies.*')
                    ->get();

                $notifications = collect($notifications)->map(function ($item, $key) {
                    $loan = Loan::where('id', $item->category_id)->first(['id', 'approved', 'disbursed', 'status', 'verified', 'user_id', 'office_id']);
                    if ($loan) {
                        $loan->user = User::where('id', $loan->user_id)->first(['id', 'name', 'phone']);
                        $loan->office = Office::where('id', $loan->office_id)->first(['id', 'name']);
                    }

                    $item->loan = $loan;
                    return $item;
                });

                return response()->json(compact('notifications'), 200);
            }

            return response()->json(['error' => 'could not fetch notifications'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not fetch notifications'], 500);
        }
    }
}
