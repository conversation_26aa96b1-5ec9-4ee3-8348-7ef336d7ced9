/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { Route, Switch } from 'react-router-dom';
import Loadable from 'react-loadable';

import Loading from '../components/Loading';

const ManageOffices = Loadable({ loader: () => import('./ManageOffices'), loading: Loading });
const ViewOffice = Loadable({ loader: () => import('./ViewOffice'), loading: Loading });
const ViewSchedule = Loadable({ loader: () => import('./ViewSchedule'), loading: Loading });
const UploadPayslip = Loadable({ loader: () => import('./UploadPayslip'), loading: Loading });
const UploadCustomers = Loadable({ loader: () => import('./UploadCustomers'), loading: Loading });
const UploadSchedule = Loadable({ loader: () => import('./UploadSchedule'), loading: Loading });

class Offices extends Component {
    render() {
        const { match } = this.props;
        return (
            <Switch>
                <Route path={`${match.url}`} component={ManageOffices} exact />
                <Route path={`${match.url}/view/:id`} component={ViewOffice} />
                <Route path={`${match.url}/upload-payslips`} component={UploadPayslip} />
                <Route path={`${match.url}/upload-schedule`} component={UploadSchedule} />
                <Route path={`${match.url}/upload-customers`} component={UploadCustomers} />
                <Route path={`${match.url}/schedule/:id`} component={ViewSchedule} />
            </Switch>
        );
    }
}

export default Offices;