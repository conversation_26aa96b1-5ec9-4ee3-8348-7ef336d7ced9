/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react'
import qs from 'querystring';
import { connect } from 'react-redux';
import { httpRequest, baseURL, walletAPI, formatDate, currency } from '../config/constants';
import ListHOC from '../container/ListHOC';
import { startBlock, stopBlock } from '../actions/ui-block';
import { doNotify } from '../actions/general';
import { setWalletDebitRequests } from '../actions/user';
import Tooltip from 'antd/lib/tooltip';
import history from '../history';

export class WalletDebitRequest extends Component {
    state = {
        requests: [],
		currentPage: 1,
		totalPage: 0,
		location: '',
		status: '',
		fromDate: null,
		toDate: null,
		pageSize: 12,
    }

    componentDidMount() {
		const { location, user } = this.props;
        if(user.role !== 'super'){
			history.push('/dashboard');
            return;
        }
		
		const query = qs.parse(location.search.replace('?', ''));		
		const page = query && query.p ? parseInt(query.p, 10) : 1;

		this.setState({ currentPage: page, location: location.pathname });
		this.fetch(page);
	}

    componentWillUpdate(nextProps, nextState) {
    }

    fetch = async (pageNumber, status = '', from = '', to = '') => {
        this.props.startBlock();
        try {
			const { lender } = this.props;
            const { pageSize } = this.state;

            const url = `${baseURL}${walletAPI}?page=${pageNumber}&pagesize=${pageSize}&lender=${lender.id}&status=${status}&from=${from}&to=${to}&type=debit-requests`;
			const rs = await httpRequest(url, 'GET', true);

			const result = rs.result;
			this.setState({ currentPage: pageNumber, totalPage: result.total, requests: result.data });
			this.props.stopBlock();
			window.scrollTo(0, 0);
		} catch (e) {
            console.log(e);
			this.props.stopBlock();	
			this.notify('', 'Could not load Wallet Debit Requests!', 'error');
		}
    }

    approveRequest = async (id) => {
		const action = window.confirm("Do you want to approve this wallet debit request?") ? true : false;
        if (action) {
			this.props.startBlock();			
            try {
				const { user } = this.props;
    			const { currentPage } = this.state;

				const data = { staff_id: user.id };

				const rs = await httpRequest(`${baseURL}${walletAPI}/approve-debit/${id}`, 'POST', true, data);
    			this.notify('', 'Wallet Debit Request Approved!', 'success');
                this.props.setWalletDebitRequests(rs.wallet_debit_requests)
				this.props.stopBlock();
				this.fetch(currentPage);
               
			} catch (e) {
				console.log(e);
				this.props.stopBlock();
				const message = e.message || 'Could not approve request';
    			this.notify('', message, 'error');
			}
        }
    }

    declineRequest = async (id) => {
        const action = window.confirm("Do you want to decline this wallet debit request?") ? true : false;
        if (action) {
            this.props.startBlock();			
            try {
				const { user } = this.props;
    			const { currentPage } = this.state;

				const data = { staff_id: user.id };

				const rs = await httpRequest(`${baseURL}${walletAPI}/decline-debit/${id}`, 'POST', true, data);
    			this.notify('', 'Wallet Debit Request Declined!', 'success');
                this.props.setWalletDebitRequests(rs.wallet_debit_requests)                
				this.props.stopBlock();
				this.fetch(currentPage);
               
			} catch (e) {
				console.log(e);
				this.props.stopBlock();
				const message = e.message || 'Could not decline request';
    			this.notify('', message, 'error');
			}
        }
    }

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

    render() {
        let { requests } = this.state;
		const { showUserProfile } = this.props;

        return (
            <div className="content-i">
                <div className="content-box">
                    <div className="row">
                        <div className="col-sm-12">
                            <div className="element-wrapper">
                                <h6 className="element-header">Wallet Debit Requests</h6>

                                <div className="row mb-3">
                                    
                                </div>

                                <div className="element-box">
                                    <div className="table-responsive">
                                        <table className="table table-striped table-lightfont">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Debit Amount</th>
                                                    <th>Wallet Balance</th>
                                                    <th>Narration</th>
                                                    <th>Request Date</th>
                                                    <th className="text-center">&nbsp;</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {
                                                    requests.map((item, i) => {
                                                        return (
                                                            <tr key={i}>                                                                
                                                                <td><a onClick={() => showUserProfile(item.id)} className="cursor link">{item.name}</a></td>
                                                                <td>{currency(item.debit_amount)}</td>
                                                                <td>{currency(item.wallet)}</td>
                                                                <td class='reason-text' >
                                                                    <Tooltip title={item.debit_request_reason}>
                                                                        <div style={{ whiteSpace: 'nowrap', width: '80px', overflow: 'hidden', textOverflow: 'ellipsis' }}>{item.debit_request_reason}</div>
                                                                    </Tooltip>
                                                                </td>
                                                                <td>{formatDate(item.debit_request_at, 'YYYY-MM-DD HH:mm:ss')}</td>
                                                                <td nowrap="nowrap">
                                                                    <button className="btn btn-sm btn-primary cursor" onClick={() => this.approveRequest(item.id)}>Approve</button>
                                                                    <button className="btn btn-sm btn-danger cursor ml-1" onClick={() => this.declineRequest(item.id)}><i className="fa fa-trash" /></button>
                                                                </td>
                                                            </tr>
                                                        )
                                                    })
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>                
            </div>
        )
    }
}

const mapStateToProps = (state) => {
	return {
		user: state.user.user,
		lender: state.lender.profile,
	};
};

export default ListHOC(connect(mapStateToProps, { doNotify, startBlock, stopBlock, setWalletDebitRequests })(WalletDebitRequest));
