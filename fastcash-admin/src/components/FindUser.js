import React, { Component } from 'react';
import { connect } from 'react-redux';

import { baseURL, httpRequest, adminFindUserAPI } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import { setUserPayslip, setRemitaPayslip } from '../actions/user';

class FindUser extends Component {
	state = {
		errorMessage: '',
		phone: '',
		accountNumber: '',
		bankId: '',
		platform: 'remita',
	};

	findUser = async e => {
		e.preventDefault();
		
		try {
			this.props.startBlock();
			this.props.setUserPayslip(null);
			this.props.setRemitaPayslip(null);
			this.setState({ errorMessage: '' });
			const { banks } = this.props;
			const { phone, accountNumber, bankId, platform } = this.state;
			const bank = banks.find(b => Number(b.id) === Number(bankId));
			const data = { phone, account_number: accountNumber, bank_code: bank?.code || '', type: this.props.type, platform };
			const url = `${baseURL}${adminFindUserAPI}`;
			const rs = await httpRequest(url, 'POST', true, data);
			this.props.setUserPayslip(rs.result);
			this.props.setRemitaPayslip(rs.result.remitaDatum);
			this.props.stopBlock();
		} catch(e) {
			this.setState({ errorMessage: e.message || 'user not found' });
			this.props.stopBlock();
		}
	};

	cancel = e => {
		e.preventDefault();
		this.props.setUserPayslip(null);
		this.props.setRemitaPayslip(null);
		this.setState({ phone: '', accountNumber: '', bankId: '', errorMessage: '' });
	};

	onChange = e => {
		const phone = e.target.value;
		this.setState({ phone });
	};

	onChangeAcn = e => {
		const account = e.target.value;
		this.setState({ accountNumber: account, bankId: '' });
	};

	onSelectBank = e => {
		const bank_id = e.target.value;
		this.setState({ bankId: bank_id });
	};

	onSelect = e => {
		const platform = e.target.value;
		this.setState({ platform, accountNumber: '', bankId: '' });
	};

	render() {
		const { type, banks } = this.props;
		const { errorMessage, platform, phone, accountNumber, bankId } = this.state;
		return (
			<div className="element-box">
				{errorMessage !== '' && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${errorMessage}`}}></div>}
				<form onSubmit={this.findUser}>
					<div className={`row ${(errorMessage !== '' && 'has-error')}`}>
						<label className="col-sm-2 col-form-label" htmlFor="phone">Phone number</label>
						<div className="col-sm-4">
							<input name="phone" placeholder="Enter phone number or ippis" type="text" className="form-control" value={phone} onChange={this.onChange} />
						</div>
						{type === 'loan' && (
							<div className="col-sm-3">
								<select name="platform" className="form-control" onChange={this.onSelect}>
									<option value="remita">remita</option>
									<option value="ippis">ippis</option>
									<option value="sme">sme</option>
								</select>
							</div>
						)}
						{type === 'loan' && platform!=='remita' &&<div className="col-sm-3">
							<button className="btn btn-primary" type="submit">Find User</button>
							<button className="btn btn-secondary" style={{marginLeft: '12px'}} onClick={this.cancel}>Cancel</button>
						</div>}
					</div>
					{((type === 'loan' && platform === 'remita') || type === 'new-user') && <div className={`row ${(errorMessage !== '' && 'has-error')}`} style={{marginTop: '8px'}}>
						<label className="col-sm-2 col-form-label" htmlFor="account_number">Account number</label>
						<div className="col-sm-4">
							<input name="account_number" placeholder="Enter account number" type="text" className="form-control" value={accountNumber} onChange={this.onChangeAcn} />
						</div>
						<div className="col-sm-3">
							<select name="platform" className="form-control" onChange={this.onSelectBank} value={Number(bankId)}>
								<option value="">Select Bank</option>
								{banks.map(b => <option value={b.id} key={b.id}>{b.name}</option>)}
							</select>
						</div>
						<div className="col-sm-3">
							<button className="btn btn-primary" type="submit">Find User</button>
							<button className="btn btn-secondary" style={{marginLeft: '12px'}} onClick={this.cancel}>Cancel</button>
						</div>
					</div>}
				</form>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		banks: state.general.banks,
	}
};

export default connect(mapStateToProps, { startBlock, stopBlock, setUserPayslip, setRemitaPayslip })(FindUser);
