<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\PaymentDate;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdatePaymentDates extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::beginTransaction();
        try {
            $payments = DB::table('payment_dates')->groupBy('loan_id')->select('loan_id')->get();
            foreach ($payments as $payment) {
                $loan = Loan::find($payment->loan_id);
                if($loan) {
                    $start_date = Carbon::createFromFormat('Y-m-d H:i:s', $loan->start_date);
                    $end_date = Carbon::createFromFormat('Y-m-d H:i:s', $loan->end_date);
                    $dates = monthsBtwDates($start_date, $end_date, true);

                    $pay = PaymentDate::where('loan_id', $loan->id)->get();
                    for ($i = 0; $i < count($dates); $i++) {
                        $p = PaymentDate::find($pay[$i]->id);
                        $p->payment_date = $dates[$i];
                        $p->save();
                    }
                }
            }

            DB::commit();

        } catch (\Exception $e){
            DB::rollBack();
            Log::error($e->getMessage());
        }
    }
}
