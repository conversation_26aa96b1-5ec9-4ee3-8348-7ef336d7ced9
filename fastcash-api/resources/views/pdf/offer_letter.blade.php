<!doctype html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Offer Letter</title>
</head>
<style type="text/css">
    /* latin-ext */
    @font-face {
        font-family: 'Italianno';
        font-style: normal;
        font-weight: 400;
        src: local('Italianno'), local('Italianno-Regular'), url(https://fonts.gstatic.com/s/italianno/v7/dg4n_p3sv6gCJkwzT6RXhpwoYQAugxW4.woff2) format('woff2');
        unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
    }
    /* latin */
    @font-face {
        font-family: 'Italianno';
        font-style: normal;
        font-weight: 400;
        src: local('Italianno'), local('Italianno-Regular'), url(https://fonts.gstatic.com/s/italianno/v7/dg4n_p3sv6gCJkwzT6RXiJwoYQAugw.woff2) format('woff2');
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
    }
    html {
        font-family: sans-serif;
        line-height: 1.15;
        -ms-text-size-adjust: 100%;
        -webkit-text-size-adjust: 100%
    }

    body {
        margin: 0
    }

    article, aside, footer, header, nav, section {
        display: block
    }

    b, strong {
        font-weight: bolder
    }

    img {
        border-style: none
    }

    html {
        -webkit-box-sizing: border-box;
        box-sizing: border-box
    }

    *, *::before, *::after {
        -webkit-box-sizing: inherit;
        box-sizing: inherit
    }

    body {
        font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-size: 10px;
        font-weight: 400;
        line-height: 1.5;
        color: #3E4B5B;
        background-color: #fff
    }

    h1, h2, h3, h4, h5, h6 {
        margin-top: 0;
        margin-bottom: .5rem
    }

    p {
        margin-top: 0;
        margin-bottom: 16px
    }

    ol, ul, dl {
        margin-top: 0;
        margin-bottom: 16px
    }

    ol ol, ul ul, ol ul, ul ol {
        margin-bottom: 0
    }

    img {
        vertical-align: middle
    }

    table {
        border-collapse: collapse;
        background-color: transparent
    }

    th {
        text-align: left
    }

    label {
        display: inline-block;
        margin-bottom: .5rem
    }

    output {
        display: inline-block
    }

    [hidden] {
        display: none !important
    }

    h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
        margin-bottom: .5rem;
        font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-weight: 500;
        line-height: 1.1;
        color: #334152
    }

    small, .small {
        font-size: 80%;
        font-weight: 400
    }

    .row {
        display: block;
        margin-right: -15px;
        margin-left: -15px
    }

    .col-width {
        position: relative;
        width: 100%;
        min-height: 1px;
        padding-right: 15px;
        padding-left: 15px;
    }

    .clearfix::after {
        display: block;
        content: "";
        clear: both
    }

    html {
        height: 100%
    }

    body {
        min-height: 100%;
        position: relative;
        padding: 50px;
        overflow-x: hidden
    }

    body:before {
        content: "";
        position: absolute;
        z-index: -1;
        top: 0px;
        left: 0px;
        bottom: 0px;
        right: 0px
    }

    .invoice-w {
        padding: 20px 1rem;
        /*margin-bottom: 14px;*/
        border-radius: 4px;
        background-color: #fff;
        -webkit-box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1);
        box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1)
    }

    .invoice-w {
        font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        width: 640px;
        margin-left: auto;
        margin-right: auto;
        position: relative;
        overflow: hidden;
        padding-bottom: 0;
    }

    /*.invoice-w:before {
        width: 140%;
        height: 215px;
        background-color: #faf9f3;
        position: absolute;
        top: -13%;
        left: -24%;
        !*-webkit-transform: rotate(-27deg);*!
        !*transform: rotate(-27deg);*!
        -webkit-transform: rotate(-2deg);
        transform: rotate(-2deg);
        content: "";
        z-index: 1
    }*/

    .invoice-w .infos {
        position: relative;
        z-index: 2;
    }

    .invoice-w .infos .info-1 {
        font-size: 10px;
        text-align: right
    }

    .invoice-w .infos .info-1 .company-name {
        font-size: 12px;
        margin-bottom: 0.1rem;
        /*margin-top: 4px*/
    }

    .invoice-w .infos .info-1 .company-extra {
        font-size: 9px;
        color: rgba(0, 0, 0, 0.4);
        margin-top: 8px
    }

    .invoice-w .infos .info-2 {
        padding-top: 4px;
        font-size: 10px;
        text-align: right
    }

    .invoice-w .infos .info-2 .company-name {
        font-size: 12px;
        margin-bottom: 0.1rem;
    }

    .invoice-w .terms {
        font-size: 8px;
    }

    .invoice-w .terms .terms-header {
        font-size: 9px;
        margin-bottom: 10px
    }

    .invoice-w .terms .terms-content {
        color: rgba(0, 0, 0, 0.6);
        font-size: 8px;
    }

    .invoice-w .terms .terms-contents {
        color: rgba(0, 0, 0, 0.6);
        font-size: 10px;
    }

    .invoice-heading {
        margin-bottom: 16px;
        margin-top: 10px;
        position: relative;
        z-index: 2
    }

    .invoice-footer {
        padding-top: 8px;
        padding-bottom: 8px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        display: block;
        margin-top: 2rem;
        font-size: 8px;
    }

    .invoice-footer .invoice-logo img {
        vertical-align: middle;
        height: 20px;
        width: auto;
        display: inline-block
    }

    .invoice-footer .invoice-logo span {
        vertical-align: middle;
        margin-left: 10px;
        display: inline-block
    }

    .invoice-footer .invoice-info span {
        display: inline-block
    }

    .invoice-footer .invoice-info span + span {
        margin-left: 16px;
        padding-left: 16px;
        border-left: 1px solid rgba(0, 0, 0, 0.1)
    }

    .invoice-body {
        display: block;
    }

    .invoice-logo-w img {
        width: 180px;
    }

    .profile-tile-meta ul {padding: 0 0 0 20px; margin: 0;}
    .profile-tile-meta ol {padding: 0 0 0 20px; margin: 0;}
    .float-left {float: left;}
    .float-right {float: right;}
    .motto {font-family: 'Italianno', cursive;font-weight: 100;  font-size: 16px !important;font-style: italic;}
    .table {margin-top: 20px;}

</style>
<body style="padding: 5px;">
<div class="invoice-w">
    <div class="infos" style="position: relative;">
        <div class="invoice-logo-w" style="position: absolute; left: 0; top: 0;"><img alt="logo" src="img/consumer-logo.png"/></div>
        <div class="info-1">
            <div class="company-name"><strong>CONSUMER MICROFINANCE BANK LTD</strong></div>
            <div class="company-name motto">Become a richer you</div>
            <div style="margin-top: 15px">
                <small style="display: block;font-size: 8px;">PLOT 2087, AISHA HOUSE, HERBERT MACAULAY WAY, (OPPOSITE FEBSON MALL) WUSE ZONE 5, ABUJA.</small>
                <small style="display: block;font-size: 8px;">Website: www.consumermfb.com E-mail: <EMAIL></small>
            </div>
        </div>
    </div>
    <div class="invoice-body">
        <div class="invoice-table">
            <div class="terms row">
                <table class="table">
                    <tr>
                        <td style="vertical-align: top;width: 50%;">
                            <div class="col-width">
                                <div class="invoice-heading">
                                    <h5 style="font-size: 9px;font-weight: 600;">LOAN OFFER LETTER</h5>
                                    <div class="terms-content">{{ \Carbon\Carbon::parse($user->loan->created_at)->format('d.m.Y') }}</div>
                                    <div class="terms-content" style="text-transform: uppercase;font-weight: 700;">{{ $user->user->name }}</div>
                                    <div class="terms-content" style="text-transform: uppercase;">{{ $user->user->office->name }}</div>
                                    <div class="terms-content" style="text-transform: uppercase;">BVN: {{ $user->user->bvn }}</div>
                                    <div class="terms-content" style="text-transform: uppercase;">Account No: {{ $user->acn->account_number }}</div>
                                    <div class="terms-content" style="text-transform: uppercase;">Bank: {{ $user->acn->bank_name }}</div>
                                    <div class="terms-content">Dear Sir/Madam</div>
                                </div>
                                <div class="terms-content" style="margin-bottom: 5px;width:100%;font-weight: 600; font-size: 9px; color: inherit;">OFFER OF LOAN OF {{ strtoupper(convertNumber($user->loan->amount)) }} NAIRA ONLY. ({{ number_format($user->loan->amount, 2) }})</div>
                                <div class="terms-content" style="text-align: justify;">We are pleased to advise you that the Management of Consumer MFB LTD has approved your request for a NGN{{ number_format($user->loan->amount, 2) }} ({{ ucwords(convertNumber($user->loan->amount)) }} Naira Only) facilities under the following terms and conditions:
                                </div>
                                <br/>
                                <div class="terms-content">
                                    <div class="profile-tile-meta">
                                        <ul>
                                            <li><span class="span">Lender:</span> CONSUMER MFB LTD ("The Bank")</li>
                                            <li><span class="span">Borrower:</span> <strong>{{ $user->user->name }}</strong> ("The Borrower")</li>
                                            <li><span class="span">Facility Type:</span> Loan</li>
                                            <li><span class="span">Facility Amount:</span> <strong>NGN{{ number_format($user->loan->amount, 2) }} ({{ ucwords(convertNumber($user->loan->amount)) }} Naira Only)</strong></li>
                                            <li><span class="span">Purpose: </span> Consumer/Personal Loan</li>
                                            <li><span class="span">Tenure:</span> {{ $user->loan->tenure }} Months</li>
                                            <li><span class="span">Repayment Plan:</span> Monthly</li>
                                            <li><span class="span">Repayment Source:</span> Payroll deduction from the borrower monthly salary</li>
                                            <li><span class="span">Interest Rate:</span> {{ $user->loan->interest_rate }}% FLAT/MONTHLY, However this rate is subject to conditions & reviews in line with changes in the money market</li>
                                            <li><span class="span">Fees:</span> Nil</li>
                                            <li><span class="span">Insurance:</span> {{ $user->loan->insurance }}%</li>
                                            <li><span class="span">Processing Fee:</span> {{ $user->loan->processing_fee }}%</li>
                                            <li><span class="span">Monthly Repayment Amount:</span> NGN{{ number_format($user->loan->monthly_deduction, 2) }}</li>
                                        </ul>
                                    </div>
                                </div>
                                <br/>
                                <div class="terms-content">
                                    The Borrower, hereby authorize Consumer Microfinance Bank Limited (The Bank) or its third party service provider (Service Provider) to make monthly deductions from his/her payroll for the above loan amount and its interest and other fees thereof.
                                    In consideration for The Bank or its Service Provider making such deductions I releases the Bank and the Service Provider from any and all liability, and waives all error, if any, made by way of the deduction or failure to make deductions.
                                </div>
                                <br/>
                                <div class="terms-content"><strong>Security:</strong> <span style="text-align: justify;">Direct debit instruction issued by the customer authorising the Bank to debit the payroll at source or via any third party service.  And/or Letter of introduction / confirmation of employment signed by the approved signatories of the organisation.</span></div>
                                <br/>
                                <div class="terms-content"><strong>Default Clause:</strong><span style="text-align: justify;">Outstanding repayment after due date shall attract a default fee of 5% and an interest at the rate of 10%./month until payment is received.
                                    The Borrower undertakes to be charged the said default fee of 5% and debit interest of 10% per month even after the expiration of the loan term or expiration of any extension granted until the final liquidation of the loan sum inclusive of default fee and debit interest charge.
                                    The borrower consents to his/her payment data being shared with necessary third parties to facilitate this loan repayment and the loan amount being directly debited from any of his or her BVN related bank account where funds are not directly available from the pre-specified repayment source or bank account'</span></div>
                            </div>
                        </td>
                        <td style="vertical-align: top;width: 50%;">
                            <div class="col-width" style="margin-top: 18px;">
                                <div class="terms-header">REPRESENTATIONS AND WARRANTIES:</div>
                                <div class="terms-content">The borrower presents and warrants that:
                                    <div class="profile-tile-meta">
                                        <ul style="text-align: justify;">
                                            <li>The Borrower has the right to accept the facility and has taken all necessary actions to authorise the same upon the terms and conditions herein.
                                            </li>
                                            <li>The Borrower is not in default in respect of any loan obligation to any other financial institution; and shall not obtain any   payroll deductible loan without written consent of The Lender for the duration of this loan.
                                            </li>
                                            <li>That the acceptance of this facility will not be or result in a breach of or default under any provision of any other agreement to which the Borrower is a party.
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <br/>
                                <div class="terms-header">VALIDITY CLAUSE:</div>
                                <div class="terms-content">
                                    <div class="profile-tile-meta">
                                        <ol>
                                            <li>This offer will lapse at the instance of Bank if not accepted within fourteen (14) days from the date of this letter.</li>
                                            <li>The Borrower will acknowledge acceptance and receipt of the loan by singing this offer and the agreement over leaf or by imprinting his or her left thumbprint on every page of this document and return same to us.</li>
                                        </ol>
                                    </div>
                                </div>
                                <div class="terms-content" style="margin-top: 12px;">Yours faithfully,</div>
                                <div class="terms-content"><strong>For: CONSUMER MICROFINANCE BANK LTD</strong></div>
                                <div style="font-size: 10px;">
                                    <table class="table" cellpadding="0" cellspacing="0" style="{{ $user->loan->has_consented == 1 ? 'width: 100%;margin-top:10px;':'width: 100%;margin-top:40px;' }}">
                                        <tr>
                                            <td style="width: 45%;">
                                                <div>
                                                    @if ($user->loan->has_consented == 1)
                                                    <div style="margin-left: 40px"><img src="img/cmfb-signature1.png" width="50px"/></div>
                                                    @endif
                                                    <div style="border-bottom: 1px dotted #3E4B5B;height:1px;">
                                                    </div>
                                                    <div style="font-size:7px;text-align: center;margin-top: 2px;">AUTHORISED SIGNATORY</div>
                                                </div>
                                            </td>
                                            <td style="width: 10%;">&nbsp;</td>
                                            <td style="width: 45%;">
                                                <div>
                                                    @if ($user->loan->has_consented == 1)
                                                    <div style="margin-left: 40px;margin-top:32px"><img src="img/cmfb-signature2.png" width="50px"/></div>
                                                    @endif
                                                    <div style="border-bottom: 1px dotted #3E4B5B;height:1px;"></div>
                                                    <div style="font-size:7px;text-align: center;margin-top: 2px;">AUTHORISED SIGNATORY</div>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
                                    <br>
                                    <div class="terms-content" style="margin-top: 14px;">Accepted by the Borrower:</div>
                                    @if ($user->loan->has_consented == 1)
                                    <table class="table" cellpadding="0" cellspacing="0" style="width: 100%;margin-top: 6px;">
                                        <tr>
                                            <td style="width: 10%;">
                                                <div class="terms-content">Name:</div>
                                            </td>
                                            <td style="width: 90%;">
                                                {{ $user->user->name }}
                                            </td>
                                        </tr>
                                    </table>
                                    <table class="table" cellpadding="0" cellspacing="0" style="width: 100%;margin-top: 6px;">
                                        <tr>
                                            <td style="width: 22%;">
                                                <div class="terms-content">Requested On:</div>
                                            </td>
                                            <td style="width: 78%;">
                                                {{ \Carbon\Carbon::parse($user->loan->created_at)->format('M d, Y h:m a') }}
                                            </td>
                                        </tr>
                                    </table>
                                    <table class="table" cellpadding="0" cellspacing="0" style="width: 100%;margin-top:6px;">
                                        <tr>
                                            <td style="width: 20%;">
                                                <div class="terms-content">Approved On:</div>
                                            </td>
                                            @if($user->loan->approved_at)
                                            <td style="width: 80%;">
                                                {{ \Carbon\Carbon::parse($user->loan->approved_at)->format('M d, Y h:m a') }}
                                            </td>
                                            @else
                                            <td style="width: 80%;">
                                                <div class="terms-content" style="border-bottom: 1px dotted #3E4B5B;height:9px;"></div>
                                            </td>
                                            @endif
                                        </tr>
                                    </table>
                                    <table class="table" cellpadding="0" cellspacing="0" style="width: 100%;margin-top: 6px;">
                                        <tr>
                                            <td style="width: 28%;">
                                                <div class="terms-content">Offer Accepted On:</div>
                                            </td>
                                            <td style="width: 72%;">
                                                {{ \Carbon\Carbon::parse($user->loan->consented_at)->format('M d, Y h:i a') }} via OTP
                                            </td>
                                        </tr>
                                    </table>
                                    @else
                                    <table class="table" cellpadding="0" cellspacing="0" style="width: 100%;margin-top: 28px;">
                                        <tr>
                                            <td style="width: 10%;">
                                                <div class="terms-content">Name:</div>
                                            </td>
                                            <td style="width: 90%;">
                                                <div class="terms-content" style="border-bottom: 1px dotted #3E4B5B;height:9px;"></div>
                                            </td>
                                        </tr>
                                    </table>
                                    <table class="table" cellpadding="0" cellspacing="0" style="width: 100%;margin-top: 20px;">
                                        <tr>
                                            <td style="width: 28%;">
                                                <div class="terms-content">Signature and Date:</div>
                                            </td>
                                            <td style="width: 72%;">
                                                <div class="terms-content" style="border-bottom: 1px dotted #3E4B5B;height:9px;"></div>
                                            </td>
                                        </tr>
                                    </table>
                                    <br>
                                    <div class="terms-content" style="margin-top: 14px;">In the presence of:</div>
                                    <table class="table" cellpadding="0" cellspacing="0" style="width: 100%;margin-top:24px;">
                                        <tr>
                                            <td style="width: 10%;">
                                                <div class="terms-content">Name:</div>
                                            </td>
                                            <td style="width: 90%;">
                                                <div class="terms-content" style="border-bottom: 1px dotted #3E4B5B;height:9px;"></div>
                                            </td>
                                        </tr>
                                    </table>
                                    <table class="table" cellpadding="0" cellspacing="0" style="width: 100%;margin-top: 20px;">
                                        <tr>
                                            <td style="width: 28%;">
                                                <div class="terms-content">Signature and Date:</div>
                                            </td>
                                            <td style="width: 72%;">
                                                <div class="terms-content" style="border-bottom: 1px dotted #3E4B5B;height:9px;"></div>
                                            </td>
                                        </tr>
                                    </table>
                                    @endif
                                    <table class="table" cellpadding="0" cellspacing="0" style="width: 100%;margin-top: 40px;">
                                        <tr>
                                            <td style="width: 100%;">
                                                <div class="terms-contents">Note: kindly send a valid <NAME_EMAIL></div>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </td>
                    </tr>
                    @if(count($user->dates) > 0)
                    <tr>
                        <td style="vertical-align: top;width: 100%;" colspan="2">
                            <div class="col-width" style="margin-top: 180px;">
                                <div class="terms-header" style="text-align: center; font-weight: bold;">LOAN REPAYMENT SCHEDULE</div>
                            </div>
                            <div class="col-width" style="width: 50%;margin: 0 auto;">
                                <table class="table" cellpadding="0" cellspacing="0" style="width:100%;margin-top: 4px !important;">
                                    <tr>
                                        <th>S/N</th>
                                        <th>Repayment Date</th>
                                        <th>Repayment Amount</th>
                                    </tr>
                                    @foreach($user->dates as $key => $value)
                                        <tr>
                                            <td>{{ $loop->index + 1 }}</td>
                                            <td>{{ \Carbon\Carbon::parse($value)->format('d-m-Y') }}</td>
                                            <td>NGN{{ number_format($user->monthly_deduction, 2) }}</td>
                                        </tr>
                                    @endforeach
                                </table>
                            </div>
                        </td>
                    </tr>
                    @endif
                    <tr>
                        <td style="vertical-align: top;width: 100%;" colspan="2">
                            <div class="col-width" style="margin-top:{{ count($user->dates) > 0 ? '20px' : '180px' }}">
                                <div class="terms-header" style="text-align: center; font-weight: bold;">LOAN AGREEMENT</div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align: top;width: 50%;">
                            <div class="col-width" style="margin-top: 18px;">
                                <ol style="padding: 0 0 0 20px;">
                                    <li><strong>THE LOAN</strong>
                                        <ol style="padding: 0; list-style: none">
                                            <li>1.1 The Loan amount overleaf shall be made available by direct payment into borrower's account with The Bank (The Bank).</li>
                                            <li>1.2 The Borrower will acknowledge receipt of the loan by singing this document or by imprinting his or her left thumb print on this document and the offer overleaf.</li>
                                        </ol>
                                    </li>
                                    <li><strong>INTEREST</strong>
                                        <ol style="padding: 0; list-style: none">
                                            <li>2.1  The Bank will charge interest on the loan amount at the rate stated in the offer letter, and the interest rate will be fixed for the period of the loan. If the Borrower fails to pay any amount which the Borrower owes The Bank in terms of this agreement  on the due date of such amount, The Bank shall be entitle to charge default fee of 5%  and interest of 10% per month on the amount the Borrower has not paid.</li>
                                        </ol>
                                    </li>
                                    <li><strong>PAYMENT</strong>
                                        <ol style="padding: 0; list-style: none">
                                            <li>3.1 The Borrower must repay the loan as shown on the offer letter over leaf.</li>
                                            <li>3.2 The Borrower agrees that The Bank shall have the rights to deduct the monthly instalment in full as set out in the offer letter directly from the Borrower's salary as a deduction from his or her employer's payroll.</li>
                                            <li>3.3 The Borrower hereby gives The Bank the right to deduct monies owing to it from any unpaid wages or any other remuneration credits payable to the Borrower if the Borrower leaves the service of his/her employer for any reason before the total amount repayable under this offer has been paid.</li>
                                            <li>3.4 The Bank can use the money paid by the Borrower to pay first legal and other costs, then interest and then the actual loan amount.</li>
                                            <li>3.5 Penalty Charges may be applied on pre-liquidated loan; 10% at 1 month and 7.5% from 2nd to the 6th month irrespective of the loan tenure.</li>
                                            <li>3.6 In the event of the Borrower's death or permanent disability, the Borrower will be liable for the repayment of any outstanding limited to his/her terminal benefit/gratuity or any other disclosed or undisclosed entitlement from his employer (IFANY).</li>
                                        </ol>
                                    </li>
                                    <li><strong>COSTS AND CHARGES</strong>
                                        <ol style="padding: 0; list-style: none">
                                            <li>4.1 The Borrower agrees that, if The Bank has to use lawyers, Debit Recovery Agents and/or Other Consultants because the Borrower has not carried out any s part of this agreement, the Borrower will pay The Bank all the costs incurred by The Lender.</li>
                                        </ol>
                                    </li>
                                </ol>
                            </div>
                        </td>
                        <td style="vertical-align: top;width: 50%;">
                            <div class="col-width" style="margin-top: 18px;">
                                <ol start="5" style="padding: 0;">
                                    <li><strong>EVENTS OF DEFAULT</strong>
                                        <p>The occurrence of any of the following events shall cause all outstanding under this agreement, together with any penalty interest and all other charges and expenses owing to The Bank by the Borrowers shall become immediately due and payable to The Bank.</p>
                                        <ol style="padding: 0 0 0 20px; list-style-type: lower-roman;">
                                            <li>Any failure by the Borrower to pay amount which owes of this agreement in full and on the date he or she has to,</li>
                                            <li>Any claim that the Borrower has failed to carry his or her duties under this agreement;</li>
                                            <li>Any situation arises which in the Bank’s opinion makes it inappropriate to continue to extend the Facility to the Borrower</li>
                                        </ol>
                                        <p>The Bank shall be entitled to terminate this Agreement and claim and/or recover from the Borrower any damages/losses it may have suffered as consequences.</p>
                                    </li>
                                    <li><strong>GENERAL</strong>
                                        <ol style="padding: 0; list-style: none">
                                            <li>6.1 This agreement and the offer over leaf is the whole agreement between The Bank and the Borrower. It cannot be changed unless the change is put into writing and sign by both The Bank and the Borrowers.</li>
                                            <li>6.2 The Borrower is not in default in respect of any loan obligation to any other financial institution; and shall not obtain any   payroll deductible loan without written consent of The Bank for the duration of this loan.</li>
                                            <li>6.3 All consents, licenses, approvals, authorisations of any governmental authority, bureau, or agency etc., required in connection with the execution, delivery performance, validity or enforceability of this facility shall be obtained by the Borrower and the originals thereof delivered to the Bank and the conditions contained therein or otherwise applicable thereto shall at the appropriate time be complied with or fulfilled. The costs of obtaining such approvals etc shall be borne by the Borrower.</li>
                                            <li>6.4 The Borrower gives The Bank permission to register details of the conduct of the Borrower's account with any credit bureau, and the Borrower waives any claims he or she may have against The Bank in respect of such disclosure.</li>
                                            <li>6.5 Disbursement is subject to provision of letter of employment and confirmation, availability of funds as well as CBN Regulation.</li>
                                        </ol>
                                    </li>
                                </ol>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    <div class="invoice-footer clearfix">
        <div class="invoice-logo" style="float: left">
            <img alt="logo" src="img/consumer-logo.png"/>
            <span>Consumer Microfinance Bank</span>
        </div>
        <div class="invoice-info" style="float: right">
            <span><EMAIL></span>
            <span>0704.634.6454</span>
        </div>
    </div>
</div>
</body>
</html>
