import React, { Component } from 'react';
import { connect } from 'react-redux';

import FindUser from '../components/FindUser';
import { setUserPayslip, setRemitaPayslip } from '../actions/user';
import ProfileData from '../components/ProfileData';
import ProfileForm from '../components/ProfileForm';

class CreateUser extends Component {
	componentWillUnmount() {
		this.props.setUserPayslip(null);
		this.props.setRemitaPayslip(null);
	}

	render() {
		const { user, remitaDatum } = this.props;
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="element-wrapper">
						<h6 className="element-header">Create New User</h6>
						{user && user.profile && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${user.profile.name} is already registered`}}></div>}
						<FindUser type="new-user"/>
						{user && !user.profile && (
							<div className="element-box">
								<div className="content-i">
									<div className="content-box">
										<div className="row">
											{user.payslip && (
												<div className="col-sm-4">
													<ProfileData user={user.payslip} lender={user.lender} />
												</div>
											)}
											<ProfileForm
												user={user.payslip}
												lenders={user.lenders}
												phone={user.phone}
												remitaDatum={remitaDatum}
											/>
										</div>
									</div>
								</div>
							</div>
						)}
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.payslip,
		remitaDatum: state.user.remitaDatum,
	}
};

export default connect(mapStateToProps, { setUserPayslip, setRemitaPayslip })(CreateUser);
