<?php

namespace Database\Seeders;

use App\Models\Loan;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class UpdateLiquidateApproveLoan extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $loans = Loan::all();
            foreach ($loans as $l){
                $loan = Loan::find($l->id);
                $loan->liquidate_approve = ($loan->liquidated == 0 ? 0 : 1);
                $loan->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }
    }
}
