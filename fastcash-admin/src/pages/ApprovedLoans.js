/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import Pagination from 'antd/lib/pagination';
import qs from 'querystring';

import ListHOC from '../container/ListHOC';
import { httpRequest, baseURL, loanAPI } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import LoanData from '../components/LoanData';
import { notifyDone } from '../actions/general';
import loading from '../assets/img/loading.gif';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 10;

class ApprovedLoans extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			currentPage: 1,
			totalPage: 0,
			loans: [],
			office: 0,
			location: '',
			exporting: false,
			editVisible: false,
			loan_id: null,
		};
	}

	componentDidMount() {
		const { location } = this.props;
		const query = qs.parse(location.search.replace('?', ''));
		
		const page = query && query.p ? parseInt(query.p, 10) : 1;
		const office = query && query.office ? parseInt(query.office, 10) : 0;

		this.setState({
			office,
			currentPage: page,
			location: location.pathname,
		});

		this.fetch(page, office);
	}

	fetch = async (pageNumber, _) => {
		try {
			this.props.startBlock();
			const { lender } = this.props;
			const rs = await httpRequest(`${baseURL}${loanAPI}?page=${pageNumber}&pagesize=${pageSize}&category=approved&q=admin&lender=${lender.id}`, 'GET', true);
            const result = rs.result;
			this.setState({ currentPage: pageNumber, totalPage: result.total, loans: result.data });
			this.props.stopBlock();
			this.props.notifyDone(false);
            window.scrollTo(0, 0);
		} catch (e) {
			this.props.stopBlock();
			this.props.doNotify({ message: 'could not fetch loans', level: 'error' });
		}
	};

	exportReport = async e => {
		e.preventDefault();
		try {
			this.setState({ exporting: true });
			const url = `${baseURL}${loanAPI}/export/approved`;
			const rs = await httpRequest(url, 'GET', true);
			if(rs.file) {
				this.setState({ exporting: false });
				this.props.doNotify({ message: 'loans exported', level: 'success' });
				window.open(rs.file, '_blank');
			} else {
				this.props.doNotify({ message: rs.message || 'could not export loans', level: 'success' });
				this.setState({ exporting: false });
			}
		} catch(error) {
			this.setState({ exporting: false });
			const message = error.message || 'could not export loans';
			this.props.doNotify({ message, level: 'error' });
		}
	};

	onChange = pageNumber => {
		const { location } = this.props;
		const query = qs.parse(location.search.replace('?', ''));
		if(query.office){
			this.props.history.push(`/lenders/approved-loans?office=${query.office}&p=${pageNumber}`);
		} else {
			this.props.history.push(`/lenders/approved-loans?p=${pageNumber}`);
		}
	};

	componentWillUpdate(nextProps, nextState) {
		const query = qs.parse(nextProps.location.search.replace('?', ''));
		const pageNumber = query && query.p ? parseInt(query.p, 10) : 1;
		const office = query && query.office ? parseInt(query.office, 10) : 0;
		if(nextState.currentPage !== pageNumber || nextProps.location.pathname !== nextState.location || office !== nextState.office){
			this.setState({
				office,
				location: nextProps.location.pathname,
				currentPage: pageNumber,
			});

			this.fetch(pageNumber, office);
		} else if(nextProps.notifdone) {
			this.fetch(pageNumber, office);
		}
	}

	doHide = () => {
		this.setState({ editVisible: false, loan_id: null });
	};

	showEditLoan = (status, loan) => {
		this.setState({ editVisible: status, loan_id: loan.id });
	};

	render() {
		const { currentPage, totalPage, loans, exporting, editVisible, loan_id } = this.state;
		const { showUserProfile, user, doApproveLoan, openModal, doVerifyLoan, bypass, disburse, verifyPayment, sendConsentSMS, doSendMail, onSaveLoanInsurance } = this.props;
		
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="element-wrapper">
						<div className="element-actions">
							{exporting ? (
								<a className="btn btn-primary btn-sm btn-export-sm pointer"><img src={loading} alt=""/></a>
							) : (
								<a className="btn btn-primary btn-sm btn-export-sm pointer" role="button" tabIndex="0" onClick={this.exportReport}><i className="os-icon os-icon-download-cloud"/><span>Export</span></a>
							)}
						</div>
						<h6 className="element-header">Approved Lender Loans For Disbursement</h6>
						<div className="element-box">
							<div className="table-responsive" style={{ overflowX: 'scroll' }}>
								<table className="table table-striped table-lightfont">
									<thead>
										<tr>
											<th/>
											<th>Lender</th>
											<th>Name of Employee</th>
											<th>IPPIS/Customer ID</th>
											<th>Loan Status</th>
											<th>Disburse</th>
											<th>Tenure</th>
											<th>Loan Consent</th>
											<th>Request Date</th>
											<th>&nbsp;</th>
										</tr>
									</thead>
									<tbody>
										{loans.map((loan, i) => (
											<LoanData
												key={i}
												loan={loan}
												user={user}
												showUserProfile={showUserProfile}
												doVerifyLoan={doVerifyLoan}
												doApproveLoan={doApproveLoan}
												openModal={openModal}
												category="disburse"
												showLender={true}
												verifyPayment={verifyPayment}
												disburse={disburse}
												bypass={bypass}
												sendConsentSMS={sendConsentSMS}
												doSendMail={doSendMail}
												onSaveLoanInsurance={onSaveLoanInsurance}
												editVisible={editVisible}
												loanID={loan_id}
												showEditLoan={this.showEditLoan}
												doHide={this.doHide}
											/>
										))}
									</tbody>
								</table>
							</div>
							<div className="pagination pagination-center mt-4">
								<Pagination
									current={currentPage}
									pageSize={pageSize}
									total={totalPage}
									showTotal={total => `Total ${total} loans`}
									itemRender={itemRender}
									onChange={this.onChange}
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
		notifdone: state.general.notifdone,
		lender: state.lender.profile,
	}
};

export default ListHOC(connect(mapStateToProps, { startBlock, stopBlock, notifyDone })(ApprovedLoans));
