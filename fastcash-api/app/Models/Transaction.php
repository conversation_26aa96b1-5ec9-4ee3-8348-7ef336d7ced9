<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Transaction extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    public function user()
    {
        return $this->belongsTo('App\Models\User');
    }

    public function loan()
    {
        return $this->belongsTo('App\Models\Loan');
    }

    public function lender()
    {
        return $this->belongsTo('App\Models\Lender');
    }

    public function wallet()
    {
        return $this->hasOne('App\Models\Wallet');
    }
}
