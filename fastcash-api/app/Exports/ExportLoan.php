<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ExportLoan implements FromArray, ShouldAutoSize, WithHeadings
{
    private $loans;
    private $headings;

    public function __construct(array $loans, array $headings)
    {
        $this->loans = $loans;
        $this->headings = $headings;
    }

    /**
     * @return array
     */
    public function array(): array
    {
        return $this->loans;
    }

    public function headings(): array
    {
        return $this->headings;
    }
}
