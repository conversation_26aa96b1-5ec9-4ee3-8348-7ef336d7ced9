import React from 'react';
import numeral from 'numeral';

const CurrentLoanDetails = ({ netEarning, amount, deduction, tenure, is_topup, myDisburseAmount, platform, interestRate, insurance, processingFee }) => {
    return (
        <div className="element-wrapper">
            <h6 className="element-header">Current Loan Application</h6>
            <div className="element-content">
                <div className="row">
                    <div className="col-sm-3">
                        <div className="element-box el-tablo">
                            <div className="label">Net Earning</div>
                            <div className="value">{`₦${numeral(netEarning).format('0,0.00')}`}</div>
                            <div className="label mt-4">Platform</div>
                            <div className="value">{platform}</div>
                        </div>
                    </div>
                    <div className="col-sm-3">
                        <div className="element-box el-tablo">
                            <div className="label">Loan Amount</div>
                            <div className="value">{`₦${numeral(amount).format('0,0.00')}`}</div>
                            <div className="label mt-4">Loan Interest</div>
                            <div className="value">{`₦${numeral(deduction - amount).format('0,0.00')}`}</div>
                        </div>
                    </div>
                    <div className="col-sm-3">
                        <div className="element-box el-tablo">
                            <div className="label">Total Deduction</div>
                            <div className="value">{`₦${numeral(deduction).format('0,0.00')}`}</div>
                        </div>
                    </div>
                    <div className="col-sm-3">
                        <div className="element-box el-tablo">
                            <div className="label">Tenure</div>
                            <div className="value">{`${tenure} Months`}</div>
                        </div>
                    </div>
                    <div className="col-sm-3">
                        <div className="element-box el-tablo">
                            <div className="label">Interest Rate</div>
                            <div className="value">{`${interestRate}%`}</div>
                        </div>
                    </div>
                    <div className="col-sm-3">
                        <div className="element-box el-tablo">
                            <div className="label">Insurance</div>
                            <div className="value">{`${insurance}%`}</div>
                        </div>
                    </div>
                    <div className="col-sm-3">
                        <div className="element-box el-tablo">
                            <div className="label">Processing Fee</div>
                            <div className="value">{`${processingFee}%`}</div>
                        </div>
                    </div>
                    {is_topup && (
                        <div className="col-sm-3">
                            <div className="element-box el-tablo">
                                <div className="label">Disburse Amount</div>
                                <div className="value">{`₦${numeral(myDisburseAmount).format('0,0.00')}`}</div>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default CurrentLoanDetails;