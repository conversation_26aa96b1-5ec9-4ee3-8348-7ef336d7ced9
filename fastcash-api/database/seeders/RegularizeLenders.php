<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\Office;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class RegularizeLenders extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $loans = Loan::all();
            foreach ($loans as $l){
                $user = User::where('id', $l->user_id)->first();
                $office = Office::where('id', $user->office_id)->where('lender_id',  $user->lender_id)->first();
                if(!$office){
                    info('not found: '.$l->id);
                }
                $loan = Loan::where('id', $l->id)->first();
                $loan->office_id = $office->id;
                $loan->lender_id = $office->lender_id;
                $loan->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }
    }
}
