<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Loan;
use App\Models\MultiFund;
use App\Models\MultiFundTransaction;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Log;

class MultiFundController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        DB::beginTransaction();

        try {
            $input = $request->all();
            $loan = Loan::where('id', $input['loan_id'])->first();
            if ($loan) {
                $balance = getBalance($input['lender_id']);
                $capital_balance = $balance - $loan->amount;
                info('lender: '.$input['lender_id'].', bal. start: '.$balance.', amt: '.$loan->amount.', bal end: '.$capital_balance);

                if ($capital_balance < 0) {
                    Log::error('loan: '.$loan->id.', no capital available');
                    return response()->json(['error' => 'No investment capital available for loan.'], 500);
                }

                $percentage = number_format(($input['amount'] * 100) / $loan->amount, 4);
                $interest = $loan->total_deduction - $loan->amount;
                $percent_interest = ($percentage * $interest) / 100;

                $multi_fund = MultiFund::where('lender_id', $input['lender_id'])->where('loan_id', $loan->id)->first();
                if($multi_fund) {
                    $multi_fund->amount = $input['amount'];
                    $multi_fund->percentage = $percentage;
                    $multi_fund->save();

                    $transaction = MultiFundTransaction::where('id', $multi_fund->mf_transaction_id)->first();
                    $transaction->amount = $input['amount'];
                    $transaction->principal = $input['amount'];
                    $transaction->interest = $percent_interest;
                    $transaction->save();

                    $notify = doNotify($multi_fund->user_id, 'multi_funds', $multi_fund->id, 'update_fund_loan', 'success', null, $input['lender_id'], $input['user_id']);
                } else {
                    $transaction = MultiFundTransaction::create([
                        'user_id' => $input['user_id'],
                        'loan_id' => $loan->id,
                        'lender_id' => $input['lender_id'],
                        'source' => strtoupper($loan->platform),
                        'amount' => $input['amount'],
                        'principal' => $input['amount'],
                        'interest' => $percent_interest,
                        'status' => 'Completed',
                        'description' => 'Multi-Fund',
                        'approved' => 1,
                        'channel' => 'multi-fund',
                    ]);

                    $multi_fund = MultiFund::create([
                        'user_id' => $input['user_id'],
                        'loan_id' => $input['loan_id'],
                        'office_id' => $loan->office_id,
                        'lender_id' => $input['lender_id'],
                        'amount' => $input['amount'],
                        'percentage' => $percentage,
                        'mf_transaction_id' => $transaction->id,
                    ]);

                    $notify = doNotify($multi_fund->user_id, 'multi_funds', $multi_fund->id, 'fund_loan', 'success', null, $input['lender_id'], $input['user_id']);
                }

                $multi_fund_amt = MultiFund::where('loan_id', $loan->id)->sum('amount');
                if($multi_fund_amt == $loan->amount) {
                    $loan->fully_funded = 1;
                    $loan->fully_funded_at = date('Y-m-d H:i:s');
                    $loan->save();
                }

                DB::commit();

                $details = getFinancialOverview($input['lender_id'], $input['lender_id']);

                return response()->json(['multi_fund' => $multi_fund, 'loan_details' => $details], 200);
            }

            DB::rollBack();
            return response()->json(['error' => 'funding failed'], 500);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'funding failed'], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();

        $data = $request->all();
        try {
            $staff = $data['staff'];

            $fund = MultiFund::where('id', $id)->first();

            $lender_id = $fund->lender_id;

            MultiFundTransaction::where('id', $fund->mf_transaction_id)->delete();

            $notify = doNotify($fund->user_id, 'loans', $fund->id, 'remove_fund', 'success', NULL, $lender_id, $staff);

            $fund->delete();
            DB::commit();

            $details = getFinancialOverview($lender_id, $lender_id);

            return response()->json(['loan_details' => $details], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Fund could not be deleted. Please try again later.'], 500);
        }
    }
}
