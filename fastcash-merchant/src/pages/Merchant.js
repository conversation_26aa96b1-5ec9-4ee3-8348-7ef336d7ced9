import React, { Component } from 'react';
import { Link } from 'react-router-dom';
import { connect } from 'react-redux';
import startCase from 'lodash.startcase';
import $ from 'jquery';

import logo from '../assets/img/fc-logo.png';
import profile from '../assets/img/avatar1.jpg';

import ViewMerchant from '../components/ViewMerchant';

class Merchant extends Component {
    constructor(props) {
		super(props);
        this.toggleMobileMenu = this.toggleMobileMenu.bind(this);
    }

    componentDidMount() {
        document.body.className="pages";
    }

    toggleMobileMenu = (e) => {
        e.preventDefault();
        $(this.refs.mmenu).toggleClass('show-mobile');
    };

    render() {
        const { user, match, lender } = this.props;
        const merchantCode = match.params.merchantCode;

        return (
            <div className="all-wrapper menu-side solid-bg-all">
                <div className="top-menu-secondary with-overflow color-scheme-dark">
                    <div className="logo-w menu-size">
                        <Link className="logo" to={`/dashboard/${merchantCode}`}>
                            <img alt="logo" src={logo}/><span>FastCash</span>
                        </Link>
                    </div>
                    <div className="top-menu-controls">
                        <Link to='/logout' className="top-icon top-settings"><i className="os-icon os-icon-signs-11"/></Link>
                    </div>
                </div>
                <div className="layout-w">
                    {/* Mobile Menu Layout */}
                    <div className="menu-mobile menu-activated-on-click color-scheme-dark">
                        <div className="mm-logo-buttons-w">
                            <Link className="mm-logo" to={`/dashboard/${merchantCode}`}>
                                <img alt="logo" src={logo}/><span>FastCash</span>
                            </Link>
                            <div className="mm-buttons">
                                <div className="content-panel-open">
                                    <div className="os-icon os-icon-grid-circles"/>
                                </div>
                                <div className="mobile-menu-trigger" onClick={this.toggleMobileMenu}>
                                    <div className="os-icon os-icon-hamburger-menu-1"/>
                                </div>
                            </div>
                        </div>
                        <div className="menu-and-user" ref='mmenu'>
                            <div className="logged-user-w">
                                <div className="avatar-w"><img alt="profile" src={profile}/></div>
                                <div className="logged-user-info-w">
                                    <div className="logged-user-name">{user.name}</div>
                                    {user && <div className="logged-user-role">{startCase(user.role)}</div>}
                                </div>
                            </div>
                            <ul className="main-menu">  
                            </ul>
                        </div>
                    </div>

                    <div className="content-w">
                        {user && (
                            <ViewMerchant 
                                merchant={user.merchant}
                                lender={lender}
                            />
                        )}
                    </div>
                </div>
            </div>
        );
    }
}

const mapStateToProps = (state, ownProps) => {
    return {
        user: state.user.user,
        showMenu: state.user.show_menu,
        lender: state.lender.profile,
    }
}

export default connect(mapStateToProps)(Merchant);
