<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements JWTSubject
{
    use HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        'roles'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $appends = [
        'role', 'role_name', 'role_category'
    ];

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    public function getRoleAttribute()
    {
        try {
            return $this->roles[0]->name;
        } catch (\Exception $e) {
            return '';
        }
    }

    public function getRoleNameAttribute()
    {
        try {
            return $this->roles[0]->category;
        } catch (\Exception $e) {
            return '';
        }
    }

    public function getRoleCategoryAttribute()
    {
        try {
            return $this->roles[0]->title;
        } catch (\Exception $e) {
            return '';
        }
    }

    public function roles()
    {
        return $this->belongsToMany('App\Models\Role', 'user_roles', 'user_id', 'role_id');
    }

    public function hasAnyRole($roles)
    {
        if (is_array($roles)) {
            foreach ($roles as $role) {
                if ($this->hasRole($role)) {
                    return true;
                }
            }
        } else {
            if ($this->hasRole($roles)) {
                return true;
            }
        }

        return false;
    }

    public function hasRole($role)
    {
        if ($this->roles()->where('category', $role)->first()) {
            return true;
        }
        return false;
    }

    public function payslip()
    {
        return $this->belongsTo('App\Models\Payslip');
    }

    public function loan()
    {
        return $this->hasMany('App\Models\Loan');
    }

    public function office()
    {
        return $this->belongsTo('App\Models\Office');
    }

    public function accountno()
    {
        return $this->hasMany('App\Models\Accountno');
    }

    public function lender()
    {
        return $this->belongsTo('App\Models\Lender');
    }
}
