/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import startCase from 'lodash.startcase';
import replace from 'lodash.replace';
import Pagination from 'antd/lib/pagination';
import qs from 'querystring';
import $ from 'jquery';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import DatePicker from 'react-datepicker';
import moment from 'moment';

import 'react-datepicker/dist/react-datepicker.css';

import loading from '../assets/img/loading.gif';
import { toggleMenu } from '../actions/user';
import BreadCrumbs from '../components/BreadCrumbs';
import ReportView from '../components/ReportView';
import { httpRequest, baseURL, reportAPI, rptHeader } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import { doNotify } from '../actions/general';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 8;

const fixTitle = title => startCase(replace(title, new RegExp("-"), " "));

class Report extends Component {
    state = {
        slug: '',
        lender: '',
        currentPage: 1,
        totalPage: 0,
        items: [],
        exporting: false,
        key: 0,
        skip: 0,
        fromDate: null,
        toDate: null,
        _fromDate: '',
        _toDate: '',
        _loading: false,
    }

    componentDidMount() {
        this.props.toggleMenu(false);

        const { match } = this.props;
        const slug = match.params.title;

        const { location } = this.props;
		const query = qs.parse(location.search.replace('?', ''));
		
		const page = query && query.p ? parseInt(query.p, 10) : 1;
        const lender = query && query.lender ? query.lender : '';
        const _fromDate = query && query.from ? query.from : '';
        const _toDate = query && query.to ? query.to : '';
        
        const index = rptHeader.findIndex(item => item.name === slug);

		this.setState({
            currentPage: page,
			location: location.pathname,
            lender,
            key: index,
            slug,
            _fromDate,
            _toDate,
            fromDate: _fromDate !== '' ? moment(_fromDate, "DD-MM-YYYY") : null,
            toDate: _toDate !== '' ? moment(_toDate, "DD-MM-YYYY") : null,
		});

		this.fetch(page, lender, slug, _fromDate, _toDate);
    }

	fetch = async (pageNumber, lender, slug, _fromDate, _toDate) => {
        try {
            this.setState({ loading: true });
            this.props.startBlock();
			const rs = await httpRequest(`${baseURL}${reportAPI}/fetch/${slug}?page=${pageNumber}&pagesize=${pageSize}&lender=${lender}&from=${_fromDate}&to=${_toDate}`, 'GET', true);
            const result = rs.result;
			this.setState({
                currentPage: pageNumber,
                totalPage: result.total,
                items: result.data,
                skip: result.skip,
                loading: false,
            });
			this.props.stopBlock();
            window.scrollTo(0, 0);
		} catch (e) {
            this.props.stopBlock();
            const message = e.message || `could not load ${slug} report`;
            this.setState({ loading: false });
            this.notify('', message, 'error');
		}
	};

	onSelect = e => {
        const lender = e.target.value;
        const { slug } = this.state;
		$(e.currentTarget).blur();
		this.props.history.push(`/report/${slug}?p=1&lender=${lender}`);
	};

	onChange = pageNumber => {
        const { lender, slug, _fromDate, _toDate } = this.state;
        const queryDate = _toDate !== '' ? `&from=${_fromDate}&to=${_toDate}` : '';
		this.props.history.push(`/report/${slug}?p=${pageNumber}&lender=${lender}${queryDate}`);
	};

	componentWillUpdate(nextProps, nextState) {
		const query = qs.parse(nextProps.location.search.replace('?', ''));
        const pageNumber = query && query.p ? parseInt(query.p, 10) : 1;
        const lender = query && query.lender ? query.lender : '';
        const _fromDate = query && query.from ? query.from : '';
        const _toDate = query && query.to ? query.to : '';

        const slug = nextProps.match.params.title;
		if(nextState.currentPage !== pageNumber || nextProps.location.pathname !== nextState.location || nextState.slug !== slug || lender !== nextState.lender || _fromDate !== nextState._fromDate || _toDate !== nextState._toDate){
            const index = rptHeader.findIndex(item => item.name === slug);
			this.setState({
				location: nextProps.location.pathname,
                currentPage: pageNumber,
                slug,
                key: index,
                lender,
                _fromDate,
                _toDate,
                fromDate: _fromDate !== '' ? nextState.fromDate : null,
                toDate: _toDate !== '' ? nextState.toDate : null,
			});

			this.fetch(pageNumber, lender, slug, _fromDate, _toDate);
		}
	}

    componentWillUnmount() {
        this.props.toggleMenu(true);
    }

	exportReport = async e => {
		e.preventDefault();
        this.setState({ exporting: true });
        const { slug, lender, _fromDate, _toDate } = this.state;
        const _slug = replace(slug, new RegExp("-"), " ");
		try {
			const rs = await httpRequest(`${baseURL}${reportAPI}/export/${slug}?lender=${lender}&from=${_fromDate}&to=${_toDate}`, 'GET', true)
			if(rs.file) {
				this.setState({ exporting: false });
				this.notify('', `${_slug} report has been exported`, 'success');
				window.open(rs.file, '_blank');
			} else {
				this.notify('', rs.message || `could not export ${_slug} report`, 'success');
				this.setState({ exporting: false });
			}
		} catch(error) {
			this.setState({ exporting: false });
			const message = error.message || `could not export ${_slug} report`;
			this.notify('', message, 'error');
		}
	};
	
	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

    handleSelect = i => {
        const slug = rptHeader[i];
        this.props.history.push(`/report/${slug.name}`);
    };
    
    getHeader = slug => {
        const data = rptHeader.find(item => item.name === slug);
        return data.list;
    };

    handleChangeDate = (date, type) => {
        this.setState({ [type]: date }, () => {
            if(type === 'fromDate'){
                this.setState({ 'toDate': null });
            }
        });

        if(type === 'toDate'){
            const { lender, slug, fromDate } = this.state;
            const from = fromDate.format("DD-MM-YYYY");
            const to = date.format("DD-MM-YYYY");
		    this.props.history.push(`/report/${slug}?p=1&lender=${lender}&from=${from}&to=${to}`);
        }
    };
    
    render() {
        const { lenders } = this.props;
        const { slug, lender, currentPage, totalPage, items, exporting, key, skip, fromDate, toDate, _loading } = this.state;
        return (
            <div>
                <BreadCrumbs url={'/dashboard'} title='go back'/>
                <div className="content-i">
                    <div className="content-box">
                        <div className="row">
                            <div className="col-sm-12">
                                <div className="element-wrapper">
                                    <div className="element-actions">
                                        {exporting ? (
                                            <a className="btn btn-primary btn-sm btn-export-sm pointer"><img src={loading} alt=""/></a>
                                        ) : (
                                            <a className="btn btn-primary btn-sm btn-export-sm pointer" href="#" onClick={this.exportReport}><i className="os-icon os-icon-download-cloud"/><span>{`Export ${fixTitle(slug)} Report`}</span></a>
                                        )}
                                    </div>
                                    <h6 className="element-header">{`${fixTitle(slug)} Report`}</h6>
                                    <div className="control-header">
                                        <div className="row align-items-center">
                                            <div className="col-12">
                                                <div className="form-inline row">
                                                    <div className="col form-group" style={{width:'110px'}}>
                                                        <label className="text-right" htmlFor="">Filter By:</label>
                                                    </div>
                                                    <label className="col-sm-1 col-form-label text-right justify-content-end" htmlFor="">Lender</label>
                                                    <div className="col">
                                                        <select name="lender" className="form-control" value={lender} onChange={this.onSelect}>
                                                            <option value="">All Lenders</option>
                                                            {lenders.map(o => <option key={o.id} value={o.id}>{o.name}</option>)}
                                                        </select>
                                                    </div>
                                                    <div className="col">
                                                        <div className="row">
                                                            <label className="col col-form-label text-right justify-content-end col_label" htmlFor="">Date:</label>
                                                            <div className="col date_input">
                                                                <DatePicker
                                                                    selected={fromDate}
                                                                    onChange={e => this.handleChangeDate(e, 'fromDate')}
                                                                    dateFormat="DD-MMM-YYYY"
                                                                    placeholderText="From Date"
                                                                    className="form-control white-bg"
                                                                    showMonthDropdown
                                                                    showYearDropdown
                                                                    dropdownMode="select"
                                                                    strictParsing
                                                                    peekNextMonth={false}
                                                                />
                                                            </div>
                                                            <label className="col text-center date_label" htmlFor=""> To </label>
                                                            <div className="col date_input">
                                                                <DatePicker
                                                                    selected={toDate}
                                                                    onChange={e => this.handleChangeDate(e, 'toDate')}
                                                                    dateFormat="DD-MMM-YYYY"
                                                                    placeholderText="To Date"
                                                                    className="form-control white-bg"
                                                                    disabled={fromDate === null}
                                                                    minDate={fromDate}
                                                                    showMonthDropdown
                                                                    showYearDropdown
                                                                    dropdownMode="select"
                                                                    strictParsing
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="element-box">
                                        <Tabs onSelect={this.handleSelect} className="os-tabs-w" selectedTabClassName="active" selectedTabPanelClassName="active" selectedIndex={key} forceRenderTabPanel={true}>
                                            <div className="os-tabs-controls" style={{marginBottom: '12px'}}>
                                                <TabList className="nav nav-tabs smaller">
                                                    {[...rptHeader].map((item, i) => (
                                                        <Tab key={i} className="nav-item cursor">
                                                            <span className="nav-link">{fixTitle(item.name)}</span>
                                                        </Tab>
                                                    ))}
                                                </TabList>
                                            </div>
                                            <div className="tab-content">
                                                {[...rptHeader].map((item, i) => (
                                                    <TabPanel className="tab-pane is-responsive" key={i}>
                                                        <ReportView
                                                            slug={item.name}
                                                            items={items}
                                                            header={this.getHeader(item.name)}
                                                            skip={skip}
                                                            loading={_loading}
                                                        />
                                                    </TabPanel>
                                                ))}
                                            </div>
                                        </Tabs>
                                        <div className="pagination pagination-center mt-4">
                                            <Pagination
                                                current={currentPage}
                                                pageSize={pageSize}
                                                total={totalPage}
                                                showTotal={total => `Total ${total} items`}
                                                itemRender={itemRender}
                                                onChange={this.onChange}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
		lenders: state.lender.list,
	}
};

export default connect(mapStateToProps, { toggleMenu, startBlock, stopBlock, doNotify })(Report);
