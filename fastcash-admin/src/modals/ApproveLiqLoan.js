import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import loading from '../assets/img/loading.gif';
import { baseURL, httpRequest, loanAPI } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import { setLoanAnalytics } from '../actions/loan';
import { setUnliquidatedLoans } from '../actions/user';
import { doNotify } from '../actions/general';

const ApproveLiqLoan = ({ closeModal, item, amount, refetch }) => {
	const [submitting, setSubmitting] = useState(false);
	const [error, setError] = useState('');
	const [bypass, setBypass] = useState(false);

	const user = useSelector(state => state.user.user);

	const dispatch = useDispatch();

	const approve = async () => {
		const action = window.confirm("Will you like to approve this liquidation?") ? true : false;
		if(action){
			try {
				setError('');
				setSubmitting(true);
				dispatch(startBlock());
				const excess_deduction = parseFloat(item.amount) - parseFloat(amount);
				const data = { user_id: user.id, bypass_remita: bypass ? 1 : 0, amount, excess_deduction };
				const url = `${baseURL}${loanAPI}/approve-liquidated/${item.id}`;
				const response = await httpRequest(url, 'POST', true, data);
				dispatch(setLoanAnalytics(response.loan_details));
				dispatch(setUnliquidatedLoans(response.unliquidated_loans));
				notify('', 'Loan liquidated!', 'success');
				setSubmitting(false);
				dispatch(stopBlock());
				refetch();
				closeModal();
			} catch (e) {
				dispatch(stopBlock());
				const message = e.message || 'could not approve liquidation';
				setSubmitting(false);
				setError(message);
			}
		}
	};

	const notify = (title, message, type) => {
		dispatch(doNotify({ message, level: type, title}));
    };

	const toggleBypass = () => setBypass(!bypass);

	return (
		<div>
			<div className="modal-header faded smaller">
				<div className="modal-title">
					<span>Approve Liquidated Loan</span>
				</div>
				<button aria-label="Close" className="close" onClick={closeModal} type="button"><span aria-hidden="true"> ×</span></button>
			</div>
			<div className="modal-body">
				{error !== '' && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}/>}
				<div className="row">
					<div className="col-md-12">
						<div className="form-check">
							<label className="form-check-label">
								<input type="checkbox" className="form-check-input" onChange={toggleBypass} checked={bypass}/> Bypass Remita Stop Loan Notification?
							</label>
						</div>
					</div>
				</div>
				<div className="modal-footer buttons-on-left">
					<button className="btn btn-teal" disabled={submitting} type="button" onClick={() => approve()}>
						{submitting ? <img src={loading} alt=""/> : 'Submit'}
					</button>
					<button className="btn btn-link cursor" onClick={closeModal} type="button"> Cancel</button>
				</div>
			</div>
		</div>
	);
};

export default ApproveLiqLoan;