import React, { Component } from 'react';
import { connect } from 'react-redux';

import FindUser from '../components/FindUser';
import { setUserPayslip } from '../actions/user';
import LoanRequestPage from '../components/LoanRequestPage';

class LoanRequest extends Component {
	componentWillUnmount() {
		this.props.setUserPayslip(null);
	}

	render() {
		const { user, remitaDatum } = this.props;
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="element-wrapper">
						<h6 className="element-header">New Loan Request</h6>
						{user && !user.profile && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> user has not registered yet`}}></div>}
						<FindUser type="loan"/>
						{user && user.profile && (
							<div className="element-box">
								<div className="element-wrapper p-0">
									<div className="element-content">
										<div className="row">
											<div className="col-sm-4">
												<div className="element-box el-tablo">
													<div className="label">NAME</div>
													<div className="value">{user.profile.name}</div>
												</div>
											</div>
											<div className="col-sm-4">
												<div className="element-box el-tablo">
													<div className="label">EMPLOYER</div>
													<div className="value">{user.profile.employer}</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						)}
						{user && user.profile && (
							<div className="element-box">
								<LoanRequestPage
									user={user.profile}
									earnings={user.earnings}
									bank_accounts={user.bank_accounts}
									payslip={user.payslip}
									loan={user.loan}
									remitaDatum={remitaDatum}
									loan_platform={user.loan_platform}
								/>
							</div>
						)}
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.payslip,
		remitaDatum: state.user.remitaDatum,
	}
};

export default connect(mapStateToProps, { setUserPayslip })(LoanRequest);
