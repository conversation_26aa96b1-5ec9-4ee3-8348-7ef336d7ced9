/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react';

import { currency, formatDate } from '../config/constants';

const AuditTrail = ({
	closeModal,
	loan,
	setPassed,
	regenWallet,
	setAudited,
}) => {
	const format = 'YYYY-MM-DD HH:mm:ss';

	return (
		loan && (
			<div>
				<div className="modal-header faded smaller">
					<div className="modal-title">
						<span>Audit Trail</span>
					</div>
					<button
						aria-label="Close"
						className="close"
						onClick={closeModal}
						type="button"
					>
						<span aria-hidden="true"> ×</span>
					</button>
				</div>
				<div className="table-responsive">
					<table className="table table-striped table-lightfont">
						<thead>
							<tr>
								<th>Loan ID</th>
								<th>Name of Employee</th>
								<th>Loan Amount</th>
								<th>Loan Disbursed</th>
								<th>Monthly P/I</th>
								<th>S/E DATE</th>
								<th>Monthly Deduction</th>
								<th>Tenure</th>
								<th>Total Deduction</th>
								<th>Is Topup</th>
								<th>Loan Dates</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td>
									<small>{loan.id}</small>
								</td>
								<td>
									<small>{loan.user.name}</small>
									<br />
									<small className="bold">{`${loan.platform.toUpperCase()}`}</small>
								</td>
								<td>
									<small>{currency(loan.amount)}</small>
								</td>
								<td>
									<small>{currency(loan.disburse_amount)}</small>
								</td>
								<td nowrap="nowrap">
									<small>{`P: ${currency(loan.monthly_principal)}`}</small>
									<br />
									<small>{`I: ${currency(loan.monthly_interest)}`}</small>
								</td>
								<td nowrap="nowrap">
									<small>{`SD: ${
										loan.verified ? formatDate(loan.start_date, format) : '-'
									}`}</small>
									<br />
									<small>{`ED: ${
										loan.verified ? formatDate(loan.end_date, format) : '-'
									}`}</small>
								</td>
								<td>
									<small>{currency(loan.monthly_deduction)}</small>
								</td>
								<td nowrap="nowrap">
									<small>{`${loan.tenure} Month${
										loan.tenure > 1 ? 's' : ''
									}`}</small>
								</td>
								<td>
									<small>{currency(loan.total_deduction)}</small>
								</td>
								<td>
									<small>{loan.is_topup === 0 ? 'No' : 'Yes'}</small>
								</td>
								<td nowrap="nowrap">
									<small>{`REQ: ${formatDate(loan.created_at, format)}`}</small>
									<br />
									<small>{`VER: ${
										loan.verified ? formatDate(loan.verified_at, format) : '-'
									}`}</small>
									<br />
									<small>{`APP: ${
										loan.approved ? formatDate(loan.approved_at, format) : '-'
									}`}</small>
									<br />
									<small>{`DIS: ${
										loan.disbursed ? formatDate(loan.disbursed_at, format) : '-'
									}`}</small>
									<br />
									<small>{`LIQ: ${
										loan.liquidated
											? formatDate(loan.liquidated_at, format)
											: '-'
									}`}</small>
									<br />
									<small>
										{loan.deleted_at ? (
											<strong>{`DEC: ${formatDate(
												loan.deleted_at,
												format
											)}`}</strong>
										) : (
											'DEL: -'
										)}
									</small>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				<div className="audit-block">
					<div className="blocks">
						<h4>Transactions</h4>
						<div className="table-responsive">
							<table className="table table-striped table-lightfont">
								<thead>
									<tr>
										<th>ID</th>
										<th>Wallet ID</th>
										<th>Amount</th>
										<th>Outst.</th>
										<th>Description</th>
										<th>Approved</th>
										<th>Deleted</th>
										<th>Date</th>
									</tr>
								</thead>
								<tbody>
									{loan.transactions.map((item, i) => {
										return (
											<tr key={i}>
												<td>
													<small>{item.id}</small>
												</td>
												<td>
													<small>{item.wallet ? item.wallet.id : '-'}</small>
												</td>
												<td>
													<small>{currency(item.amount)}</small>
												</td>
												<td>
													<small>{currency(item.outst_amount)}</small>
												</td>
												<td>
													<small>{item.description}</small>
												</td>
												<td>
													<small>{item.approved}</small>
												</td>
												<td>
													<small>
														{item.deleted_at
															? formatDate(item.deleted_at)
															: '-'}
													</small>
												</td>
												<td>
													<small>{formatDate(item.created_at)}</small>
												</td>
											</tr>
										);
									})}
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<div className="audit-block">
					<div className="blocks">
						<h4>{`Wallet (Balance: ${currency(loan.wallet_balance)})`}</h4>
						<div className="table-responsive">
							<table className="table table-striped table-lightfont">
								<thead>
									<tr>
										<th>ID</th>
										<th>Trans. ID</th>
										<th>Loan ID</th>
										<th>User ID</th>
										<th>Amount</th>
										<th>Type</th>
										<th>Approved</th>
										<th>Category</th>
										<th>Deleted</th>
										<th>Date</th>
									</tr>
								</thead>
								<tbody>
									{loan.wallets.map((item, i) => {
										return (
											<tr key={i}>
												<td>
													<small>{item.id}</small>
												</td>
												<td>
													<small>{item.transaction_id || '-'}</small>
												</td>
												<td>
													<small>{item.loan_id || '-'}</small>
												</td>
												<td>
													<small>{item.user_id || '-'}</small>{' '}
													{item.is_lender === 1 && <small className="bold">{`is lender: ${item.is_lender === 0 ? '-' : 'YES'}`}</small>}
												</td>
												<td>
													<small>{currency(item.amount)}</small>
												</td>
												<td>
													<small>{item.type}</small>
												</td>
												<td>
													<small>{item.approved}</small>
												</td>
												<td>
													<small>{item.category}</small>
												</td>
												<td>
													{item.deleted_at ? formatDate(item.deleted_at) : '-'}
												</td>
												<td>
													<small>{formatDate(item.created_at)}</small>
												</td>
											</tr>
										);
									})}
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<div className="audit-block">
					<div className="blocks" style={{ width: '60%' }}>
						<h4>Activities</h4>
						<div className="table-responsive">
							<table className="table table-striped table-lightfont">
								<thead>
									<tr>
										<th>ID</th>
										<th>Category</th>
										<th>Category ID</th>
										<th>Action</th>
										<th>Date</th>
									</tr>
								</thead>
								<tbody>
									{loan.activities.map((item, i) => {
										return (
											<tr key={i}>
												<td>
													<small>{item.id}</small>
												</td>
												<td>
													<small>{item.category}</small>
												</td>
												<td>
													<small>{item.category_id}</small>
												</td>
												<td>
													<small>{item.action.name}</small>
												</td>
												<td>
													<small>{formatDate(item.created_at)}</small>
												</td>
											</tr>
										);
									})}
								</tbody>
							</table>
						</div>
					</div>
					<div className="btn-block" style={{ width: '40%' }}>
						{loan.audited === 0 && (
							<a
								className="btn btn-primary cursor"
								onClick={setPassed(loan.id)}
							>
								Pass
							</a>
						)}
						{loan.audited === 1 && (
							<a
								className="btn btn-info cursor ml-4"
								onClick={regenWallet(loan.id, loan.transactions)}
							>
								Re-generate Wallet
							</a>
						)}
						<a
							className="btn btn-success cursor ml-4"
							onClick={setAudited(loan.id)}
						>
							Do Audit
						</a>
					</div>
				</div>
			</div>
		)
	);
};

export default AuditTrail;
