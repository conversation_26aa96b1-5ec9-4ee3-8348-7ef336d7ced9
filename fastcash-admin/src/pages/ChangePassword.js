import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, SubmissionError, reset } from 'redux-form';

import { doNotify } from '../actions/general';
import { baseURL, httpRequest, userAPI } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';

const validate = values => {
    const errors = {}
    if (!values.old_password) {
        errors.old_password = 'Enter your old password';
	}
    if (!values.password) {
        errors.password = 'Enter your new password';
	}
    if (!values.password_confirmation) {
        errors.password_confirmation = 'Re-enter your new password';
	}
    return errors;
};

const renderField = ({input, id, label, type, meta: {touched, error}}) => (
	<div className={`form-group row ${(touched && error && 'has-error')}`}>
		<label className="col-sm-5 col-form-label text-right" htmlFor={id}>{label}</label>
		<div className="col-sm-7">
			<input {...input} placeholder={label} type={type} className="form-control" />
			{touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
		</div>
	</div>
);

class ChangePassword extends Component {
	doChangePassword = data => {
		this.props.startBlock();
		const { user } = this.props;
		return httpRequest(`${baseURL}${userAPI}/change-password/${user.id}`, 'POST', true, data)
			.then((_) => {
				this.props.stopBlock();
				this.props.reset('changepassword');
				this.notify('', 'password changed!', 'success');
			})
			.catch((error) => {
				this.props.stopBlock();
				const message = error.message || 'could not change password';
				this.notify('', message, 'error');
				throw new SubmissionError({
					_error: message,
				});
			});
	};
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	render() {
		const { handleSubmit, error } = this.props;
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="element-wrapper" style={{ paddingBottom: 0 }}>
						<h6 className="element-header">Change Password</h6>
					</div>
					<div className="row">
						<div className="col-lg-7 col-xxl-6">
							<div className="element-wrapper">
								<div className="element-box">
									{error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
									<form onSubmit={handleSubmit(this.doChangePassword)}>
										<Field
											name="old_password"
											id="old_password"
											type="password"
											component={renderField}
											label="Old Password"
										/>
										<Field
											name="password"
											id="password"
											type="password"
											component={renderField}
											label="New Password"
										/>
										<Field
											name="password_confirmation"
											id="password_confirmation"
											type="password"
											component={renderField}
											label="Re-enter New Password"
										/>
										<div className="form-buttons-w">
											<button className="btn btn-primary" type="submit">Change Password</button>
										</div>
									</form>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}

ChangePassword = reduxForm({
    form: 'changepassword',
    validate,
})(ChangePassword);

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
	}
};

export default connect(mapStateToProps, { startBlock, stopBlock, doNotify, reset })(ChangePassword);
