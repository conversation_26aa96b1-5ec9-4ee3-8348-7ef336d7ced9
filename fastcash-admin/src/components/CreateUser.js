import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';

import { doNotify, notifyDone } from '../actions/general';
import { baseURL, httpRequest, userAPI } from '../config/constants';
import loading from '../assets/img/loading.gif';

const validate = values => {
    const errors = {}
    if (!values.firstname) {
        errors.firstname = 'Enter the first name';
    }
    if (!values.lastname) {
        errors.lastname = 'Enter the last name';
    }
    if (!values.email) {
        errors.email = 'Enter email address';
    }
    if (!values.phone) {
        errors.phone = 'Please give us the phone number';
    }
    if (!values.gender) {
        errors.gender = 'Select the gender';
    }
    if (!values.role) {
        errors.role = 'Select the role of the user';
    }
    return errors;
};
const genders = [
    {id: 'male', name: 'Male'},
    {id: 'female', name: 'Female'},
];

const renderField = ({input, id, label, type, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" />
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

const renderSelectField = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label>{label}</label>
        <select {...input} className="form-control">
            <option value="">{`Select ${label}`}</option>
            {data.map((c, i) => <option value={c.id} key={i}>{c.name}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class CreateUser extends Component {
    createAdminUser = async data => {
        const { user, lender } = this.props;
        const details = { ...data, user_id: user.id, lender_id: lender.id };
		try {
            const url = `${baseURL}${userAPI}`;
            await httpRequest(url, 'POST', true, details);
            this.props.reset('createadmin');
            this.props.notifyDone(true);
            this.notify('', 'admin user created!', 'success');
        }
        catch (error) {
            const message = error.message || 'could not create admin user';
            this.notify('', message, 'error');
            throw new SubmissionError({
                _error: message,
            });
        }
    };
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

    render() {
        const { handleSubmit, error, submitting, pristine, roleList } = this.props;
        const roles = roleList.map(r => ({id: r.id, name: r.title}));

        return (
            <div className="element-wrapper">
                <h6 className="element-header">Create user</h6>
                <div className="element-box">
                    {error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                    <form onSubmit={handleSubmit(this.createAdminUser)}>
                        <div className="row">
                            <div className="col-sm-4">
                                <Field
                                    name="firstname"
                                    id="firstname"
                                    type="text"
                                    component={renderField}
                                    label="First Name"
                                />
                            </div>
                            <div className="col-sm-4">
                                <Field
                                    name="lastname"
                                    id="lastname"
                                    type="text"
                                    component={renderField}
                                    label="Last Name"
                                />
                            </div>
                            <div className="col-sm-4">
                                <Field
                                    name="email"
                                    id="email"
                                    type="email"
                                    component={renderField}
                                    label="Email"
                                />
                            </div>
                            <div className="col-sm-4">
                                <Field
                                    name="gender"
                                    component={renderSelectField}
                                    label="Gender"
                                    data={genders}
                                />
                            </div>
                            <div className="col-sm-4">
                                <Field
                                    name="phone"
                                    id="phone"
                                    type="text"
                                    component={renderField}
                                    label="Phone Number"
                                />
                            </div>
                            <div className="col-sm-4">
                                <Field
                                    name="role"
                                    component={renderSelectField}
                                    label="Role"
                                    data={roles}
                                />
                            </div>
                        </div>
                        <div className="form-buttons-w">
                            <button className="btn btn-primary" disabled={pristine || submitting} type="submit">
                                {submitting? <img src={loading} alt=""/> : 'Create'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    }
}

CreateUser = reduxForm({
    form: 'createadmin',
    validate,
})(CreateUser);

const mapStateToProps = (state) => {
    return {
        initialValues: {
            gender: "",
            role: "",
        },
        roleList: state.general.roles,
        user: state.user.user,
        lender: state.lender.profile,
    }
}

export default connect(mapStateToProps, { reset, doNotify, notifyDone })(CreateUser);