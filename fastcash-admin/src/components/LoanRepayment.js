import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import DatePicker from 'react-datepicker';

import 'react-datepicker/dist/react-datepicker.css';

import { baseURL, httpRequest, transactionAPI } from '../config/constants';
import loading from '../assets/img/loading.gif';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {}
    if (!values.amount) {
        errors.amount = 'Enter the amount';
    }
    if (!values.repayment_reference) {
        errors.repayment_reference = 'Enter repayment reference';
    }
    return errors;
};

const renderField = ({input, id, label, type, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" />
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

const renderSelectField = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label>{label}</label>
        <select {...input} className="form-control">
            {data.map((item, i) => <option value={item} key={i}>{item}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class LoanRepayment extends Component {
    state = {
        date_error: '',
        repaymentDate: null ,
    };

    makeRepayment = async data => {
        try {
            const { user, lender, loan } = this.props;
            const { repaymentDate } = this.state;
            if(!repaymentDate) {
                this.setState({ date_error: 'select repayment date' });
                return;
            }
            const details = { ...data, loan_id: loan.id, staff_id: user.id, lender_id: lender.id, repayment_date: repaymentDate.format("YYYY-MM-DD") };
            await httpRequest(`${baseURL}${transactionAPI}`, 'POST', true, details);
            this.props.reset('loanrepay');
            this.notify('', 'loan repayment done!', 'success');
            this.props.closeModal();
        } catch (error) {
            const message = error.message || 'could not repay loan';
            this.notify('', message, 'error');
            throw new SubmissionError({
                _error: message,
            });
        }
    };
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
    };
    
    handleChange = date => this.setState({ repaymentDate: date });

	render() {
        const { closeModal, handleSubmit, error, submitting, pristine } = this.props;
        const { date_error, repaymentDate } = this.state;
		return (
			<>
                <div className="modal-header faded smaller">
                    <div className="modal-title">
                        <span>Loan repayment</span>
                    </div>
                    <button aria-label="Close" className="close" onClick={closeModal} type="button"><span aria-hidden="true"> ×</span></button>
                </div>
                <form onSubmit={handleSubmit(this.makeRepayment)}>
                    <div className="modal-body">
                        {error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}/>}
                        <div className="row">
                            <div className="col-sm-6">
								<Field
									name="amount"
									id="amount"
									type="text"
									component={renderField}
									label="Amount"
								/>
                            </div>
                            <div className="col-sm-6">
                                <div className={`form-group row ${(date_error !== '' && 'has-error')}`} style={{marginLeft: 0, marginRight: 0}}>
                                    <label>Repayment Date</label>
                                    <DatePicker
                                        selected={repaymentDate}
                                        onChange={this.handleChange}
                                        dateFormat="DD-MMM-YYYY"
                                        placeholderText="Click to select date"
                                        className="form-control white-bg"
                                    />
                                    {date_error !== '' && <span className="help-block form-text with-errors form-control-feedback">{date_error}</span>}
                                </div>
                            </div>
                        </div>
                        <div className="row">
                            <div className="col-sm-6">
								<Field
									name="repayment_source"
									id="repayment_source"
									component={renderSelectField}
									label="Repayment Source"
                                    data={['IPPIS', 'Remita']}
								/>
                            </div>
                            <div className="col-sm-6">
								<Field
									name="repayment_reference"
									id="repayment_reference"
									type="text"
									component={renderField}
									label="Repayment Reference"
								/>
                            </div>
                        </div>
                    </div>
                    <div className="modal-footer buttons-on-left">
                        <button className="btn btn-teal" disabled={pristine || submitting} type="submit">
                            {submitting? <img src={loading} alt=""/> : 'Make Repayment'}
                        </button>
                        <button className="btn btn-link cursor" onClick={closeModal} type="button"> Cancel</button>
                    </div>
                </form>
            </>
		);
	}
}

LoanRepayment = reduxForm({
    form: 'loanrepay',
    validate,
})(LoanRepayment);

const mapStateToProps = (state, ownProps) => {
    return {
        initialValues: {
            repayment_source: 'Remita',
        },
		user: state.user.user,
		lender: state.lender.profile,
    }
}

export default connect(mapStateToProps, { reset, doNotify })(LoanRepayment);
