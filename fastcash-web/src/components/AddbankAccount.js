import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, SubmissionError } from 'redux-form';

import loading from '../assets/img/loading.gif';
import { newBankAccount } from '../actions/user';
import { doNotify } from '../actions/general';
import { httpRequest, baseURL, accountAPI } from '../config/constants';

const validate = values => {
    const errors = {};
    if (!values.account_number) {
        errors.account_number = 'Enter account number';
    }
    if (values.bank_code === '') {
        errors.bank_code = 'Select bank';
    }
    return errors;
};

const renderField = ({input, id, label, type, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" />
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

const renderSelectField = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label className="lighter">{label}</label>
        <select {...input} className="form-control">
            <option value="">Select Bank</option>
            {data.map(t => <option value={t.id} key={t.id}>{t.name}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class AddbankAccount extends Component {
    state = {
        submitting: false,
    };

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

    addAccount = async data => {
		this.setState({ submitting: true });
		try {
            const { user } = this.props;
            let details = { ...data, user_id: user.id, };
			const rs = await httpRequest(`${baseURL}${accountAPI}`, 'POST', true, details);
			this.props.newBankAccount(rs.account);
			this.setState({ submitting: false });
			this.notify('', 'New bank account added', 'success');
			this.props.closeModal();
		}
		catch (error) {
			this.setState({ submitting: false });
			const message = error.message || 'could not save account';
			this.notify('', message, 'error');
			throw new SubmissionError({
				_error: message,
			});
		}
    };

	render() {
		const { closeModal, error, handleSubmit, banks } = this.props;
		const { submitting } = this.state;
		return (
			<div>
                <div className="modal-header faded smaller">
                    <div className="modal-title">
                        <span>Add Bank Account</span>
                    </div>
                    <button aria-label="Close" className="close" onClick={closeModal} type="button"><span aria-hidden="true"> ×</span></button>
                </div>
                <form onSubmit={handleSubmit(this.addAccount)}>
                    <div className="modal-body">
                        {error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}/>}
                        <div className="row">
                            <div className="col-sm-6">
								<Field
									name="account_number"
									id="account_number"
									type="text"
									component={renderField}
									label="Account Number"
								/>
                            </div>
                            <div className="col-sm-6">
                                <Field
                                    name="bank_code"
                                    component={renderSelectField}
                                    data={banks}
                                    label="Select Bank"
                                />
                            </div>
                        </div>
                    </div>
                    <div className="modal-footer buttons-on-left">
                        <button className="btn btn-teal" disabled={submitting} type="submit">
                            {submitting? <img src={loading} alt=""/> : 'Save'}
                        </button>
                    </div>
                </form>
            </div>
		);
	}
}

AddbankAccount = reduxForm({
    form: 'addbankact',
    validate,
})(AddbankAccount);

const mapStateToProps = (state, ownProps) => {
    return {
        initialValues: {
            bank_code: '',
        },
        user: state.user.user,
        banks: state.general.banks,
    }
};

export default connect(mapStateToProps, { newBankAccount, doNotify })(AddbankAccount);
