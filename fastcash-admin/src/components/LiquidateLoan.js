import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, SubmissionError, change } from 'redux-form';
import numeral from 'numeral';
import sha512 from 'crypto-js/sha512';

import { baseURL, httpRequest, loanAPI, merchantId, apiKey, apiToken } from '../config/constants';
import loading from '../assets/img/loading.gif';
import { doNotify } from '../actions/general';
import { startBlock, stopBlock } from '../actions/ui-block';

const validate = values => {
    const errors = {}
    if (!values.core_bank_id) {
        errors.core_bank_id = 'Enter core bank id';
    }
    return errors;
};

const renderField = ({input, id, label, type, readOnly, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" readOnly={readOnly} />
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class LiquidateLoan extends Component {
	state = {
		agree: true,
		amount: 0,
	};

	async componentDidMount() {
		try {
			const { loan } = this.props;
			this.props.startBlock();
			const url = `${baseURL}${loanAPI}/balance/${loan.id}`;
			const rs = await httpRequest(url, 'GET', true);
			if (rs && rs.result) {
				const amount = parseFloat(rs.result.amount);
				this.props.change('amount', numeral(amount).format('0.00'));
				this.setState({ amount: numeral(amount).format('0.00') });
			}
			this.props.stopBlock();
		} catch (error) {
			this.props.stopBlock();
			const message = error.message || 'could not get balance';
			this.notify('', message, 'error');
		}
	}
	
	liquidate = async data => {
		const action = window.confirm("Do you want to liquidate this loan?") ? true : false;
		if(action){
			try {
				const { loan, user } = this.props;

				let remitaData;
				if (loan.platform === 'remita') {
					const approved = JSON.parse(loan.approved_remita);
					if (approved) {
						const d = new Date();
						const requestId = d.getTime();
						const apiHash = sha512(`${apiKey}${requestId}${apiToken}`);
						const authorization = `remitaConsumerKey=${apiKey}, remitaConsumerToken=${apiHash}`;
						const body = {
							authorisationCode: loan.auth_code,
							customerId: loan.user.customer_id,
							mandateReference: approved.data.mandateReference,
						};
						remitaData = { merchantId, apiKey, requestId, authorization, body };
					}
				}

				const details = {
					...data,
					staff_id: user.id,
					platform: loan.platform,
					...remitaData,
					payment_type: 'admin',
				};

				const url = `${baseURL}${loanAPI}/liquidate/${loan.id}`;
				await httpRequest(url, 'POST', true, details);
				this.notify('', 'loan liqudation sent for approval', 'success');
				this.props.closeModal();
			} catch (error) {
				const message = error.message || 'could not liquidate loan';
				this.notify('', message, 'error');
				throw new SubmissionError({
					_error: message,
				});
			}
		}
	}

	onChange = e => {
		const { loan } = this.props;
		const { amount } = this.state;
		this.setState({ agree: e.target.checked });
		if(e.target.checked) {
			this.props.change('amount', amount);
		} else {
			const _amount = parseFloat(amount) - parseFloat(loan.monthly_interest);
			this.props.change('amount', numeral(_amount).format('0.00'));
		}
	}
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	render() {
        const { closeModal, handleSubmit, error, submitting } = this.props;
        const { agree } = this.state;
		return (
			<div>
                <div className="modal-header faded smaller">
                    <div className="modal-title">
                        <span>Liquidate Loan</span>
                    </div>
                    <button aria-label="Close" className="close" onClick={closeModal} type="button"><span aria-hidden="true"> ×</span></button>
                </div>
                <form onSubmit={handleSubmit(this.liquidate)}>
                    <div className="modal-body">
                        {error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}/>}
                        <div className="row">
                            <div className="col-sm-12">
								<Field
									name="amount"
									id="amount"
									type="text"
									component={renderField}
									label="Amount"
									readOnly={true}
								/>
                            </div>
                        </div>
						<div className="row">
							<div className="col-sm-12">
								<div className="form-check">
									<label htmlFor="agree" className="form-check-label">
										<input name="agree" id="agree" type="checkbox" className="form-check-input" onChange={this.onChange} checked={agree} /> Add interest
									</label>
								</div>
							</div>
						</div>
                    </div>
                    <div className="modal-footer buttons-on-left">
                        <button className="btn btn-teal" disabled={submitting} type="submit">
                            {submitting? <img src={loading} alt=""/> : 'Liquidate'}
                        </button>
                        <button className="btn btn-link cursor" onClick={closeModal} type="button"> Cancel</button>
                    </div>
                </form>
            </div>
		);
	}
}

LiquidateLoan = reduxForm({
    form: 'liquidate-frm',
    validate,
})(LiquidateLoan);

const mapStateToProps = (state, ownProps) => {
    return {
		initialValues: {
			amount: 0,
		},
		user: state.user.user,
    }
}

export default connect(mapStateToProps, { doNotify, change, startBlock, stopBlock })(LiquidateLoan);
