<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\Transaction;
use Illuminate\Database\Seeder;

class SplitTransactionAmt extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $transactions = Transaction::where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate')->get();
            foreach ($transactions as $t){
                info('Deduction/Loan Liquidated: '.json_encode($t));
                $loan = Loan::find($t->loan_id);

                $transaction = Transaction::find($t->id);
                $transaction->interest = $loan->monthly_interest;
                $transaction->principal = $loan->monthly_principal;
                $transaction->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }

        try {
            $transactions = Transaction::where('transaction_flag', 'loan')->get();
            foreach ($transactions as $t){
                info('Loan Taken: '.json_encode($t));
                $loan = Loan::find($t->loan_id);

                $transaction = Transaction::find($t->id);
                $transaction->interest = $loan->monthly_interest * $loan->tenure;
                $transaction->principal = $loan->amount;
                $transaction->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }
    }
}
