<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddLoanFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->integer('insurance')->default(1)->after('interest_rate');
            $table->integer('processing_fee')->default(1)->after('insurance');
            $table->string('oletter_updated')->nullable()->after('oletter');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropColumn('insurance');
            $table->dropColumn('processing_fee');
            $table->dropColumn('oletter_updated');
        });
    }
}
