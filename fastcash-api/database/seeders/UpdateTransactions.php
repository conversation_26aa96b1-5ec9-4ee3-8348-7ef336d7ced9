<?php

namespace Database\Seeders;

use App\Models\Transaction;
use Illuminate\Database\Seeder;

class UpdateTransactions extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $transactions = Transaction::all();
        foreach ($transactions as $transaction) {
            $t = Transaction::find($transaction->id);
            $t->deleted_at = $transaction->deleted;
            $t->save();
        }
    }
}
