<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\PaymentDate;
use App\Models\Transaction;
use Illuminate\Database\Seeder;

class UpdateTopupLoan extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $loan = Loan::find(58);

        $__tenure = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)->count();
        $__amount = ($loan->monthly_principal * $__tenure) + $loan->monthly_interest;

        $transaction = Transaction::create([
            'user_id' => $loan->user_id,
            'loan_id' => $loan->id,
            'office_id' => $loan->office_id,
            'source' => strtoupper($loan->platform),
            'amount' => $__amount,
            'interest' => $loan->monthly_interest,
            'principal' => $__amount - $loan->monthly_interest,
            'outst_amount' => 0,
            'status' => 'Pending',
            'description' => 'Loan Liquidated by Topup',
            'transaction_flag' => 'liquidate',
            'lender_id' => $loan->lender_id,
            'repayment_date' => date('Y-m-d'),
        ]);
    }
}
