<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Lender;
use App\Models\Role;
use App\Models\State;
use App\Models\User;
use App\Models\Wallet;
use App\Services\MailHandler;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use DB;
use Log;

class LenderController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $limit = $request->has('pagesize') && $request->input('pagesize') != '' ? $request->input('pagesize') : 10;
            $page = $request->has('page') && $request->input('page') != '' ? $request->input('page') : 1;
            $skip = ($page - 1) * $limit;

            $lenders = Lender::orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

            $lenders_count = Lender::count();

            $result = (object)null;
            $result->total = $lenders_count;
            $result->size = $limit;
            $result->page = $page;
            $result->data = $lenders;

            return response()->json(compact('result'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not fetch users'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required|email|unique:users',
            'address' => 'required',
            'phone' => 'required',
            'next_of_kin' => 'required',
            'bank' => 'required',
            'account_number' => 'required',
            'fullname' => 'required',
            'username' => 'required|unique:users',
            'password' => 'required',
            'interest_rate' => 'required',
            'commission' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        DB::beginTransaction();
        try {
            $data = $request->all();

            $lender_code = randomPassword(4, 2);
            do {
                $check = Lender::where('lender_code', $lender_code)->first();
                if ($check) {
                    $lender_code = randomPassword(4, 2);
                }
            } while ($check != null);

            $lender = Lender::create([
                'name' => $data['name'],
                'email' => $data['email'],
                'address' => $data['address'],
                'state_id' => $data['state'],
                'phone_number' => $data['phone'],
                'bank_code_id' => $data['bank'],
                'account_number' => $data['account_number'],
                'interest_rate' => $data['interest_rate'],
                'commission' => $data['commission'],
                'lender_code' => $lender_code,
                'next_of_kin' => $data['next_of_kin'],
                'auto_disburse' => $data['auto_disburse'],
            ]);

            $user = User::where('email', $data['email'])->first();
            if ($user) {
                DB::rollBack();
                return response()->json(['error' => 'could not create lender profile for admin, email already exists.'], 500);
            }

            $user = User::create([
                'name' => $data['fullname'],
                'username' => $data['username'],
                'password' => bcrypt($data['password']),
                'email' => $data['email'],
                'phone' => $data['phone'],
                'enabled' => 1,
                'lender_id' => $lender->id,
            ]);
            $user->roles()->attach(Role::where('category', 'super-l')->first());

            $remita = installRemita($lender->id);

            $notify = doNotify(null, 'lenders', $lender->id, 'lender_create', 'success', null, $lender->id, $data['user_id']);

            DB::commit();

            try {
                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $lender->name);
                $mail->to_email = $lender->email;
                $mail->template = 'email.new_lender';
                $mail->login = env('ADMIN_URL') . '/signin';
                $mail->subject = 'Lender Created!';

                $sendmail = (new MailHandler())->sendMail($mail);

            } catch (\Exception $e) {
                Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            }

            return response()->json(compact('lender'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not create lender, try again later'], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        try {
            $lender = Lender::with('bank')->where('id', $id)->first();
            $lender->state = State::where('id', $lender->state_id)->first();
            $lender->transactions = Wallet::withTrashed()->where('lender_id', $lender->id)->where('is_lender', 1)->get();

            return response()->json(compact('lender'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'network error, try again'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'merchant_commission' => 'required|numeric',
            'min_loan_tenure' => 'required|numeric',
            'max_loan_tenure' => 'required|numeric',
            'interest_rate' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }
        try {
            $data = $request->all();

            $lender = Lender::where('id', $id)->first();

            if ($lender) {
                $lender->merchant_commission = $data['merchant_commission'];
                $lender->min_loan_tenure = $data['min_loan_tenure'];
                $lender->max_loan_tenure = $data['max_loan_tenure'];
                $lender->interest_rate = $data['interest_rate'];
                $lender->save();

                $notify = doNotify(null, 'lenders', $lender->id, 'lender_update', 'success', json_encode($data), $lender->id, $data['staff_id']);

                return response()->json(compact('lender'), 200);
            }

            return response()->json(['error' => 'could not find user, try again later'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not save user, try again later'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function profileUpdate(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'phone' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }
        try {
            $data = $request->all();

            $lender = Lender::where('id', $id)->first();
            $email_check = Lender::where('email', $data['email'])->first();

            if ($email_check && $lender && $lender->email != $data['email']) {
                return response()->json(['error' => 'the email has already been taken'], 500);
            }

            if ($lender) {
                $lender->email = $data['email'];
                $lender->phone_number = $data['phone'];
                $lender->auto_disburse = $data['auto_disburse'];
                $lender->save();

                $notify = doNotify(null, 'lenders', $lender->id, 'lender_update', 'success', null, $lender->id, $data['user_id']);

                return response()->json(compact('lender'), 200);
            }

            return response()->json(['error' => 'could not find user, try again later'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not save user, try again later'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function disable(Request $request, $id)
    {
        try {
            $data = $request->all();

            $lender = Lender::where('id', $id)->first();
            $lender->status = 0;
            $lender->save();

            $notify = doNotify(null, 'lenders', $id, 'lender_disable', 'success', null, $lender->id, $data['user_id']);

            return response()->json(compact('lender'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'network error, try again'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function enable(Request $request, $id)
    {
        try {
            $data = $request->all();

            $lender = Lender::where('id', $id)->first();
            $lender->status = 1;
            $lender->save();

            $notify = doNotify(null, 'lenders', $id, 'lender_enable', 'success', null, $lender->id, $data['user_id']);

            return response()->json(compact('lender'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'network error, try again'], 500);
        }
    }
}
