/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Fragment } from 'react';
import numeral from 'numeral';
import { Link } from 'react-router-dom';

import Loading from './Loading';

const FinancialOverview = ({ details, user, office, retrieving }) => {
	const lender_id = details.lender_id;
	return (
		<Fragment>
			{retrieving && <Loading />}
			{details && !retrieving && (
				<div className="element-content">
					<div className="row">
						{!office && (
							<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
								<Link className="element-box el-tablo narrow" to="/settings">
									<div className="label">Invested Capital</div>
									<div className="value">{`₦${numeral(details.invested_capital).format('0,0.00')}`}</div>
								</Link>
							</div>
						)}
						<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
							<a className="element-box el-tablo narrow">
								<div className="label">Principal Disbursed</div>
								<div className="value">{`₦${numeral(details.principal_disbursed).format('0,0.00')}`}</div>
							</a>
						</div>
						<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
							<a className="element-box el-tablo narrow">
								<div className="label">Interest Earned</div>
								<div className="value">{`₦${numeral(details.cummulative_profit).format('0,0.00')}`}</div>
							</a>
						</div>
						{!office && (
							<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
								<a className="element-box el-tablo narrow">
									<div className="label">Payout Balance</div>
									<div className="value">{`₦${numeral(details.payout_balance).format('0,0.00')}`}</div>
								</a>
							</div>
						)}
						<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
							<a className="element-box el-tablo narrow">
								<div className="label">Outstanding Principal</div>
								<div className="value">{`₦${numeral(details.outstanding_principal).format('0,0.00')}`}</div>
							</a>
						</div>
						<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
							<a className="element-box el-tablo narrow">
								<div className="label">Default Principal</div>
								<div className="value">{`₦${numeral(details.default_principal).format('0,0.00')}`}</div>
							</a>
						</div>
						<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
							<a className="element-box el-tablo narrow">
								<div className="label">Outstanding Interest</div>
								<div className="value">{`₦${numeral(details.outstanding_interest).format('0,0.00')}`}</div>
							</a>
						</div>
						<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
							<a className="element-box el-tablo narrow">
								<div className="label">Default Interest</div>
								<div className="value">{`₦${numeral(details.default_interest).format('0,0.00')}`}</div>
							</a>
						</div>
						<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
							<a className="element-box el-tablo narrow">
								<div className="label">Principal Repaid</div>
								<div className="value">{`₦${numeral(details.repaid_principal).format('0,0')}`}</div>
							</a>
						</div>
						<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
							{office ? (
								<a className="element-box el-tablo narrow">
									<div className="label">Registered Users</div>
									<div className="value">{`${numeral(details.users).format('0,0')}`}</div>
								</a>
							) : (
								<Link className="element-box el-tablo narrow" to={`/users?lender=${lender_id}`}>
									<div className="label">Registered Users</div>
									<div className="value">{`${numeral(details.users).format('0,0')}`}{' '}<span className="">({details.users_this_month})</span></div>
								</Link>
							)}
						</div>
						<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
							<a className="element-box el-tablo narrow">
								<div className="label">No of Loans</div>
								<div className="value">{`${numeral(details.loans).format('0,0')}`}</div>
							</a>
						</div>
						<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
							{office ? (
								<a className="element-box el-tablo narrow">
									<div className="label">Active Loans</div>
									<div className="value">{`${numeral(details.active_loans).format('0,0')}`}</div>
								</a>
							) : (
								<Link className="element-box el-tablo narrow" to={`/active-loans?lender=${lender_id}`}>
									<div className="label">Active Loans</div>
									<div className="value">{`${numeral(details.active_loans).format('0,0')}`}</div>
								</Link>
							)}
						</div>
						<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
							{office ? (
								<a className="element-box el-tablo narrow">
									<div className="label">Default Loans</div>
									<div className="value">{`${numeral(details.default_loans_count).format('0,0')}`}</div>
								</a>
							) : (
								<Link className="element-box el-tablo narrow" to={`/default-loans?lender=${lender_id}`}>
									<div className="label">Default Loans</div>
									<div className="value">{`${numeral(details.default_loans_count).format('0,0')}`}</div>
								</Link>
							)}
						</div>
						<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
							<a className="element-box el-tablo narrow">
								<div className="label">Requests This Month</div>
								<div className="value">{`${numeral(details.loan_request_this_month).format('0,0')}`}</div>
							</a>
						</div>
						{user && (user.role_name === 'super' || user.role_name === 'super-l' || user.role_name === 'admin-v') && (
							<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
								{office ? (
									<a className="element-box el-tablo narrow">
										<div className="label">Loans to Verify</div>
										<div className="value">{`${numeral(details.loans_unverified).format('0,0')}`}</div>
									</a>
								) : (
									<Link className="element-box el-tablo narrow" to={`/verify-loans?lender=${lender_id}`}>
										<div className="label">Loans to Verify</div>
										<div className="value">{`${numeral(details.loans_unverified).format('0,0')}`}</div>
									</Link>
								)}
							</div>
						)}
						{user && (user.role_name === 'super' || user.role_name === 'super-l' || user.role_name === 'admin-a') && (
							<div className={office ? 'col-sm-2 col-xxxl-2' : 'col-sm-3 col-xxxl-3'}>
								{office  ? (
									<a className="element-box el-tablo narrow">
										<div className="label">Loans to Approve</div>
										<div className="value">{`${numeral(details.loans_unapproved).format('0,0')}`}</div>
									</a>
								) : (
									<Link className="element-box el-tablo narrow" to={`/approve-loans?lender=${lender_id}`}>
										<div className="label">Loans to Approve</div>
										<div className="value">{`${numeral(details.loans_unapproved).format('0,0')}`}</div>
									</Link>
								)}
							</div>
						)}
					</div>
				</div>	
			)}
		</Fragment>
	);
};

export default FinancialOverview;
