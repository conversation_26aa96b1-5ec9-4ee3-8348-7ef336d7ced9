<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AuditLoans extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->tinyInteger('audited')->default(0)->after('decline_reason')->comment('0:pending,1:passed,2:wallet-fixed,3:audited');
            $table->dateTime('audited_at')->nullable()->after('audited');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropColumn(['audited', 'audited_at']);
        });
    }
}
