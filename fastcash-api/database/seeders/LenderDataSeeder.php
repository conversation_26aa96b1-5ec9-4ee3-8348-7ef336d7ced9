<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\Merchant;
use App\Models\Notify;
use App\Models\Office;
use App\Models\Payslip;
use App\Models\PayslipUpload;
use App\Models\Schedule;
use App\Models\ScheduleHistory;
use App\Models\Setting;
use App\Models\Support;
use App\Models\SupportMail;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class LenderDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $update_lender = User::withTrashed()->whereNull('lender_id')->update(['lender_id' => 1]);
            $update_wallet = Wallet::withTrashed()->whereNull('lender_id')->update(['lender_id' => 1]);
            $update_deposit_approved = Wallet::withTrashed()->where('category', 'deposit')->update(['approved' => 1]);
            $update_loans = Loan::withTrashed()->whereNull('lender_id')->update(['lender_id' => 1]);
            $update_support = Support::withTrashed()->whereNull('lender_id')->update(['lender_id' => 1]);
            $update_support_mail = SupportMail::withTrashed()->whereNull('lender_id')->update(['lender_id' => 1]);
            $update_payslip = Payslip::withTrashed()->whereNull('lender_id')->update(['lender_id' => 1]);
            $update_payslip_uploads = PayslipUpload::whereNull('lender_id')->update(['lender_id' => 1]);
            $update_merchant = Merchant::withTrashed()->whereNull('lender_id')->update(['lender_id' => 1]);
            $update_office = Office::withTrashed()->whereNull('lender_id')->update(['lender_id' => 1]);
            $update_schedule = Schedule::withTrashed()->whereNull('lender_id')->update(['lender_id' => 1]);
            $update_schedule_hist = ScheduleHistory::whereNull('lender_id')->update(['lender_id' => 1]);
            $update_setting = Setting::whereNull('lender_id')->update(['lender_id' => 1]);
            $update_transaction = Transaction::whereNull('lender_id')->update(['lender_id' => 1]);
            $update_notify = Notify::withTrashed()->whereNull('lender_id')->update(['lender_id' => 1]);
        }
        catch (\Exception $e){
            error_log($e->getLine().' '.$e->getMessage().' '.$e->getFile());
        }
    }
}
