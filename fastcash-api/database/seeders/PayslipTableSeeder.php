<?php

namespace Database\Seeders;

use App\Models\Payslip;
use Illuminate\Database\Seeder;

class PayslipTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $payslip = new Payslip();
        $payslip->name = 'MUJ<PERSON>, NJARA TIMOTHY';
        $payslip->ippis = '351239';
        $payslip->employer = 'Bureau Of Public Procurement';
        $payslip->designation = 'Principal Legal Officer';
        $payslip->gender = 'Male';
        $payslip->first_appointment = '2015-05-18';
        $payslip->birth_date = '1977-12-23';
        $payslip->salary_bank = 'ZENITH BANK PLC';
        $payslip->salary_bank_branch = '';
        $payslip->salary_account_number = '**********';
        $payslip->account_pin = '6099';
        $payslip->pfa_name = 'IBTC Pension Managers';
        $payslip->pension_pin = 'PEN100745529318';
        $payslip->grade = 'GL12_CONBPE';
        $payslip->location = 'Federal Capital Territory';
        $payslip->step = '2';
        $payslip->tax_state = 'Abuja';
        $payslip->trade_union = 'ASSOCIATION OF SENIOR CIVIL SERVANT OF NIGERIA';
        $payslip->save();

        $payslip = new Payslip();
        $payslip->name = 'MOHAMMED, BASHIR';
        $payslip->ippis = '235401';
        $payslip->employer = 'Advanced Manufacturing Technology Programme';
        $payslip->designation = 'Engineer';
        $payslip->gender = 'Male';
        $payslip->first_appointment = '2013-11-07';
        $payslip->birth_date = '1981-04-24';
        $payslip->salary_bank = 'SKYE BANK PLC';
        $payslip->salary_bank_branch = 'Jalingo';
        $payslip->salary_account_number = '**********';
        $payslip->account_pin = '3853';
        $payslip->pfa_name = 'Trust Funds Pensions Plc';
        $payslip->pension_pin = 'PEN100164725811';
        $payslip->grade = 'GL08_CONRAISS';
        $payslip->location = 'STATE CAPITAL';
        $payslip->step = '7';
        $payslip->tax_state = 'Taraba';
        $payslip->trade_union = 'ACADEMIC STAFF UNION OF RESEARCH INST';
        $payslip->save();

        $payslip = new Payslip();
        $payslip->name = 'OKECHUKWU, CHIJIOKE';
        $payslip->ippis = '235101';
        $payslip->employer = 'Advanced Manufacturing Technology Programme';
        $payslip->designation = 'Senior Engineer';
        $payslip->gender = 'Male';
        $payslip->first_appointment = '2011-06-27';
        $payslip->birth_date = '1981-09-24';
        $payslip->salary_bank = 'GUARANTY TRUST BANK PLC';
        $payslip->salary_bank_branch = 'EFFURUN, DELTA STATE';
        $payslip->salary_account_number = '**********';
        $payslip->account_pin = '6205';
        $payslip->pfa_name = 'IBTC Pension Managers';
        $payslip->pension_pin = 'PEN100563983618';
        $payslip->grade = 'GL09_CONRAISS';
        $payslip->location = 'STATE CAPITAL';
        $payslip->step = '7';
        $payslip->tax_state = 'Taraba';
        $payslip->trade_union = 'NON MEMBER';
        $payslip->save();

        $payslip = new Payslip();
        $payslip->name = 'MATALAWO, BAKO RONALD';
        $payslip->ippis = '260553';
        $payslip->employer = 'Advanced Manufacturing Technology Programme';
        $payslip->designation = 'Senior Foreman';
        $payslip->gender = 'Male';
        $payslip->first_appointment = '2011-06-24';
        $payslip->birth_date = '1970-06-21';
        $payslip->salary_bank = 'FIRST CITY MONUMENT BANK';
        $payslip->salary_bank_branch = 'Jalingo';
        $payslip->salary_account_number = '**********';
        $payslip->account_pin = '3014';
        $payslip->pfa_name = 'Premium Pension Limited';
        $payslip->pension_pin = 'PEN100394088039';
        $payslip->grade = 'GL06_CONRAISS';
        $payslip->location = 'STATE CAPITAL';
        $payslip->step = '9';
        $payslip->tax_state = '';
        $payslip->trade_union = 'NON-ACADEMIC STAFF UNION OF EDUCATIONAL AND ASSOCIATED INSTITUTIONS';
        $payslip->save();
    }
}
