/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import numeral from 'numeral';
import moment from 'moment';
import { toggleMenu } from '../actions/user';
import Naira from './Naira';


class ViewMerchant extends Component {
    componentWillMount() {
        this.props.toggleMenu(false);
    }

    componentWillUnmount() {
        this.props.toggleMenu(true);
    }
    
    render() {
        const { merchant, loans, lender } = this.props;
        
        let amount, percent1, percent2, percent3 = 0;
        if(merchant){
            amount = merchant.count * parseInt(lender.merchant_commission, 10);
            percent1 = parseInt((parseInt(merchant.count, 10) / 20) * 100, 10);
            percent2 = parseInt((parseInt(merchant.count, 10) / 20) * 100 * 1.5, 10);
            percent3 = percent2 > 100 ? 100 : percent2;
        }

        return (
            <div>
                <div className="content-i">
                    <div className="content-box">
                        {merchant && (
                            <div className="row">
                                <div className="col-sm-12">
                                    <div className="padded-lg">
                                        <div className="element-wrapper">
                                            <div className="element-box">
                                                <div className="row">
                                                    <div className="col-sm-3">
                                                        <div className="element-box el-tablo" style={{marginBottom: '0'}}>
                                                            <div className="el-tablo highlight bigger">
                                                                <div className="lead">{merchant.user}</div>
                                                            </div>
                                                            <div className="label">{lender.name}</div>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-6">
                                                        <div className="element-box el-tablo" style={{marginBottom: '0'}}>
                                                            <div className="label">Office</div>
                                                            <div className="lead">{merchant.office}</div>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-3">
                                                        <div className="element-box el-tablo" style={{marginBottom: '0'}}>
                                                            <div className="label">Merchant ID</div>
                                                            <div className="lead">{merchant.merchant_code}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                        <div className="row">
                            <div className="col-lg-7">
                                <div className="padded-lg">
                                    <div className="element-wrapper">
                                        <h6 className="element-header">Transaction Statistics</h6>
                                        <div className="element-box">
                                            <div className="table-responsive">
                                                <table className="table table-striped">
                                                    <thead>
                                                    <tr>
                                                        <th>Employee Name</th>
                                                        <th>Loan Amount</th>
                                                        <th className="text-center">Date</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    {loans.map(l => {
                                                        return (
                                                            <tr key={l.id}>
                                                                <td>{l.user && l.user.name}</td>
                                                                <td>{`₦${numeral(l.amount).format('0,0.00')}`}</td>
                                                                <td>{moment(l.created_at).format('D.MMM.YYYY')}</td>
                                                            </tr>
                                                        )
                                                    })}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="col-lg-5 b-l-lg">
                                <div className="padded-lg">
                                    <div className="element-wrapper">
                                        <h6 className="element-header">Project Statistics</h6>
                                        <div className="element-box">
                                            <div className="padded m-b">
                                                <div className="centered-header"><h6>Count & Earnings</h6></div>
                                                <div className="row">
                                                    <div className="col-6 b-r b-b">
                                                        <div className="el-tablo centered padded-v-big highlight bigger">
                                                            <div className="label">Transaction Count</div>
                                                            {merchant && <div className="value">{merchant.count}</div>}
                                                        </div>
                                                    </div>
                                                    <div className="col-6 b-b">
                                                        <div className="el-tablo centered padded-v-big highlight bigger">
                                                            <div className="label">Amount Earned</div>
                                                            <div className="value"><Naira/>{`${numeral(amount).format('0,0.00')}`}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {merchant && (
                                                <div className="padded m-b">
                                                    <div className="centered-header"><h6> Transaction Target</h6></div>
                                                    <div className="os-progress-bar primary">
                                                        <div className="bar-labels">
                                                            <div className="bar-label-left"><span>Progress</span><span className="positive">{merchant.count}</span></div>
                                                            <div className="bar-label-right"><span className="info">{merchant.count}/20</span></div>
                                                        </div>
                                                        <div className="bar-level-1" style={{width: '100%'}}>
                                                            <div className="bar-level-2" style={{width: `${percent3}%`}}>
                                                                <div className="bar-level-3" style={{width: `${percent1}%`}}></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

const mapStateToProps = (state, ownProps) => {
    return {
        loans: state.loans,
        lender: state.lender.profile,
    }
}

export default connect(mapStateToProps, { toggleMenu })(ViewMerchant);