/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';

import { baseURL, httpRequest, httpRequest2, uploadScheduleAPI, scheduleAPI } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import Upload from '../components/Upload';
import { doNotify } from '../actions/general';

class UploadSchedule extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
            files: [],
            error: '',
            month: '',
            year: '',
            disabled: true,
            schedules: [],
        }

        this.changeMonth = this.changeMonth.bind(this);
        this.changeYear = this.changeYear.bind(this);
        this.onOpenDropzone = this.onOpenDropzone.bind(this);
        this.checkDisabled = this.checkDisabled.bind(this);
        this.onDrop = this.onDrop.bind(this);
        this.delete = this.delete.bind(this);
    }

    onDrop = file => {
        this.props.startBlock();
        const { lender, user } = this.props;
        const { files, month, year } = this.state;
        const { name } = file[0];

        let formData = new FormData();
        formData.append('file', file[0]);
        formData.append('month', month);
        formData.append('year', year);
        formData.append('lender_id', lender.id);
		formData.append('staff', user.id);

        return httpRequest2(`${baseURL}${uploadScheduleAPI}`, 'POST', formData)
            .then((response) => {
				const schedules = response.schedules && response.schedules.length > 0 ? response.schedules.slice(0, 1) : [];
                this.setState({
					files: [ ...files, { name, status: 1 } ],
					disabled: true,
					schedules: [ ...this.state.schedules, ...schedules ],
				});
				this.setState({ month: '', year: '' });
                // window.location.reload(true);
                this.props.doNotify({ message: 'loan schedules uploaded!', level: 'success' });
                this.props.stopBlock();
            })
            .catch((error) => {
                this.setState({ files: [ ...files, { name, status: 0 } ] });
                this.setState({ month: '', year: '' });
                const message = error.message || 'could not process schedule!';
                this.setState({ error: message, disabled: true });
                this.props.stopBlock();
            });
    };

    checkDisabled = (m, y) => {
        if(m !== '' && y !== ''){
            this.setState({ disabled: false });
        }
        else {
            this.setState({ disabled: true });
        }
    }

    changeMonth = e => {
        this.setState({ month: e.target.value });
        this.checkDisabled(e.target.value, this.state.year);
    }

    changeYear = e => {
        this.setState({ year: e.target.value });
        this.checkDisabled(this.state.month, e.target.value);
    }

    onOpenDropzone = e => {
        this.setState({ error: '' });
    }
    
    delete = id => {
        const action = window.confirm("Are you sure you want to delete schedule?") ? true : false;
        if(action){
			this.props.startBlock();
            return httpRequest(`${baseURL}${scheduleAPI}/${id}`, 'DELETE', true)
                .then((response) => {
					const schedules = this.state.schedules.find(s => s.id === JSON.parse(response.id, 10));
					this.setState({ schedules: [ ...schedules ] });
					// window.location.reload(true);
					this.props.stopBlock();
                })
                .catch(error => {
                    const message = error.message || 'could not delete schedule!';
                    this.setState({ error: message, disabled: true });
                    this.props.stopBlock();
                });
		}
    };

    render() {
		const { error, year, month, files, disabled, schedules } = this.state;
        const thisYear = moment().format('YYYY');
        const years = [ ...Array(4).keys() ].map(y => thisYear - y );
        const months = [ ...Array(12).keys() ].map(m => moment().month(m).format('MMMM') );
        return (
            <div className="content-i">
                <div className="content-box">
                    <div className="element-wrapper compact pt-4">
                        <h6 className="element-header">Upload Repayment Schedule</h6>
                        <div className="element-box-tp">
                            <div className="row">
                                <div className="col-lg-6 col-xxl-6">
                                    <Upload
                                        error={error}
                                        message=''
                                        changeMonth={this.changeMonth}
                                        month={month}
                                        months={months}
                                        changeYear={this.changeYear}
                                        year={year}
                                        years={years}
                                        onDrop={this.onDrop}
                                        onOpenDropzone={this.onOpenDropzone}
                                        disabled={disabled}
										files={files}
										schedule={true}
                                    />
                                </div>
								<div className="col-lg-6 col-xxl-6">
									<div className="element-wrapper">
										<h6 className="element-header">Schedules</h6>
										<div className="element-box-tp">
											<div className="table-responsive">
												<table className="table table-striped table-lightfont">
													<thead>
														<tr>
															<th>Office</th>
															<th>Date</th>
															<th></th>
														</tr>
													</thead>
													<tbody>
														{schedules.map((s, i) => {
															return (
                                                                <tr key={i}>
                                                                    <td>{s.office && s.office.name}</td>
                                                                    <td>{moment(`${s.month}-${s.year}`, 'M-YYYY').format('MMM-YYYY')}</td>
                                                                    <td><a className="text-danger cursor" onClick={() => this.delete(s.id)}><i className="os-icon os-icon-database-remove"/></a></td>
                                                                </tr>
                                                            )
														})}
													</tbody>
												</table>
											</div>
										</div>
									</div>
								</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

const mapStateToProps = (state, ownProps) => {
    return {
        lender: state.lender.profile,
        user: state.user.user,
    }
};

export default connect(mapStateToProps, { startBlock, stopBlock, doNotify })(UploadSchedule);