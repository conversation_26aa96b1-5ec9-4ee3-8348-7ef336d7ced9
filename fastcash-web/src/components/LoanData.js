/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useState } from 'react';
import startCase from 'lodash.startcase';
import Popover from 'antd/lib/popover';
import { useDispatch } from 'react-redux';

import { currency, httpRequest, baseURL, loanAPI } from '../config/constants';
import DeclineMessage from './DeclineMessage';
import { updateCurrentLoan } from '../actions/loan';
import { doNotify } from '../actions/general';

const LoanData = ({ name, loan, topup }) => {
	const [submitting, setSubmitting] = useState(false);
	const [cancelReason, setDeclineReason] = useState('');
	const [otherReason, setOtherReason] = useState('');
	const [visible, setVisible] = useState(false);

	const loanAmt = loan ? parseFloat(loan.amount) : 0;
	const approved = loan ? loan.approved : -1;
	const isDisbursed = loan && loan.disbursed === 1;
	const tenure = loan ? loan.tenure : null;
	const loanMonthlyDeduction = loan ? loan.monthly_deduction : 0;
	const tenureBal = loan ? loan.tenure_balance : 0;
	const balance = loan ? loan.outstanding_balance : 0;
	// const defaultAmount = loan ? loan.default_amount : 0;
	const defaultAmountActual = loan ? loan.default_amount_actual : 0;
	const cancelRequested = loan && loan.cancel_request === -1;
	const cancelDeclined = loan && loan.cancel_declined_at;

	const dispatch = useDispatch();

	const cancelLoanRequest = async (e) => {
		try {
			e.preventDefault();
			const action = window.confirm('Are your sure?') ? true : false;
			if (!action) {
				return;
			}

			const data = {
				reason: cancelReason === 'others' ? otherReason : cancelReason,
			};

			setSubmitting(true);
			const url = `${baseURL}${loanAPI}/cancel/${loan.id}`;
			const rs = await httpRequest(url, 'PUT', true, data);
			dispatch(updateCurrentLoan(rs.loan));
			const message = 'Your request to cancel your loan request has been sent';
			notify('', message, 'success');
			setSubmitting(false);
			doHide();
		} catch (error) {
			setSubmitting(false);
			const message = error.message || 'error, please try again';
			notify('', message, 'error');
		}
	};

	const notify = (title, message, type) => {
		dispatch(doNotify({ message, level: type, title }));
	};

	const doHide = () => {
		setVisible(false);
		setDeclineReason('');
		setOtherReason('');
	};

	const onChangeReason = (e) => {
		setDeclineReason(e.target.value);
	};

	const showDeclineReason = (status) => {
		setVisible(status);
		setDeclineReason('');
		setOtherReason('');
	};

	const onChangeOthers = (e) => {
		setOtherReason(e.target.value);
	};

	return (
		<div className="element-wrapper">
			<h6 className="element-header">
				{topup
					? 'Loan Topup'
					: `Hi ${startCase(name.toLowerCase())}! Welcome to Your Dashboard`}
			</h6>
			<div className="element-content">
				{cancelDeclined && (
					<div className="row">
						<div className="col-sm-12 m-b">
							<div className="alert alert-danger">
								Your cancellation request has been declined.
								<br />
								<strong>REASON: </strong>{' '}
								<em>{loan.cancel_decline_reason || ''}</em>
								<br />
								If you still want this loan cancelled call this number
								09068233203
							</div>
						</div>
					</div>
				)}
				{approved === 0 && !cancelRequested && !cancelDeclined && (
					<div className="row">
						<div className="col-sm-12 m-b text-right">
							<Popover
								title=""
								content={
									<DeclineMessage
										doHide={doHide}
										submitting={submitting}
										onChangeReason={onChangeReason}
										onChangeOthers={onChangeOthers}
										cancelLoanRequest={cancelLoanRequest}
										cancelReason={cancelReason}
									/>
								}
								trigger="click"
								visible={visible}
								onVisibleChange={(status) => showDeclineReason(status)}
							>
								<a className="btn btn-danger btn-sm text-white">
									Cancel Loan Request
								</a>
							</Popover>
						</div>
					</div>
				)}
				{cancelRequested && !cancelDeclined && (
					<div className="row">
						<div className="col-sm-12 m-b">
							<div className="alert alert-danger">{`your ${
								topup ? 'loan topup' : 'loan'
							} cancel request has not been approved yet.`}</div>
						</div>
					</div>
				)}
				{approved === 0 && !cancelRequested && !cancelDeclined && (
					<div className="row">
						<div className="col-sm-12 m-b">
							<div className="alert alert-danger">{`your ${
								topup ? 'loan topup' : 'loan'
							} has not been approved yet.`}</div>
						</div>
					</div>
				)}
				{approved === 1 && !isDisbursed && (
					<div className="row">
						<div className="col-sm-12 m-b">
							<div className="alert alert-warning">{`your ${
								topup ? 'loan topup' : 'loan'
							} amount has not been disbursed yet.`}</div>
						</div>
					</div>
				)}
				<div className="row">
					<div className="col-sm-3">
						<div className="element-box el-tablo">
							<div className="label">{`${
								approved > 0 ? 'Running ' : ''
							}Loan Amount`}</div>
							<div className="value">{currency(loanAmt)}</div>
						</div>
					</div>
					<div className="col-sm-3">
						<div className="element-box el-tablo">
							<div className="label">Outstanding Balance</div>
							<div className="value">{currency(balance)}</div>
						</div>
					</div>
					<div className="col-sm-3">
						<div className="element-box el-tablo">
							<div className="label">Deductions Per Month</div>
							<div className="value">{currency(loanMonthlyDeduction)}</div>
						</div>
					</div>
					<div className="col-sm-3">
						<div className="element-box el-tablo">
							<div className="label">Unpaid Tenure</div>
							<div className="value">{`${tenureBal} Month${
								tenureBal > 1 ? 's' : ''
							}`}</div>
						</div>
					</div>
					{tenure && (
						<div className="col-sm-3">
							<div className="element-box el-tablo">
								<div className="label">Loan Tenure</div>
								<div className="value">{`${tenure} Month${
									tenure > 1 ? 's' : ''
								}`}</div>
							</div>
						</div>
					)}
					<div className="col-sm-3">
						<div
							className={`element-box el-tablo ${
								defaultAmountActual > 0 ? 'label-danger' : ''
							}`}
						>
							<div className="label">Default Amount</div>
							<div className="value">
								{currency(defaultAmountActual > 0 ? defaultAmountActual : 0)}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default LoanData;
