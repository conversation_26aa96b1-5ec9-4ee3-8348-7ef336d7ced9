import React from 'react';

import Loading from './Loading';

const LenderDetails = ({ fetching, lender }) => {
	return (
		<div className="element-wrapper">
			<h6 className="element-header">{lender.name}</h6>
			<div className="element-box" style={{padding: '1.5rem 10px'}}>
				{fetching && <Loading />}
				{!fetching && (
					<div className="table-responsive" style={{overflowX: 'scroll'}}>
						<table className="table table-lightborder">
							<tbody>
								<tr>
									<td>Lender code</td>
									<td className="text-left">{lender.lender_code}</td>
								</tr>
								<tr>
									<td>Lender/Organization</td>
									<td className="text-left">{lender.name}</td>
								</tr>
								<tr>
									<td>Email address</td>
									<td className="text-left">{lender.email}</td>
								</tr>
								<tr>
									<td>Phone number</td>
									<td className="text-left">{lender.phone_number}</td>
								</tr>
								<tr>
									<td>Address</td>
									<td className="text-left">{lender.address || '-'}</td>
								</tr>
								<tr>
									<td>State</td>
									<td className="text-left">{lender.state ? `${lender.state?.name} ${lender.state?.id === 15 ? '':'State'}`: '--'}</td>
								</tr>
								<tr>
									<td>Country</td>
									<td className="text-left">Nigeria</td>
								</tr>
								<tr>
									<td>Next of kin</td>
									<td className="text-left">{lender.next_of_kin || '-'}</td>
								</tr>
								<tr>
									<td>Bank name</td>
									<td className="text-left">{lender.bank ? lender.bank.name : '-'}</td>
								</tr>
								<tr>
									<td>Account number</td>
									<td className="text-left">{lender.account_number || '-'}</td>
								</tr>
								<tr>
									<td>Interest rate</td>
									<td className="text-left">{`${lender.interest_rate || 0}%`}</td>
								</tr>
								<tr>
									<td>Commission</td>
									<td className="text-left">{`${lender.commission || 0}%`}</td>
								</tr>
							</tbody>
						</table>
					</div>
				)}
			</div>
		</div>
	);
};

export default LenderDetails;