<?php

namespace App\Http\Controllers\Api\V1;

use App\Exports\ExportLoan;
use App\Models\Accountno;
use App\Models\Earning;
use App\Models\Lender;
use App\Models\Loan;
use App\Models\Localgovt;
use App\Models\Merchant;
use App\Models\MerchantPay;
use App\Models\MultiFund;
use App\Models\MultiFundTransaction;
use App\Models\Notify;
use App\Models\Office;
use App\Models\PaymentDate;
use App\Models\Payslip;
use App\Models\RemitaUser;
use App\Models\Setting;
use App\Models\State;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use App\Services\MailHandler;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Response;
use Maatwebsite\Excel\Facades\Excel;
use PDF;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\Remita;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Log;
use Rave;

class LoanController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index(Request $request)
    {
        $q = $request->has('q') ? $request->input('q') : '';
        $lender = $request->has('lender') ? $request->input('lender') : '';
        $disbursed = $request->has('disbursed') ? $request->input('disbursed') : '';

        if ($lender != '') {
            if ($q == 'admin') {
                $limit = $request->has('pagesize') && $request->input('pagesize') != '' ? $request->input('pagesize') : 10;
                $page = $request->has('page') && $request->input('page') != '' ? $request->input('page') : 1;
                $skip = ($page - 1) * $limit;
                $category = $request->has('category') && $request->input('category') != '' ? $request->input('category') : '';
                $office_id = $request->has('office') && $request->input('office') != '' ? $request->input('office') : 0;
                $when = $request->has('when') && $request->input('when') != '' ? $request->input('when') : 0;

                $status = $request->has('status') && $request->input('status') != '' ? $request->input('status') : 'pending';
                $from = $request->has('from') && $request->input('from') != '' ? $request->input('from') : '';
                $to = $request->has('to') && $request->input('to') != '' ? $request->input('to') : '';
                $_from = $from == '' ? '' : Carbon::createFromFormat('d-m-Y', $from)->toDateTimeString();
                $_to = $to == '' ? '' : Carbon::createFromFormat('d-m-Y', $to)->toDateTimeString();

                if ($category == 'unverified') {
                    if ($lender == 0) {
                        if ($office_id == 0) {
                            $loans = Loan::with('office', 'accountno')->where('verified', 0)->where('approved', 0)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                            $count = Loan::where('verified', 0)->where('approved', 0)->count();
                        } else {
                            $loans = Loan::with('office', 'accountno')->where('verified', 0)->where('approved', 0)->where('office_id', $office_id)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                            $count = Loan::where('verified', 0)->where('approved', 0)->where('office_id', $office_id)->count();
                        }

                        $all_loans = Loan::where('verified', 0)->where('approved', 0)->get(['office_id']);
                    } else {
                        if ($office_id == 0) {
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('verified', 0)->where('approved', 0)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                            $count = Loan::where('lender_id', $lender)->where('verified', 0)->where('approved', 0)->count();
                        } else {
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('verified', 0)->where('approved', 0)->where('office_id', $office_id)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                            $count = Loan::where('lender_id', $lender)->where('verified', 0)->where('approved', 0)->where('office_id', $office_id)->count();
                        }

                        $all_loans = Loan::where('lender_id', $lender)->where('verified', 0)->where('approved', 0)->get(['office_id']);
                    }

                    $loans = $loans->map(function ($item, $key) {
                        $user = User::with('office', 'lender', 'accountno')->where('id', $item->user_id)->first();
                        $user->loans = Loan::with('user', 'lender')->where('user_id', $user->id)->withTrashed()->get();
                        $item->user = $user;
                        $item->lender = Lender::where('id', $item->lender_id)->first();
                        $item->remita_user = RemitaUser::where('id', $item->remita_user_id)->first();

                        return $item;
                    });
                } else if ($category == 'unapproved') {
                    if ($lender == 0) {
                        if ($office_id == 0) {
                            $loans = Loan::with('office', 'accountno')->where('verified', 1)->where('approved', 0)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                            $count = Loan::where('verified', 1)->where('approved', 0)->count();
                        } else {
                            $loans = Loan::with('office', 'accountno')->where('verified', 1)->where('approved', 0)->where('office_id', $office_id)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                            $count = Loan::where('verified', 1)->where('approved', 0)->where('office_id', $office_id)->count();
                        }

                        $all_loans = Loan::where('verified', 1)->where('approved', 0)->get(['office_id']);
                    } else {
                        if ($office_id == 0) {
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('verified', 1)->where('approved', 0)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                            $count = Loan::where('lender_id', $lender)->where('verified', 1)->where('approved', 0)->count();
                        } else {
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('verified', 1)->where('approved', 0)->where('office_id', $office_id)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                            $count = Loan::where('lender_id', $lender)->where('verified', 1)->where('approved', 0)->where('office_id', $office_id)->count();
                        }

                        $all_loans = Loan::where('lender_id', $lender)->where('verified', 1)->where('approved', 0)->get(['office_id']);
                    }

                    $loans = $loans->map(function ($item, $key) {
                        $user = User::with('office', 'lender', 'accountno')->where('id', $item->user_id)->first();
                        $user->loans = Loan::with('user', 'lender')->where('user_id', $user->id)->withTrashed()->get();
                        $item->user = $user;
                        $item->lender = Lender::where('id', $item->lender_id)->first();
                        $item->remita_user = RemitaUser::where('id', $item->remita_user_id)->first();

                        return $item;
                    });
                } else if ($category == 'approved') {
                    if ($office_id == 0) {
                        $loans = Loan::with('office', 'accountno')->where('verified', 1)->where('approved', 1)->where('disbursed', 0)->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();

                        $count = Loan::where('verified', 1)->where('approved', 1)->where('disbursed', 0)->count();
                    } else {
                        $loans = Loan::with('office', 'accountno')->where('verified', 1)->where('approved', 1)->where('disbursed', 0)->where('office_id', $office_id)->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();

                        $count = Loan::where('verified', 1)->where('approved', 1)->where('disbursed', 0)->where('office_id', $office_id)->count();
                    }

                    $all_loans = Loan::where('verified', 1)->where('approved', 1)->where('disbursed', 0)->get(['office_id']);

                    $loans = $loans->map(function ($item, $key) {
                        $user = User::with('office', 'lender', 'accountno')->where('id', $item->user_id)->first();
                        $user->loans = Loan::with('user', 'lender')->where('user_id', $user->id)->withTrashed()->get();
                        $item->user = $user;
                        $item->lender = Lender::where('id', $item->lender_id)->first();
                        $item->remita_user = RemitaUser::where('id', $item->remita_user_id)->first();

                        return $item;
                    });
                } else if ($category == 'cancelled') {

                    if ($office_id == 0) {
                        if($status === 'pending' && !empty($from)){
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('cancel_request', '!=', 0)->whereBetween('cancel_request_at', [$_from, $_to])->whereNotNull('cancel_reason')->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();
                            $count = Loan::where('cancel_request', '!=', 0)->where('lender_id', $lender)->whereNotNull('cancel_reason')->count();
                        }else if($status === 'pending' && empty($from)){
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('cancel_request', '!=', 0)->whereNotNull('cancel_reason')->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();
                            $count = Loan::where('cancel_request', '!=', 0)->where('lender_id', $lender)->whereNotNull('cancel_reason')->count();
                        }else if($status === 'approved' && !empty($from)){
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('cancel_request', '!=', 0)->whereBetween('cancel_request_at', [$_from, $_to])->whereNotNull('cancelled_at')->whereNotNull('cancelled_by')->whereNotNull('deleted_by')->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();
                            $count = Loan::where('cancel_request', '!=', 0)->where('lender_id', $lender)->whereNotNull('cancelled_at')->whereNotNull('cancelled_by')->whereNotNull('deleted_by')->count();
                        }else if($status === 'approved' && empty($from)){
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('cancel_request', '!=', 0)->whereNotNull('cancelled_at')->whereNotNull('cancelled_by')->whereNotNull('deleted_by')->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();
                            $count = Loan::where('cancel_request', '!=', 0)->where('lender_id', $lender)->whereNotNull('cancelled_at')->whereNotNull('cancelled_by')->whereNotNull('deleted_by')->count();
                        }else if($status === 'declined' && !empty($from)){
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('cancel_request', '!=', 1)->whereBetween('cancel_request_at', [$_from, $_to])->whereNotNull('cancel_decline_reason')->whereNotNull('cancel_declined_at')->whereNotNull('cancel_declined_by')->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();
                            $count = Loan::where('cancel_request', '!=', 1)->where('lender_id', $lender)->whereNotNull('cancel_decline_reason')->whereNotNull('cancel_declined_at')->whereNotNull('cancel_declined_by')->count();
                        }else if($status === 'declined' && empty($from)){
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('cancel_request', '!=', 1)->whereNotNull('cancel_decline_reason')->whereNotNull('cancel_declined_at')->whereNotNull('cancel_declined_by')->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();
                            $count = Loan::where('cancel_request', '!=', 1)->where('lender_id', $lender)->whereNotNull('cancel_decline_reason')->whereNotNull('cancel_declined_at')->whereNotNull('cancel_declined_by')->count();
                        }
                    } else {
                        if($status === 'pending' && !empty($from)){
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('office_id', $office_id)->whereBetween('cancel_request_at', [$_from, $_to])->where('cancel_request', '!=', 0)->whereNotNull('cancel_reason')->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();
                            $count = Loan::where('office_id', $office_id)->where('lender_id', $lender)->where('cancel_request', '!=', 0)->whereNotNull('cancel_reason')->count();
                        }if($status === 'pending' && empty($from)){
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('office_id', $office_id)->where('cancel_request', '!=', 0)->whereNotNull('cancel_reason')->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();
                            $count = Loan::where('office_id', $office_id)->where('lender_id', $lender)->where('cancel_request', '!=', 0)->whereNotNull('cancel_reason')->count();
                        }else if($status === 'approved' && !empty($from)){
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('office_id', $office_id)->whereBetween('cancel_request_at', [$_from, $_to])->where('cancel_request', '!=', 0)->whereNotNull('cancelled_at')->whereNotNull('cancelled_by')->whereNotNull('deleted_by')->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();
                            $count = Loan::where('office_id', $office_id)->where('lender_id', $lender)->where('cancel_request', '!=', 0)->whereNotNull('cancelled_at')->whereNotNull('cancelled_by')->whereNotNull('deleted_by')->count();
                        }else if($status === 'approved' && empty($from)){
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('office_id', $office_id)->where('cancel_request', '!=', 0)->whereNotNull('cancelled_at')->whereNotNull('cancelled_by')->whereNotNull('deleted_by')->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();
                            $count = Loan::where('office_id', $office_id)->where('lender_id', $lender)->where('cancel_request', '!=', 0)->whereNotNull('cancelled_at')->whereNotNull('cancelled_by')->whereNotNull('deleted_by')->count();
                        }else if($status === 'declined' && !empty($from)){
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('office_id', $office_id)->whereBetween('cancel_request_at', [$_from, $_to])->where('cancel_request', '!=', 1)->whereNotNull('cancel_decline_reason')->whereNotNull('cancel_declined_at')->whereNotNull('cancel_declined_by')->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();
                            $count = Loan::where('office_id', $office_id)->where('lender_id', $lender)->where('cancel_request', '!=', 1)->whereNotNull('cancel_decline_reason')->whereNotNull('cancel_declined_at')->whereNotNull('cancel_declined_by')->count();
                        }else if($status === 'declined' && empty($from)){
                            $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('office_id', $office_id)->where('cancel_request', '!=', 1)->whereNotNull('cancel_decline_reason')->whereNotNull('cancel_declined_at')->whereNotNull('cancel_declined_by')->orderBy('approved_at', 'desc')->skip($skip)->take($limit)->get();
                            $count = Loan::where('office_id', $office_id)->where('lender_id', $lender)->where('cancel_request', '!=', 1)->whereNotNull('cancel_decline_reason')->whereNotNull('cancel_declined_at')->whereNotNull('cancel_declined_by')->count();
                        }
                    }

                    $all_loans = Loan::where('cancel_request', '!=', 0)->get(['office_id']);

                    $loans = $loans->map(function ($item, $key) {
                        $user = User::with('office', 'lender', 'accountno')->where('id', $item->user_id)->first();
                        $user->loans = Loan::with('user', 'lender')->where('user_id', $user->id)->withTrashed()->get();
                        $item->user = $user;
                        $item->lender = Lender::where('id', $item->lender_id)->first();
                        $item->remita_user = RemitaUser::where('id', $item->remita_user_id)->first();

                        return $item;
                    });
                } else if ($category == 'active') {
                    if ($when == 1) {
                        $date = Carbon::now()->format('Y-m');

                        $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('approved', 1)->where('liquidated', 0)->where('disbursed', $disbursed)->where('disbursed_at', 'LIKE', '%' . $date . '%')->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                        $loans = $loans->reject(function ($item) {
                            $repayments = Transaction::where('loan_id', $item->id)->where('transaction_flag', 'deduction')->count();
                            return $repayments > 0;
                        })->values();

                        $counts = Loan::where('lender_id', $lender)->where('approved', 1)->where('liquidated', 0)->where('disbursed', $disbursed)->where('disbursed_at', 'LIKE', '%' . $date . '%')->get();

                        $counts = $counts->reject(function ($item) {
                            $repayments = Transaction::where('loan_id', $item->id)->where('transaction_flag', 'deduction')->count();
                            return $repayments > 0;
                        })->values();

                        $count = count($counts);

                        $all_loans = [];
                    } else {
                        if ($lender == 0) {
                            if ($office_id == 0) {
                                if ($disbursed == 'all') {
                                    $loans = Loan::with('office', 'accountno')->where('approved', 1)->where('liquidated', 0)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                                    $count = Loan::where('approved', 1)->where('liquidated', 0)->count();
                                } else {
                                    $loans = Loan::with('office', 'accountno')->where('approved', 1)->where('liquidated', 0)->where('disbursed', $disbursed)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                                    $count = Loan::where('approved', 1)->where('liquidated', 0)->where('disbursed', $disbursed)->count();
                                }
                            } else {
                                if ($disbursed == 'all') {
                                    $loans = Loan::with('office', 'accountno')->where('approved', 1)->where('liquidated', 0)->where('office_id', $office_id)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                                    $count = Loan::where('approved', 1)->where('liquidated', 0)->where('office_id', $office_id)->count();
                                } else {
                                    $loans = Loan::with('office', 'accountno')->where('approved', 1)->where('liquidated', 0)->where('disbursed', $disbursed)->where('office_id', $office_id)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                                    $count = Loan::where('approved', 1)->where('liquidated', 0)->where('disbursed', $disbursed)->where('office_id', $office_id)->count();
                                }
                            }

                            $all_loans = Loan::where('approved', 1)->where('liquidated', 0)->get(['office_id']);
                        } else {
                            if ($office_id == 0) {
                                if ($disbursed == 'all') {
                                    $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('approved', 1)->where('liquidated', 0)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                                    $count = Loan::where('lender_id', $lender)->where('approved', 1)->where('liquidated', 0)->count();
                                } else {
                                    $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('approved', 1)->where('liquidated', 0)->where('disbursed', $disbursed)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                                    $count = Loan::where('lender_id', $lender)->where('approved', 1)->where('liquidated', 0)->where('disbursed', $disbursed)->count();
                                }
                            } else {
                                if ($disbursed == 'all') {
                                    $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('approved', 1)->where('liquidated', 0)->where('office_id', $office_id)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                                    $count = Loan::where('lender_id', $lender)->where('approved', 1)->where('liquidated', 0)->where('office_id', $office_id)->count();
                                } else {
                                    $loans = Loan::with('office', 'accountno')->where('lender_id', $lender)->where('approved', 1)->where('liquidated', 0)->where('disbursed', $disbursed)->where('office_id', $office_id)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                                    $count = Loan::where('lender_id', $lender)->where('approved', 1)->where('liquidated', 0)->where('disbursed', $disbursed)->where('office_id', $office_id)->count();
                                }
                            }

                            $all_loans = Loan::where('lender_id', $lender)->where('approved', 1)->where('liquidated', 0)->get(['office_id']);
                        }
                    }

                    $loans = $loans->map(function ($item, $key) {
                        $user = User::with('office', 'lender', 'accountno')->where('id', $item->user_id)->first();
                        $user->loans = Loan::with('user', 'lender')->where('user_id', $user->id)->withTrashed()->get();
                        $item->user = $user;
                        $item->lender = Lender::where('id', $item->lender_id)->first();
                        $item->repayments = Transaction::where('loan_id', $item->id)->where('transaction_flag', 'deduction')->count();
                        $item->remita_user = RemitaUser::where('id', $item->remita_user_id)->first();

                        return $item;
                    });
                } else if ($category == 'multi-fund') {
                    if ($office_id == 0) {
                        $loans = Loan::with('office', 'accountno')->where('is_multifund', 1)->where('cancel_multifund', 0)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                        $count = Loan::where('is_multifund', 1)->where('cancel_multifund', 0)->count();
                    } else {
                        $loans = Loan::with('office', 'accountno')->where('is_multifund', 1)->where('cancel_multifund', 0)->where('office_id', $office_id)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();

                        $count = Loan::where('is_multifund', 1)->where('cancel_multifund', 0)->where('office_id', $office_id)->count();
                    }

                    $all_loans = Loan::where('is_multifund', 1)->where('cancel_multifund', 0)->get(['office_id']);

                    $loans = $loans->map(function ($item, $key) use ($lender) {
                        $user = User::with('office', 'lender', 'accountno')->where('id', $item->user_id)->first();
                        $user->loans = Loan::with('user', 'lender')->where('user_id', $user->id)->withTrashed()->get();
                        $item->user = $user;
                        $item->lender = Lender::where('id', $item->lender_id)->first();

                        $funds = MultiFund::with('lender')->where('loan_id', $item->id)->get();
                        $funds = $funds->map(function ($item, $key) use ($lender) {
                            $item->repayments = MultiFundTransaction::where('loan_id', $item->id)->where('lender_id', $lender)->get();

                            return $item;
                        });
                        $item->remita_user = RemitaUser::where('id', $item->remita_user_id)->first();

                        $item->funds = $funds;

                        return $item;
                    });
                } else if ($category == 'defaults') {
                    if ($office_id == 0) {
                        $default_loans = DB::table('payment_dates')
                            ->join('loans', 'loans.id', '=', 'payment_dates.loan_id')
                            ->where('payment_dates.paid', 0)->where('payment_dates.payment_date', '<', Carbon::now()->format('Y-m-d H:i:s'))->whereNull('loans.deleted_at')->where('loans.disbursed', 1)
                            ->select('payment_dates.loan_id')
                            ->groupBy('payment_dates.loan_id')
                            ->get();
                    } else {
                        $default_loans = DB::table('payment_dates')
                            ->join('loans', 'loans.id', '=', 'payment_dates.loan_id')
                            ->where('payment_dates.paid', 0)->where('payment_dates.payment_date', '<', Carbon::now()->format('Y-m-d H:i:s'))->whereNull('loans.deleted_at')->where('loans.disbursed', 1)->where('loans.office_id', $office_id)
                            ->select('payment_dates.loan_id')
                            ->groupBy('payment_dates.loan_id')
                            ->get();
                    }

                    $all_loans = DB::table('payment_dates')
                        ->join('loans', 'loans.id', '=', 'payment_dates.loan_id')
                        ->where('payment_dates.paid', 0)->where('payment_dates.payment_date', '<', Carbon::now()->format('Y-m-d H:i:s'))->whereNull('loans.deleted_at')->where('loans.disbursed', 1)
                        ->select('loans.office_id')
                        ->get();

                    $data = [];
                    foreach ($default_loans as $loan) {
                        $data[] = Loan::with('office', 'accountno')->where('id', $loan->loan_id)->first();
                    }

                    $filtered_loans = [];
                    if ($lender != 0) {
                        foreach ($data as $item) {
                            if ($item->lender_id == $lender) {
                                $filtered_loans[] = $item;
                            }
                        }
                    } else {
                        $filtered_loans = $data;
                    }

                    $count = count($filtered_loans);
                    $loans = array_slice($filtered_loans, $skip, $limit);

                    $loans = collect($loans)->map(function ($item, $key) {
                        $user = User::with('office', 'lender', 'accountno')->where('id', $item->user_id)->first();
                        $user->loans = Loan::with('user', 'lender', 'remita_user')->where('user_id', $user->id)->withTrashed()->get();

                        $item->user = $user;
                        $item->lender = Lender::where('id', $item->lender_id)->first();
                        $item->remita_user = RemitaUser::where('id', $item->remita_user_id)->first();

                        return $item;
                    });
                }

                $office_ids = [];
                foreach ($all_loans as $loan) {
                    $office_ids[] = $loan->office_id;
                }
                $offices = Office::find($office_ids);

                $result = (object)null;
                $result->total = $count;
                $result->size = $limit;
                $result->page = $page;
                $result->data = $loans;
                $result->offices = $offices;

                return response()->json(compact('result'), 200);
            } else if ($q == 'recent') {
                $loans = Loan::with('office')->where('lender_id', $lender)->take(15)->orderBy('created_at', 'desc')->take(10)->get();
                $loans = $loans->map(function ($item, $key) {
                    $user = User::with('office', 'lender', 'accountno')->where('id', $item->user_id)->first();
                    $user->loans = Loan::with('user', 'lender', 'accountno', 'remita_user')->where('user_id', $user->id)->withTrashed()->get();
                    $user->payslip = Payslip::where('id', $user->payslip)->first();

                    $item->user = $user;
                    $item->accountno = Accountno::find($item->accountno_id);
                    $item->lender = Lender::where('id', $item->lender_id)->first();
                    $item->remita_user = RemitaUser::where('id', $item->remita_user_id)->first();

                    return $item;
                });
            }

            return response()->json(compact('loans'), 200);
        }

        return response()->json(['error' => 'no loans found!'], 500);
    }

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function audit(Request $request)
    {
        try {
            $limit = $request->has('pagesize') && $request->input('pagesize') != '' ? $request->input('pagesize') : 10;
            $page = $request->has('page') && $request->input('page') != '' ? $request->input('page') : 1;
            $skip = ($page - 1) * $limit;

            $loans = Loan::withTrashed()->with('user:id,name')->where('audited', '!=', 3)->where('disbursed', 1)->orderBy('created_at', 'asc')->orderBy('audited', 'desc')->skip($skip)->take($limit)->get();
            $loans = $loans->map(function ($item, $key) {
                $item->wallets = Wallet::withTrashed()->where('loan_id', $item->id)->orderBy('transaction_id', 'asc')->get();
                $item->transactions = Transaction::withTrashed()->with('wallet')->where('loan_id', $item->id)->orderBy('created_at', 'asc')->get();
                $item->dates = PaymentDate::where('loan_id', $item->id)->orderBy('created_at', 'asc')->get();
                $item->activities = Notify::withTrashed()->with('action')->where('user_id', $item->user_id)->orderBy('created_at', 'asc')->get();
                $item->wallet_balance = Wallet::where('user_id', $item->user_id)->where('approved', 1)->sum('amount');

                return $item;
            });

            $count = Loan::withTrashed()->where('audited', '!=', 3)->where('disbursed', 1)->count();

            $result = (object)null;
            $result->total = $count;
            $result->size = $limit;
            $result->page = $page;
            $result->data = $loans;

            return response()->json(compact('result'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not fetch loans'], 500);
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function pendingLiquidated(Request $request)
    {
        try {
            $limit = $request->has('pagesize') && $request->input('pagesize') != '' ? $request->input('pagesize') : 10;
            $page = $request->has('page') && $request->input('page') != '' ? $request->input('page') : 1;
            $skip = ($page - 1) * $limit;

            $transactions = Transaction::where('approved', 0)->where('transaction_flag', 'liquidate')->skip($skip)->take($limit)->orderBy('created_at', 'DESC')->get();
            $transactions = $transactions->map(function ($item, $key) {
                $item->user = User::where('id', $item->user_id)->first(['ippis', 'customer_id', 'name', 'phone']);
                $item->loan = Loan::where('id', $item->loan_id)->first();
                $item->lender = Lender::where('id', $item->lender_id)->first();

                return $item;
            });

            $count = Transaction::where('approved', 0)->where('transaction_flag', 'liquidate')->count();

            $result = (object)null;
            $result->total = $count;
            $result->size = $limit;
            $result->page = $page;
            $result->data = $transactions;

            return response()->json(compact('result'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not fetch transactions'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required',
            'amount' => 'required',
            'monthly_deduction' => 'required',
            'tenure' => 'required',
            'status' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        DB::beginTransaction();

        $transaction = null;

        $user = User::find($request->get('user_id'));

        if ($user && $user->enabled == 0) {
            return response()->json(['error' => 'your account has been disabled, please contact support'], 500);
        }

        $lender = Lender::where('id', $user->lender_id)->first();

        try {
            $data = $request->all();
            $check_loan = Loan::where('user_id', $data['user_id'])->where('liquidated', 0)->first();
            info(json_encode($check_loan));
            if ($check_loan && ($data['topup'] == '0' || $data['topup'] == 0)) {
                return response()->json(['error' => 'you cant take a loan at this moment because you have not liquidated your current loan'], 500);
            }

            $merchant = null;
            if ($request->has('merchant_code') && ($request->get('merchant_code') != null || $request->get('merchant_code') != '')) {
                $merchant = Merchant::where('merchant_code', $request->get('merchant_code'))->first();
                if (!$merchant) {
                    return response()->json(['error' => 'your merchant code is incorrect'], 500);
                }
            }

            $old___loan = null;

            $is_topup = 0;
            if ($data['topup'] == '1' || $data['topup'] == 1) {
                $is_topup = 1;

                $old___loan = Loan::find($data['old_loan']);
            }

            if ($is_topup == 1 && $old___loan && $old___loan->topup != null) {
                return response()->json(['error' => 'you already have a pending topup request.'], 500);
            }

            $user->office = Office::find($user->office_id);

            $monthly_commission = $lender->commission > 0 ? ($lender->commission / 100) * $data['monthly_interest'] : 0.00;
            $deduction = $data['monthly_deduction'] * $data['tenure'];

            $_loan = Loan::create([
                'user_id' => $data['user_id'],
                'amount' => $data['amount'],
                'disburse_amount' => $data['disburse_amount'],
                'net_earning' => $data['net_earning'],
                'interest_rate' => $data['interest_rate'],
                'monthly_deduction' => $data['monthly_deduction'],
                'total_deduction' => round($deduction, 2),
                'tenure' => $data['tenure'],
                'status' => $data['status'],
                'office_id' => $user->office_id,
                'accountno_id' => $data['account_number'],
                'monthly_principal' => $data['monthly_principal'],
                'monthly_interest' => $data['monthly_interest'],
                'commission_percent' => $lender->commission,
                'interest_percent' => 100 - $lender->commission,
                'monthly_commission' => round($monthly_commission, 2),
                'total_commission' => round($monthly_commission * $data['tenure'], 2),
                'net_earnings' => $data['net_earnings'],
                'platform' => $data['platform'],
                'auth_code' => isset($data['auth_code']) && $data['auth_code'] != '0' ? $data['auth_code'] : null,
                'lender_id' => $user->lender_id,
                'approved' => 0,
                'verified' => 0,
                'liquidated' => 0,
                'disbursed' => 0,
                'topup' => null,
                'is_topup' => $is_topup,
                'liquidate_approve' => 0,
                'admin_request' => $data['admin_request'],
                'remita_user_id' => $data['remita_id'] == '' ? null : $data['remita_id'],
            ]);

            $loan = Loan::find($_loan->id);

            if ($data['topup'] == '1' || $data['topup'] == 1) {
                try {
                    $old__loan = Loan::find($data['old_loan']);
                    $old__loan->topup = $loan->id;
                    $old__loan->topup_date = date('Y-m-d H:i:s');
                    $old__loan->liquidated = 1;
                    $old__loan->liquidated_at = date('Y-m-d H:i:s');
                    $old__loan->liquidate_approve = 0;
                    $old__loan->save();

                    $__balance_amount = $data['balance_amount'];

                    $transaction = Transaction::create([
                        'user_id' => $old__loan->user_id,
                        'loan_id' => $old__loan->id,
                        'office_id' => $old__loan->office_id,
                        'source' => strtoupper($old__loan->platform),
                        'amount' => $__balance_amount,
                        'interest' => $old__loan->monthly_interest,
                        'principal' => $__balance_amount - $old__loan->monthly_interest,
                        'outst_amount' => 0,
                        'status' => 'Pending',
                        'transaction_flag' => 'liquidate',
                        'description' => 'Loan Liquidated by Topup',
                        'channel' => 'loan-liquidated-topup',
                        'lender_id' => $old__loan->lender_id,
                        'repayment_date' => date('Y-m-d'),
                    ]);

                    $transaction->user = User::where('id', $transaction->user_id)->first();
                    $transaction->loan = Loan::where('id', $transaction->loan_id)->first();

                } catch (\Exception $e) {
                    Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
                }
            }

            if ($request->has('merchant_code') && $merchant != null) {
                $loan->merchant_code = $merchant->merchant_code;
                $loan->save();

                MerchantPay::create([
                    'loan_id' => $loan->id,
                    'merchant_id' => $merchant->id,
                ]);
            }

            $uu = User::where('id', $user->id)->first();
            $uu->loan_id = $loan->id;
            $uu->save();

            //customer id not there
            $earnings = Earning::where('auth_code', $loan->auth_code)->where('platform', 'remita')->get();
            if (count($earnings) > 0) {
                $_user = User::find($loan->user_id);
                if ($_user && ($_user->customer_id == null || $_user->customer_id == '')) {
                    Log::debug('update customer id: ' . $earnings[0]->customer_id);
                    $_user->customer_id = $earnings[0]->customer_id;
                    $_user->save();
                }
            }

            $notify = doNotify($data['user_id'], 'loans', $loan->id, 'loan_request', 'success', null, $user->lender_id, null);

            DB::commit();

            $loan->user = User::with('office', 'lender')->where('id', $loan->user_id)->first();
            $loan->has_topup = Loan::where('topup', $loan->id)->first();
            $loan->repayments = [];

            return response()->json(['loan' => $loan, 'transaction' => $transaction], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not give out loan, try again later'], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            $loan = Loan::where('id', $id)->first();
            $is_admin = $request->has('is_admin') && $request->input('is_admin') == 1;

            if ($is_admin) {
                $user = User::where('id', $loan->user_id)->first();
                if ($user) {
                    $user->lender = Lender::where('id', $user->lender_id)->first();
                    $user->settings = Setting::where('lender_id', 1)->get();
                    $user->office = Office::find($user->office_id);
                }

                $payslip = null;
                if ($user->payslip_id) {
                    $payslip = Payslip::where('id', $user->payslip_id)->first();
                    if ($payslip) {
                        try {
                            $max_age = Setting::where('lender_id', 1)->where('name', 'max_age')->first();
                            $era = Carbon::parse($payslip->birth_date)->addYears($max_age->value)->format('Y-m-d');
                            $max_served = Setting::where('lender_id', 1)->where('name', 'max_served')->first();

                            if ($payslip->first_appointment) {
                                $ers = Carbon::parse($payslip->first_appointment)->addYears($max_served->value)->format('Y-m-d');

                                $dates = [$era, $ers];
                                $expected_date_of_retirement = min(array_map('strtotime', $dates));
                                $payslip->retire_expected_at = date('Y-m-d', $expected_date_of_retirement);
                            }

                            $payslip->save();
                        } catch (\Exception $e) {
                        }

                        $payslip->lender = Lender::where('id', $payslip->lender_id)->first();
                        $payslip->settings = Setting::where('lender_id', 1)->get();
                    }
                }

                $earnings = $loan->platform == 'ippis' ?
                    DB::table('earnings')
                        ->select(DB::raw('DISTINCT ippis, month, year, net_earning, platform, gross_earning, auth_code, created_at'))
                        ->where('ippis', $user->ippis)->where('platform', $loan->platform)->where('net_earning', '!=', '0.00')
                        ->orderBy('year', 'desc')->orderBy('month', 'desc')
                        ->take(12)->get()
                    :
                    DB::table('earnings')
                        ->select(DB::raw('DISTINCT customer_id, month, year, net_earning, platform, gross_earning, auth_code, created_at'))
                        ->where('customer_id', $user->customer_id)->where('platform', $loan->platform)->where('net_earning', '!=', '0.00')
                        ->orderBy('year', 'desc')->orderBy('month', 'desc')
                        ->take(12)->get();

                $bank_accounts = Accountno::where('user_id', $user->id)->get();

                $lenders = Lender::get(['id', 'name']);

                $result = (object)null;
                $result->profile = $user;
                $result->payslip = $payslip;
                $result->lenders = $lenders;
                $result->loan = $loan;
                $result->earnings = $earnings;
                $result->bank_accounts = $bank_accounts;
                $result->has_remita = $user->customer_id;
                $result->loan_platform = $loan->platform;
                $result->phone = $user->phone;

                return response()->json(compact('result'), 200);
            } else {
                $total_paid = PaymentDate::where('loan_id', $loan->id)->where('paid', 1)->count();
                $should_have_paid = round($loan->tenure / 2);
                $has_ended = $loan->disbursed == 1 ? hasLoanEnded($loan) : true;
                $eligible = $total_paid >= $should_have_paid && $has_ended == false ? 1 : 0;

                $loan->topup_eligible = $eligible;
                $loan->has_topup = Loan::where('topup', $loan->id)->first();
                $loan->total_paid = $total_paid;

                $transactions = Transaction::where('loan_id', $loan->id)->where('transaction_flag', 'deduction')->where('approved', 1)->get(['amount', 'principal', 'interest']);
                $t_amount = [];
                $amt_paid = [];
                foreach ($transactions as $item) {
                    $t_amount[] = $item->amount - ($item->principal + $item->interest);
                    $amt_paid[] = $item->amount;
                }
                $outstanding = array_sum($t_amount);

                $loan->outstanding_balance = $loan->total_deduction - array_sum($amt_paid);
                $loan->tenure_balance = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)->count();

                $not_paid_default = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)->whereDate('payment_date', '<', Carbon::now())->count();

                $excess = array_sum($amt_paid) - ($total_paid * ($loan->monthly_principal + $loan->monthly_interest));

                $default = $loan->disbursed == 1 && $has_ended ? $loan->total_deduction - array_sum($amt_paid) : $not_paid_default * ($loan->monthly_principal + $loan->monthly_interest) + $outstanding - $excess;

                $loan->default_amount = $default;
                $loan->default_amount_actual = getLoanBalance($loan);

                $_transactions = Transaction::with('user')->where('loan_id', $loan->id)->where('approved', 1)->orderByRaw('description = "Loan Taken" desc')->orderBy('created_at')->get();
                $_transactions = $_transactions->map(function ($item, $key) {
                    $item->loan = Loan::where('id', $item->loan_id)->first();

                    return $item;
                });
                $loan->transactions = $_transactions;
                $loan->repayments = PaymentDate::where('loan_id', $loan->id)->get();

                $loans = [];
                if ($loan) {
                    $loans = Loan::where('user_id', $loan->user_id)->get();
                }

                return response()->json(['loan' => $loan, 'loans' => $loans], 200);
            }
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'could not fetch loan'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required',
            'amount' => 'required',
            'monthly_deduction' => 'required',
            'tenure' => 'required',
            'status' => 'required',
            'insurance' => 'required',
            'processing_fee' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        DB::beginTransaction();

        $transaction = null;

        $user = User::find($request->get('user_id'));

        if ($user && $user->enabled == 0) {
            return response()->json(['error' => 'your account has been disabled, please contact support'], 500);
        }

        $lender = Lender::where('id', $user->lender_id)->first();

        try {
            $data = $request->all();

            $loan = Loan::where('id', $id)->first();

            info('------------ update loan 1');
            if ($loan) {
                $old_amount = $loan->amount;

                $user->office = Office::find($user->office_id);

                $monthly_commission = $lender->commission > 0 ? ($lender->commission / 100) * $data['monthly_interest'] : 0.00;

                $deduction = $data['monthly_deduction'] * $data['tenure'];
                info(json_encode($data));
                info('------------ update loan 2');

                $loan->amount = $data['amount'];
                $loan->disburse_amount = $data['disburse_amount'];
                $loan->net_earning = $data['net_earning'];
                $loan->interest_rate = $data['interest_rate'];
                $loan->monthly_deduction = $data['monthly_deduction'];
                $loan->total_deduction = round($deduction, 2);
                $loan->tenure = $data['tenure'];
                $loan->monthly_principal = $data['monthly_principal'];
                $loan->monthly_interest = $data['monthly_interest'];
                $loan->insurance = $data['insurance'];
                $loan->processing_fee = $data['processing_fee'];
                $loan->commission_percent = $lender->commission;
                $loan->interest_percent = 100 - $lender->commission;
                $loan->monthly_commission = round($monthly_commission, 2);
                $loan->total_commission = round($monthly_commission * $data['tenure'], 2);
                $loan->net_earnings = $data['net_earnings'];
                $loan->save();

                $notify = doNotify($data['user_id'], 'loans', $loan->id, 'edit_loan_request', 'success', 'last loan amount: ' . $old_amount, $user->lender_id, null);

                DB::commit();

                $loan->user = User::with('office', 'lender')->where('id', $loan->user_id)->first();
                $loan->has_topup = Loan::where('topup', $loan->id)->first();
                $loan->repayments = [];

                return response()->json(['loan' => $loan, 'transaction' => $transaction], 200);
            }

            return response()->json(['error' => 'loan not found'], 500);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not edit loan, try again later'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function updateInsurance(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();

            $loan = Loan::where('id', $id)->first();
            if (!$loan) {
                return response()->json(['error' => 'loan not found'], 500);    
            }

            $description = 'last insurance: ' . $loan->insurance . ', last processing fee: ' . $loan->processing_fee;

            $loan->insurance = $data['insurance'];
            $loan->processing_fee = $data['processing_fee'];
            $loan->save();

            DB::commit();

            $user = User::where('id', $loan->user_id)->first();
            $notify = doNotify($loan->user_id, 'loans', $loan->id, 'edit_loan_request', 'success', $description, $user->lender_id, $data['staff_id']);

            return response()->json(['loan' => $loan], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not edit loan, try again later'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function destroy(Request $request, $id)
    {
        try {
            $staff = $request->get('staff');
            $reason = $request->get('reason');

            $loan = Loan::find($id);
            $loan->deleted_by = $staff;
            $loan->decline_reason = $reason;
            $loan->audited = 3;
            $loan->audited_at = date('Y-m-d H:i:s');
            $loan->save();

            $user = User::find($loan->user_id);

            $topup = Loan::where('topup', $id)->first();
            if ($topup) {
                $topup->topup = null;
                $topup->topup_date = null;
                $topup->liquidated = 0;
                $topup->liquidated_at = null;
                $topup->liquidate_approve = 0;
                $topup->save();

                $user->loan_id = $topup->id;
                $user->save();

                $t = Transaction::where('loan_id', $topup->id)->where('status', 'Pending')->where('channel', 'loan-liquidated-topup')->delete();

                $transactions = Transaction::where('loan_id', $topup->id)->where('approved', -1)->get();
                foreach ($transactions as $item) {
                    $action = Transaction::where('id', $item->id)->first();
                    $action->approved = 0;
                    $action->save();
                }
            } else {
                $user->loan_id = null;
                $user->save();
            }

            $loan->delete();

            try {
                MerchantPay::where('loan_id', $id)->delete();

                if ($loan->admin_request == 0) {
                    $mail = (object)null;
                    $mail->from_name = 'FastCash';
                    $mail->from_email = '<EMAIL>';
                    $mail->to_name = str_replace(',', '', $user->name);
                    $mail->to_email = $user->email;
                    $mail->template = 'email.loan_declined';
                    $mail->subject = $topup ? 'Loan Topup Declined' : 'Loan Declined';
                    $mail->login = env('MAIN_URL');
                    $mail->content = $reason;
                    $mail->loan_type = $topup ? 'topup loan' : 'loan';
                    $mail->amount = number_format($loan->amount, 2);

                    $send = (new MailHandler())->sendMail($mail);
                }
            } catch (\Exception $e) {
                Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            }

            $notify = doNotify($loan->user_id, 'loans', $id, 'loan_deleted', 'success', null, $loan->lender_id, $staff);

            $details = getFinancialOverview($loan->lender_id, $loan->lender_id);

            return response()->json(['loan' => $loan, 'loan_details' => $details], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Error declining loan, try again later'], 500);
        }
    }

    private function sendOfferLetter($id, $send_mail = false, $dates = [], $monthly_deduction = 0, $is_updated=false)
    {
        $loan = Loan::where('id', $id)->first();

        if ($loan) {
            $_user = User::with('office')->where('id', $loan->user_id)->first();
            $_user->state_oo = State::find($_user->state_of_origin);
            $_user->lga_oo = Localgovt::where('serialno', $_user->lga_of_origin)->where('state_id', $_user->state_of_origin)->first();

            $last_date = array_pop($dates);
            $dates = count($dates) > 0 ? array_merge([$loan->approved_at], $dates) : [];

            $user = (object)null;
            $user->user = $_user;
            $user->loan = $loan;
            $user->acn = Accountno::where('id', $loan->accountno_id)->first();
            $user->dates = $dates;
            $user->monthly_deduction = $monthly_deduction == 0 ? $loan->monthly_deduction : $monthly_deduction;

            $loan_name = 'LoanRef' . $loan->id . '.' . time();

            $file_name = $is_updated ?  $loan_name. '-updated.pdf': $loan_name . '.pdf';
            $file_path = public_path() . '/oletter/' . $file_name;
            $pdf = PDF::loadView('pdf.offer_letter', compact('user'))->save($file_path);

            if($is_updated){
                $loan->oletter_updated = $file_name;
            } else {
                $loan->oletter = $file_name;
            }

            $loan->save();

            //chmod($file_path, 0644);

            if ($send_mail) {
                $loan_topup = Loan::where('topup', $id)->first(['id']);

                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $_user->name);
                $mail->to_email = $_user->email;
                $mail->template = 'email.offer_letter';
                $mail->amount = number_format(($loan->disburse_amount > 0 ? $loan->disburse_amount : $loan->amount), 2);
                $mail->login = env('MAIN_URL');
                $mail->subject = $loan_topup ? 'Loan Topup' : 'Loan Submitted';
                $mail->subject_text = $loan_topup ? 'topup loan' : 'loan';

                $_file = (object)null;
                $_file->name = $file_name;

                $mail->attachments = [$_file];
                $mail->uri = public_path() . '/oletter/';

                if ($loan->admin_request == 0 && $_user->email != null && $_user->email != "") {
                    $send = (new MailHandler())->sendMailAttach($mail);
                }
            }
        }

        return $loan;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function verifyLoan(Request $request, $id)
    {
        try {
            $staff = $request->get('staff');

            $loan = Loan::find($id);
            $loan->verified = 1;
            $loan->verified_by = $staff;
            $loan->verified_at = date('Y-m-d H:i:s');
            $loan->save();

            $notify = doNotify($loan->user_id, 'loans', $loan->id, 'loan_verified', 'success', null, $loan->lender_id, $staff);

            try {
                $loan = $this->sendOfferLetter($id, true);

                // $u = User::where('id', $loan->user_id)->first();
                // $message = 'Dear Customer, your loan request has been verified. Kindly check your email to accept offer and provide required documentation';
                // if ($loan->admin_request == 0) {
                //     $credit = checkSMSCredit();
                //     if ($credit != null) {
                //         $amount = str_replace(",", "", $credit);
                //         $amount = (float)$amount;
                //         if ($amount > 0 && env('APP_DEBUG') == false) {
                //             $data = sendSMS($message, $u->phone);//sent
                //         }
                //     }
                // }
            } catch (\Exception $e) {
                Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            }

            try {
                $verifier = User::find($staff);
                $approvers = DB::table('users')
                    ->join('user_roles', 'users.id', '=', 'user_roles.user_id')
                    ->where('user_roles.role_id', 3)
                    ->where('users.lender_id', $verifier->lender_id)
                    ->select('users.email', 'users.name')
                    ->get();

                $loan_topup = Loan::where('topup', $id)->first(['id']);

                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->template = 'email.admin_verified';
                $mail->login = env('ADMIN_URL');
                $mail->subject = $loan_topup ? 'Loan Topup' : 'Loan Submitted';
                $mail->subject_text = $loan_topup ? 'Topup loan' : 'Loan';
                $mail->loan_url = env('ADMIN_URL') . '/approve-loans';

                foreach ($approvers as $approver) {
                    $mail->to_name = str_replace(',', '', $approver->name);
                    $mail->to_email = $approver->email;

                    if ($loan->admin_request == 0) {
                        $send = (new MailHandler())->sendMail($mail);
                    }
                }
            } catch (\Exception $e) {
                Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            }

            $details = getFinancialOverview($loan->lender_id, $loan->lender_id);

            $loan->user = User::with('office', 'lender')->where('id', $loan->user_id)->first();

            return response()->json(['loan' => $loan, 'loan_details' => $details], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'loan not verified, try again later'], 500);
        }
    }

    /**
     * Send loan email.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function email(Request $request, $id)
    {
        try {
            $pay_dates = PaymentDate::where('loan_id', $id)->get();

            $dates = [];
            foreach ($pay_dates as $pay) {
                $dates[] = $pay->payment_date_approved;
            }

            $_loan = Loan::where('id', $id)->first();

            $is_updated = $_loan->has_consented == 1;
            $monthly_deduction = $_loan->monthly_deduction;

            $loan = $this->sendOfferLetter($id, !$is_updated, $dates, $monthly_deduction, $is_updated);

            return response()->json(compact('loan'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'loan email not sent, try again later'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function approveLoan(Request $request, $id)
    {

        $data = $request->all();
        $loan = Loan::where('id', $id)->first();

        $user = User::where('id', $loan->user_id)->first();

        try {
            if ($user && ($user->bvn == null || $user->bvn == '')) {
                return response()->json(['error' => 'BVN is required.'], 500);
            }
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'User not found.'], 500);
        }

        try {
            $balance = getBalance($loan->lender_id);
            $capital_balance = $balance - $loan->amount;
            info('lender: ' . $loan->lender_id . ', bal. start: ' . $balance . ', amt: ' . $loan->amount . ', bal end: ' . $capital_balance);

            if ($capital_balance < 0) {
                Log::error('loan: ' . $loan->id . ', no capital available');
                return response()->json(['error' => 'No investment capital available and loan could not be approved.'], 500);
            }
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'No investment capital available and loan could not be approved.'], 500);
        }

        DB::beginTransaction();

	$disburse_amount = $loan->disburse_amount > 0 ? $data['disburse_amount'] : $loan->amount;

	/**
        try {
            $disburse_amount = $loan->disburse_amount > 0 ? $data['disburse_amount'] : $loan->amount;

            $bank_account = Accountno::where('id', $loan->accountno_id)->first();
            if ($bank_account->verified == 0) {
                if (strlen($bank_account->bvn) != 11) {
                    return response()->json(['error' => 'Loan not approved. BVN must be 11 characters long.'], 500);
                }

                $body = [
                    'bvn' => $bank_account->bvn,
                    'account_number' => $bank_account->account_number,
                    'bank_code' => $bank_account->bank_code
                ];

                $check = bvnMatch($body);

                $status = $check == null ? 'failed' : ($check->status ? 'success' : $check->message);

                logAPI('stop-loan', $user->phone, $status);


                if ($check == null) {
                    return response()->json(['error' => 'Loan not approved. Please try again later.'], 500);
                }

                if ($check->status) {
                    $bank_account->verified = 1;
                    $bank_account->save();

                    $fee = 500;
                    $disburse_amount = $disburse_amount - $fee;

                    $loan->disburse_amount = $disburse_amount;

                    // Decline any loan cancel request
                    if ($loan->cancel_request != 0) {
                        $loan->cancel_request = 0;
                        $loan->cancel_decline_reason = 'Loan has been approved';
                        $loan->cancel_declined_by = $data['staff'];
                        $loan->cancel_declined_at = date('Y-m-d H:i:s');
                    }
                    $loan->save();

                    $transaction = Transaction::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'office_id' => $loan->office_id,
                        'source' => strtoupper($loan->platform),
                        'amount' => $fee,
                        'interest' => 0,
                        'principal' => 0,
                        'outst_amount' => 0,
                        'status' => 'Completed',
                        'description' => 'Account Verification',
                        'transaction_flag' => 'verify-bvn',
                        'lender_id' => $loan->lender_id,
                        'approved' => 1,
                        'approved_by' => $data['staff'],
                        'approved_at' => date('Y-m-d H:i:s'),
                    ]);

                    // withdraw from wallet
                    $w1 = Wallet::create([
                        'user_id' => $loan->user_id,
                        'category' => 'account-check',
                        'type' => 'debit',
                        'amount' => (-1 * $fee),
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $data['staff'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'paid' => 1,
                    ]);

                    // credit lender
                    $w2 = Wallet::create([
                        'user_id' => $data['staff'],
                        'category' => 'account-check',
                        'type' => 'credit',
                        'amount' => $fee,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $data['staff'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 1,
                        'paid' => 1,
                    ]);

                    $notify_tc = doNotify($transaction->user_id, 'transactions', $transaction->id, 'account_check', 'success', null, $transaction->lender_id, $data['staff']);
                } else {
                    DB::rollBack();
                    Log::error('bvn match error: ' . $check->message);
                    return response()->json(['error' => 'Loan not approved. ' . $check->message], 500);
                }
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Loan not approved. Please try again later.'], 500);
        } 
        **/

        $wasRefered = $loan->merchant_code !== null ? true : false;

        if ($wasRefered) {
            try {
                $merchant = Merchant::where('merchant_code', $loan->merchant_code)->first();
                $lender = Lender::where('id', $merchant->lender_id)->first();

                $transaction = Transaction::create([
                    'user_id' => $merchant->user_id,
                    'loan_id' => $loan->id,
                    'office_id' => $loan->office_id,
                    'source' => strtoupper($loan->platform),
                    'amount' => $lender->merchant_commission,
                    'interest' => 0,
                    'principal' => 0,
                    'outst_amount' => 0,
                    'status' => 'Completed',
                    'description' => 'Loan Referral Commission',
                    'transaction_flag' => 'commission',
                    'lender_id' => $loan->lender_id,
                    'approved' => 1,
                    'approved_by' => $data['staff'],
                    'approved_at' => date('Y-m-d H:i:s'),
                ]);

                // credit merchant
                $w2 = Wallet::create([
                    'user_id' => $merchant->user_id,
                    'category' => 'loan-referral-commission',
                    'loan_id' => $loan->id,
                    'type' => 'credit',
                    'amount' => $lender->merchant_commission,
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $data['staff'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $loan->lender_id,
                    'is_lender' => 1,
                    'paid' => 1,
                ]);

                $notify_tc = doNotify($transaction->user_id, 'transactions', $transaction->id, 'loan_referral_commission', 'success', null, $transaction->lender_id, $data['staff']);

            } catch (\Exception $e) {
                DB::rollBack();
                Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                return response()->json(['error' => 'Loan not approved. Please try again later.'], 500);
            }
        }

        try {
            $start_date = Carbon::now()->endOfMonth()->addDays(1)->startOfMonth()->format('Y-m-d H:i:s');
            $end_date = Carbon::now()->addMonths($loan->tenure)->endOfMonth()->format('Y-m-d H:i:s');

            $approved_date = date('Y-m-d H:i:s');

            $loan->disburse_amount = $disburse_amount;
            $loan->approved = 1;
            $loan->approved_by = $data['staff'];
            $loan->approved_at = $approved_date;
            $loan->status = 1;
            $loan->start_date = $start_date;
            $loan->end_date = $end_date;
            $loan->topup_date = getTopupDate(date('Y-m-d H:i:s'), $loan->tenure);
            $loan->save();

            $notify = doNotify($loan->user_id, 'loans', $loan->id, 'loan_approved', 'success', null, $loan->lender_id, $data['staff']);

            $_start_date = Carbon::createFromFormat('Y-m-d H:i:s', $start_date);
            $_end_date = Carbon::createFromFormat('Y-m-d H:i:s', $end_date);

            $dates = monthsBtwDates($_start_date, $_end_date, true);
            info(json_encode($dates));

            // generate start date from approved date
            $app_start_date = Carbon::createFromFormat('Y-m-d H:i:s', $approved_date);
            $app_end_date = Carbon::createFromFormat('Y-m-d H:i:s', $approved_date)->addMonths($loan->tenure);

            $app_end = Carbon::createFromFormat('Y-m-d H:i:s', $approved_date)->addMonths($loan->tenure + 1);
            $app_dates = monthsBtwDates($app_start_date, $app_end, false);
            info(json_encode($app_dates));

            for ($i = 0; $i < count($dates); $i++) {
                $pay_schedule = PaymentDate::create([
                    'user_id' => $loan->user_id,
                    'loan_id' => $loan->id,
                    'start_date' => $loan->start_date,
                    'end_date' => $loan->end_date,
                    'tenure' => $loan->tenure,
                    'monthly_deduction' => $loan->monthly_deduction,
                    'payment_date' => $dates[$i],

                    'start_date_approved' => $app_start_date->format('Y-m-d H:i:s'),
                    'end_date_approved' => $app_end_date->format('Y-m-d H:i:s'),
                    'payment_date_approved' => isset($app_dates[$i]) ? $app_dates[$i] : Carbon::createFromFormat('Y-m-d H:i:s', $app_dates[$i - 1])->addMonths(1)->format('Y-m-d H:i:s'),
                ]);
            }

            $l = $this->sendOfferLetter($loan->id, false, $app_dates, $loan->monthly_deduction);

            $ln = null;
            try {
                if ($data['platform'] == 'remita') {
                    $ln = (new Remita())->sendLoanNotification($data['merchantId'], $data['apiKey'], $data['requestId'], $data['authorization'], $data['body']);

                    $status = $ln == null ? 'failed' : ($ln->responseCode == '00' ? 'success' : $ln->responseMsg);

                    logAPI('loan-notification', $user->phone, $status);

                    if ($ln != null) {
                        if ($ln->responseCode != '00') {
                            DB::rollBack();
                            Log::error('LoanController.php Line 1195 loan notification:-> ' . json_encode($ln));
                            return response()->json(['error' => 'could not approve loan. please try again later.'], 500);
                        }
                    } else {
                        DB::rollBack();
                        return response()->json(['error' => 'loan could not approved. please try again later.'], 500);
                    }
                }
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                return response()->json(['error' => 'Loan not approved. Please try again later.'], 500);
            }

            if ($data['platform'] == 'remita') {
                $loan->approved_remita = json_encode($ln);
            }
            $loan->save();

            $loan_topup = Loan::where('topup', $id)->first(['id']);
            if ($loan_topup) {
                $liq_transaction = Transaction::where('loan_id', $loan_topup->id)->where('channel', 'loan-liquidated-topup')->first();

                $liq_transaction->amount = $data['loan_balance'];
                $liq_transaction->principal = $data['loan_balance'] - $liq_transaction->interest;
                $liq_transaction->save();
            }

            $consent_otp = randomPassword(6, 2);
                
            $loan->consent_otp = $consent_otp;
            $loan->consent_otp_expire_at = Carbon::now()->addDays(1)->format('Y-m-d H:i:s');
            $loan->save();

            DB::commit();

            $loan = Loan::where('id', $id)->first();
            $consent_template = Setting::where('lender_id', 1)->where('name', 'consent_template')->first();

            $this->sendApprovedEmail($user, $loan, $loan_topup, $consent_otp, $consent_template->value);

            $details = getFinancialOverview($loan->lender_id, $loan->lender_id);

            return response()->json(['loan' => $loan, 'loan_details' => $details], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Loan not approved. Please try again later.'], 500);
        }
    }

    public function sendConsentSMS($id)
    {
        try {
            $consent_otp = randomPassword(6, 2);
            $consent_template = Setting::where('lender_id', 1)->where('name', 'consent_template')->first();
            
            $loan = Loan::find($id);
            $loan->consent_otp = $consent_otp;
            $loan->consent_otp_expire_at = Carbon::now()->addDays(1)->format('Y-m-d H:i:s');
            $loan->save();
            
            $user = User::where('id', $loan->user_id)->first();
            $loan_topup = Loan::where('topup', $id)->first(['id']);
            
            $this->sendApprovedEmail($user, $loan, $loan_topup, $consent_otp, $consent_template->value);

            return response()->json(['message' => 'Consent OTP SMS sent'], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Could not send sms'], 422);
        }
    }

    private function sendApprovedEmail($user, $loan, $loan_topup, $consent_otp, $template)
    {
        $link = env('MAIN_URL').'/consent-loan?p='.$user->id;
        $message = Str::of($template)->replace('{link}', $link)->replace('{otp}', $consent_otp);
        info($message);
        
        try {
            $credit = checkSMSCredit();
            info('sms credit: '.$credit);
            if ($credit != null) {
                $amount = str_replace(",", "", $credit);
                $amount = (float)$amount;
                if ($amount > 0 && env('APP_DEBUG') == false) {
                    $data = sendSMS($message, $user->phone);//sent
                    info('sms sent: '.$data);
                }
            }
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
        }

        try {
            $mail = (object)null;
            $mail->from_name = 'FastCash';
            $mail->from_email = '<EMAIL>';
            $mail->to_name = str_replace(',', '', $user->name);
            $mail->to_email = $user->email;
            $mail->login = env('MAIN_URL');
            $mail->subject = $loan_topup ? 'Loan Topup Approved' : 'Loan Approved';
            $mail->loan_type = $loan_topup ? 'topup loan' : 'loan';
            $mail->template = 'email.loan_consent';
            $mail->amount = number_format(($loan->disburse_amount > 0 ? $loan->disburse_amount : $loan->amount), 2);
            $mail->message = Str::of($template)->replace('{link}', '<a href="'.$link.'">'.$link.'</a> or click the button below')->replace('{otp}', $consent_otp);
            $mail->consent_url = $link;

            $send = (new MailHandler())->sendMail($mail);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
        }
    }

    public function grantConsent(Request $request)
    {
        try {
            $data = $request->all();

            $user = User::where('id', $data['phone_number'])->first();
            if(!$user){
                return response()->json(['error' => 'User not found'], 422);
            }

            $loan = Loan::where('user_id', $user->id)->where('disbursed', 0)->first();
            if(!$loan){
                return response()->json(['error' => 'No loan request found'], 422);
            }

            if ($loan->has_consented == 1){
                return response()->json(['error' => 'You have already given consent to this loan application'], 422);
            }

            $expired = Carbon::now()->diffInMinutes(Carbon::parse($loan->consent_otp_expire_at), false);
            if ($expired <= 0){
                return response()->json(['error' => 'OTP has expired please contact support'], 422);
            }

            if($data['otp'] != $loan->consent_otp) {
                return response()->json(['error' => 'Invalid OTP please contact support'], 422);
            }

            $loan->consent_otp = null;
            $loan->consent_otp_expire_at = null;
            $loan->has_consented = 1;
            $loan->consented_at = Carbon::now()->format('Y-m-d H:i:s');
            $loan->save();

            $pay_dates = PaymentDate::where('loan_id', $loan->id)->get();

            $dates = [];
            foreach ($pay_dates as $pay) {
                $dates[] = $pay->payment_date_approved;
            }

            $l = $this->sendOfferLetter($loan->id, false, $dates, $loan->monthly_deduction, true);

            return response()->json(['message' => 'Consent granted'], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Error occured while trying to grant consent'], 422);
        }
    }

    public function loanPhone(Request $request)
    {
        try {
            $data = $request->all();

            $user = User::where('id', $data['phone_number'])->first();
            if(!$user){
                return response()->json(['error' => 'User not found'], 422);
            }

            $loan = Loan::where('user_id', $user->id)->where('disbursed', 0)->first(['id', 'amount', 'tenure']);
            if(!$loan){
                return response()->json(['error' => 'No loan found'], 422);
            }

            return response()->json(['loan' => $loan], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Error occured while trying to find loan'], 422);
        }
    }

    public function disburseLoan(Request $request, $id)
    {
        $data = $request->all();

        try {
            $df = (new Remita())->disburseFund($data['paymentMerchantId'], $data['paymentApiKey'], $data['requestId'], $data['requestTS'], $data['apiPaymentHash'], $data['dfBody']);
            // info(json_encode($df));

            if ($df) {
                $loan = Loan::find($id);
                $loan->disbursement = json_encode($df);
                $loan->save();

                $transRef = $df->data->transRef;

                $details = getFinancialOverview($loan->lender_id, $loan->lender_id);

                $loan->user = User::with('office', 'lender')->where('id', $loan->user_id)->first();

                return response()->json(['loan' => $loan, 'transRef' => $transRef, 'loan_details' => $details], 200);
            }

            return response()->json(['error' => 'Funds could not be disbursed'], 422);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Funds could not be disbursed'], 422);
        }
    }

    public function verifyPayment(Request $request, $id)
    {
        $data = $request->all();

        try {
            $vp = (new Remita())->verifyPayment($data['paymentMerchantId'], $data['paymentApiKey'], $data['requestId'], $data['requestTS'], $data['apiPaymentHash'], $data['dfBody']);

            if ($vp) {
                $loan = Loan::find($id);
                $loan->payment_status = json_encode($vp);
                $loan->disbursed = 1;
                $loan->disbursed_at = date('Y-m-d H:i:s');
                $loan->disbursed_by = $data['staff'];
                $loan->save();

                try {
                    $pay_dates = PaymentDate::where('loan_id', $loan->id)->count();
                    if ($pay_dates == 0) {
                        $start_date = Carbon::now()->endOfMonth()->addDays(1)->startOfMonth()->format('Y-m-d H:i:s');
                        $end_date = Carbon::now()->addMonths($loan->tenure)->endOfMonth()->format('Y-m-d H:i:s');

                        $loan->start_date = $start_date;
                        $loan->end_date = $end_date;
                        $loan->topup_date = getTopupDate(date('Y-m-d H:i:s'), $loan->tenure);
                        $loan->save();

                        $approved_date = $loan->approved_at;

                        $_start_date = Carbon::createFromFormat('Y-m-d H:i:s', $start_date);
                        $_end_date = Carbon::createFromFormat('Y-m-d H:i:s', $end_date);

                        $dates = monthsBtwDates($_start_date, $_end_date, true);

                        // generate start date from approved date
                        $app_start_date = Carbon::createFromFormat('Y-m-d H:i:s', $approved_date);
                        $app_end_date = Carbon::createFromFormat('Y-m-d H:i:s', $approved_date)->addMonths($loan->tenure);

                        $app_end = Carbon::createFromFormat('Y-m-d H:i:s', $approved_date)->addMonths($loan->tenure + 1);
                        $app_dates = monthsBtwDates($app_start_date, $app_end, false);
                        info(json_encode($app_dates));

                        for ($i = 0; $i < count($dates); $i++) {
                            $schedule = PaymentDate::create([
                                'user_id' => $loan->user_id,
                                'loan_id' => $loan->id,
                                'start_date' => $loan->start_date,
                                'end_date' => $loan->end_date,
                                'tenure' => $loan->tenure,
                                'monthly_deduction' => $loan->monthly_deduction,
                                'payment_date' => $dates[$i],

                                'start_date_approved' => $app_start_date->format('Y-m-d H:i:s'),
                                'end_date_approved' => $app_end_date->format('Y-m-d H:i:s'),
                                'payment_date_approved' => $app_dates[$i],
                            ]);
                        }
                    }

                    $old_loan = Loan::where('topup', $loan->id)->first();
                    if ($old_loan) {
                        $__loan = completeLiquidate($old_loan, $data['staff']);
                    }

                    $category = $old_loan ? 'topup_loan' : 'loan';

                    $transaction = Transaction::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'office_id' => $loan->office_id,
                        'schedule_id' => null,
                        'source' => strtoupper($loan->platform),
                        'amount' => $loan->total_deduction,
                        'interest' => $loan->monthly_interest * $loan->tenure,
                        'principal' => $loan->amount,
                        'outst_amount' => $loan->total_deduction,
                        'status' => 'Completed',
                        'description' => 'Loan Taken',
                        'transaction_flag' => 'loan',
                        'lender_id' => $loan->lender_id,
                        'approved' => 1,
                        'approved_by' => $loan->approved_by,
                        'approved_at' => date('Y-m-d H:i:s'),
                    ]);

                    // credit user wallet
                    Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => $category,
                        'type' => 'credit',
                        'amount' => $loan->amount,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $data['staff'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'paid' => 1,
                    ]);

                    // withdraw from wallet to user account
                    $amount = $loan->disburse_amount > 0 ? $loan->disburse_amount : $loan->amount;
                    Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => $category,
                        'type' => 'debit',
                        'amount' => (-1 * $amount),
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $data['staff'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'paid' => 1,
                    ]);

                    // debit from lender
                    Wallet::create([
                        'user_id' => null,
                        'loan_id' => $loan->id,
                        'category' => $category,
                        'type' => 'debit',
                        'amount' => (-1 * $amount),
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $data['staff'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 1,
                        'paid' => 1,
                    ]);

                    $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'loan_disbursed', 'success', null, $loan->lender_id, $data['staff']);

                    $u = User::where('id', $loan->user_id)->first();
                    $message = 'Dear Customer, your Loan request has been approved and disbursed to your Bank account. Kindly call ‭***********‬ or visit fastcash.ng if credit is not received within the next 24 hrs';
                    if ($loan->admin_request == 0) {
                        $credit = checkSMSCredit();
                        if ($credit != null) {
                            $amount = str_replace(",", "", $credit);
                            $amount = (float)$amount;
                            if ($amount > 0 && env('APP_DEBUG') == false) {
                                $data = sendSMS($message, $u->phone);//sent
                            }
                        }
                    }
                } catch (\Exception $e) {
                    Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                }

                $details = getFinancialOverview($loan->lender_id, $loan->lender_id);

                $loan->user = User::with('office', 'lender')->where('id', $loan->user_id)->first();

                return response()->json(['loan' => $loan, 'loan_details' => $details], 200);
            }

            return response()->json(['error' => 'Payment not verified'], 422);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Payment not verified'], 422);
        }
    }

    public function bypassPayment(Request $request, $id)
    {
        $data = $request->all();
        DB::beginTransaction();
        try {
            $res = (object)null;
            $res->status = "success";

            $loan = Loan::find($id);
            $loan->disbursement = json_encode($res);
            $loan->payment_status = json_encode($res);
            $loan->disbursed = 1;
            $loan->disbursed_at = Carbon::now()->format('Y-m-d H:i:s');
            $loan->disbursed_by = $data['staff'];
            $loan->save();

            try {
                $pay_dates = PaymentDate::where('loan_id', $loan->id)->count();
                if ($pay_dates == 0) {
                    $start_date = Carbon::now()->endOfMonth()->addDays(1)->startOfMonth()->format('Y-m-d H:i:s');
                    $end_date = Carbon::now()->addMonths($loan->tenure)->endOfMonth()->format('Y-m-d H:i:s');

                    $loan->start_date = $start_date;
                    $loan->end_date = $end_date;
                    $loan->topup_date = getTopupDate(date('Y-m-d H:i:s'), $loan->tenure);
                    $loan->save();

                    $approved_date = $loan->approved_at;

                    $_start_date = Carbon::createFromFormat('Y-m-d H:i:s', $start_date);
                    $_end_date = Carbon::createFromFormat('Y-m-d H:i:s', $end_date);

                    $dates = monthsBtwDates($_start_date, $_end_date, true);

                    // generate start date from approved date
                    $app_start_date = Carbon::createFromFormat('Y-m-d H:i:s', $approved_date);
                    $app_end_date = Carbon::createFromFormat('Y-m-d H:i:s', $approved_date)->addMonths($loan->tenure);

                    $app_end = Carbon::createFromFormat('Y-m-d H:i:s', $approved_date)->addMonths($loan->tenure + 1);
                    $app_dates = monthsBtwDates($app_start_date, $app_end, false);
                    info(json_encode($app_dates));

                    for ($i = 0; $i < count($dates); $i++) {
                        $schedule = PaymentDate::create([
                            'user_id' => $loan->user_id,
                            'loan_id' => $loan->id,
                            'start_date' => $loan->start_date,
                            'end_date' => $loan->end_date,
                            'tenure' => $loan->tenure,
                            'monthly_deduction' => $loan->monthly_deduction,
                            'payment_date' => $dates[$i],

                            'start_date_approved' => $app_start_date->format('Y-m-d H:i:s'),
                            'end_date_approved' => $app_end_date->format('Y-m-d H:i:s'),
                            'payment_date_approved' => $app_dates[$i],
                        ]);
                    }
                }

                $old_loan = Loan::where('topup', $loan->id)->first();
                if ($old_loan) {
                    $__loan = completeLiquidate($old_loan, $data['staff']);

                    if ($old_loan->platform == 'remita') {
                        $merchantId = env('MERCHANT_ID');
                        $apiKey = env('API_KEY');
                        $apiToken = env('API_TOKEN');

                        $requestId = time() * 1000;
                        $apiHash = hash('sha512', $apiKey . $requestId . $apiToken);
                        $authorization = "remitaConsumerKey=" . $apiKey . ", remitaConsumerToken=" . $apiHash;
                        $approved = json_decode($old_loan->approved_remita);

                        $body = [
                            "authorisationCode" => $old_loan->auth_code,
                            "customerId" => $old_loan->user->customer_id,
                            "mandateReference" => $approved->data->mandateReference,
                        ];

                        $sl = (new Remita())->stopLoanCollection($merchantId, $apiKey, $requestId, $authorization, $body);

                        $status = $sl == null ? 'failed' : ($sl->responseCode == '00' ? 'success' : $sl->responseMsg);

                        logAPI('stop-loan', $old_loan->user->phone, $status);

                        if ($sl != null) {
                            if ($sl->responseCode != '00') {
                                Log::error('LoanController.php Line 5138 stop loan:-> ' . json_encode($sl));
                            }
                        }
                    }
                }

                $category = $old_loan ? 'topup_loan' : 'loan';

                $transaction = Transaction::create([
                    'user_id' => $loan->user_id,
                    'loan_id' => $loan->id,
                    'office_id' => $loan->office_id,
                    'schedule_id' => null,
                    'source' => strtoupper($loan->platform),
                    'amount' => $loan->total_deduction,
                    'interest' => $loan->monthly_interest * $loan->tenure,
                    'principal' => $loan->amount,
                    'outst_amount' => $loan->total_deduction,
                    'status' => 'Completed',
                    'description' => 'Loan Taken',
                    'transaction_flag' => 'loan',
                    'lender_id' => $loan->lender_id,
                    'approved' => 1,
                    'approved_by' => $data['staff'],
                    'approved_at' => date('Y-m-d H:i:s'),
                ]);

                // credit user wallet
                Wallet::create([
                    'user_id' => $loan->user_id,
                    'loan_id' => $loan->id,
                    'category' => $category,
                    'type' => 'credit',
                    'amount' => $loan->amount,
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $data['staff'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $loan->lender_id,
                    'is_lender' => 0,
                    'paid' => 1,
                ]);

                // withdraw from wallet to user bank account
                $amount = $loan->disburse_amount > 0 ? $loan->disburse_amount : $loan->amount;
                Wallet::create([
                    'user_id' => $loan->user_id,
                    'loan_id' => $loan->id,
                    'category' => $category,
                    'type' => 'debit',
                    'amount' => (-1 * $amount),
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $data['staff'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $loan->lender_id,
                    'is_lender' => 0,
                    'paid' => 1,
                ]);

                // debit from lender
                Wallet::create([
                    'user_id' => null,
                    'loan_id' => $loan->id,
                    'category' => $category,
                    'type' => 'debit',
                    'amount' => (-1 * $amount),
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $data['staff'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $loan->lender_id,
                    'is_lender' => 1,
                    'paid' => 1,
                ]);

                $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'loan_disbursed', 'success', null, $loan->lender_id, $data['staff']);

                DB::commit();

                $u = User::where('id', $loan->user_id)->first();
                $loan_type = $old_loan ? 'topup loan' : 'loan';
                $message = 'Dear Customer, your ' . $loan_type . ' request has been approved and disbursed to your Bank account. Kindly call *********** or visit fastcash.ng if credit is not received within the next 24 hrs';
                if ($loan->admin_request == 0) {
                    $credit = checkSMSCredit();
                    if ($credit != null) {
                        $amount = str_replace(",", "", $credit);
                        $amount = (float)$amount;
                        if ($amount > 0 && env('APP_DEBUG') == false) {
                            $data = sendSMS($message, $u->phone);//sent
                        }
                    }
                }
            } catch (\Exception $e) {
                DB::rollBack();
                Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                return response()->json(['error' => 'Payment failed'], 422);
            }

            $details = getFinancialOverview($loan->lender_id, $loan->lender_id);

            $loan->user = User::with('office', 'lender')->where('id', $loan->user_id)->first();

            return response()->json(['loan' => $loan, 'loan_details' => $details], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Payment failed'], 422);
        }
    }

    /**
     * Start liquidation for card loans.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function startLiquidateLoan(Request $request, $id)
    {
        DB::beginTransaction();
        $input = $request->all();
        try {
            $loan = Loan::where('id', $id)->where('disbursed', 1)->first();

            if ($loan) {
                if ($loan->liquidated == 1) {
                    info('loan:' . $loan->id . ' already liquidated and awaiting approval.');
                    return response()->json(['error' => 'loan already liquidated and awaiting approval.'], 500);
                }

                $loan->liquidated = 1;
                $loan->liquidate_approve = 0;
                $loan->liquidated_at = date('Y-m-d H:i:s');
                $loan->save();

                $channel = $input['payment_type'] == 'card' ? 'flutterwave' : $input['payment_type'];

                $transaction = Transaction::where('loan_id', $loan->id)->where('status', 'Incomplete')->where('approved', 0)->first();

                if (!$transaction) {
                    $transaction = Transaction::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'office_id' => $loan->office_id,
                        'source' => strtoupper($loan->platform),
                        'amount' => $input['amount'],
                        'interest' => $loan->monthly_interest,
                        'principal' => $input['amount'] - $loan->monthly_interest,
                        'outst_amount' => 0,
                        'status' => 'Incomplete',
                        'description' => 'Loan Liquidated',
                        'transaction_flag' => 'liquidate',
                        'lender_id' => $loan->lender_id,
                        'payment_type' => $input['payment_type'],
                        'reference' => null,
                        'channel' => $channel,
                        'approved' => 0,
                        'repayment_date' => date('Y-m-d'),
                        'repayment_source' => $input['payment_type'],
                    ]);

                    $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'start_loan_liquidated', 'success', null, $loan->lender_id);
                }

                DB::commit();

                return response()->json(compact('transaction'), 200);
            }

            DB::rollBack();

            return response()->json(['error' => 'loan not found, try again later'], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'loan liquidation failed, try again later'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function liquidateLoan(Request $request, $id)
    {
        DB::beginTransaction();
        $input = $request->all();
        try {
            $loan = Loan::where('id', $id)->where('disbursed', 1)->first();

            if ($loan) {
                $checkLiqTransaction = Transaction::where('loan_id', $loan->id)->where('status', 'Pending')->where('transaction_flag', 'liquidate')->where('approved', 0)->first();
                if ($checkLiqTransaction) {
                    info('loan:' . $loan->id . ' already liquidated and awaiting approval.');
                    return response()->json(['error' => 'loan already liquidated and awaiting approval.'], 500);
                }

                $enc = null;
                if (isset($input['data'])) {
                    $enc = isset($input['enc']) && $input['enc'] == '1' ? base64_decode($input['data']) : $input['data'];
                }

                $info = null;
                if ($input['payment_type'] == 'card') {
                    try {
                        $info = Rave::verifyTransaction($input['reference']);
                    } catch (\Exception $e) {
                        Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getTraceAsString());
                        return response()->json(['error' => 'payment failed'], 422);
                    }
                }

                $transaction = null;

                if (isset($input['trans_id'])) {
                    $transaction = Transaction::where('id', $input['trans_id'])->where('loan_id', $loan->id)->where('status', 'Incomplete')->where('approved', 0)->first();
                } else {
                    $transaction = Transaction::where('loan_id', $loan->id)->where('status', 'Incomplete')->where('approved', 0)->first();
                }

                if (($input['payment_type'] == 'card' && $info && $info->status == 'success') || $input['payment_type'] == 'bank' || $input['payment_type'] == 'admin') {
                    $loan->transaction_data = $enc != null ? json_encode($enc) : null;  // data from payment
                    $loan->liquidated = 1;
                    $loan->liquidate_approve = 0;
                    $loan->liquidated_at = date('Y-m-d H:i:s');
                    $loan->save();

                    $channel = $input['payment_type'] == 'card' ? 'flutterwave' : $input['payment_type'];

                    if ($transaction) {
                        $transaction->source = strtoupper($loan->platform);
                        $transaction->status = 'Pending';
                        $transaction->payment_type = $input['payment_type'];
                        $transaction->reference = $info != null ? json_encode($info->data) : null;
                        $transaction->repayment_source = $input['payment_type'];
                        $transaction->save();
                    } else {
                        $transaction = Transaction::create([
                            'user_id' => $loan->user_id,
                            'loan_id' => $loan->id,
                            'office_id' => $loan->office_id,
                            'source' => strtoupper($loan->platform),
                            'amount' => $input['amount'],
                            'interest' => $loan->monthly_interest,
                            'principal' => $input['amount'] - $loan->monthly_interest,
                            'outst_amount' => 0,
                            'status' => 'Pending',
                            'description' => 'Loan Liquidated',
                            'transaction_flag' => 'liquidate',
                            'lender_id' => $loan->lender_id,
                            'payment_type' => $input['payment_type'],
                            'reference' => $info != null ? json_encode($info->data) : null,
                            'channel' => $channel,
                            'approved' => 0,
                            'repayment_date' => date('Y-m-d'),
                            'repayment_source' => $input['payment_type'],
                        ]);
                    }

                    $staff = isset($input['staff_id']) ? $input['staff_id'] : null;
                    $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'loan_liquidated', 'success', null, $loan->lender_id, $staff);

                    DB::commit();

                    $user = User::where('id', $loan->user_id)->first();

                    if ($input['payment_type'] != 'admin') {
                        try {
                            $mail = (object)null;
                            $mail->from_name = 'FastCash';
                            $mail->from_email = '<EMAIL>';
                            $mail->to_name = str_replace(',', '', $user->name);
                            $mail->to_email = $user->email;
                            $mail->login = env('MAIN_URL');
                            $mail->subject = 'Loan Liquidate';
                            $mail->amount = number_format($input['amount'], 2);

                            if ($input['payment_type'] == 'bank') {
                                $mail->template = 'email.liquidate_bank';
                            } else {
                                $mail->template = 'email.liquidate_card';
                            }

                            if ($loan->admin_request == 0) {
                                $send = (new MailHandler())->sendMail($mail);
                            }
                        } catch (\Exception $e) {
                            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                        }
                    }

                    $transaction->user = User::find($loan->user_id);
                    $transaction->loan = Loan::where('id', $loan->id)->first();

                    return response()->json(['loan' => $loan, 'transaction' => $transaction], 200);
                }
            }

            return response()->json(['error' => 'loan not found, try again later'], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'loan liquidation failed, try again later'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function liquidateApprove(Request $request, $id)
    {
        $input = $request->all();
        DB::beginTransaction();
        try {
            $transaction = Transaction::where('id', $id)->first();
            if ($transaction) {
                $loan = Loan::where('id', $transaction->loan_id)->first();

                $bypass = $input['bypass_remita'];
                $initial_transaction_amount = $transaction->amount;
                $transaction_amount = $input['amount'];
                $excess_deduction = $input['excess_deduction'];

                $user_lender = User::where('lender_id', $transaction->lender_id)->whereNull('ippis')->whereNotNull('username')->first();

                if ($loan) {
                    $sl = null;

                    if ($loan->platform == 'remita' && $bypass == 0) {
                        $merchantId = env('MERCHANT_ID');
                        $apiKey = env('API_KEY');
                        $apiToken = env('API_TOKEN');

                        $requestId = time() * 1000;
                        $apiHash = hash('sha512', $apiKey . $requestId . $apiToken);
                        $authorization = "remitaConsumerKey=" . $apiKey . ", remitaConsumerToken=" . $apiHash;
                        $approved = json_decode($loan->approved_remita);

                        $body = [
                            "authorisationCode" => $loan->auth_code,
                            "customerId" => $loan->user->customer_id,
                            "mandateReference" => $approved->data->mandateReference,
                        ];

                        $sl = (new Remita())->stopLoanCollection($merchantId, $apiKey, $requestId, $authorization, $body);

                        $status = $sl == null ? 'failed' : ($sl->responseCode == '00' ? 'success' : $sl->responseMsg);

                        logAPI('stop-loan', $loan->user->phone, $status);

                        if ($sl != null) {
                            if ($sl->responseCode != '00') {
                                Log::error('LoanController.php Line 1897 stop loan:-> ' . json_encode($sl));
                                return response()->json(['error' => 'Remita stop loan notification failed.'], 500);
                            }
                        } else {
                            return response()->json(['error' => 'Remita stop loan notification failed.'], 500);
                        }
                    }

                    $loan->transaction_tx = $sl != null ? json_encode($sl) : null;   // stop loan
                    $loan->liquidate_approve = 1;
                    $loan->liquidate_approve_at = date('Y-m-d H:i:s');
                    $loan->liquidate_approve_by = $input['user_id'];
                    $loan->save();

                    $transaction->amount = $transaction_amount;
                    $transaction->amount_paid = $initial_transaction_amount;
                    $transaction->approved = 1;
                    $transaction->status = 'Completed';
                    $transaction->approved_at = date('Y-m-d H:i:s');
                    $transaction->approved_by = $input['user_id'];
                    $transaction->save();

                    $user = User::where('id', $loan->user_id)->first();
                    $user->loan_id = null;
                    $user->save();

                    $notify = doNotify($loan->user_id, 'loans', $loan->id, 'loan_liquidate_approved', 'success', null, $loan->lender_id, $input['user_id']);

                    $pd = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)
                        ->update(['paid' => 1, 'paid_at' => date('Y-m-d H:i:s'), 'transaction_id' => $id]);

                    // pay money into wallet
                    $wallet1 = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'liquidate',
                        'type' => 'credit',
                        'amount' => $transaction_amount,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $input['user_id'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'paid' => 1,
                    ]);

                    // debit user wallet
                    $wallet2 = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'liquidate',
                        'type' => 'debit',
                        'amount' => $transaction_amount * -1,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $input['user_id'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'paid' => 1,
                    ]);

                    // credit lender
                    $wallet3 = Wallet::create([
                        'user_id' => $user_lender ? $user_lender->id : $input['user_id'],
                        'loan_id' => $loan->id,
                        'category' => 'liquidate',
                        'type' => 'credit',
                        'amount' => $transaction_amount,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $input['user_id'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 1,
                        'paid' => 1,
                    ]);

                    if ($excess_deduction > 0) {
                        // post excess to wallet
                        $repay_deduction = Transaction::create([
                            'user_id' => $loan->user_id,
                            'loan_id' => $loan->id,
                            'office_id' => $loan->office_id,
                            'lender_id' => $loan->lender_id,
                            'source' => strtoupper($loan->platform),
                            'amount' => $excess_deduction,
                            'status' => 'Pending',
                            'reference' => null,
                            'channel' => 'credit-wallet-repayment',
                            'approved' => -1,
                            'transaction_flag' => 'credit-wallet',
                            'repayment_source' => $transaction->repayment_source,
                            'uploaded_by' => $input['user_id'],
                            'repayment_date' => date('Y-m-d H:i:s'),
                            'principal' => $loan->monthly_principal,
                            'interest' => $loan->monthly_interest,
                            'outst_amount' => 0.00,
                        ]);

                        $notify = doNotify($repay_deduction->user_id, 'transactions', $repay_deduction->id, 'repay_deduction_transaction', 'success', null, $repay_deduction->lender_id, $input['user_id']);
                    }

                    DB::commit();

                    try {
                        $mail = (object)null;
                        $mail->from_name = 'FastCash';
                        $mail->from_email = '<EMAIL>';
                        $mail->to_name = str_replace(',', '', $user->name);
                        $mail->to_email = $user->email;
                        $mail->login = env('MAIN_URL');
                        $mail->subject = 'Loan Liquidated';
                        $mail->template = 'email.loan_liquidated';

                        if ($loan->admin_request == 0) {
                            $mail = (new MailHandler())->sendMail($mail);
                        }
                    } catch (\Exception $e) {
                        Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                    }

                    $loan->user = User::with('office', 'lender')->where('id', $loan->user_id)->first();

                    $details = getFinancialOverview($loan->lender_id, $loan->lender_id);

                    // all liquidated loans
                    $unliquidated_loans = Loan::where('liquidated', 1)->where('liquidate_approve', 0)->count();

                    return response()->json(['loan' => $loan, 'loan_details' => $details, 'unliquidated_loans' => $unliquidated_loans], 200);
                }
            }

            return response()->json(['error' => 'transaction or loan not found'], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'loan liquidate approval failed, try again later'], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function liquidateDecline(Request $request, $id)
    {
        $input = $request->all();
        try {
            $transaction = Transaction::where('id', $id)->first();

            if ($transaction) {
                $loan = Loan::withTrashed()->where('id', $transaction->loan_id)->first();

                if ($loan) {
                    if ($loan->liquidated == 1 && $loan->liquidate_approve == 0 && $loan->disbursed == 1) {
                        $loan->transaction_data = null;  // data from payment
                        $loan->transaction_tx = null;   // verify transaction
                        $loan->liquidated = 0;
                        $loan->liquidate_approve = 0;
                        $loan->liquidated_at = null;
                        $loan->save();
                    }

                    if ($loan->disbursed == 1) {
                        $transactions = Transaction::where('loan_id', $loan->id)->where('approved', -1)->get();
                        foreach ($transactions as $item) {
                            $action = Transaction::where('id', $item->id)->first();
                            $action->approved = 0;
                            $action->save();
                        }
                    }

                    $notify = doNotify($loan->user_id, 'loans', $loan->id, 'liquidate_declined', 'success', null, $loan->lender_id, $input['user_id']);

                    Transaction::where('id', $id)->delete();

                    DB::commit();

                    $loan->user = User::with('office', 'lender')->where('id', $loan->user_id)->first();

                    $details = getFinancialOverview($loan->lender_id, $loan->lender_id);

                    // all liquidated loans
                    $unliquidated_loans = Loan::where('liquidated', 1)->where('liquidate_approve', 0)->count();

                    return response()->json(['loan' => $loan, 'loan_details' => $details, 'unliquidated_loans' => $unliquidated_loans], 200);
                }
            }

            return response()->json(['error' => 'loan liquidate approval failed, loan not found'], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'loan liquidate approval failed, try again later'], 500);
        }
    }

    public function loanHistory(Request $request)
    {
        try {
            $remita = $request->all();

            $admin = $request->has('role_category') && $request->input('role_category') == 'super' && $request->has('fetch') && $request->input('fetch') == 'search';

            // info('admin, ' . $admin);

            $search_method = $remita['search_method'] ?? '';

            if ($search_method == 'phone') {
                $remita_result = fetchRemitaHistoryPhone($remita['phone'], $admin);
                
                $lh = $remita_result ? $remita_result->data : null;
            } else if ($search_method == 'account_number') {
                $remita_result = fetchRemitaHistoryAccount($remita['account_number'], $remita['bank_code'], $admin);

                // info(json_encode($remita_result));
                $lh = $remita_result ? $remita_result->data : null;
            } else {
                $lh = null;

                $remita_result = (object)null;
                $remita_result->errorMessage = 'Could not get loan history';
            }

            if ($lh == null) {
                // Log::warning(json_encode($remita_result));
                return response()->json(['error' => $remita_result->errorMessage], 500);
            }

            if ($request->has('fetch') && $request->input('fetch') == 'customer_id') {
                $loan = Loan::where('id', $remita['loan_id'])->first();

                if (!$loan) {
                    return response()->json(['error' => 'Network error, loan not found.'], 500);
                }

                $not_paid = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)->count();

                $result = (object)null;
                $result->authCode = $remita_result->auth_code;
                $result->customerId = $lh->data->customerId;
                $result->loan_amount = $loan->monthly_principal * $not_paid;
                $result->total_deduction = ($loan->monthly_principal + $loan->monthly_interest) * $not_paid;
                $result->loan_tenure = $not_paid;
                $result->remitaId = $remita_result->remita_id;

                return response()->json(compact('result'), 200);
            }

            if ($request->has('fetch') && $request->input('fetch') == 'search') {
                return response()->json(['result' => $lh->data], 200);
            }

            $salaries = ($lh) ? $lh->data->salaryPaymentDetails : [];
            $customerId = $lh ? $lh->data->customerId : null;

            if ($customerId) {
                foreach ($salaries as $earning) {
                    $d = explode('+', $earning->paymentDate);

                    $month = Carbon::parse($d[0])->format('n');
                    $year = Carbon::parse($d[0])->format('Y');

                    $earn = Earning::where('customer_id', $customerId)->where('month', $month)->where('year', $year)->where('net_earning', $earning->amount)->where('platform', 'remita')->first();

                    if (!$earn) {
                        $earn = Earning::create([
                            'customer_id' => $customerId,
                            'month' => $month,
                            'year' => $year,
                            'net_earning' => $earning->amount,
                            'platform' => 'remita',
                            'auth_code' => $remita_result->auth_code,
                        ]);
                    }
                }

                $earnings = DB::table('earnings')
                    ->select(DB::raw('DISTINCT customer_id, month, year, net_earning, platform, gross_earning, auth_code, created_at'))
                    ->where('customer_id', $customerId)->where('platform', 'remita')->where('net_earning', '!=', '0.00')
                    ->orderBy('year', 'desc')->orderBy('month', 'desc')
                    ->take(12)->get();

                return response()->json(['earnings' => $earnings, 'remita_id' => $remita_result->remita_id], 200);
            }

            return response()->json(['earnings' => []], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Network error, please try again later'], 500);
        }
    }

    public function loanHistoryRequest(Request $request)
    {
        try {
            $remita = $request->all();

            $admin = $request->has('role_category') && $request->input('role_category') == 'super' && $request->has('fetch') && $request->input('fetch') == 'search';

            // info('admin, ' . $admin);

            $live = ($request->live ?? '') == '1' || $admin == true;

            $remita_result = fetchRemitaHistoryPhone($remita['phone'], $live);

            // info(json_encode($remita_result));
            $lh = $remita_result ? $remita_result->data : null;

            if ($lh == null) {
                $user = User::where('phone', $remita['phone'])->first();
                if ($user == null) {
                    return response()->json(['error' => 'Could not find user'], 500);
                }

                $bank_account = Accountno::where('user_id', $user->id)->where('status', 'primary')->first();
                if ($bank_account == null) {
                    return response()->json(['error' => 'Could not find user'], 500);
                }

                $remita_result = fetchRemitaHistoryAccount($bank_account->account_number, $bank_account->bank_code, $live);

                // info(json_encode($remita_result));
                $lh = $remita_result ? $remita_result->data : null;
            }

            if ($lh == null) {
                // Log::warning(json_encode($remita_result));
                return response()->json(['error' => $remita_result->errorMessage ?? 'Could not get loan history'], 500);
            }

            if ($request->has('fetch') && $request->input('fetch') == 'customer_id') {
                $loan = Loan::where('id', $remita['loan_id'])->first();

                if (!$loan) {
                    return response()->json(['error' => 'Network error, loan not found.'], 500);
                }

                $not_paid = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)->count();

                $result = (object)null;
                $result->authCode = $remita_result->auth_code;
                $result->customerId = $lh->data->customerId;
                $result->loan_amount = $loan->monthly_principal * $not_paid;
                $result->total_deduction = ($loan->monthly_principal + $loan->monthly_interest) * $not_paid;
                $result->loan_tenure = $not_paid;
                $result->remitaId = $remita_result->remita_id;

                return response()->json(compact('result'), 200);
            }

            if ($request->has('fetch') && $request->input('fetch') == 'search') {
                return response()->json(['result' => $lh->data], 200);
            }

            $salaries = ($lh) ? $lh->data->salaryPaymentDetails : [];
            $customerId = $lh ? $lh->data->customerId : null;

            if ($customerId) {
                foreach ($salaries as $earning) {
                    $d = explode('+', $earning->paymentDate);

                    $month = Carbon::parse($d[0])->format('n');
                    $year = Carbon::parse($d[0])->format('Y');

                    $earn = Earning::where('customer_id', $customerId)->where('month', $month)->where('year', $year)->where('net_earning', $earning->amount)->where('platform', 'remita')->first();

                    if (!$earn) {
                        $earn = Earning::create([
                            'customer_id' => $customerId,
                            'month' => $month,
                            'year' => $year,
                            'net_earning' => $earning->amount,
                            'platform' => 'remita',
                            'auth_code' => $remita_result->auth_code,
                        ]);
                    }
                }

                $earnings = DB::table('earnings')
                    ->select(DB::raw('DISTINCT customer_id, month, year, net_earning, platform, gross_earning, auth_code, created_at'))
                    ->where('customer_id', $customerId)->where('platform', 'remita')->where('net_earning', '!=', '0.00')
                    ->orderBy('year', 'desc')->orderBy('month', 'desc')
                    ->take(12)->get();

                return response()->json(['earnings' => $earnings, 'remita_id' => $remita_result->remita_id], 200);
            }

            return response()->json(['earnings' => []], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Network error, please try again later'], 500);
        }
    }

    public function getLoanBalance(Request $request, $id)
    {
        try {
            $is_topup = $request->has('is_topup') && $request->input('is_topup') == 1 ? 1 : 0;

            $loan = Loan::where('id', $id)->first();

            if ($loan) {
                $amount = getLoanBalance($loan);

                $disburse_amount = 0;

                if ($is_topup == 1) {
                    $topup_loan = Loan::where('topup', $loan->id)->first();

                    $amount = getLoanBalance($topup_loan);
                    $disburse_amount = $loan->amount - $amount;
                }

                $result = (object)null;
                $result->amount = $amount;
                $result->disburse_amount = $disburse_amount;

                return response()->json(compact('result'), 200);
            }

            return response()->json(['error' => 'you dont have a running loan'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Network error, please try again later'], 500);
        }
    }

    public function export(Request $request, $slug)
    {
        try {
            switch ($slug) {
                case 'approved':
                    $loans = Loan::with('office', 'accountno')->where('verified', 1)->where('approved', 1)->where('disbursed', 0)->get();

                    $data = [];

                    $i = 0;
                    foreach ($loans as $loan) {
                        $i++;

                        $user = User::where('id', $loan->user_id)->first(['ippis', 'name', 'phone', 'office_id', 'employer', 'customer_id']);
                        $office = Office::where('id', $user->office_id)->first(['name']);
                        $bank = Accountno::where('id', $loan->accountno_id)->first();

                        if ($office) {
                            $office_name = $office->name;
                            if ($office->name == 'Remita') {
                                $office_name = $user->employer;
                            }
                        } else {
                            $office_name = $user->employer;
                        }

                        $transaction = Transaction::where('loan_id', $loan->id)->where('description', 'Account Verification')->first();
                        $lender = Lender::where('id', $loan->lender_id)->first();

                        $approved = json_decode($loan->approved_remita);
                        $mandate_ref = $approved == null ? '-' : $approved->data->mandateReference;

                        $item = (object)null;
                        $item->sn = $i;
                        $item->ippis = $user->ippis;
                        $item->remita_customer_id = $user->customer_id;
                        $item->name = $user->name;
                        $item->lender = $lender->name;
                        $item->office = $office_name;
                        $item->platform = $loan->platform;
                        $item->type = $loan->is_topup === 1 ? 'topup' : 'new';
                        $item->loan_amount = $loan->amount;
                        $item->repayment = $loan->monthly_deduction;
                        $item->transfer_amount = $loan->disburse_amount > 0 ? $loan->disburse_amount : $loan->amount;
                        $item->bvn_fees = $transaction ? 500.00 : (($loan->amount - $loan->disburse_amount) == 500 ? 500.00 : 0.00);
                        $item->tenure = $loan->tenure . ' Months';
                        $item->date_of_application = Carbon::parse($loan->created_at)->format('Y-m-d');
                        $item->phone = $user->phone;
                        $item->mandate_reference = $mandate_ref;
                        $item->bank_name = $bank->bank_name;
                        $item->account_number = $bank->account_number;
                        $item->bvn = $bank->bvn;
                        $item->admin_requested_loan = $loan->admin_request == 0 ? 'No' : 'Yes';
                        $item->loan_application_method = ($loan->method ?? '') != '' ? $loan->method : 'Web';
                        $item->acceptance_method = $loan->has_consented == 1 ? 'OTP' : '-';
                        $item->acceptance_date = $loan->has_consented == 1 ? Carbon::createFromFormat('Y-m-d H:i:s', $loan->consented_at)->format('d-M-Y h:ia') : '-';

                        $data[] = $item;
                    }

                    $allData = json_decode(json_encode($data), true);

                    $report = [];
                    foreach ($allData as $item) {
                        $new_report = [];
                        foreach ($item as $key => $value) {
                            $new_report[ucwords(str_replace("_", " ", $key))] = $value;
                            unset($new_report[$key]);
                        }
                        $report[] = $new_report;
                    }

                    if (count($report) == 0) {
                        return response()->json(['message' => 'no data available'], 200);
                    }

                    $headings = getHeadings($report[0]);

                    $filename = 'approved-loans-' . time();
                    $filepath = 'reports/' . $filename . '.xlsx';

                    Excel::store(new ExportLoan($report, $headings), $filepath, 'public');

                    $file = env('APP_URL') . '/reports/' . $filename . '.xlsx';
                    return response()->json(compact('file'), 200);

                    break;
                default:
                    $user_id = $slug;
                    $loan_id = $request->input('loan_id');
                    
                    if ($loan_id != '' && $loan_id != null) {
                        $loans = Loan::withTrashed()->where('id', $loan_id)->get();
                    } else {
                        $loans = Loan::where('user_id', $user_id)->withTrashed()->orderBy('created_at', 'desc')->get();
                    }

                    if (count($loans) > 0) {
                        $user = User::where('id', $user_id)->first();
                        $office = Office::where('id', $user->office_id)->first();

                        $mail = (object)null;
                        $mail->from_name = 'FastCash';
                        $mail->from_email = '<EMAIL>';
                        $mail->to_name = str_replace(',', '', $user->name);
                        $mail->to_email = $user->email;
                        $mail->template = 'email.loan_history';
                        $mail->login = env('MAIN_URL');
                        $mail->subject = 'Loan History';

                        $loans = $loans->map(function ($item, $key) {
                            $item->transactions = Transaction::with('user')->where('loan_id', $item->id)->where('approved', 1)->orderByRaw('description = "Loan Taken" desc')->orderBy('created_at')->get();

                            return $item;
                        });

                        $history = (object)null;
                        $history->loans = $loans;
                        $history->user = $user;
                        $history->office = $office;

                        $file_name = 'LoanHistory-' . time() . '.pdf';
                        $file_path = public_path() . '/history/' . $file_name;
                        $pdf = PDF::loadView('pdf.loan_history', compact('history'))->save($file_path);

                        if ($request->has('download') && ($request->input('download') == 1 || $request->input('download') == '1')) {
                            $file = env('APP_URL') . '/history/' . $file_name;
                            return response()->json(compact('file'), 200);
                        }

                        $file = (object)null;
                        $file->name = $file_name;

                        $mail->attachments = [$file];
                        $mail->uri = public_path() . '/history/';

                        $send = (new MailHandler())->sendMailAttach($mail);

                        return response()->json(compact('send'), 200);
                    }

                    return response()->json(['result' => 'could not export history, no loans found'], 200);
                    break;
            }
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Network error, could not export file'], 500);
        }
    }

    /**
     * Transfer ongoing ippis loan to remita.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function transferLoan(Request $request, $id)
    {
        $data = $request->all();

        $ln = null;

        try {
            $loan = Loan::where('id', $id)->first();

            $user = User::where('id', $loan->user_id)->first();

            if ($data['platform'] == 'remita') {
                $ln = (new Remita())->sendLoanNotification($data['merchantId'], $data['apiKey'], $data['requestId'], $data['authorization'], $data['body']);

                $status = $ln == null ? 'failed' : ($ln->responseCode == '00' ? 'success' : $ln->responseMsg);

                logAPI('loan-notification', $user->phone, $status);

                if ($ln != null) {
                    if ($ln->responseCode != '00') {
                        Log::error('LoanController.php Line 1708 loan notification:-> ' . json_encode($ln) . ' --- ' . json_encode($data['body']));
                        return response()->json(['error' => 'could not transfer loan. please try again later.'], 500);
                    }
                } else {
                    return response()->json(['error' => 'loan could not transferred. please try again later.'], 500);
                }
            }
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Loan not transferred. Please try again later.'], 500);
        }

        try {
            $staff = $data['staff'];

            $loan->approved_remita = json_encode($ln);
            $loan->platform = 'remita';
            $loan->auth_code = $data['auth_code'];
            $loan->remita_user_id = $data['remita_id'];
            $loan->save();

            $user->customer_id = $data['customer_id'];
            $user->save();

            $notify = doNotify($loan->user_id, 'loans', $loan->id, 'loan_transferred_remita', 'success', null, $loan->lender_id, $staff);

            $details = getFinancialOverview($loan->lender_id, $loan->lender_id);

            return response()->json(['loan' => $loan, 'loan_details' => $details], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Loan not transferred. Please try again later.'], 500);
        }
    }

    /**
     * Enable/cancel multi funding.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function multiFund(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $staff = $data['staff'];

            $loan = Loan::where('id', $id)->first();
            $settings = Setting::where('name', 'multifund_expire')->first();

            if ($data['status'] == 1) {
                $loan->is_multifund = 1;
                $loan->cancel_multifund = 0;
                $loan->is_multifund_at = date('Y-m-d H:i:s');
                $loan->is_multifund_by = $staff;
                $loan->multifund_expire_at = Carbon::now()->addDays($settings->value)->format('Y-m-d H:i:s');
                $loan->cancel_multifund_at = null;
                $loan->cancel_multifund_by = null;
                $loan->save();
            } else {
                $loan->cancel_multifund = 1;
                $loan->cancel_multifund_at = date('Y-m-d H:i:s');
                $loan->cancel_multifund_by = $staff;
                $loan->save();

                $multi_funds = MultiFund::where('loan_id', $id)->get();
                foreach ($multi_funds as $fund) {
                    MultiFundTransaction::where('id', $fund->mf_transaction_id)->delete();
                }

                MultiFund::where('loan_id', $id)->delete();
            }

            $funding = $data['status'] == 1 ? 'enable_multi_funding' : 'cancel_multi_funding';

            $notify = doNotify($loan->user_id, 'loans', $loan->id, $funding, 'success', null, $loan->lender_id, $staff);
            DB::commit();

            $details = getFinancialOverview($loan->lender_id, $loan->lender_id);

            return response()->json(['loan' => $loan, 'loan_details' => $details], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Loan not transferred. Please try again later.'], 500);
        }
    }

    /**
     * Fetch loan analytics.
     *
     * @param Request $request
     * @param int $lender_id
     * @return Response
     */
    public function loanAnalytics($lender_id, $main_lender)
    {
        try {
            if ($lender_id == 0) {
                $analytics = getOverallFinancialOverview($main_lender);
            } else {
                $analytics = getFinancialOverview($lender_id, $main_lender);
            }

            return response()->json(['analytics' => $analytics], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Could not fetch analytics.'], 500);
        }
    }

    /**
     * loan audit passed.
     *
     * @param Request $request
     * @param int $lender_id
     * @return Response
     */
    public function auditPassed(Request $request, $id)
    {
        try {
            $data = $request->all();

            $loan = Loan::withTrashed()->where('id', $id)->first();
            $loan->audited = $data['audited'];
            $loan->audited_at = date('Y-m-d H:i:s');
            $loan->save();

            return response()->json(compact('loan'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'could not pass audit.'], 500);
        }
    }

    /**
     * loan regenerate wallet.
     *
     * @param Request $request
     * @param int $lender_id
     * @return Response
     */
    public function regenerateWallet(Request $request, $id)
    {
        // DB::beginTransaction();
        // try {
        //     $data = $request->all();

        //     $loan = Loan::where('id', $id)->first();
        //     $loan->audited = $data['audited'];
        //     $loan->audited_at = date('Y-m-d H:i:s');
        //     $loan->save();

        //     $staff = 1;
        //     $transactions = $data['transactions'];
        //     info(json_encode($data['transactions']));

        //     $transaction = Transaction::where('approved', 1)->where('loan_id', $loan->id)->where('transaction_flag', 'loan')->first();

        //     if ($transaction) {
        //         // credit user wallet
        //         $credit_user = Wallet::create([
        //             'user_id' => $transaction->user_id,
        //             'loan_id' => $transaction->loan_id,
        //             'category' => 'loan',
        //             'amount' => $transaction->principal,
        //             'type' => 'credit',
        //             'lender_id' => $transaction->lender_id,
        //             'approved' => 1,
        //             'approved_at' => date('Y-m-d H:i:s'),
        //             'approved_by' => $staff,
        //             'transaction_id' => $transaction->id,
        //             'is_lender' => 0,
        //         ]);

        //         // withdraw from wallet to users bank
        //         $debit_user = Wallet::where('loan_id', $transaction->loan_id)->where('type', 'debit')->where('is_lender', 0)->first();
        //         if ($debit_user) {
        //             $debit_user->transaction_id = $transaction->id;
        //             $debit_user->approved = 1;
        //             $debit_user->approved_at = date('Y-m-d H:i:s');
        //             $debit_user->approved_by = $staff;
        //             $debit_user->save();
        //         }

        //         // debit lender
        //         $debit_lender = Wallet::create([
        //             'user_id' => null,
        //             'loan_id' => $transaction->loan_id,
        //             'category' => 'loan',
        //             'amount' => $transaction->principal * -1,
        //             'type' => 'debit',
        //             'lender_id' => $transaction->lender_id,
        //             'approved' => 1,
        //             'approved_at' => date('Y-m-d H:i:s'),
        //             'approved_by' => $staff,
        //             'transaction_id' => $transaction->id,
        //             'is_lender' => 1,
        //         ]);
        //     } else {
        //         $_transaction = Transaction::create([
        //             'user_id' => $loan->user_id,
        //             'loan_id' => $loan->id,
        //             'office_id' => $loan->office_id,
        //             'schedule_id' => null,
        //             'source' => strtoupper($loan->platform),
        //             'amount' => $loan->total_deduction,
        //             'interest' => $loan->monthly_interest * $loan->tenure,
        //             'principal' => $loan->amount,
        //             'outst_amount' => $loan->total_deduction,
        //             'status' => 'Completed',
        //             'description' => 'Loan Taken',
        // 'transaction_flag' => 'loan',
        //             'lender_id' => $loan->lender_id,
        //             'approved' => 1,
        //             'approved_by' => $staff,
        //             'approved_at' => date('Y-m-d H:i:s'),
        //         ]);

        //         // credit user wallet
        //         Wallet::create([
        //             'user_id' => $loan->user_id,
        //             'loan_id' => $loan->id,
        //             'category' => 'loan',
        //             'type' => 'credit',
        //             'amount' => $loan->amount,
        //             'transaction_id' => $_transaction->id,
        //             'approved' => 1,
        //             'approved_by' => $staff,
        //             'approved_at' => date('Y-m-d H:i:s'),
        //             'lender_id' => $loan->lender_id,
        //             'is_lender' => 0,
        //         ]);

        //         // withdraw from wallet to user bank account
        //         Wallet::create([
        //             'user_id' => $loan->user_id,
        //             'loan_id' => $loan->id,
        //             'category' => 'loan',
        //             'type' => 'debit',
        //             'amount' => (-1 * $loan->amount),
        //             'transaction_id' => $_transaction->id,
        //             'approved' => 1,
        //             'approved_by' => $staff,
        //             'approved_at' => date('Y-m-d H:i:s'),
        //             'lender_id' => $loan->lender_id,
        //             'is_lender' => 0,
        //         ]);

        //         // debit from lender
        //         Wallet::create([
        //             'user_id' => null,
        //             'loan_id' => $loan->id,
        //             'category' => 'loan',
        //             'type' => 'debit',
        //             'amount' => (-1 * $loan->amount),
        //             'transaction_id' => $_transaction->id,
        //             'approved' => 1,
        //             'approved_by' => $staff,
        //             'approved_at' => date('Y-m-d H:i:s'),
        //             'lender_id' => $loan->lender_id,
        //             'is_lender' => 1,
        //         ]);

        //         $notify1 = doNotify($_transaction->user_id, 'loans', $_transaction->loan_id, 'loan_request', 'success', null, $_transaction->lender_id, null);

        //         $notify2 = doNotify($_transaction->user_id, 'transactions', $_transaction->id, 'loan_disbursed', 'success', null, $loan->lender_id, $staff);
        //     }

        //     foreach ($transactions as $item) {
        //         $t_repay = Transaction::where('id', $item)->where('approved', 1)->where('loan_id', $loan->id)->where('transaction_flag', 'deduction')->first();

        //         if ($t_repay) {
        //             // deposit repayment into wallet
        //             $credit_user1 = Wallet::where('loan_id', $t_repay->loan_id)->where('transaction_id', $t_repay->id)->where('type', 'credit')->where('amount', $t_repay->amount)->where('is_lender', 0)->first();

        //             info('repay: ' . json_encode($credit_user1));
        //             if ($credit_user1) {
        //                 $credit_user1->approved = 1;
        //                 $credit_user1->approved_at = date('Y-m-d H:i:s');
        //                 $credit_user1->approved_by = $staff;
        //                 $credit_user1->save();
        //             } else {
        //                 $deposit1 = Wallet::create([
        //                     'user_id' => $t_repay->user_id,
        //                     'loan_id' => $t_repay->loan_id,
        //                     'category' => 'repay_loan',
        //                     'type' => 'credit',
        //                     'amount' => $t_repay->amount,
        //                     'transaction_id' => $t_repay->id,
        //                     'approved' => 1,
        //                     'approved_by' => $staff,
        //                     'approved_at' => date('Y-m-d H:i:s'),
        //                     'lender_id' => $t_repay->lender_id,
        //                     'is_lender' => 0,
        //                 ]);
        //                 info('new repay: ' . json_encode($deposit1));
        //             }

        //             // debit user wallet to repay loan
        //             $wallet1 = Wallet::create([
        //                 'user_id' => $t_repay->user_id,
        //                 'loan_id' => $t_repay->loan_id,
        //                 'category' => 'repay_loan',
        //                 'type' => 'debit',
        //                 'amount' => $t_repay->amount * -1,
        //                 'transaction_id' => $t_repay->id,
        //                 'approved' => 1,
        //                 'approved_by' => $staff,
        //                 'approved_at' => date('Y-m-d H:i:s'),
        //                 'lender_id' => $t_repay->lender_id,
        //                 'is_lender' => 0,
        //             ]);

        //             // credit lender
        //             $wallet2 = Wallet::create([
        //                 'user_id' => null,
        //                 'loan_id' => $t_repay->loan_id,
        //                 'category' => 'repay_loan',
        //                 'type' => 'credit',
        //                 'amount' => $t_repay->amount,
        //                 'transaction_id' => $t_repay->id,
        //                 'approved' => 1,
        //                 'approved_by' => $staff,
        //                 'approved_at' => date('Y-m-d H:i:s'),
        //                 'lender_id' => $t_repay->lender_id,
        //                 'is_lender' => 1,
        //             ]);
        //         }

        //         $t_liquidated = Transaction::where('id', $item)->where('approved', 1)->where('loan_id', $loan->id)->where('transaction_flag', 'liquidate')->first();

        //         if ($t_liquidated) {
        //             // deposit liquidate amount into wallet
        //             $credit_user2 = Wallet::where('loan_id', $t_liquidated->loan_id)->where('transaction_id', $t_liquidated->id)->where('type', 'credit')->where('amount', $t_liquidated->amount)->where('is_lender', 0)->first();

        //             info('liq: ' . json_encode($credit_user2));
        //             if ($credit_user2) {
        //                 $credit_user2->category = 'liquidate';
        //                 $credit_user2->approved = 1;
        //                 $credit_user2->approved_at = date('Y-m-d H:i:s');
        //                 $credit_user2->approved_by = $staff;
        //                 $credit_user2->save();
        //             } else {
        //                 $deposit2 = Wallet::create([
        //                     'user_id' => $t_liquidated->user_id,
        //                     'loan_id' => $t_liquidated->loan_id,
        //                     'category' => 'liquidate',
        //                     'type' => 'credit',
        //                     'amount' => $t_liquidated->amount,
        //                     'transaction_id' => $t_liquidated->id,
        //                     'approved' => 1,
        //                     'approved_by' => $staff,
        //                     'approved_at' => date('Y-m-d H:i:s'),
        //                     'lender_id' => $t_liquidated->lender_id,
        //                     'is_lender' => 0,
        //                 ]);
        //                 info('new liq: ' . json_encode($deposit2));
        //             }

        //             // debit user wallet to liquidate loan
        //             $wallet1 = Wallet::create([
        //                 'user_id' => $t_liquidated->user_id,
        //                 'loan_id' => $t_liquidated->loan_id,
        //                 'category' => 'liquidate',
        //                 'type' => 'debit',
        //                 'amount' => $t_liquidated->amount * -1,
        //                 'transaction_id' => $t_liquidated->id,
        //                 'approved' => 1,
        //                 'approved_by' => $staff,
        //                 'approved_at' => date('Y-m-d H:i:s'),
        //                 'lender_id' => $t_liquidated->lender_id,
        //                 'is_lender' => 0,
        //             ]);

        //             // credit lender
        //             $wallet2 = Wallet::create([
        //                 'user_id' => null,
        //                 'loan_id' => $t_liquidated->loan_id,
        //                 'category' => 'liquidate',
        //                 'type' => 'credit',
        //                 'amount' => $t_liquidated->amount,
        //                 'transaction_id' => $t_liquidated->id,
        //                 'approved' => 1,
        //                 'approved_by' => $staff,
        //                 'approved_at' => date('Y-m-d H:i:s'),
        //                 'lender_id' => $t_liquidated->lender_id,
        //                 'is_lender' => 1,
        //             ]);
        //         }
        //     }

        //     DB::commit();

        //     return response()->json(compact('loan'), 200);
        // } catch (\Exception $e) {
        //     DB::rollBack();
        //     Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
        //     return response()->json(['error' => 'could not regenerate wallet.'], 500);
        // }
    }

    /**
     * loan audited.
     *
     * @param Request $request
     * @param int $lender_id
     * @return Response
     */
    public function audited(Request $request, $id)
    {
        try {
            $data = $request->all();

            $loan = Loan::withTrashed()->where('id', $id)->first();
            $loan->audited = $data['audited'];
            $loan->audited_at = date('Y-m-d H:i:s');
            $loan->save();

            return response()->json(compact('loan'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'could not audit loan.'], 500);
        }
    }

    /**
     * sell loans by transfer.
     *
     * @param Request $request
     * @param int $lender_id
     * @return Response
     */
    public function transferLoans(Request $request)
    {
        DB::beginTransaction();
        try {
            $input = $request->all();

            $loans = [];

            $new_lender = Lender::where('id', $input['lender_id'])->first(['id', 'name']);

            $balance = getBalance($new_lender->id);
            if ($input['amount'] > $balance) {
                Log::error('insufficient balance: ' . $balance . ', amount: ' . $input['amount']);
                return response()->json(['error' => 'insufficient funds.'], 500);
            }

            foreach ($input['loans'] as $item) {
                $loan = Loan::where('id', $item)->first();
                $old_lender = $loan->lender_id;

                $user = User::where('id', $loan->user_id)->first();

                $office = Office::where('id', $user->office_id)->first();

                $new_office = Office::where('name', $office->name)->where('lender_id', $new_lender->id)->first();
                if (!$new_office) {
                    $new_office = Office::create([
                        'name' => $office->name,
                        'lender_id' => $new_lender->id,
                    ]);
                }

                $user->lender_id = $new_lender->id;
                $user->office_id = $new_office->id;
                $user->save();

                if ($loan) {
                    $loan->lender_id = $new_lender->id;
                    $loan->office_id = $new_office->id;
                    $loan->save();

                    $upd_notify = Notify::where('category', 'loans')->where('action_id', 1)->where('user_id', $user->id)->where('category_id', $loan->id)->update(['lender_id' => $new_lender->id]);

                    $upd_wallets = Wallet::where('loan_id', $loan->id)->update(['lender_id' => $new_lender->id]);

                    $upd_transactions = Transaction::where('loan_id', $loan->id)->update(['lender_id' => $new_lender->id]);
                }

                $notify = doNotify(null, 'loans', $loan->id, 'transfer_loan', 'success', 'old lender:' . $old_lender, $user->lender_id, $input['staff_id']);

                $loans[] = $loan->id;
            }

            $transaction1 = Transaction::create([
                'user_id' => $input['staff_id'],
                'amount' => $input['amount'],
                'interest' => 0.00,
                'principal' => 0.00,
                'outst_amount' => 0.00,
                'status' => 'Completed',
                'description' => 'Loans Sold',
                'transaction_flag' => 'loans-sold',
                'lender_id' => $input['super_lender'],
                'approved' => 1,
                'approved_by' => $input['staff_id'],
                'approved_at' => date('Y-m-d H:i:s'),
            ]);

            $notify1 = doNotify(null, 'transactions', $transaction1->id, 'sell_loans', 'success', json_encode($loans), $input['super_lender'], $input['staff_id']);

            $lender_user = DB::table('users')
                ->join('user_roles', 'users.id', '=', 'user_roles.user_id')
                ->where('users.lender_id', $input['lender_id'])->where('user_roles.role_id', 7)
                ->select('users.id')
                ->first();

            $transaction2 = Transaction::create([
                'user_id' => $lender_user->id,
                'amount' => $input['amount'],
                'interest' => 0.00,
                'principal' => 0.00,
                'outst_amount' => 0.00,
                'status' => 'Completed',
                'description' => 'Loans Bought',
                'transaction_flag' => 'loans-bought',
                'lender_id' => $input['lender_id'],
                'approved' => 1,
                'approved_by' => $input['staff_id'],
                'approved_at' => date('Y-m-d H:i:s'),
            ]);

            $notify2 = doNotify(null, 'transactions', $transaction2->id, 'buy_loans', 'success', json_encode($loans), $input['lender_id'], $input['staff_id']);

            DB::commit();

            info('loans sold:' . json_encode($loans));

            return response()->json(compact('loans'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'could not transfer loans.'], 500);
        }
    }

    /**
     * get duplicate mandate references.
     *
     * @param Request $request
     * @param int $lender_id
     * @return Response
     */
    public function getDuplicateMandates(Request $request)
    {
        try {
            $mandates = [];

            $loans = Loan::where('platform', 'remita')->groupBy('auth_code')->get(['auth_code']);
            foreach ($loans as $l) {
                if ($l->auth_code) {
                    $_loan = Loan::where('auth_code', $l->auth_code)->first();

                    $approved = json_decode($_loan->approved_remita);
                    if ($approved != null) {
                        $mandate_ref = $approved->data->mandateReference;

                        $count = Loan::where('user_id', $_loan->user_id)->where('approved_remita', 'LIKE', '%' . $mandate_ref . '%')->count();
                        if ($count > 1) {
                            $mandates[] = $_loan;
                        }
                    }
                }
            }

            $data = [];
            $i = 0;
            foreach ($mandates as $mandate) {
                $i++;

                $user = User::where('id', $mandate->user_id)->first();
                $lender = Lender::where('id', $user->lender_id)->first();

                $item = (object)null;
                $item->sn = $i;
                $item->name = $user->name;
                $item->ippis = $user->ippis;
                $item->phone = $user->phone;
                $item->lender = $lender->name;

                $data[] = $item;
            }

            $allData = json_decode(json_encode($data), true);
            $report = [];
            foreach ($allData as $item) {
                $new_report = [];
                foreach ($item as $key => $value) {
                    $new_report[ucwords(str_replace("_", " ", $key))] = $value;
                    unset($new_report[$key]);
                }
                $report[] = $new_report;
            }

            if (count($report) == 0) {
                return response()->json(['message' => 'no data available'], 200);
            }

            $headings = getHeadings($report[0]);

            $filename = time();
            $filepath = 'reports/' . $filename . '.xlsx';

            Excel::store(new ExportLoan($report, $headings), $filepath, 'public');

            $file = env('APP_URL') . '/reports/' . $filename . '.xlsx';

            return response()->json(['file' => $file], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json($e, 500);
        }
    }

    /**
     * check duplicate auth code.
     *
     * @param Request $request
     * @param $code
     * @return Response
     */
    public function authCodeCheck(Request $request, $code, $id)
    {
        try {
            $auth_code = $code;

            $loan = Loan::where('id', $id)->first();

            $checks = Loan::where('auth_code', $code)->where('user_id', $loan->user_id)->count();

            if ($checks > 1) {
                $user = User::where('id', $loan->user_id)->first();

                $result = fetchRemitaHistoryPhone($user->phone, true);

                $lh = $result ? $result->data : null;

                if ($lh == null) {
                    $bank_account = Accountno::where('user_id', $user->id)->where('status', 'primary')->first();
                    if ($bank_account == null) {
                        return response()->json(['error' => 'Could not find user'], 500);
                    }

                    $result = fetchRemitaHistoryAccount($bank_account->account_number, $bank_account->bank_code, true);
                    $lh = $result ? $result->data : null;
                }

                if ($lh != null && $lh->responseCode == '00') {
                    $auth_code = $result->auth_code;

                    $loan->auth_code = $auth_code;
                    $loan->save();

                    $remita_data = RemitaUser::updateOrCreate(
                        ['phone' => $user->phone],
                        ['data' => json_encode($lh), 'auth_code' => $auth_code, 'user_id' => $user ? $user->id : null]
                    );
                }
            }

            return response()->json(compact('auth_code'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'could not approve loan.'], 500);
        }
    }


    /**
     * Create a request to cancel a loan request.
     *
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function cancelLoan(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'reason' => ['required'],
        ], [
            'reason.required' => 'Please select the reason for cancellation'
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        DB::beginTransaction();

        try {
            $data = $request->all();

            $loan = Loan::where('id', $id)->first();
            if (!$loan) {
                return response()->json(['error' => 'Loan does not exist'], 500);
            }

            $loan->cancel_request = -1;
            $loan->cancel_reason = $data['reason'];
            $loan->cancel_request_at = date('Y-m-d H:i:s');
            $loan->save();

            $loan->user = User::with('office', 'lender')->where('id', $loan->user_id)->first();
            $loan->has_topup = Loan::where('topup', $loan->id)->first();
            $loan->repayments = [];

            DB::commit();

            $notify = doNotify($loan->user_id, 'loans', $loan->id, 'loan_cancel_request', 'success', null, $loan->lender_id, null);

            $user = User::where('id', $loan->user_id)->first(['id', 'name', 'email', 'phone']);

            try {
                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->subject = 'Loan Cancellation Request';
                $mail->to_name = str_replace(',', '', $user->name);
                $mail->to_email = $user->email;
                $mail->login = env('MAIN_URL');
                $mail->template = 'email.loan_cancel_request';
                $mail->to_user = true;

                $send = (new MailHandler())->sendMail($mail);

                $admin_mail = $mail;
                $admin_mail->to_name = 'FastCash Support';
                $admin_mail->to_email = '<EMAIL>';
                $admin_mail->login = '';
                $admin_mail->to_user = false;
                $admin_mail->user_name = str_replace(',', '', $user->name);
                $admin_mail->user_phone = $user->phone;

                $admin_send = (new MailHandler())->sendMail($admin_mail);
            } catch (\Exception $e) {
                Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            }

            return response()->json(compact('loan'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not submit loan cancel request, try again later'], 500);
        }
    }


    /**
     * Approve loan cancellation requests.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function approveCancelLoan(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();

            $loan = Loan::where('id', $id)->first();
            if (!$loan) {
                return response()->json(['message' => "Loan doesn't exist"]);
            }

            $loan_amount = $loan->amount;
            $monthly_deduction = $loan->monthly_deduction;
            $loan_tenure = $loan->tenure;

            $loan->cancel_request = 1;
            $loan->cancelled_at = date('Y-m-d H:i:s');
            $loan->cancelled_by = $data['staff_id'];
            $loan->deleted_by = $data['staff_id'];
            $loan->save();

            $notify = doNotify($loan->user_id, 'loans', $loan->id, 'loan_cancel_approved', 'success', null, $loan->lender_id, $data['staff_id']);

            $user = User::where('id', $loan->user_id)->first();

            $topup = Loan::where('topup', $id)->first();
            if ($topup) {
                $topup->topup = null;
                $topup->topup_date = null;
                $topup->liquidated = 0;
                $topup->liquidated_at = null;
                $topup->liquidate_approve = 0;
                $topup->save();

                $user->loan_id = $topup->id;
                $user->save();

                $t = Transaction::where('loan_id', $topup->id)->where('status', 'Pending')->where('channel', 'loan-liquidated-topup')->delete();

                $transactions = Transaction::where('loan_id', $topup->id)->where('approved', -1)->get();
                foreach ($transactions as $item) {
                    $action = Transaction::where('id', $item->id)->first();
                    $action->approved = 0;
                    $action->save();
                }
            } else {
                $user->loan_id = null;
                $user->save();
            }

            $loan->delete();

            MerchantPay::where('loan_id', $id)->delete();

            DB::commit();

            try {
                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $user->name);
                $mail->to_email = $user->email;
                $mail->subject = 'Loan Cancellation Request Approved';
                $mail->template = 'email.loan_cancel_status';
                $mail->login = env('MAIN_URL');

                $mail->status = 'approved';
                $mail->amount = number_format($loan_amount, 2);
                $mail->monthly_deduction = number_format($monthly_deduction, 2);
                $mail->tenure = $loan_tenure;

                $send = (new MailHandler())->sendMail($mail);
            } catch (\Exception $e) {
                Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            }

            // all loan cancel requests
            $cancel_requests = Loan::where('cancel_request', -1)->count();

            return response()->json(['loan' => $loan, 'cancel_requests' => $cancel_requests], 200);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not approve loan cancel request'], 500);
        }
    }

    /**
     * Decline loan cancellation requests.
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function declineCancelLoan(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();

            $loan = Loan::where('id', $id)->first();
            if (!$loan) {
                return response()->json(['message' => "Loan doesn't exist"]);
            }

            $loan->cancel_request = 0;
            $loan->cancel_decline_reason = $data['reason'];
            $loan->cancel_declined_at = date('Y-m-d H:i:s');
            $loan->cancel_declined_by = $data['staff_id'];
            $loan->save();

            $notify = doNotify($loan->user_id, 'loans', $loan->id, 'loan_cancel_declined', 'success', null, $loan->lender_id, $data['staff_id']);

            DB::commit();

            $user = User::find($loan->user_id);

            try {
                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $user->name);
                $mail->to_email = $user->email;
                $mail->subject = 'Loan Cancellation Request Declined';
                $mail->template = 'email.loan_cancel_status';
                $mail->login = env('MAIN_URL');

                $mail->status = 'declined';
                $mail->amount = number_format($loan->amount, 2);
                $mail->monthly_deduction = number_format($loan->monthly_deduction, 2);
                $mail->tenure = $loan->tenure;

                $send = (new MailHandler())->sendMail($mail);
            } catch (\Exception $e) {
                Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            }

            // all loan cancel requests
            $cancel_requests = Loan::where('cancel_request', -1)->count();

            return response()->json(['loan' => $loan, 'cancel_requests' => $cancel_requests], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => $e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile()], 500);
        }
    }
}
