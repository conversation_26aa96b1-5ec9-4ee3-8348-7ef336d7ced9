.loading-page {position: absolute;left: 0;width: 100%;height: 100%;background: #fff;}
.loading-page img {position: absolute;top:0;bottom:0;margin:auto;left: 0;right: 0;}
.hidden, .hide {
    display: none !important;
}
input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
  margin: 0;
}
.modal-open .ReactModalPortal {position:fixed;top:0;right:0;bottom:0;left:0;z-index:1050;outline:0;overflow-x:hidden;overflow-y:auto;}
.ReactModalPortal .modal-dialog{-webkit-transition:-webkit-transform 0.3s ease-out;transition:-webkit-transform 0.3s ease-out;transition:transform 0.3s ease-out;transition:transform 0.3s ease-out, -webkit-transform 0.3s ease-out;-webkit-transform:translate(0, -25%);transform:translate(0, -25%)}
.ReactModalPortal .modal-dialog{-webkit-transform:translate(0, 0);transform:translate(0, 0)}
.invoice-logo-w {width: 80px;}
.invoice-logo-w img {max-width: 100%;}
/* @media(min-width: 992px){ */
  .modal-lg {max-width: 1200px;}
  .modal-sm {max-width: 500px;}
  .invoice-w {max-width: 1024px;}
/* } */
.span {display: inline-block; width: 184px;}
.user-title {
  font-size: 13px;
}
.table .row-actions a {margin-right: 0.43rem;color: #3b75e3; text-decoration: none;}
.table .row-actions a:last-child {margin-right: 0 !important;}
.cursor {cursor: pointer !important;}
.el-tablo.bigger .value {font-size: 2rem;}
.el-tablo.bigger .label {font-size: 10px;}
.elabel {
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
}
.elabel-danger {background-color: #d9534f;}
.elabel-success {background-color: #5cb85c;}
.elabel-info {background-color: #383d41;}
.elabel-warning {background-color: #ffb443;}
.scroll {overflow-x: scroll !important;}
.report {width: 100% !important;max-width: 100% !important;}
.scroll::-webkit-scrollbar {
    -webkit-appearance: none;
}

.scroll::-webkit-scrollbar:vertical {
    width: 11px;
}

.scroll::-webkit-scrollbar:horizontal {
    height: 11px;
}

.scroll::-webkit-scrollbar-thumb {
    border-radius: 8px;
    border: 2px solid white; /* should match background, can't be transparent */
    background-color: rgba(0, 0, 0, .5);
}

.scroll::-webkit-scrollbar-track { 
    background-color: #fff; 
    border-radius: 8px; 
}

.input-group-preappend, .input-group-append {
	display: flex;
}
.input-group-append {
	margin-left: -2px;
}
.input-group>.input-group-append>.btn, .all-wrapper .input-group>.input-group-append>.fc-button, .input-group>.input-group-append>.input-group-text, .input-group>.input-group-prepend:not(:first-child)>.btn, .all-wrapper .input-group>.input-group-prepend:not(:first-child)>.fc-button, .input-group>.input-group-prepend:not(:first-child)>.input-group-text, .input-group>.input-group-prepend:first-child>.btn:not(:first-child), .all-wrapper .input-group>.input-group-prepend:first-child>.fc-button:not(:first-child), .input-group>.input-group-prepend:first-child>.input-group-text:not(:first-child) {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
}

.input-group-text {
	display: flex;
	align-items: center;
	padding: .375rem .75rem;
	margin-bottom: 0;
	font-size: 0.9rem;
	font-weight: 400;
	line-height: 1.5;
	color: #495057;
	text-align: center;
	white-space: nowrap;
	border: 1px solid #cecece;
	border-radius: 4px;
	background: #e9ecef;
}

.react-datepicker-popper {z-index: 1000 !important;}
.white-bg { background: #fff !important; }
.top-menu-secondary li:focus {
	outline: none !important;
}
.link {color: #047bf8 !important; text-decoration: underline !important;}

.element-box .element-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 0;
    margin-top: 2rem;
    position: relative;
    z-index: 1
}

.element-box .element-header:after {
    content: "";
    background-color: #047bf8;
    width: 22px;
    height: 6px;
    border-radius: 2px;
    display: block;
    position: absolute;
    bottom: -3px;
    left: 0px
}