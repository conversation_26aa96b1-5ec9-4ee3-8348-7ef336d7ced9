import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, SubmissionError } from 'redux-form';

import { doNotify } from '../actions/general';
import { baseURL, httpRequest, userAPI } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import BreadCrumbs from '../components/BreadCrumbs';
import loader from '../assets/img/loader.gif';
import { toggleMenu, editCustomer } from '../actions/user';

const validate = values => {
    const errors = {}
    if (!values.name) {
        errors.name = 'Enter name';
	}
    if (!values.ippis) {
        errors.ippis = 'Enter ippis';
	}
    if (!values.bvn) {
        errors.bvn = 'Enter bvn';
	}
    if (!values.phone) {
        errors.phone = 'Enter phone number';
	}
    if (!values.email) {
        errors.email = 'Enter email address';
	}
    return errors;
};

const renderField = ({input, id, label, type, disabled, meta: {touched, error}}) => (
	<div className={`form-group row ${(touched && error && 'has-error')}`} style={{marginBottom: '10px'}}>
		<label className="col-sm-5 col-form-label text-right" htmlFor={id}>{label}</label>
		<div className="col-sm-7">
			<input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
			{touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
		</div>
	</div>
);

class EditUser extends Component {
	state = {
		url: '',
		fetching: false,
	};

	componentDidMount() {
		this.props.toggleMenu(false);
		const { match, location } = this.props;
		this.fetchUser(match.params.id);
		const query = location.search.split('?_q=');
		if(query.length > 0){
			this.setState({ url: query[1] });
		}
	}

	componentWillUnmount() {
		this.props.editCustomer(null);
		this.props.toggleMenu(true);
	}

	fetchUser = async id => {
		this.setState({ fetching: true });
		try {
			const rs = await httpRequest(`${baseURL}/profile/${id}`, 'GET', true);
			this.setState({ fetching: false });
			this.props.editCustomer(rs.user);
		}
		catch (error) {
			this.setState({ fetching: false });
			const message = error.message || 'could not fetch customer data';
			this.notify('', message, 'error');
		}
	};
	
	doUpdateUser = async data => {
		this.props.startBlock();
		try {
			const { customer, user } = this.props;
			let details = { ...data, user_id: user.id };
			await httpRequest(`${baseURL}${userAPI}/update-customer/${customer.id}`, 'PUT', true, details);
			this.props.stopBlock();
			this.notify('', 'customer data saved!', 'success');
		}
		catch (error) {
			this.props.stopBlock();
			const message = error.message || 'could not save customer data';
			this.notify('', message, 'error');
			throw new SubmissionError({
				_error: message,
			});
		}
	};
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	render() {
		const { handleSubmit, error, customer } = this.props;
		const { url, fetching } = this.state;
		return (
			<div>
				{fetching && <div className="fetching-load"><img alt="fetching" src={loader} /></div>}
				{url !== '' && <BreadCrumbs url={url}/>}
				{customer && (
					<div className="content-i">
						<div className="content-box">
							<div className="element-wrapper" style={{ paddingBottom: 0 }}>
								<h6 className="element-header">Edit Customer Data</h6>
							</div>
							<div className="row">
								<div className="col-lg-7 col-xxl-6">
									<div className="element-wrapper">
										<div className="element-box">
											{error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
											<form onSubmit={handleSubmit(this.doUpdateUser)}>
												<Field
													name="name"
													id="name"
													type="text"
													component={renderField}
													label="Customer name"
													disabled="disabled"
												/>
												<Field
													name="phone"
													id="phone"
													type="text"
													component={renderField}
													label="Phone number"
												/>
												<Field
													name="ippis"
													id="ippis"
													type="text"
													component={renderField}
													label="IPPIS"
												/>
												<Field
													name="bvn"
													id="bvn"
													type="text"
													component={renderField}
													label="BVN"
												/>
												<Field
													name="email"
													id="email"
													type="email"
													component={renderField}
													label="Email address"
												/>
												<Field
													name="employer"
													id="employer"
													type="text"
													component={renderField}
													label="Employer"
												/>
												<div className="form-buttons-w">
													<button className="btn btn-primary" type="submit">Save</button>
												</div>
											</form>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				)}
			</div>
		);
	}
}

EditUser = reduxForm({
    form: 'edituserform',
    validate,
})(EditUser);

const mapStateToProps = (state, ownProps) => {
	return {
        initialValues: state.user.customer,
		customer: state.user.customer,
		user: state.user.user,
	}
};

export default connect(mapStateToProps, { startBlock, stopBlock, doNotify, toggleMenu, editCustomer })(EditUser);
