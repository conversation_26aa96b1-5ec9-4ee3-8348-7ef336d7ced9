import * as types from './types';
import { httpRequest, baseURL, scheduleAPI } from '../config/constants';

export const setSchedules = data => {
	return {
		type: types.LOAD_SCHEDULES,
		payload: data,
	};
};

export const loadSchedules = (id, year, lender) => {
	return dispatch => {
		return httpRequest(`${baseURL}${scheduleAPI}/${id}/office?year=${year}&lender=${lender}`, 'GET', true);
	};
};

export const unLoadSchedules = () => {
	return {
		type: types.LOAD_SCHEDULES,
		payload: [],
	};
};
