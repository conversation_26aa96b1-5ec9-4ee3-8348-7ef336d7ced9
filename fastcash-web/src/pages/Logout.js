import React, { Component } from 'react';
import { Redirect } from 'react-router-dom';
import { connect } from 'react-redux';
import { signOut } from '../actions/user';
import { authKey, settingKey } from '../config/constants';

class Logout extends Component {
    UNSAFE_componentWillMount() {
        localStorage.removeItem(authKey);
        localStorage.removeItem(settingKey);
        this.props.signOut();
        document.body.className = '';
    }
    
    render() {
        return (
            <Redirect to='/' />
        );
    }
}

export default connect(null, { signOut })(Logout);
