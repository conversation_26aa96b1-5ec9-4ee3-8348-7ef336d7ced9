import React, { Component } from 'react';
import { connect } from 'react-redux';
import Modal from 'react-modal';
import moment from 'moment';
import sha512 from 'crypto-js/sha512';
import utf8 from 'crypto-js/enc-utf8';
import aes from 'crypto-js/aes';
import pkcs7 from 'crypto-js/pad-pkcs7';
import { mode } from 'crypto-js';
import { bindActionCreators } from 'redux';
import qs from 'querystring';

import { liveStatus, ENC_Key, ENC_Vector, paymentMerchantId, paymentApiKey, paymentApiToken, merchantId, apiKey, apiToken, bankCode, bankAccount, httpRequest, baseURL, loanAPI, currency } from '../config/constants';
import { approveMoney, verifyLoan, updateLoan, setLoanAnalytics, declineLoan, updateLoanDecline, disburseMoney, verifyMoneyPaid, bypassDisburse, sendMail, updateLoanAnalytics, consentSMS } from '../actions/loan';
import { startBlock, stopBlock } from '../actions/ui-block';
import { toggleMenu } from '../actions/user';
import BreadCrumbs from '../components/BreadCrumbs';
import LoanProfile from '../components/LoanProfile';
import { doNotify, notifyDone } from '../actions/general';
import { SHOW_PROFILE } from '../actions/types';
import LiquidateLoan from '../components/LiquidateLoan';

const ListHOC = WrappedComponent => {
	class ListContainer extends Component {
		constructor(props, context) {
			super(props, context);
			this.state = {
				modalIsOpen: false,
				id: '',
				loanID: '',
				page: '',
				customer: null,
				loan: null,
				liqModalIsOpen: false,
			};
			this.openModal = this.openModal.bind(this);
			this.closeModal = this.closeModal.bind(this);
			this.doDeclineLoan = this.doDeclineLoan.bind(this);
			this.doVerifyLoan = this.doVerifyLoan.bind(this);
			this.doApproveLoan = this.doApproveLoan.bind(this);
			this.doLiquidateLoan = this.doLiquidateLoan.bind(this);
			this.doSendConsentSMS = this.doSendConsentSMS.bind(this);
			this.doSendMail = this.doSendMail.bind(this);
			this.onSaveLoanInsurance = this.onSaveLoanInsurance.bind(this);
		}
		
		componentWillMount() {
			Modal.setAppElement('body');
		}
	
		componentDidMount() {
			this.props.toggleMenu(false);
			const query = qs.parse(this.props.location.search.replace('?', ''));
			const page = query && query.p ? query.p : 1;
			this.setState({ page });
		}
		
		componentWillUnmount() {
			this.props.toggleMenu(true);
		}
		
		openModal = (customer, loan) => {
			document.body.className="pages modal-open";
			this.setState({ modalIsOpen: true, customer, loan });
		};
	
		closeModal = () => {
			this.setState({ modalIsOpen: false, customer: null, loan: null });
			document.body.className="pages";
		}
	
		notify = (title, message, type) => {
			this.props.doNotify({
				message,
				level: type,
				title,
			});
		};
		
		showUserProfile = (id) => this.props.showProfile({ show_profile: true, user_id: id });
	
		success = (message, response) => {
			if(response){
				this.props.updateLoan(response.loan);
			}
	
			this.props.stopBlock();
			this.notify('', message, 'success');
		}
	
		doVerifyLoan = (id, userID) => {
			this.props.startBlock();
			this.props.verifyLoan(id, userID)
				.then((response) => {
					this.props.stopBlock();
					this.props.notifyDone(true);
					this.props.setLoanAnalytics(response.loan_details);
					this.success("Loan verified!", response);
				})
				.catch(error => {
					console.log(error);
					this.props.stopBlock();
					const message = error.message || 'could not verify loan';
					this.notify('', message, 'error');
				});
		};
		
		AES_128_ENCRYPT = (rawData) => {
			let key = ENC_Key;
			let iv  = ENC_Vector;
			key = utf8.parse(key);
			iv = utf8.parse(iv);
			const encryptData = aes.encrypt(utf8.parse(rawData), key,
				{
					keySize: 128 / 8,
					iv: iv,
					mode: mode.CBC,
					padding: pkcs7
				});
			return encryptData;
		};
		
		doDeclineLoan = id => {
			const action = window.confirm("Do you want to decline this loan?") ? true : false;
			if(action){
				this.props.startBlock();
				const data = {
					staff: this.props.user.id,
					id,
				}
	
				this.props.declineLoan(data)
					.then(response => {
						this.props.updateLoanDecline(response.loan);
						this.props.setLoanAnalytics(response.loan_details);
						this.success("Loan declined!");
					})
					.catch(error => {
						this.props.stopBlock();
						const message = error.message || 'could not decline loan';
						this.notify('', message, 'error');
					});
			}
		}
	
		verifyPayment = loan => {
			this.props.startBlock();
			const { user } = this.props;
			if(loan){
				const disbursement = loan.disbursement ? JSON.parse(loan.disbursement) : null;
				if(disbursement){
					const transRef = this.AES_128_ENCRYPT(disbursement.data.transRef);
					const dfBody = {
						transRef: `${transRef}`,
					};
					
					const d = new Date();
					const requestId = d.getTime();
					const apiPaymentHash = sha512(paymentApiKey + requestId + paymentApiToken);
		
					let dd = d.getDate();
					let mm = d.getMonth()+1; //January is 0!
					const yyyy = d.getFullYear();
					if(dd<10){
						dd='0'+dd;
					} 
					if(mm<10){
						mm='0'+mm;
					} 
					const hours = d.getUTCHours();
					const minutes = d.getUTCMinutes();
					const seconds = d.getUTCSeconds();
					const requestTS = yyyy+'-'+mm+'-'+dd+'T'+hours+':'+minutes+':'+seconds+'+000000';
		
					const datum = { id: loan.id, paymentMerchantId, paymentApiKey, requestId, requestTS, apiPaymentHash: `${apiPaymentHash}`, dfBody, staff: user.id, };
					this.props.verifyMoneyPaid(datum)
						.then(response => {
							const retLoan = response.loan;
							this.props.updateLoan(retLoan);
							const paymentStatus = retLoan.payment_status ? JSON.parse(retLoan.payment_status) : null;
							if(paymentStatus && paymentStatus.status === 'success'){
								this.props.updateLoanAnalytics(response.loan_details, 'lloans_approved');
								this.success("Payment Verified!");
								this.props.notifyDone(true);
							}
							else {
								this.props.stopBlock();
								const message = paymentStatus.data.responseDescription;
								this.notify('', message, 'error');
							}
						})
						.catch(error => {
							this.props.stopBlock();
							const message = error.message || 'could not verify payment';
							this.notify('', message, 'error');
						});
				}
				else {
					this.props.stopBlock();
					const message = 'loan amount not disbursed yet!';
					this.notify('', message, 'error');
				}
			}
			else {
				this.props.stopBlock();
				const message = 'loan not found!';
				this.notify('', message, 'error');
			}
		}
	
		disburse = loan => {
			this.props.startBlock();
			if(loan){
				const user = loan.user;
	
				let fromBank = bankCode;
				let debitAccount = bankAccount;
				let toBank = liveStatus ? loan.accountno.bank_code : '058';
				let creditAccount = liveStatus ? loan.accountno.account_number : '*************';
				let narration = 'Loan Disbursement';
				let amount = loan.amount;
				let beneficiaryEmail = user.email;
				
				const d = new Date();
				const requestId = d.getTime();
				const randomnumber = Math.floor(Math.random()*1101233);
				let transRef = randomnumber;
				const apiPaymentHash = sha512(paymentApiKey + requestId + paymentApiToken);
				
				toBank = this.AES_128_ENCRYPT(toBank);
				creditAccount = this.AES_128_ENCRYPT(creditAccount);
				narration = this.AES_128_ENCRYPT(narration);
				amount = this.AES_128_ENCRYPT(amount);
				transRef = this.AES_128_ENCRYPT(transRef);
				fromBank = this.AES_128_ENCRYPT(fromBank);
				debitAccount = this.AES_128_ENCRYPT(debitAccount);
				beneficiaryEmail = this.AES_128_ENCRYPT(beneficiaryEmail);
	
				let dd = d.getDate();
				let mm = d.getMonth()+1; //January is 0!
				const yyyy = d.getFullYear();
				if(dd<10){
					dd='0'+dd;
				} 
				if(mm<10){
					mm='0'+mm;
				} 
				const hours = d.getUTCHours();
				const minutes = d.getUTCMinutes();
				const seconds = d.getUTCSeconds();
				const requestTS = yyyy+'-'+mm+'-'+dd+'T'+hours+':'+minutes+':'+seconds+'+000000';
	
				const dfBody = {
					toBank: `${toBank}`,
					creditAccount: `${creditAccount}`,
					narration: `${narration}`,
					amount: `${amount}`,
					transRef: `${transRef}`,
					fromBank: `${fromBank}`,
					debitAccount: `${debitAccount}`,
					beneficiaryEmail: `${beneficiaryEmail}`,
				};
	
				const datum = { id: loan.id, paymentMerchantId, paymentApiKey, requestId, requestTS, apiPaymentHash: `${apiPaymentHash}`, dfBody };
				this.props.disburseMoney(datum)
					.then(response => {
						const retLoan = response.loan;
						this.props.updateLoan(retLoan);
						const disbursement = retLoan.disbursement ? JSON.parse(retLoan.disbursement) : null;
						if(disbursement && disbursement.status === 'success'){
							this.props.updateLoanAnalytics(response.loan_details, 'lloans_approved');
							this.props.notifyDone(true);
							this.verifyPayment(retLoan);
							this.success("Money Disbursed!");
						}
						else {
							this.props.stopBlock();
							const message = 'could not disburse loan';
							this.notify('', message, 'error');
						}
					})
					.catch(error => {
						this.props.stopBlock();
						const message = error.message || 'could not disburse loan';
						this.notify('', message, 'error');
					});
			}
			else {
				this.props.stopBlock();
				const message = 'loan not found!';
				this.notify('', message, 'error');
			}
		}
	
		doApproveLoan = async (loan, staffID) => {
			if(loan){
				try {
					this.props.startBlock();
					const is_topup = loan.is_topup && loan.is_topup !== '' && loan.is_topup === 1 ? 1 : 0;
					const url = `${baseURL}${loanAPI}/balance/${loan.id}?is_topup=${is_topup}`;
					const rs = await httpRequest(url, 'GET', true);
					this.props.stopBlock();
					if (rs && rs.result) {
						let action;
						if(parseFloat(rs.result.disburse_amount) > parseFloat(loan.disburse_amount) && is_topup === 1){
							// tell him the disburse amount has changed
							const amount = currency(rs.result.disburse_amount)
							action = window.confirm(`The new disburse amount is ${amount}. Will you like to approve this loan?`) ? true : false;
						} else {
							action = window.confirm("Will you like to approve this loan?") ? true : false;
						}
		
						if(action){
							this.props.startBlock();
							const user = loan.user;
							// const account = loan.accountno;
							const post = { id: loan.id, staff: staffID, platform: loan.platform };
							let data = null;
							
							const d = new Date();
							const requestId = d.getTime();
							const apiHash = sha512(apiKey + requestId + apiToken);
				
							let dd = d.getDate();
							let mm = d.getMonth()+1; //January is 0!
							const yyyy = d.getFullYear();
							if(dd<10){
								dd='0'+dd;
							} 
							if(mm<10){
								mm='0'+mm;
							} 
							const hours = d.getUTCHours();
							const minutes = d.getUTCMinutes();
							const seconds = d.getUTCSeconds();
							const requestTS = yyyy+'-'+mm+'-'+dd+'T'+hours+':'+minutes+':'+seconds+'+000000';
				
							if(loan.platform === 'remita'){
								const authCheck = await httpRequest(`${baseURL}${loanAPI}/auth-check/${loan.auth_code}/${loan.id}`, 'GET', true);

								const authorisationCode = authCheck.auth_code;
								const authorization = `remitaConsumerKey=${apiKey}, remitaConsumerToken=${apiHash}`;
				
								const body = {
									customerId: user.customer_id,
									authorisationCode,
									authorisationChannel: "USSD",
									phoneNumber: user.phone,
									accountNumber: **********,
									currency: "NGN",
									loanAmount: loan.amount,
									collectionAmount: loan.monthly_deduction,
									totalCollectionAmount: loan.total_deduction,
									dateOfDisbursement: moment().format('DD-MM-YYYY HH:mm:ss+0000'),
									dateOfCollection: moment().format('DD-MM-YYYY HH:mm:ss+0000'),
									numberOfRepayments: loan.tenure,
									bankCode: '011'
								};
								data = { authorization, body };
							}
				
							const datum = {
								...data,
								...post,
								merchantId,
								apiKey,
								requestId,
								requestTS,
								loan_balance: rs.result.amount,
								disburse_amount: rs.result.disburse_amount,
							};
							console.log(datum)

							this.props.approveMoney(datum)
								.then(response => {
									if (response.fails && parseInt(response.fails, 10) > 0) {
										this.props.stopBlock();
										const message = `could not approve loan, ${response.fails} loan approvals failed`;
										this.notify('', message, 'error');
									} else {
										this.success("Loan approved!", response);
										this.props.setLoanAnalytics(response.loan_details);
										this.props.notifyDone(true);
										this.props.stopBlock();
									}
								})
								.catch(error => {
									this.props.stopBlock();
									const message = error.message || 'could not approve loan';
									this.notify('', message, 'error');
								});
						}
					}
				} catch (error) {
					this.props.stopBlock();
					const message = error.message || 'could not get balance';
					this.notify('', message, 'error');
				}
			}
			else {
				this.props.stopBlock();
				const message = 'loan not found!';
				this.notify('', message, 'error');
			}
		}
	
		bypass = id => {
			const action = window.confirm("Have you already disbursed this loan?") ? true : false;
			if(action){
				this.props.startBlock();
				const data = {
					staff: this.props.user.id,
					id,
				}
	
				this.props.bypassDisburse(data)
					.then(response => {
						this.props.updateLoanAnalytics(response.loan_details, 'lloans_approved');
						this.props.notifyDone(true);
						this.success("Loan disbursed!", response);
					})
					.catch(error => {
						this.props.stopBlock();
						const message = error.message || 'error please try again';
						this.notify('', message, 'error');
					});
			}
		}
	
		doSendMail = id => () => {
			const action = window.confirm("Do you want to generate offer letter?") ? true : false;
			if(action){
				this.props.startBlock();
				const data = {
					staff: this.props.user.id,
					id,
				}
	
				this.props.sendMail(data)
					.then(response => {
						this.success("Loan offer letter generated!", response);
						window.location.reload(true);
					})
					.catch(error => {
						this.props.stopBlock();
						const message = error.message || 'could not generate offer letter!';
						this.notify('', message, 'error');
					});
			}
		}
	
		doMultiFund = (loan, status) => async () => {
			const _status = status === 1 ? 'enable' : 'cancel';
			const __status = status === 1 ? 'enabled' : 'cancelled';
			const action = window.confirm(`Do you want to ${_status} multi funding?`) ? true : false;
			if(action) {
				this.props.startBlock();
				const { user } = this.props;
				try {
					const data = { staff: user.id, status };
					const rs = await httpRequest(`${baseURL}${loanAPI}/multi-fund/${loan.id}`, 'POST', true, data);
					this.props.setLoanAnalytics(rs.loan_details);
					this.success(`multi fund ${__status} for loan!`, rs);
					this.props.notifyDone(true);
				} catch (e) {
					this.props.stopBlock();
					const message = e.message || `could not ${_status} multi funding!`;
					this.notify('', message, 'error');
				}
			}
		}
	
		doSendConsentSMS = loan => () => {
			const action = window.confirm("Do you want to send the sms?") ? true : false;
			if(action){
				this.props.startBlock();
				const data = { id: loan.id };
	
				this.props.consentSMS(data)
					.then(response => {
						this.success("Consent SMS sent!", response);
					})
					.catch(error => {
						this.props.stopBlock();
						const message = error.message || 'could not send sms!';
						this.notify('', message, 'error');
					});
			}
		}
	
		doLiquidateLoan = loan => async () => {
			document.body.className="pages modal-open";
			this.setState({ liqModalIsOpen: true, loan });
		}
	
		closeLiqModal = () => {
			this.setState({ liqModalIsOpen: false, loan: null });
			document.body.className="pages";
			this.props.notifyDone(true);
		}

		onSaveLoanInsurance = response => {
			this.success("Loan Saved", response);
		}

		render() {
			const { modalIsOpen, customer, loan, liqModalIsOpen } = this.state;
			return (
				<div>
					<BreadCrumbs url="/dashboard" />
					<div className="content-i">
						<div className="content-box">
							<WrappedComponent
								showUserProfile={this.showUserProfile}
								doVerifyLoan={this.doVerifyLoan}
								doApproveLoan={this.doApproveLoan}
								openModal={this.openModal}
								bypass={this.bypass}
								disburse={this.disburse}
								verifyPayment={this.verifyPayment}
								doFundLoan={this.doFundLoan}
								doMultiFund={this.doMultiFund}
								doLiquidateLoan={this.doLiquidateLoan}
								sendConsentSMS={this.doSendConsentSMS}
								doSendMail={this.doSendMail}
								onSaveLoanInsurance={this.onSaveLoanInsurance}
								{...this.props}
							/>
						</div>
						<Modal
							isOpen={liqModalIsOpen}
							onRequestClose={this.closeLiqModal}
							contentLabel="Liquidate Loan"
							shouldCloseOnOverlayClick={false}
							overlayClassName="modal-dialog modal-sm sd"
							className="modal-content"
							portalClassName="ReactModalPortall"
							bodyOpenClassName="ReactModal__Body--openl"
						>
							<LiquidateLoan closeModal={this.closeLiqModal} loan={loan}/>
						</Modal>
						{liqModalIsOpen? (<div className="modal-backdrop fade show"/>) : ''}

						<Modal
							isOpen={modalIsOpen}
							onRequestClose={this.closeModal}
							contentLabel="Loan Profile"
							shouldCloseOnOverlayClick={false}
							overlayClassName="modal-dialog modal-lg modal-extra-lg"
							className="modal-content">
							<LoanProfile closeModal={this.closeModal} user={customer} loan={loan} />
						</Modal>
						{modalIsOpen ? <div className="modal-backdrop fade show" /> : ''}
					</div>
				</div>
			);
		}
	}

	const showProfile = (data) => {
		return { type: SHOW_PROFILE, data };
	};

	const mapStateToProps = (state, ownProps) => {
		return {
			settings: state.settings,
			user: state.user.user,
		};
	};

	const mapDispatchToProps = dispatch => {
		return bindActionCreators({ approveMoney, verifyLoan, startBlock, stopBlock, updateLoan, setLoanAnalytics, declineLoan, updateLoanDecline, showProfile, disburseMoney, verifyMoneyPaid, bypassDisburse, toggleMenu, sendMail, doNotify, notifyDone, updateLoanAnalytics, consentSMS }, dispatch);
	};

	return connect(
		mapStateToProps,
		mapDispatchToProps,
	)(ListContainer);
};

export default ListHOC;
