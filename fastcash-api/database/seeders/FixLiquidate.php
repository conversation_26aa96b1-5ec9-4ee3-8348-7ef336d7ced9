<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\Transaction;
use App\Models\User;
use App\Services\MailHandler;
use App\Services\Remita;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use KingFlamez\Rave\Facades\Rave;

class FixLiquidate extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::beginTransaction();

        $id = 1190;

        try {
            $loan = Loan::with('user')->where('id', $id)->first();

            $merchantId = env('MERCHANT_ID');
            $apiKey = env('API_KEY');
            $apiToken = env('API_TOKEN');

            $requestId = time() * 1000;
            $apiHash = hash('sha512', $apiKey . $requestId . $apiToken);
            $authorization = "remitaConsumerKey=" . $apiKey . ", remitaConsumerToken=" . $apiHash;

            $approved = json_decode($loan->approved_remita);

            $body = [
                "authorisationCode" => $loan->auth_code,
                "customerId" => $loan->user->customer_id,
                "mandateReference" => $approved->data->mandateReference,
            ];

            $input = [
                'platform' => 'remita',
                'merchantId' => $merchantId,
                'apiKey' => $apiKey,
                'requestId' => $requestId,
                'authorization' => $authorization,
                'body' => $body,
                'data' => null,
                'enc' => null,
                'payment_type' => 'card',
                'reference' => 'XYKn46DunP',
                'amount' => '33000',
            ];

            if ($loan) {
                $info = null;
                if ($input['payment_type'] == 'card') {
                    $info = Rave::verifyTransaction($input['reference']);
                    info(json_encode($info));
                }

                $sl = null;
                if ($input['platform'] == 'remita') {
                    $sl = (new Remita())->stopLoanCollection($input['merchantId'], $input['apiKey'], $input['requestId'], $input['authorization'], $input['body']);

                    $status = $sl == null ? 'failed' : ($sl->responseCode == '00' ? 'success' : $sl->responseMsg);

                    logAPI('stop-loan', $loan->user->phone, $status);

                    if ($sl != null) {
                        if ($sl->responseCode != '00') {
                            Log::error('stop loan:-> ' . json_encode($sl));
                        }
                    } else {
                        Log::error('Remita stop loan notification failed.');
                    }
                }

                $enc = null;
                if (isset($input['data'])) {
                    $enc = isset($input['enc']) && $input['enc'] == '1' ? base64_decode($input['data']) : $input['data'];
                }

                if (($input['payment_type'] == 'card' && $info && $info->status == 'success') || ($input['payment_type'] == 'bank')) {
                    $loan->transaction_data = $enc != null ? json_encode($enc) : null;  // data from payment
                    $loan->transaction_tx = $sl != null ? json_encode($sl) : null;   // verify transaction
                    $loan->liquidated = 1;
                    $loan->liquidate_approve = 0;
                    $loan->liquidated_at = date('Y-m-d H:i:s');
                    $loan->save();

                    $channel = $input['payment_type'] == 'card' ? 'flutterwave' : $input['payment_type'];

                    $transaction = Transaction::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'office_id' => $loan->office_id,
                        'source' => strtoupper($loan->platform),
                        'amount' => $input['amount'],
                        'interest' => $loan->monthly_interest,
                        'principal' => $input['amount'] - $loan->monthly_interest,
                        'outst_amount' => 0,
                        'status' => 'Pending',
                        'description' => 'Loan Liquidated',
                        'transaction_flag' => 'liquidate',
                        'lender_id' => $loan->lender_id,
                        'payment_type' => $input['payment_type'],
                        'reference' => $info != null ? json_encode($info->data) : null,
                        'channel' => $channel,
                        'approved' => 0,
                        'repayment_date' => date('Y-m-d'),
                        'repayment_source' => $input['payment_type'],
                    ]);

                    $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'loan_liquidated', 'success', null, $loan->lender_id, null);

                    DB::commit();

                    $user = User::where('id', $loan->user_id)->first();

                    try {
                        $mail = (object)null;
                        $mail->from_name = 'FastCash';
                        $mail->from_email = '<EMAIL>';
                        $mail->to_name = str_replace(',', '', $user->name);
                        $mail->to_email = $user->email;
                        $mail->login = env('MAIN_URL');
                        $mail->subject = 'Loan Liquidate';
                        $mail->amount = number_format($input['amount'], 2);

                        if ($input['payment_type'] == 'bank') {
                            $mail->template = 'email.liquidate_bank';
                        } else {
                            $mail->template = 'email.liquidate_card';
                        }

                        if ($loan->admin_request == 0) {
                            $send = (new MailHandler())->sendMail($mail);
                        }
                    } catch (\Exception $e) {
                        Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                    }

                    info('done');
                }
            } else {
                DB::rollBack();

                Log::error('loan not found, try again later');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
        }
    }
}
