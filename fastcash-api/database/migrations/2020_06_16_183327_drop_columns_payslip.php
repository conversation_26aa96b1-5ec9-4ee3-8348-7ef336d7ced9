<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class DropColumnsPayslip extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('payslips', function (Blueprint $table) {
            $table->dropColumn(['salary_bank_branch', 'pfa_name', 'pension_pin', 'grade', 'step', 'tax_state', 'trade_union']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('payslips', function (Blueprint $table) {
            $table->string('salary_bank_branch')->nullable()->after('salary_bank');
            $table->string('pfa_name')->nullable()->after('account_pin');
            $table->string('pension_pin')->nullable()->after('pfa_name');
            $table->string('grade')->nullable()->after('pension_pin');
            $table->string('step')->nullable()->after('location');
            $table->string('tax_state')->nullable()->after('year');
            $table->string('trade_union')->nullable()->after('tax_state');
        });
    }
}
