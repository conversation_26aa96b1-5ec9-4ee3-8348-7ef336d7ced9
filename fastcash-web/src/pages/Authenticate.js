import React, { Component } from 'react';
import { Link } from 'react-router-dom';

import logo from '../assets/img/fc-logo.png';
import Footer from '../components/Footer';
import AuthForm from '../components/AuthForm';
import ForgotForm from '../components/ForgotForm';
import ChangeEmail from '../components/ChangeEmail';

class Authenticate extends Component {
    UNSAFE_componentWillMount() {
        document.body.className = '';
    }

    render() {
        const { location } = this.props;
        const path = location.pathname.split('/').pop();
        return (
            <div className="wrapper-front">
                <div className="desktop-menu menu-top-f menu-activated-on-hover">
                    <div className="menu-top-i os-container">
                        <div className="logo-w">
                            <Link className="logo" to="/">
                                <img alt="logo" src={logo} style={{width:"200px"}}/>
                            </Link>
                        </div>
                        <ul className="main-menu">
                            <li className="active"><Link to="/">Home</Link></li>
                        </ul>
                    </div>
                    <div className="mobile-menu-w">
                        <div className="mobile-menu-holder color-scheme-dark">
                            <ul className="mobile-menu">
                                <li className="active"><Link to="/">Home</Link></li>
                            </ul>
                        </div>
                        <div className="mobile-menu-i">
                            <div className="mobile-logo-w">
                                <Link className="logo" to="/">
                                    <img alt="logo" src={logo} style={{width:"150px"}}/>
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="intro-w layout-v1" id="sectionIntro">
                    <div className="os-container">
                        <div className="intro-i">
                            <div className="intro-description has-bg">
                                <div className="intro-text">Welcome to FastCash. The fastest and most secure Lending platform for employees under the Federal Civil Service of the Federal Republic of Nigeria.
                                </div>
                            </div>
                            <div className="intro-media"/>
                            <div>
                                {path === 'forgot-password' ? (
                                    <ForgotForm />
                                ) : (
                                    path === 'change-email' ? <ChangeEmail /> : <AuthForm />
                                )}
                            </div>
                        </div>
                    </div>
                </div>
                <Footer/>
            </div>
        );
    }
}

export default Authenticate;
