/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import Pagination from 'antd/lib/pagination';
import qs from 'querystring';
import { Link } from 'react-router-dom';

import Lender from '../components/Lender';
import { httpRequest, baseURL, lenderAPI, lenderKey } from '../config/constants';
import { doNotify } from '../actions/general';
import { startBlock, stopBlock } from '../actions/ui-block';
import { editLender } from '../actions/lender';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 20;

class ManageLenders extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			currentPage: 1,
			totalPage: 0,
			lenders: [],
		};
	}

	componentDidMount() {
		const query = qs.parse(this.props.location.search.replace('?', ''));
		const page = query && query.p ? parseInt(query.p, 10) : 1;
		this.fetch(page);
	}

	fetch = async pageNumber => {
		try {
			this.props.startBlock();
			const rs = await httpRequest(`${baseURL}${lenderAPI}?page=${pageNumber}&pagesize=${pageSize}`, 'GET', true);
			const result = rs.result;
			this.setState({ currentPage: pageNumber, totalPage: result.total, lenders: result.data });
			this.props.stopBlock();
			window.scrollTo(0, 0);
		} catch (e) {
			this.props.stopBlock();
			this.props.doNotify({ message: 'could not load lenders', level: 'error' });
		}
	};

	onChange = pageNumber => {
		this.fetch(pageNumber);
	};

	editLender = id => {
		const { lenders } = this.state;
		const lender = lenders.find(l => l.id === id);
		if(lender){
			localStorage.setItem(lenderKey, JSON.stringify(lender));
			this.props.editLender(lender);
			this.props.history.push(`/lenders/edit/${id}`);
		}
		else {
			this.props.doNotify({ message: 'could not find lender profile', level: 'error' });
		}
	};

	enableLender = id => {
		const action = window.confirm('Are you sure you want to enable lender?') ? true : false;
		if (action) {
			const { user } = this.props;
            this.props.startBlock();
			httpRequest(`${baseURL}${lenderAPI}/enable/${id}`, 'POST', true, {user_id: user.id})
                .then(_ => {
                    this.props.stopBlock();
                    this.props.doNotify({ message: 'lender enabled!', level: 'success' });
                    this.fetch(this.state.currentPage);
                })
                .catch(error => {
                    this.props.stopBlock();
                    const message = error.message || 'lender could not be enabled';
                    this.props.doNotify({ message: message, level: 'error' });
                });
		}
	};

	disableLender = id => {
		const action = window.confirm('Are you sure you want to disable lender?') ? true : false;
		if (action) {
			const { user } = this.props;
            this.props.startBlock();
			httpRequest(`${baseURL}${lenderAPI}/disable/${id}`, 'POST', true, {user_id: user.id})
                .then(_ => {
                    this.props.stopBlock();
                    this.props.doNotify({ message: 'lender disabled!', level: 'success' });
                    this.fetch(this.state.currentPage);
                })
                .catch(error => {
                    this.props.stopBlock();
                    const message = error.message || 'lender could not be disabled';
                    this.props.doNotify({ message: message, level: 'error' });
                });
		}
	};

	render() {
		const { currentPage, totalPage, lenders } = this.state;
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="element-wrapper">
						<div className="element-actions">
							<Link
								to="/lenders/create"
								className="btn btn-primary btn-sm cursor"
								style={{ color: '#ffffff' }}>
								<i className="os-icon os-icon-ui-22" />
								<span>New Lender</span>
							</Link>
						</div>
						<h6 className="element-header">List of Lenders</h6>
						<div className="element-box">
							<div className="table-responsive">
								<table className="table table-striped table-lightfont">
									<thead>
										<tr>
											<th>Lender Code</th>
											<th>Name of Establishment</th>
											<th>Email</th>
											<th>Phone Number</th>
											<th>Status</th>
											<th className="text-center">&nbsp;</th>
										</tr>
									</thead>
									<tbody>
										{lenders.map((item, i) => {
											return (
												<Lender
													key={i}
													item={item}
													editLender={this.editLender}
													enableLender={this.enableLender}
													disableLender={this.disableLender}
												/>
											);
										})}
									</tbody>
								</table>
							</div>
							<div className="pagination pagination-center mt-4">
								<Pagination
									current={currentPage}
									pageSize={pageSize}
									total={totalPage}
									showTotal={total => `Total ${total} lenders`}
									itemRender={itemRender}
									onChange={this.onChange}
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
	};
};

export default connect(
	mapStateToProps,
	{ doNotify, startBlock, stopBlock, editLender },
)(ManageLenders);
