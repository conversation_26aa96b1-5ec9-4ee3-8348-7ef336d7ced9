services:
  backend-service:
    container_name: backend-service
    image: dev/fastcash-backend
    build: .
    command: /bin/sh -c "php artisan serve --host=0.0.0.0 --port=8000"
    restart: always
    ports:
      - 8000:8000
    env_file:
      - .env
    volumes:
      - .:/var/www/html
      - /var/www/html/vendor
    networks:
      - fastcash

  mysql-service:
    container_name: mysql-service
    image: mysql:5.7.33
    command: mysqld --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    ports:
      - 3306:3306
    environment:
      - MYSQL_ROOT_PASSWORD=${DB_PASSWORD}
    volumes:
      - ./mysql_dump:/docker-entrypoint-initdb.d
      - mysql-data:/var/lib/mysql
    networks:
      - fastcash

  phpmyadmin:
    container_name: phpmyadmin
    image: phpmyadmin/phpmyadmin
    restart: always
    environment:
      - PMA_ARBITRARY=1
      - PMA_HOST=mysql-service
      - PMA_USER=${DB_USERNAME}
      - PMA_PASSWORD=${DB_PASSWORD}
    ports:
      - 8888:80
    networks:
      - fastcash

  mail-service:
    container_name: mail-service
    image: axllent/mailpit:latest
    restart: always
    ports:
      - 8025:8025
      - 1025:1025
    environment:
      - MP_MAX_MESSAGES=5000
      - MP_DATABASE=/data/mailpit.db
      - MP_SMTP_AUTH_ACCEPT_ANY=1
      - MP_SMTP_AUTH_ALLOW_INSECURE=1
      - TZ=Africa/Lagos
    volumes:
      - mail-data:/data
    networks:
      - fastcash
  
  redis-service:
    container_name: redis-service
    image: redis:latest
    ports:
      - 6379:6379
    volumes:
      - redis-data:/data
    networks:
      - fastcash

volumes:
  mysql-data: {}
  mail-data: {}
  redis-data: {}

networks:
  fastcash:
    driver: bridge
