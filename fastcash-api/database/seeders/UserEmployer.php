<?php

namespace Database\Seeders;

use App\Models\Payslip;
use App\Models\User;
use Illuminate\Database\Seeder;

class UserEmployer extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $users = User::whereNull('employer')->get();
        foreach ($users as $user){
            $payslip = Payslip::find($user->payslip_id);

            if($payslip){
                $u = User::find($user->id);
                $u->employer = $payslip->employer;
                $u->save();
            }
        }
    }
}
