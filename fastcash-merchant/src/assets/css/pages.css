/*!
 * Bootstrap v4.0.0-alpha.6 (https://getbootstrap.com)
 * Copyright 2011-2017 The Bootstrap Authors
 * Copyright 2011-2017 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */


/*! normalize.css v5.0.0 | MIT License | github.com/necolas/normalize.css */



body.pages {
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: .9rem;
    font-weight: 400;
    line-height: 1.5;
    color: #3E4B5B;
    background-color: #fff
}

h1,
.h1 {
    font-size: 2.5rem
}

h2,
.h2 {
    font-size: 2rem
}

mark,
.mark {
    padding: .2em;
    background-color: #F9F1D9
}

.img-thumbnail {
    padding: .25rem;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: .25rem;
    -webkit-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    max-width: 100%;
    height: auto
}

.table .table {
    background-color: #fff
}

.table-warning,
.table-warning>th,
.table-warning>td {
    background-color: #F9F1D9
}

.table-hover .table-warning:hover {
    background-color: #f6e9c3
}

.table-hover .table-warning:hover>td,
.table-hover .table-warning:hover>th {
    background-color: #f6e9c3
}

.thead-inverse th {
    color: #fff;
    background-color: #292b2c
}

.table-inverse {
    color: #fff;
    background-color: #292b2c
}

.table-inverse th,
.table-inverse td,
.table-inverse thead th {
    border-color: #fff
}

.col-form-label-sm {
    padding-top: calc(.25rem - 1px * 2);
    padding-bottom: calc(.25rem - 1px * 2);
    font-size: .8rem
}

.has-success .form-control-feedback,
.has-success .form-control-label,
.has-success .col-form-label,
.has-success .form-check-label,
.has-success .custom-control {
    color: #579D1B
}

.has-success .form-control {
    border-color: #579D1B
}

.has-success .input-group-addon {
    color: #579D1B;
    border-color: #579D1B;
    background-color: #bfed97
}

.has-success .form-control-success {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23579D1B' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3E%3C/svg%3E")
}

.has-warning .form-control-feedback,
.has-warning .form-control-label,
.has-warning .col-form-label,
.has-warning .form-check-label,
.has-warning .custom-control {
    color: #F9F1D9
}

.has-warning .form-control {
    border-color: #F9F1D9
}

.has-warning .input-group-addon {
    color: #F9F1D9;
    border-color: #F9F1D9;
    background-color: #fff
}

.has-warning .form-control-warning {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23F9F1D9' d='M4.4 5.324h-.8v-2.46h.8zm0 1.42h-.8V5.89h.8zM3.76.63L.04 7.075c-.115.2.016.425.26.426h7.397c.242 0 .372-.226.258-.426C6.726 4.924 5.47 2.79 4.253.63c-.113-.174-.39-.174-.494 0z'/%3E%3C/svg%3E")
}

.btn,
.all-wrapper .fc-button {
    display: inline-block;
    font-weight: 400;
    line-height: 1.45;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid transparent;
    padding: .5rem .9rem;
    font-size: .9rem;
    border-radius: .25rem;
    -webkit-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out
}


.all-wrapper .fc-button.fc-state-active:focus,
.all-wrapper .focus.fc-button.fc-state-active {
    -webkit-box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.5);
    box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.5)
}

.all-wrapper .disabled.fc-button.fc-state-active,
.all-wrapper .fc-button.fc-state-active:disabled {
    background-color: #047bf8;
    border-color: #047bf8
}

.all-wrapper .fc-button.fc-state-active:active,
.all-wrapper .active.fc-button.fc-state-active,
.all-wrapper .show>.dropdown-toggle.fc-button.fc-state-active {
    color: #fff;
    background-color: #0362c6;
    background-image: none;
    border-color: #035dbc
}

.btn-success {
    color: #fff;
    background-color: #579D1B;
    border-color: #579D1B
}

.btn-success:hover {
    color: #fff;
    background-color: #3f7114;
    border-color: #3a6912
}

.btn-success:focus,
.btn-success.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(87, 157, 27, 0.5);
    box-shadow: 0 0 0 2px rgba(87, 157, 27, 0.5)
}

.btn-success.disabled,
.btn-success:disabled {
    background-color: #579D1B;
    border-color: #579D1B
}

.btn-success:active,
.btn-success.active,
.show>.btn-success.dropdown-toggle {
    color: #fff;
    background-color: #3f7114;
    background-image: none;
    border-color: #3a6912
}

.btn-warning {
    color: #fff;
    background-color: #F9F1D9;
    border-color: #F9F1D9
}

.btn-warning:hover {
    color: #fff;
    background-color: #f2e1ad;
    border-color: #f1dea4
}

.btn-warning:focus,
.btn-warning.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(249, 241, 217, 0.5);
    box-shadow: 0 0 0 2px rgba(249, 241, 217, 0.5)
}

.btn-warning.disabled,
.btn-warning:disabled {
    background-color: #F9F1D9;
    border-color: #F9F1D9
}

.btn-warning:active,
.btn-warning.active,
.show>.btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #f2e1ad;
    background-image: none;
    border-color: #f1dea4
}

.btn-outline-success {
    color: #579D1B;
    background-image: none;
    background-color: transparent;
    border-color: #579D1B
}

.btn-outline-success:hover {
    color: #fff;
    background-color: #579D1B;
    border-color: #579D1B
}

.btn-outline-success:focus,
.btn-outline-success.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(87, 157, 27, 0.5);
    box-shadow: 0 0 0 2px rgba(87, 157, 27, 0.5)
}

.btn-outline-success.disabled,
.btn-outline-success:disabled {
    color: #579D1B;
    background-color: transparent
}

.btn-outline-success:active,
.btn-outline-success.active,
.show>.btn-outline-success.dropdown-toggle {
    color: #fff;
    background-color: #579D1B;
    border-color: #579D1B
}

.btn-outline-warning {
    color: #F9F1D9;
    background-image: none;
    background-color: transparent;
    border-color: #F9F1D9
}

.btn-outline-warning:hover {
    color: #fff;
    background-color: #F9F1D9;
    border-color: #F9F1D9
}

.btn-outline-warning:focus,
.btn-outline-warning.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(249, 241, 217, 0.5);
    box-shadow: 0 0 0 2px rgba(249, 241, 217, 0.5)
}

.btn-outline-warning.disabled,
.btn-outline-warning:disabled {
    color: #F9F1D9;
    background-color: transparent
}

.btn-outline-warning:active,
.btn-outline-warning.active,
.show>.btn-outline-warning.dropdown-toggle {
    color: #fff;
    background-color: #F9F1D9;
    border-color: #F9F1D9
}

.all-wrapper .btn-group-lg>.fc-button {
    padding: .75rem 1.5rem;
    font-size: 1.25rem;
    border-radius: .3rem
}

.btn-sm,
.btn-group-sm>.btn,
.all-wrapper .btn-group-sm>.fc-button {
    padding: .2rem .5rem;
    font-size: .8rem;
    border-radius: .2rem
}

.dropdown-header {
    display: block;
    padding: .5rem 1.5rem;
    margin-bottom: 0;
    font-size: .8rem;
    color: #636c72;
    white-space: nowrap
}

.all-wrapper .btn-group>.fc-button,
.all-wrapper .btn-group-vertical>.fc-button {
    position: relative;
    -webkit-box-flex: 0;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto
}

.all-wrapper .btn-group>.fc-button:hover,
.all-wrapper .btn-group-vertical>.fc-button:hover {
    z-index: 2
}

.all-wrapper .btn-group>.fc-button:focus,
.all-wrapper .btn-group>.fc-button:active,
.all-wrapper .btn-group>.active.fc-button,
.all-wrapper .btn-group-vertical>.fc-button:focus,
.all-wrapper .btn-group-vertical>.fc-button:active,
.all-wrapper .btn-group-vertical>.active.fc-button {
    z-index: 2
}

.btn-group .all-wrapper .fc-button+.btn,
.all-wrapper .btn-group .fc-button+.btn,
.btn-group .all-wrapper .btn+.fc-button,
.all-wrapper .btn-group .btn+.fc-button,
.btn-group .all-wrapper .fc-button+.fc-button,
.all-wrapper .btn-group .fc-button+.fc-button,
.btn-group .all-wrapper .fc-button+.btn-group,
.all-wrapper .btn-group .fc-button+.btn-group,
.btn-group .all-wrapper .btn-group+.fc-button,
.all-wrapper .btn-group .btn-group+.fc-button,
.btn-group-vertical .all-wrapper .fc-button+.btn,
.all-wrapper .btn-group-vertical .fc-button+.btn,
.btn-group-vertical .all-wrapper .btn+.fc-button,
.all-wrapper .btn-group-vertical .btn+.fc-button,
.btn-group-vertical .all-wrapper .fc-button+.fc-button,
.all-wrapper .btn-group-vertical .fc-button+.fc-button,
.btn-group-vertical .all-wrapper .fc-button+.btn-group,
.all-wrapper .btn-group-vertical .fc-button+.btn-group,
.btn-group-vertical .all-wrapper .btn-group+.fc-button,
.all-wrapper .btn-group-vertical .btn-group+.fc-button {
    margin-left: -1px
}

.all-wrapper .btn-group>.fc-button:not(:first-child):not(:last-child):not(.dropdown-toggle) {
    border-radius: 0
}

.all-wrapper .btn-group>.fc-button:first-child {
    margin-left: 0
}

.all-wrapper .btn-group>.fc-button:first-child:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.all-wrapper .btn-group>.fc-button:last-child:not(:first-child),
.btn-group>.dropdown-toggle:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.all-wrapper .btn-group>.btn-group:not(:first-child):not(:last-child)>.fc-button {
    border-radius: 0
}

.btn-group>.btn-group:first-child:not(:last-child)>.btn:last-child,
.all-wrapper .btn-group>.btn-group:first-child:not(:last-child)>.fc-button:last-child,
.btn-group>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.btn-group>.btn-group:last-child:not(:first-child)>.btn:first-child,
.all-wrapper .btn-group>.btn-group:last-child:not(:first-child)>.fc-button:first-child {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.btn+.dropdown-toggle-split,
.all-wrapper .fc-button+.dropdown-toggle-split {
    padding-right: .675rem;
    padding-left: .675rem
}

.btn+.dropdown-toggle-split::after,
.all-wrapper .fc-button+.dropdown-toggle-split::after {
    margin-left: 0
}

.btn-sm+.dropdown-toggle-split,
.btn-group-sm>.btn+.dropdown-toggle-split,
.all-wrapper .btn-group-sm>.fc-button+.dropdown-toggle-split {
    padding-right: .375rem;
    padding-left: .375rem
}

.btn-lg+.dropdown-toggle-split,
.btn-group-lg>.btn+.dropdown-toggle-split,
.all-wrapper .btn-group-lg>.fc-button+.dropdown-toggle-split {
    padding-right: 1.125rem;
    padding-left: 1.125rem
}

.btn-group-vertical .btn,
.btn-group-vertical .all-wrapper .fc-button,
.all-wrapper .btn-group-vertical .fc-button,
.btn-group-vertical .btn-group {
    width: 100%
}

.btn-group-vertical>.btn+.btn,
.all-wrapper .btn-group-vertical>.fc-button+.btn,
.all-wrapper .btn-group-vertical>.btn+.fc-button,
.all-wrapper .btn-group-vertical>.fc-button+.fc-button,
.btn-group-vertical>.btn+.btn-group,
.all-wrapper .btn-group-vertical>.fc-button+.btn-group,
.btn-group-vertical>.btn-group+.btn,
.all-wrapper .btn-group-vertical>.btn-group+.fc-button,
.btn-group-vertical>.btn-group+.btn-group {
    margin-top: -1px;
    margin-left: 0
}

.btn-group-vertical>.btn:not(:first-child):not(:last-child),
.all-wrapper .btn-group-vertical>.fc-button:not(:first-child):not(:last-child) {
    border-radius: 0
}

.btn-group-vertical>.btn:first-child:not(:last-child),
.all-wrapper .btn-group-vertical>.fc-button:first-child:not(:last-child) {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn:last-child:not(:first-child),
.all-wrapper .btn-group-vertical>.fc-button:last-child:not(:first-child) {
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn,
.all-wrapper .btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.fc-button {
    border-radius: 0
}

.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child,
.all-wrapper .btn-group-vertical>.btn-group:first-child:not(:last-child)>.fc-button:last-child,
.btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child,
.all-wrapper .btn-group-vertical>.btn-group:last-child:not(:first-child)>.fc-button:first-child {
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

[data-toggle="buttons"]>.btn input[type="radio"],
.all-wrapper [data-toggle="buttons"]>.fc-button input[type="radio"],
[data-toggle="buttons"]>.btn input[type="checkbox"],
.all-wrapper [data-toggle="buttons"]>.fc-button input[type="checkbox"],
[data-toggle="buttons"]>.btn-group>.btn input[type="radio"],
.all-wrapper [data-toggle="buttons"]>.btn-group>.fc-button input[type="radio"],
[data-toggle="buttons"]>.btn-group>.btn input[type="checkbox"],
.all-wrapper [data-toggle="buttons"]>.btn-group>.fc-button input[type="checkbox"] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none
}

.input-group-addon {
    padding: .5rem .7rem;
    margin-bottom: 0;
    font-size: .9rem;
    font-weight: 400;
    line-height: 1.25;
    color: #464a4c;
    text-align: center;
    background-color: #eceeef;
    border: 1px solid #cecece;
    border-radius: .25rem
}

.input-group-addon.form-control-sm,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.input-group-addon.btn,
.all-wrapper .input-group-sm>.input-group-btn>.input-group-addon.fc-button {
    padding: .25rem .7rem;
    font-size: .8rem;
    border-radius: .2rem
}

.input-group-addon.form-control-lg,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.input-group-addon.btn,
.all-wrapper .input-group-lg>.input-group-btn>.input-group-addon.fc-button {
    padding: .75rem 1.5rem;
    font-size: 1.25rem;
    border-radius: .3rem
}

.input-group .form-control:not(:last-child),
.input-group-addon:not(:last-child),
.input-group-btn:not(:last-child)>.btn,
.all-wrapper .input-group-btn:not(:last-child)>.fc-button,
.input-group-btn:not(:last-child)>.btn-group>.btn,
.all-wrapper .input-group-btn:not(:last-child)>.btn-group>.fc-button,
.input-group-btn:not(:last-child)>.dropdown-toggle,
.input-group-btn:not(:first-child)>.btn:not(:last-child):not(.dropdown-toggle),
.all-wrapper .input-group-btn:not(:first-child)>.fc-button:not(:last-child):not(.dropdown-toggle),
.input-group-btn:not(:first-child)>.btn-group:not(:last-child)>.btn,
.all-wrapper .input-group-btn:not(:first-child)>.btn-group:not(:last-child)>.fc-button {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.input-group .form-control:not(:first-child),
.input-group-addon:not(:first-child),
.input-group-btn:not(:first-child)>.btn,
.all-wrapper .input-group-btn:not(:first-child)>.fc-button,
.input-group-btn:not(:first-child)>.btn-group>.btn,
.all-wrapper .input-group-btn:not(:first-child)>.btn-group>.fc-button,
.input-group-btn:not(:first-child)>.dropdown-toggle,
.input-group-btn:not(:last-child)>.btn:not(:first-child),
.all-wrapper .input-group-btn:not(:last-child)>.fc-button:not(:first-child),
.input-group-btn:not(:last-child)>.btn-group:not(:first-child)>.btn,
.all-wrapper .input-group-btn:not(:last-child)>.btn-group:not(:first-child)>.fc-button {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.input-group-btn>.btn,
.all-wrapper .input-group-btn>.fc-button {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.input-group-btn>.btn+.btn,
.all-wrapper .input-group-btn>.fc-button+.btn,
.all-wrapper .input-group-btn>.btn+.fc-button,
.all-wrapper .input-group-btn>.fc-button+.fc-button {
    margin-left: -1px
}

.input-group-btn>.btn:focus,
.all-wrapper .input-group-btn>.fc-button:focus,
.input-group-btn>.btn:active,
.all-wrapper .input-group-btn>.fc-button:active,
.input-group-btn>.btn:hover,
.all-wrapper .input-group-btn>.fc-button:hover {
    z-index: 3
}

.input-group-btn:not(:last-child)>.btn,
.all-wrapper .input-group-btn:not(:last-child)>.fc-button,
.input-group-btn:not(:last-child)>.btn-group {
    margin-right: -1px
}

.input-group-btn:not(:first-child)>.btn,
.all-wrapper .input-group-btn:not(:first-child)>.fc-button,
.input-group-btn:not(:first-child)>.btn-group {
    z-index: 2;
    margin-left: -1px
}

.input-group-btn:not(:first-child)>.btn:focus,
.all-wrapper .input-group-btn:not(:first-child)>.fc-button:focus,
.input-group-btn:not(:first-child)>.btn:active,
.all-wrapper .input-group-btn:not(:first-child)>.fc-button:active,
.input-group-btn:not(:first-child)>.btn:hover,
.all-wrapper .input-group-btn:not(:first-child)>.fc-button:hover,
.input-group-btn:not(:first-child)>.btn-group:focus,
.input-group-btn:not(:first-child)>.btn-group:active,
.input-group-btn:not(:first-child)>.btn-group:hover {
    z-index: 3
}

.custom-control-input:focus~.custom-control-indicator {
    -webkit-box-shadow: 0 0 0 1px #fff, 0 0 0 3px #047bf8;
    box-shadow: 0 0 0 1px #fff, 0 0 0 3px #047bf8
}

.custom-select {
    display: inline-block;
    max-width: 100%;
    height: calc(2.125rem + 2px);
    padding: .375rem 1.75rem .375rem .75rem;
    line-height: 1.25;
    color: #464a4c;
    vertical-align: middle;
    background: #fff url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right .75rem center;
    background-size: 8px 10px;
    border: 1px solid #cecece;
    border-radius: .25rem;
    -moz-appearance: none;
    -webkit-appearance: none
}

.pagination-sm .page-link {
    padding: .25rem .5rem;
    font-size: .8rem
}

.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 75%;
    font-weight: 500;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25rem
}

.btn .badge,
.all-wrapper .fc-button .badge {
    position: relative;
    top: -1px
}

.badge-success {
    background-color: #579D1B
}

.badge-success[href]:focus,
.badge-success[href]:hover {
    background-color: #3f7114
}

.badge-warning {
    background-color: #e0af5c
}

.badge-warning[href]:focus,
.badge-warning[href]:hover {
    background-color: #d89a31
}

@-webkit-keyframes progress-bar-stripes {
    from {
        background-position: 1rem 0
    }
    to {
        background-position: 0 0
    }
}

@keyframes progress-bar-stripes {
    from {
        background-position: 1rem 0
    }
    to {
        background-position: 0 0
    }
}

.progress {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    overflow: hidden;
    font-size: .75rem;
    line-height: 1rem;
    text-align: center;
    background-color: #eceeef;
    border-radius: .25rem
}

.progress-bar {
    height: 1rem;
    color: #fff;
    background-color: #047bf8
}

.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem
}

.progress-bar-animated {
    -webkit-animation: progress-bar-stripes 1s linear infinite;
    animation: progress-bar-stripes 1s linear infinite
}

.close {
    float: right;
    font-size: 1.35rem;
    font-weight: 500;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5
}

.close:focus,
.close:hover {
    color: #000;
    text-decoration: none;
    cursor: pointer;
    opacity: .75
}

button.close {
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
    -webkit-appearance: none
}

.modal-open {
    overflow: hidden
}

.modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    display: none;
    overflow: hidden;
    outline: 0
}

.modal.fade .modal-dialog {
    -webkit-transition: -webkit-transform 0.3s ease-out;
    transition: -webkit-transform 0.3s ease-out;
    transition: transform 0.3s ease-out;
    transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
    -webkit-transform: translate(0, -25%);
    transform: translate(0, -25%)
}

.modal.show .modal-dialog {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0)
}

.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 10px
}

.modal-content {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: .3rem;
    outline: 0
}

.modal-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1040;
    background-color: #000
}

.modal-backdrop.fade {
    opacity: 0
}

.modal-backdrop.show {
    opacity: .5
}

.modal-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 15px 25px;
    border-bottom: 1px solid #eceeef
}

.modal-title {
    margin-bottom: 0;
    line-height: 1.5
}

.modal-body {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 15px 25px
}

.modal-footer {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    padding: 15px 25px;
    border-top: 1px solid #eceeef
}

.modal-footer>:not(:first-child) {
    margin-left: .25rem
}

.modal-footer>:not(:last-child) {
    margin-right: .25rem
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll
}

@media (min-width: 576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 30px auto
    }
    .modal-sm {
        max-width: 300px
    }
}

@media (min-width: 992px) {
    .modal-lg {
        max-width: 800px
    }
}

.tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-style: normal;
    font-weight: 400;
    letter-spacing: normal;
    line-break: auto;
    line-height: 1.5;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    white-space: normal;
    word-break: normal;
    word-spacing: normal;
    font-size: .8rem;
    word-wrap: break-word;
    opacity: 0
}

.tooltip.show {
    opacity: .9
}

.tooltip.tooltip-top,
.tooltip.bs-tether-element-attached-bottom {
    padding: 5px 0;
    margin-top: -3px
}

.tooltip.tooltip-top .tooltip-inner::before,
.tooltip.bs-tether-element-attached-bottom .tooltip-inner::before {
    bottom: 0;
    left: 50%;
    margin-left: -5px;
    content: "";
    border-width: 5px 5px 0;
    border-top-color: #000
}

.tooltip.tooltip-right,
.tooltip.bs-tether-element-attached-left {
    padding: 0 5px;
    margin-left: 3px
}

.tooltip.tooltip-right .tooltip-inner::before,
.tooltip.bs-tether-element-attached-left .tooltip-inner::before {
    top: 50%;
    left: 0;
    margin-top: -5px;
    content: "";
    border-width: 5px 5px 5px 0;
    border-right-color: #000
}

.tooltip.tooltip-bottom,
.tooltip.bs-tether-element-attached-top {
    padding: 5px 0;
    margin-top: 3px
}

.tooltip.tooltip-bottom .tooltip-inner::before,
.tooltip.bs-tether-element-attached-top .tooltip-inner::before {
    top: 0;
    left: 50%;
    margin-left: -5px;
    content: "";
    border-width: 0 5px 5px;
    border-bottom-color: #000
}

.tooltip.tooltip-left,
.tooltip.bs-tether-element-attached-right {
    padding: 0 5px;
    margin-left: -3px
}

.tooltip.tooltip-left .tooltip-inner::before,
.tooltip.bs-tether-element-attached-right .tooltip-inner::before {
    top: 50%;
    right: 0;
    margin-top: -5px;
    content: "";
    border-width: 5px 0 5px 5px;
    border-left-color: #000
}

.tooltip-inner {
    max-width: 200px;
    padding: 3px 8px;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: .25rem
}

.tooltip-inner::before {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid
}

.popover {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1060;
    display: block;
    max-width: 300px;
    padding: 0px;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-style: normal;
    font-weight: 400;
    letter-spacing: normal;
    line-break: auto;
    line-height: 1.5;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    white-space: normal;
    word-break: normal;
    word-spacing: normal;
    font-size: .8rem;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: padding-box;
    border: 0px solid rgba(0, 0, 0, 0.2);
    border-radius: .3rem
}

.popover.popover-top,
.popover.bs-tether-element-attached-bottom {
    margin-top: -6px
}

.popover.popover-top::before,
.popover.popover-top::after,
.popover.bs-tether-element-attached-bottom::before,
.popover.bs-tether-element-attached-bottom::after {
    left: 50%;
    border-bottom-width: 0
}

.popover.popover-top::before,
.popover.bs-tether-element-attached-bottom::before {
    bottom: -7px;
    margin-left: -7px;
    border-top-color: #fff
}

.popover.popover-top::after,
.popover.bs-tether-element-attached-bottom::after {
    bottom: -6px;
    margin-left: -6px;
    border-top-color: #fff
}

.popover.popover-right,
.popover.bs-tether-element-attached-left {
    margin-left: 6px
}

.popover.popover-right::before,
.popover.popover-right::after,
.popover.bs-tether-element-attached-left::before,
.popover.bs-tether-element-attached-left::after {
    top: 50%;
    border-left-width: 0
}

.popover.popover-right::before,
.popover.bs-tether-element-attached-left::before {
    left: -7px;
    margin-top: -7px;
    border-right-color: #fff
}

.popover.popover-right::after,
.popover.bs-tether-element-attached-left::after {
    left: -6px;
    margin-top: -6px;
    border-right-color: #fff
}

.popover.popover-bottom,
.popover.bs-tether-element-attached-top {
    margin-top: 6px
}

.popover.popover-bottom::before,
.popover.popover-bottom::after,
.popover.bs-tether-element-attached-top::before,
.popover.bs-tether-element-attached-top::after {
    left: 50%;
    border-top-width: 0
}

.popover.popover-bottom::before,
.popover.bs-tether-element-attached-top::before {
    top: -7px;
    margin-left: -7px;
    border-bottom-color: #fff
}

.popover.popover-bottom::after,
.popover.bs-tether-element-attached-top::after {
    top: -6px;
    margin-left: -6px;
    border-bottom-color: #047bf8
}

.popover.popover-bottom .popover-title::before,
.popover.bs-tether-element-attached-top .popover-title::before {
    position: absolute;
    top: 0;
    left: 50%;
    display: block;
    width: 20px;
    margin-left: -10px;
    content: "";
    border-bottom: 1px solid #047bf8
}

.popover.popover-left,
.popover.bs-tether-element-attached-right {
    margin-left: -6px
}

.popover.popover-left::before,
.popover.popover-left::after,
.popover.bs-tether-element-attached-right::before,
.popover.bs-tether-element-attached-right::after {
    top: 50%;
    border-right-width: 0
}

.popover.popover-left::before,
.popover.bs-tether-element-attached-right::before {
    right: -7px;
    margin-top: -7px;
    border-left-color: #fff
}

.popover.popover-left::after,
.popover.bs-tether-element-attached-right::after {
    right: -6px;
    margin-top: -6px;
    border-left-color: #fff
}

.popover-title {
    padding: 12px 15px;
    margin-bottom: 0;
    font-size: .9rem;
    background-color: #047bf8;
    border-bottom: 0px solid #046fdf;
    border-top-right-radius: calc(.3rem - 0px);
    border-top-left-radius: calc(.3rem - 0px)
}

.popover-title:empty {
    display: none
}

.popover-content {
    padding: 15px 20px
}

.popover::before,
.popover::after {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid
}

.popover::before {
    content: "";
    border-width: 7px
}

.popover::after {
    content: "";
    border-width: 6px
}

.carousel {
    position: relative
}

.carousel-inner {
    position: relative;
    width: 100%;
    overflow: hidden
}

.carousel-item {
    position: relative;
    display: none;
    width: 100%
}

@media (-webkit-transform-3d) {
    .carousel-item {
        -webkit-transition: -webkit-transform 0.6s ease-in-out;
        transition: -webkit-transform 0.6s ease-in-out;
        transition: transform 0.6s ease-in-out;
        transition: transform 0.6s ease-in-out, -webkit-transform 0.6s ease-in-out;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-perspective: 1000px;
        perspective: 1000px
    }
}

@supports ((-webkit-transform: translate3d(0,
0,
0)) or (transform: translate3d(0,
0,
0))) {
    .carousel-item {
        -webkit-transition: -webkit-transform 0.6s ease-in-out;
        transition: -webkit-transform 0.6s ease-in-out;
        transition: transform 0.6s ease-in-out;
        transition: transform 0.6s ease-in-out, -webkit-transform 0.6s ease-in-out;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-perspective: 1000px;
        perspective: 1000px
    }
}

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.carousel-item-next,
.carousel-item-prev {
    position: absolute;
    top: 0
}

@media (-webkit-transform-3d) {
    .carousel-item-next.carousel-item-left,
    .carousel-item-prev.carousel-item-right {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
    .carousel-item-next,
    .active.carousel-item-right {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
    }
    .carousel-item-prev,
    .active.carousel-item-left {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
    }
}

@supports ((-webkit-transform: translate3d(0,
0,
0)) or (transform: translate3d(0,
0,
0))) {
    .carousel-item-next.carousel-item-left,
    .carousel-item-prev.carousel-item-right {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
    .carousel-item-next,
    .active.carousel-item-right {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
    }
    .carousel-item-prev,
    .active.carousel-item-left {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
    }
}

.carousel-control-prev,
.carousel-control-next {
    position: absolute;
    top: 0;
    bottom: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 15%;
    color: #fff;
    text-align: center;
    opacity: .5
}

.carousel-control-prev:focus,
.carousel-control-prev:hover,
.carousel-control-next:focus,
.carousel-control-next:hover {
    color: #fff;
    text-decoration: none;
    outline: 0;
    opacity: .9
}

.carousel-control-prev {
    left: 0
}

.carousel-control-next {
    right: 0
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: transparent no-repeat center center;
    background-size: 100% 100%
}

.carousel-control-prev-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3E%3Cpath d='M4 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E")
}

.carousel-control-next-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3E%3Cpath d='M1.5 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E")
}

.carousel-indicators {
    position: absolute;
    right: 0;
    bottom: 10px;
    left: 0;
    z-index: 15;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding-left: 0;
    margin-right: 15%;
    margin-left: 15%;
    list-style: none
}

.carousel-indicators li {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1 0 auto;
    flex: 1 0 auto;
    max-width: 30px;
    height: 3px;
    margin-right: 3px;
    margin-left: 3px;
    text-indent: -999px;
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.5)
}

.carousel-indicators li::before {
    position: absolute;
    top: -10px;
    left: 0;
    display: inline-block;
    width: 100%;
    height: 10px;
    content: ""
}

.carousel-indicators li::after {
    position: absolute;
    bottom: -10px;
    left: 0;
    display: inline-block;
    width: 100%;
    height: 10px;
    content: ""
}

.carousel-indicators .active {
    background-color: #fff
}

.carousel-caption {
    position: absolute;
    right: 15%;
    bottom: 20px;
    left: 15%;
    z-index: 10;
    padding-top: 20px;
    padding-bottom: 20px;
    color: #fff;
    text-align: center
}

.bg-faded {
    background-color: #f7f7f7
}

.bg-success {
    background-color: #579D1B !important
}

a.bg-success:focus,
a.bg-success:hover {
    background-color: #3f7114 !important
}

.bg-warning {
    background-color: #F9F1D9 !important
}

a.bg-warning:focus,
a.bg-warning:hover {
    background-color: #f2e1ad !important
}

@media (min-width: 1450px) {
    .d-xxl-none {
        display: none !important
    }
    .d-xxl-inline {
        display: inline !important
    }
    .d-xxl-inline-block {
        display: inline-block !important
    }
    .d-xxl-block {
        display: block !important
    }
    .d-xxl-table {
        display: table !important
    }
    .d-xxl-table-cell {
        display: table-cell !important
    }
    .d-xxl-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }
    .d-xxl-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media (min-width: 1450px) {
    .flex-xxl-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1
    }
    .flex-xxl-last {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }
    .flex-xxl-unordered {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
    .flex-xxl-row {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }
    .flex-xxl-column {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }
    .flex-xxl-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }
    .flex-xxl-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }
    .flex-xxl-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }
    .flex-xxl-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }
    .flex-xxl-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }
    .justify-content-xxl-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }
    .justify-content-xxl-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }
    .justify-content-xxl-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important
    }
    .justify-content-xxl-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }
    .justify-content-xxl-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }
    .align-items-xxl-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }
    .align-items-xxl-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }
    .align-items-xxl-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important
    }
    .align-items-xxl-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }
    .align-items-xxl-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }
    .align-content-xxl-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }
    .align-content-xxl-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }
    .align-content-xxl-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }
    .align-content-xxl-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }
    .align-content-xxl-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }
    .align-content-xxl-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }
    .align-self-xxl-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important
    }
    .align-self-xxl-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }
    .align-self-xxl-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }
    .align-self-xxl-center {
        -ms-flex-item-align: center !important;
        align-self: center !important
    }
    .align-self-xxl-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }
    .align-self-xxl-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important
    }
}

@media (min-width: 1450px) {
    .float-xxl-left {
        float: left !important
    }
    .float-xxl-right {
        float: right !important
    }
    .float-xxl-none {
        float: none !important
    }
}

@media (min-width: 1450px) {
    .m-xxl-0 {
        margin: 0 0 !important
    }
    .mt-xxl-0 {
        margin-top: 0 !important
    }
    .mr-xxl-0 {
        margin-right: 0 !important
    }
    .mb-xxl-0 {
        margin-bottom: 0 !important
    }
    .ml-xxl-0 {
        margin-left: 0 !important
    }
    .mx-xxl-0 {
        margin-right: 0 !important;
        margin-left: 0 !important
    }
    .my-xxl-0 {
        margin-top: 0 !important;
        margin-bottom: 0 !important
    }
    .m-xxl-1 {
        margin: .25rem .25rem !important
    }
    .mt-xxl-1 {
        margin-top: .25rem !important
    }
    .mr-xxl-1 {
        margin-right: .25rem !important
    }
    .mb-xxl-1 {
        margin-bottom: .25rem !important
    }
    .ml-xxl-1 {
        margin-left: .25rem !important
    }
    .mx-xxl-1 {
        margin-right: .25rem !important;
        margin-left: .25rem !important
    }
    .my-xxl-1 {
        margin-top: .25rem !important;
        margin-bottom: .25rem !important
    }
    .m-xxl-2 {
        margin: .5rem .5rem !important
    }
    .mt-xxl-2 {
        margin-top: .5rem !important
    }
    .mr-xxl-2 {
        margin-right: .5rem !important
    }
    .mb-xxl-2 {
        margin-bottom: .5rem !important
    }
    .ml-xxl-2 {
        margin-left: .5rem !important
    }
    .mx-xxl-2 {
        margin-right: .5rem !important;
        margin-left: .5rem !important
    }
    .my-xxl-2 {
        margin-top: .5rem !important;
        margin-bottom: .5rem !important
    }
    .m-xxl-3 {
        margin: 1rem 1rem !important
    }
    .mt-xxl-3 {
        margin-top: 1rem !important
    }
    .mr-xxl-3 {
        margin-right: 1rem !important
    }
    .mb-xxl-3 {
        margin-bottom: 1rem !important
    }
    .ml-xxl-3 {
        margin-left: 1rem !important
    }
    .mx-xxl-3 {
        margin-right: 1rem !important;
        margin-left: 1rem !important
    }
    .my-xxl-3 {
        margin-top: 1rem !important;
        margin-bottom: 1rem !important
    }
    .m-xxl-4 {
        margin: 1.5rem 1.5rem !important
    }
    .mt-xxl-4 {
        margin-top: 1.5rem !important
    }
    .mr-xxl-4 {
        margin-right: 1.5rem !important
    }
    .mb-xxl-4 {
        margin-bottom: 1.5rem !important
    }
    .ml-xxl-4 {
        margin-left: 1.5rem !important
    }
    .mx-xxl-4 {
        margin-right: 1.5rem !important;
        margin-left: 1.5rem !important
    }
    .my-xxl-4 {
        margin-top: 1.5rem !important;
        margin-bottom: 1.5rem !important
    }
    .m-xxl-5 {
        margin: 3rem 3rem !important
    }
    .mt-xxl-5 {
        margin-top: 3rem !important
    }
    .mr-xxl-5 {
        margin-right: 3rem !important
    }
    .mb-xxl-5 {
        margin-bottom: 3rem !important
    }
    .ml-xxl-5 {
        margin-left: 3rem !important
    }
    .mx-xxl-5 {
        margin-right: 3rem !important;
        margin-left: 3rem !important
    }
    .my-xxl-5 {
        margin-top: 3rem !important;
        margin-bottom: 3rem !important
    }
    .p-xxl-0 {
        padding: 0 0 !important
    }
    .pt-xxl-0 {
        padding-top: 0 !important
    }
    .pr-xxl-0 {
        padding-right: 0 !important
    }
    .pb-xxl-0 {
        padding-bottom: 0 !important
    }
    .pl-xxl-0 {
        padding-left: 0 !important
    }
    .px-xxl-0 {
        padding-right: 0 !important;
        padding-left: 0 !important
    }
    .py-xxl-0 {
        padding-top: 0 !important;
        padding-bottom: 0 !important
    }
    .p-xxl-1 {
        padding: .25rem .25rem !important
    }
    .pt-xxl-1 {
        padding-top: .25rem !important
    }
    .pr-xxl-1 {
        padding-right: .25rem !important
    }
    .pb-xxl-1 {
        padding-bottom: .25rem !important
    }
    .pl-xxl-1 {
        padding-left: .25rem !important
    }
    .px-xxl-1 {
        padding-right: .25rem !important;
        padding-left: .25rem !important
    }
    .py-xxl-1 {
        padding-top: .25rem !important;
        padding-bottom: .25rem !important
    }
    .p-xxl-2 {
        padding: .5rem .5rem !important
    }
    .pt-xxl-2 {
        padding-top: .5rem !important
    }
    .pr-xxl-2 {
        padding-right: .5rem !important
    }
    .pb-xxl-2 {
        padding-bottom: .5rem !important
    }
    .pl-xxl-2 {
        padding-left: .5rem !important
    }
    .px-xxl-2 {
        padding-right: .5rem !important;
        padding-left: .5rem !important
    }
    .py-xxl-2 {
        padding-top: .5rem !important;
        padding-bottom: .5rem !important
    }
    .p-xxl-3 {
        padding: 1rem 1rem !important
    }
    .pt-xxl-3 {
        padding-top: 1rem !important
    }
    .pr-xxl-3 {
        padding-right: 1rem !important
    }
    .pb-xxl-3 {
        padding-bottom: 1rem !important
    }
    .pl-xxl-3 {
        padding-left: 1rem !important
    }
    .px-xxl-3 {
        padding-right: 1rem !important;
        padding-left: 1rem !important
    }
    .py-xxl-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important
    }
    .p-xxl-4 {
        padding: 1.5rem 1.5rem !important
    }
    .pt-xxl-4 {
        padding-top: 1.5rem !important
    }
    .pr-xxl-4 {
        padding-right: 1.5rem !important
    }
    .pb-xxl-4 {
        padding-bottom: 1.5rem !important
    }
    .pl-xxl-4 {
        padding-left: 1.5rem !important
    }
    .px-xxl-4 {
        padding-right: 1.5rem !important;
        padding-left: 1.5rem !important
    }
    .py-xxl-4 {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important
    }
    .p-xxl-5 {
        padding: 3rem 3rem !important
    }
    .pt-xxl-5 {
        padding-top: 3rem !important
    }
    .pr-xxl-5 {
        padding-right: 3rem !important
    }
    .pb-xxl-5 {
        padding-bottom: 3rem !important
    }
    .pl-xxl-5 {
        padding-left: 3rem !important
    }
    .px-xxl-5 {
        padding-right: 3rem !important;
        padding-left: 3rem !important
    }
    .py-xxl-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important
    }
    .m-xxl-auto {
        margin: auto !important
    }
    .mt-xxl-auto {
        margin-top: auto !important
    }
    .mr-xxl-auto {
        margin-right: auto !important
    }
    .mb-xxl-auto {
        margin-bottom: auto !important
    }
    .ml-xxl-auto {
        margin-left: auto !important
    }
    .mx-xxl-auto {
        margin-right: auto !important;
        margin-left: auto !important
    }
    .my-xxl-auto {
        margin-top: auto !important;
        margin-bottom: auto !important
    }
}

@media (min-width: 1450px) {
    .text-xxl-left {
        text-align: left !important
    }
    .text-xxl-right {
        text-align: right !important
    }
    .text-xxl-center {
        text-align: center !important
    }
}

.text-muted,
.invoice-body .invoice-desc .desc-value {
    color: #aaa !important
}

a.text-muted:focus,
.invoice-body .invoice-desc a.desc-value:focus,
a.text-muted:hover,
.invoice-body .invoice-desc a.desc-value:hover {
    color: #919090 !important
}

.text-success {
    color: #579D1B !important
}

a.text-success:focus,
a.text-success:hover {
    color: #3f7114 !important
}

.text-warning {
    color: #F9F1D9 !important
}

a.text-warning:focus,
a.text-warning:hover {
    color: #f2e1ad !important
}

@media (max-width: 1449px) {
    .hidden-xl-down {
        display: none !important
    }
}

@media (min-width: 1450px) {
    .hidden-xxl-up {
        display: none !important
    }
}

.hidden-xxl-down {
    display: none !important
}

.table .smaller {
    font-size: .72rem
}

.table .lighter {
    color: rgba(90, 99, 126, 0.49)
}

.table tfoot th,
.table thead th {
    font-size: .63rem;
    text-transform: uppercase;
    border-top: none
}

.table .cell-image-list .cell-img-more {
    font-size: .63rem;
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    right: -70%;
    background-color: #fff;
    padding: 3px 5px;
    border-radius: 4px;
    z-index: 7;
    white-space: nowrap
}

@media (min-width: 1100px) {
    .table-responsive {
        overflow: visible
    }
}

.controls-above-table .btn,
.controls-above-table .all-wrapper .fc-button,
.all-wrapper .controls-above-table .fc-button {
    margin-right: 0.5rem
}

.controls-below-table {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    font-size: .81rem
}

.all-wrapper table.dataTable {
    border-collapse: collapse !important
}

.table.table-padded {
    border-collapse: separate;
    border-spacing: 0 5px
}

.table.table-padded thead tr th {
    border: none;
    font-size: .81rem;
    color: rgba(90, 99, 126, 0.49);
    letter-spacing: 1px;
    padding: 0.3rem 1.1rem
}

.table.table-padded tbody tr {
    border-radius: 4px;
    -webkit-transition: all 0.1s ease;
    transition: all 0.1s ease
}

.table.table-padded tbody tr:hover {
    -webkit-box-shadow: 0px 2px 5px rgba(69, 101, 173, 0.1);
    box-shadow: 0px 2px 5px rgba(69, 101, 173, 0.1);
    -webkit-transform: translateY(-1px) scale(1.01);
    transform: translateY(-1px) scale(1.01)
}

.table.table-padded tbody td {
    padding: 0.9rem 1.1rem;
    background-color: #fff;
    border: none;
    border-right: 1px solid rgba(0, 0, 0, 0.03)
}

.table.table-padded tbody td.bolder {
    font-weight: 500;
    font-size: .99rem
}

.table.table-padded tbody td img {
    display: inline-block;
    vertical-align: middle
}

.table.table-padded tbody td img+span {
    display: inline-block;
    margin-left: 10px;
    vertical-align: middle
}

.table.table-padded tbody td span+span {
    margin-left: 5px
}

.table.table-padded tbody td .status-pill+span {
    margin-left: 10px
}

.table.table-padded tbody td:first-child {
    border-radius: 4px 0px 0px 4px
}

.table.table-padded tbody td:last-child {
    border-radius: 0px 4px 4px 0px;
    border-right: none
}

.form-text {
    font-size: .81rem
}

.has-danger .form-control-feedback.text-muted,
.has-danger .invoice-body .invoice-desc .form-control-feedback.desc-value,
.invoice-body .invoice-desc .has-danger .form-control-feedback.desc-value {
    color: #e65252 !important
}

.form-group .date-input {
    position: relative
}

.form-group .date-input:before {
    content: "\e972";
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 15px;
    color: #047bf8;
    position: absolute;
    top: 50%;
    left: 10px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.form-group .date-input .form-control {
    padding-left: 35px
}

.form-buttons-w.compact {
    margin-top: 0.5rem
}

.form-buttons-w .btn+.btn,
.form-buttons-w .all-wrapper .fc-button+.btn,
.all-wrapper .form-buttons-w .fc-button+.btn,
.form-buttons-w .all-wrapper .btn+.fc-button,
.all-wrapper .form-buttons-w .btn+.fc-button,
.form-buttons-w .all-wrapper .fc-button+.fc-button,
.all-wrapper .form-buttons-w .fc-button+.fc-button {
    margin-left: 10px
}

label.bigger {
    font-size: 1.08rem;
    margin-bottom: 1rem;
    margin-top: 1rem
}

label.lighter {
    color: rgba(90, 99, 126, 0.49)
}

legend {
    font-size: .99rem;
    display: block;
    margin-bottom: 1.5rem;
    position: relative;
    color: #047bf8
}

.nav.smaller {
    font-size: .72rem
}

.nav.upper {
    font-size: .9rem;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 2px
}

.nav-link span {
    display: block;
    font-size: .72rem
}

.nav-tabs .nav-link.active:after,
.nav-tabs .active .nav-link:after,
.nav-tabs .nav-item.show .nav-link:after,
.nav-tabs .nav-link:hover:after,
.nav-tabs .nav-item:hover .nav-link:after {
    width: 100%
}

.toggled-buttons .btn-toggled {
    border: 2px solid #047bf8;
    border-radius: 4px
}

.btn-sm,
.btn-group-sm>.btn,
.all-wrapper .btn-group-sm>.fc-button {
    padding: .2rem .5rem;
    font-size: .675rem;
    border-radius: .2rem;
    text-transform: uppercase
}

.btn-white,
.all-wrapper .fc-button {
    color: #333;
    background-color: #fff;
    border-color: rgba(0, 0, 0, 0.5)
}

.btn-white:hover,
.all-wrapper .fc-button:hover {
    color: #333;
    background-color: #e6e5e5;
    border-color: rgba(0, 0, 0, 0.5)
}

.btn-white:focus,
.all-wrapper .fc-button:focus,
.btn-white.focus,
.all-wrapper .focus.fc-button {
    -webkit-box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.5)
}

.btn-white.disabled,
.all-wrapper .disabled.fc-button,
.btn-white:disabled,
.all-wrapper .fc-button:disabled {
    background-color: #fff;
    border-color: rgba(0, 0, 0, 0.5)
}

.btn-white:active,
.all-wrapper .fc-button:active,
.btn-white.active,
.all-wrapper .active.fc-button,
.show>.btn-white.dropdown-toggle,
.all-wrapper .show>.dropdown-toggle.fc-button {
    color: #333;
    background-color: #e6e5e5;
    background-image: none;
    border-color: rgba(0, 0, 0, 0.5)
}

.btn-grey {
    color: #464D6C;
    background-color: #E5E6EB;
    border-color: #E5E6EB
}

.btn-grey:hover {
    color: #464D6C;
    background-color: #c8cad5;
    border-color: #c2c5d0
}

.btn-grey:focus,
.btn-grey.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(229, 230, 235, 0.5);
    box-shadow: 0 0 0 2px rgba(229, 230, 235, 0.5)
}

.btn-grey.disabled,
.btn-grey:disabled {
    background-color: #E5E6EB;
    border-color: #E5E6EB
}

.btn-grey:active,
.btn-grey.active,
.show>.btn-grey.dropdown-toggle {
    color: #464D6C;
    background-color: #c8cad5;
    background-image: none;
    border-color: #c2c5d0
}

.btn-white-gold {
    color: #CF8D0D;
    background-color: #fff;
    border-color: #fff
}

.btn-white-gold:hover {
    color: #CF8D0D;
    background-color: #e6e5e5;
    border-color: #e0e0e0
}

.btn-white-gold:focus,
.btn-white-gold.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5)
}

.btn-white-gold.disabled,
.btn-white-gold:disabled {
    background-color: #fff;
    border-color: #fff
}

.btn-white-gold:active,
.btn-white-gold.active,
.show>.btn-white-gold.dropdown-toggle {
    color: #CF8D0D;
    background-color: #e6e5e5;
    background-image: none;
    border-color: #e0e0e0
}

.btn-teal {
    color: #fff;
    background-color: #31C37F;
    border-color: transparent
}

.btn-teal:hover {
    color: #fff;
    background-color: #279a64;
    border-color: transparent
}

.btn-teal:focus,
.btn-teal.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.5)
}

.btn-teal.disabled,
.btn-teal:disabled {
    background-color: #31C37F;
    border-color: transparent
}

.btn-teal:active,
.btn-teal.active,
.show>.btn-teal.dropdown-toggle {
    color: #fff;
    background-color: #279a64;
    background-image: none;
    border-color: transparent
}

.btn-link.btn-danger {
    color: #e65252
}

.btn,
.all-wrapper .fc-button {
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 400;
    outline: none;
    line-height: 1
}

.btn.btn-secondary:focus,
.all-wrapper .btn-secondary.fc-button:focus,
.btn.btn-secondary.focus,
.all-wrapper .btn-secondary.focus.fc-button {
    -webkit-box-shadow: none;
    box-shadow: none
}

.btn .os-icon,
.all-wrapper .fc-button .os-icon {
    font-size: .9rem;
    display: inline-block;
    vertical-align: middle
}

.btn .os-icon+span,
.all-wrapper .fc-button .os-icon+span {
    margin-left: 10px;
    display: inline-block;
    vertical-align: middle
}

.btn span+.os-icon,
.all-wrapper .fc-button span+.os-icon {
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px
}

.btn.btn-sm .os-icon,
.btn-group-sm>.btn .os-icon,
.all-wrapper .btn-group-sm>.fc-button .os-icon,
.all-wrapper .btn-sm.fc-button .os-icon {
    font-size: 14px
}

.btn.btn-sm .os-icon+span,
.btn-group-sm>.btn .os-icon+span,
.all-wrapper .btn-group-sm>.fc-button .os-icon+span,
.all-wrapper .btn-sm.fc-button .os-icon+span {
    margin-left: .5rem
}

.btn.btn-sm span+.os-icon,
.btn-group-sm>.btn span+.os-icon,
.all-wrapper .btn-group-sm>.fc-button span+.os-icon,
.all-wrapper .btn-sm.fc-button span+.os-icon {
    margin-left: .5rem
}

.btn.btn-rounded,
.all-wrapper .btn-rounded.fc-button {
    border-radius: 40px
}

.btn.btn-upper,
.all-wrapper .btn-upper.fc-button {
    text-transform: uppercase;
    letter-spacing: 2px;
    line-height: 1
}

.btn.btn-underlined,
.all-wrapper .btn-underlined.fc-button {
    background-color: transparent;
    padding-left: 0px;
    padding-right: 0px;
    text-decoration: none
}

.btn.btn-underlined span,
.all-wrapper .btn-underlined.fc-button span {
    border-bottom: 2px solid #047bf8
}

.btn.btn-underlined i,
.all-wrapper .btn-underlined.fc-button i {
    font-size: .63rem;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.btn.btn-underlined:hover,
.all-wrapper .btn-underlined.fc-button:hover {
    color: #0362c6
}

.btn.btn-underlined:hover span,
.all-wrapper .btn-underlined.fc-button:hover span {
    border-color: #0362c6
}

.btn.btn-underlined:hover i,
.all-wrapper .btn-underlined.fc-button:hover i {
    -webkit-transform: translateX(5px);
    transform: translateX(5px)
}

.btn.btn-underlined.btn-gold,
.all-wrapper .btn-underlined.btn-gold.fc-button {
    color: #BE8B1C
}

.btn.btn-underlined.btn-gold span,
.all-wrapper .btn-underlined.btn-gold.fc-button span {
    border-color: #BE8B1C
}

.btn.btn-underlined.btn-gold:hover,
.all-wrapper .btn-underlined.btn-gold.fc-button:hover {
    color: #926a15
}

.btn.btn-underlined.btn-gold:hover span,
.all-wrapper .btn-underlined.btn-gold.fc-button:hover span {
    border-color: #926a15
}

.breadcrumb {
    list-style: none;
    margin: 0px;
    padding: 10px 30px 10px 30px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background-color: transparent
}

.breadcrumb li {
    margin-bottom: 0px;
    display: inline-block;
    text-transform: uppercase;
    font-size: .585rem;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif
}

.breadcrumb li a {
    color: #3E4B5B
}

.breadcrumb li span {
    color: rgba(0, 0, 0, 0.4)
}

.breadcrumbs+.content-box {
    padding-top: 0px
}

.text-muted,
.invoice-body .invoice-desc .desc-value {
    font-weight: 300
}

.modal-content {
    -webkit-box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.5);
    box-shadow: 0px 10px 60px rgba(0, 0, 0, 0.5)
}

.modal-footer.buttons-on-left {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.modal-header.faded {
    background-color: rgba(0, 0, 0, 0.05)
}

.modal-header.smaller {
    font-size: .99rem
}

.modal-header span,
.modal-header strong,
.modal-header .avatar {
    display: inline-block;
    vertical-align: middle
}

.modal-header span {
    color: #999;
    margin-right: 5px
}

.modal-header .avatar {
    border-radius: 50%;
    width: 40px;
    height: auto
}

.modal-header .avatar+span {
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    padding-left: 15px;
    margin-left: 15px
}

.popover {
    font-size: .99rem;
    -webkit-box-shadow: 0 5px 50px rgba(4, 123, 248, 0.3);
    box-shadow: 0 5px 50px rgba(4, 123, 248, 0.3)
}

.popover-title {
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: .9rem;
    color: #fff
}

.alert {
    font-weight: 300
}

.alert.alert-warning .alert-heading {
    color: #3E3221
}

.alert.borderless {
    border: none
}

.badge {
    font-size: .81rem
}

.br-theme-osadmin .br-widget {
    height: 28px;
    white-space: nowrap
}

.br-theme-osadmin .br-widget a {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-decoration: none;
    margin-right: 2px
}

.br-theme-osadmin .br-widget a:after {
    content: '\e970';
    color: #d2d2d2
}

.br-theme-osadmin .br-widget a.br-active:after {
    color: #EDB867
}

.br-theme-osadmin .br-widget a.br-selected:after {
    color: #EDB867
}

.br-theme-osadmin .br-widget .br-current-rating {
    display: none
}

.br-theme-osadmin .br-readonly a {
    cursor: default
}

@media print {
    .br-theme-osadmin .br-widget a:after {
        content: '\f006';
        color: black
    }
    .br-theme-osadmin .br-widget a.br-active:after,
    .br-theme-osadmin .br-widget a.br-selected:after {
        content: '\e970';
        color: black
    }
}

.irs-line-mid,
.irs-line-left,
.irs-line-right,
.irs-bar,
.irs-bar-edge,
.irs-slider {
    background-color: #dddddd
}

.irs {
    height: 40px
}

.irs-with-grid {
    height: 60px
}

.irs-line {
    height: 5px;
    top: 25px;
    border-radius: 2px
}

.irs-line-left {
    height: 12px
}

.irs-line-mid {
    height: 12px
}

.irs-line-right {
    height: 12px
}

.irs-bar {
    height: 5px;
    top: 25px;
    background-color: #98c9fd
}

.irs-bar-edge {
    top: 25px;
    height: 12px;
    width: 9px
}

.irs-shadow {
    height: 3px;
    top: 34px;
    background: #000;
    opacity: 0.25
}

.lt-ie9 .irs-shadow {
    filter: alpha(opacity=25)
}

.irs-slider {
    width: 11px;
    height: 11px;
    top: 22px;
    background-color: #047bf8;
    -webkit-box-shadow: 0px 0px 0px 2px #fff;
    box-shadow: 0px 0px 0px 2px #fff;
    border-radius: 20px;
    cursor: pointer
}

.irs-slider:hover {
    background-color: #024994
}

.irs-min,
.irs-max {
    color: #999;
    font-size: 10px;
    line-height: 1.333;
    text-shadow: none;
    top: 0;
    padding: 1px 3px;
    background: #e1e4e9;
    border-radius: 4px
}

.irs-from,
.irs-to,
.irs-single {
    color: #fff;
    font-size: 10px;
    line-height: 1.333;
    text-shadow: none;
    padding: 1px 5px;
    background: #ed5565;
    border-radius: 4px
}

.irs-from:after,
.irs-to:after,
.irs-single:after {
    position: absolute;
    display: block;
    content: "";
    bottom: -6px;
    left: 50%;
    width: 0;
    height: 0;
    margin-left: -3px;
    overflow: hidden;
    border: 3px solid transparent;
    border-top-color: #ed5565
}

.irs-grid-pol {
    background: #e1e4e9
}

.irs-grid-text {
    color: #999
}

.dropzone {
    border: 2px dashed #047bf8
}

.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
    border-color: #cecece
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #e2ebff;
    border: 1px solid #4771d2
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: #474a50;
    margin-right: 4px
}

.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: #eff2ff
}

.form-control {
    font-weight: 300
}

.select2 {
    font-weight: 300
}

body.pages .daterangepicker {
    -webkit-box-shadow: 3px 3px 40px rgba(0, 0, 0, 0.2);
    box-shadow: 3px 3px 40px rgba(0, 0, 0, 0.2);
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif
}

body.pages .daterangepicker td.in-range {
    background-color: #bdd5ff
}

body.pages .daterangepicker td.active,
body.pages .daterangepicker td.active:hover {
    background-color: #047bf8
}

body.pages .daterangepicker th {
    font-weight: 500
}

body.pages .daterangepicker:before {
    border-bottom-color: #3E4B5B
}

body.pages .daterangepicker .calendar td {
    font-weight: 300;
    font-size: .81rem
}

body.pages .daterangepicker .calendar th.month {
    color: #047bf8
}

body.pages .daterangepicker thead tr:first-child th {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 5px
}

body.pages .daterangepicker thead tr:first-child+tr th {
    padding-top: 10px
}

body.pages .daterangepicker .daterangepicker_input i {
    left: 4px;
    top: 3px;
    font-size: 18px
}

body.pages .daterangepicker .fa.fa-calendar.glyphicon.glyphicon-calendar:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e926"
}

body.pages .daterangepicker .fa.fa-chevron-left.glyphicon.glyphicon-chevron-left:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e919";
    font-size: 10px
}

body.pages .daterangepicker .fa.fa-chevron-right.glyphicon.glyphicon-chevron-right:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e910";
    font-size: 10px
}

.dataTables_length select {
    display: inline-block;
    width: 50px;
    margin: 0px 5px;
    vertical-align: middle
}

.dataTables_filter input {
    display: inline-block;
    width: 130px;
    margin: 0px 5px;
    vertical-align: middle
}

.dataTables_wrapper .row:first-child {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    margin-top: 1rem;
    padding-bottom: 0.5rem
}

.dataTables_wrapper .row:last-child {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
    margin-bottom: 1rem;
    padding-top: 0.5rem
}

.fc-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.fc-header td {
    padding: 10px 0px
}

.fc-header h2 {
    text-transform: uppercase;
    font-size: 18px
}

.fc-content {
    color: #fff
}

.fc-event {
    background-color: #3584ff;
    -webkit-box-shadow: 5px 5px 10px 0px #bdd4ff;
    box-shadow: 5px 5px 10px 0px #bdd4ff;
    border: none;
    padding: 6px;
    padding-left: 9px;
    color: #fff;
    border-radius: 4px
}

.fc-day-number {
    color: #6B6862
}

.fc-day-header {
    font-weight: 300;
    color: #6B6862;
    text-transform: uppercase;
    font-size: 12px
}

.fc-other-month {
    background-color: #eee
}

.all-wrapper .fc-button {
    padding: 5px 10px;
    height: auto;
    margin: 0px 5px;
    background-image: none;
    -webkit-box-shadow: none;
    box-shadow: none
}

.all-wrapper .fc-button.fc-state-active {
    outline: none;
    text-shadow: none
}

table.dataTable {
    clear: both;
    margin-top: 6px !important;
    margin-bottom: 6px !important;
    max-width: none !important;
    border-collapse: separate !important
}

table.dataTable td,
table.dataTable th {
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

table.dataTable td.dataTables_empty,
table.dataTable th.dataTables_empty {
    text-align: center
}

table.dataTable.nowrap th,
table.dataTable.nowrap td {
    white-space: nowrap
}

div.dataTables_wrapper div.dataTables_length label {
    font-weight: normal;
    text-align: left;
    white-space: nowrap
}

div.dataTables_wrapper div.dataTables_length select {
    width: 75px;
    display: inline-block
}

div.dataTables_wrapper div.dataTables_filter {
    text-align: right
}

div.dataTables_wrapper div.dataTables_filter label {
    font-weight: normal;
    white-space: nowrap;
    text-align: left
}

div.dataTables_wrapper div.dataTables_filter input {
    margin-left: 0.5em;
    display: inline-block;
    width: auto
}

div.dataTables_wrapper div.dataTables_info {
    padding-top: 0.85em;
    white-space: nowrap
}

div.dataTables_wrapper div.dataTables_paginate {
    margin: 0;
    white-space: nowrap;
    text-align: right
}

div.dataTables_wrapper div.dataTables_paginate ul.pagination {
    margin: 2px 0;
    white-space: nowrap;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

div.dataTables_wrapper div.dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    margin-left: -100px;
    margin-top: -26px;
    text-align: center;
    padding: 1em 0
}

table.dataTable thead>tr>th.sorting_asc,
table.dataTable thead>tr>th.sorting_desc,
table.dataTable thead>tr>th.sorting,
table.dataTable thead>tr>td.sorting_asc,
table.dataTable thead>tr>td.sorting_desc,
table.dataTable thead>tr>td.sorting {
    padding-right: 30px
}

table.dataTable thead>tr>th:active,
table.dataTable thead>tr>td:active {
    outline: none
}

table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_desc,
table.dataTable thead .sorting_asc_disabled,
table.dataTable thead .sorting_desc_disabled {
    cursor: pointer;
    position: relative
}

table.dataTable thead .sorting:before,
table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:before,
table.dataTable thead .sorting_desc_disabled:after {
    position: absolute;
    bottom: 0.9em;
    display: block;
    opacity: 0.3
}

table.dataTable thead .sorting:before,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_desc_disabled:before {
    right: 1em;
    content: "\2191"
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:after {
    right: 0.5em;
    content: "\2193"
}

table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_desc:after {
    opacity: 1
}

table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_desc_disabled:after {
    opacity: 0
}

div.dataTables_scrollHead table.dataTable {
    margin-bottom: 0 !important
}

div.dataTables_scrollBody table {
    border-top: none;
    margin-top: 0 !important;
    margin-bottom: 0 !important
}

div.dataTables_scrollBody table thead .sorting:after,
div.dataTables_scrollBody table thead .sorting_asc:after,
div.dataTables_scrollBody table thead .sorting_desc:after {
    display: none
}

div.dataTables_scrollBody table tbody tr:first-child th,
div.dataTables_scrollBody table tbody tr:first-child td {
    border-top: none
}

div.dataTables_scrollFoot table {
    margin-top: 0 !important;
    border-top: none
}

@media screen and (max-width: 767px) {
    div.dataTables_wrapper div.dataTables_length,
    div.dataTables_wrapper div.dataTables_filter,
    div.dataTables_wrapper div.dataTables_info,
    div.dataTables_wrapper div.dataTables_paginate {
        text-align: center
    }
}

table.dataTable.table-condensed>thead>tr>th {
    padding-right: 20px
}

table.dataTable.table-condensed .sorting:after,
table.dataTable.table-condensed .sorting_asc:after,
table.dataTable.table-condensed .sorting_desc:after {
    top: 6px;
    right: 6px
}

table.table-bordered.dataTable th,
table.table-bordered.dataTable td {
    border-left-width: 0
}

table.table-bordered.dataTable th:last-child,
table.table-bordered.dataTable th:last-child,
table.table-bordered.dataTable td:last-child,
table.table-bordered.dataTable td:last-child {
    border-right-width: 0
}

table.table-bordered.dataTable tbody th,
table.table-bordered.dataTable tbody td {
    border-bottom-width: 0
}

div.dataTables_scrollHead table.table-bordered {
    border-bottom-width: 0
}

div.table-responsive>div.dataTables_wrapper>div.row {
    margin: 0
}

div.table-responsive>div.dataTables_wrapper>div.row>div[class^="col-"]:first-child {
    padding-left: 0
}

div.table-responsive>div.dataTables_wrapper>div.row>div[class^="col-"]:last-child {
    padding-right: 0
}

@-webkit-keyframes fadeLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(50px);
        transform: translateX(50px)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0px);
        transform: translateX(0px)
    }
}

@keyframes fadeLeft {
    0% {
        opacity: 0;
        -webkit-transform: translateX(50px);
        transform: translateX(50px)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0px);
        transform: translateX(0px)
    }
}

@-webkit-keyframes fadeRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-50px);
        transform: translateX(-50px)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0px);
        transform: translateX(0px)
    }
}

@keyframes fadeRight {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-50px);
        transform: translateX(-50px)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateX(0px);
        transform: translateX(0px)
    }
}

@-webkit-keyframes fadeUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(30px);
        transform: translateY(30px)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0px);
        transform: translateY(0px)
    }
}

@keyframes fadeUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(30px);
        transform: translateY(30px)
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0px);
        transform: translateY(0px)
    }
}

@-webkit-keyframes jumpUp {
    0% {
        -webkit-transform: scale(0.5) translate(70px, 70px);
        transform: scale(0.5) translate(70px, 70px)
    }
    80% {
        -webkit-transform: scale(1.1) translate(-10px, -10px);
        transform: scale(1.1) translate(-10px, -10px)
    }
    100% {
        -webkit-transform: scale(1) translate(0px);
        transform: scale(1) translate(0px)
    }
}

@keyframes jumpUp {
    0% {
        -webkit-transform: scale(0.5) translate(70px, 70px);
        transform: scale(0.5) translate(70px, 70px)
    }
    80% {
        -webkit-transform: scale(1.1) translate(-10px, -10px);
        transform: scale(1.1) translate(-10px, -10px)
    }
    100% {
        -webkit-transform: scale(1) translate(0px);
        transform: scale(1) translate(0px)
    }
}

.floated-chat-btn {
    -webkit-animation-name: jumpUp;
    animation-name: jumpUp;
    -webkit-animation-duration: 0.5s;
    animation-duration: 0.5s
}

.element-box,
.invoice-w,
.big-error-w {
    -webkit-animation-name: fadeUp;
    animation-name: fadeUp;
    -webkit-animation-duration: 1s;
    animation-duration: 1s
}

.element-balances .balance {
    -webkit-animation-name: fadeRight;
    animation-name: fadeRight;
    -webkit-animation-duration: 0.5s;
    animation-duration: 0.5s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.element-balances .balance:nth-child(1) {
    -webkit-animation-delay: 0s;
    animation-delay: 0s
}

.element-balances .balance:nth-child(2) {
    -webkit-animation-delay: 0.1s;
    animation-delay: 0.1s
}

.element-balances .balance:nth-child(3) {
    -webkit-animation-delay: 0.2s;
    animation-delay: 0.2s
}

.element-balances .balance:nth-child(4) {
    -webkit-animation-delay: 0.3s;
    animation-delay: 0.3s
}

.element-balances .balance:nth-child(5) {
    -webkit-animation-delay: 0.4s;
    animation-delay: 0.4s
}

.pipeline {
    -webkit-animation-name: fadeUp;
    animation-name: fadeUp;
    -webkit-animation-duration: 1s;
    animation-duration: 1s
}

.project-box {
    -webkit-animation-name: fadeUp;
    animation-name: fadeUp;
    -webkit-animation-duration: 1s;
    animation-duration: 1s
}

.aec-full-message-w {
    -webkit-animation-name: fadeUp;
    animation-name: fadeUp;
    -webkit-animation-duration: 1s;
    animation-duration: 1s
}

.user-profile {
    -webkit-animation-name: fadeUp;
    animation-name: fadeUp;
    -webkit-animation-duration: 1s;
    animation-duration: 1s
}

.content-panel {
    -webkit-animation-name: fadeLeft;
    animation-name: fadeLeft;
    -webkit-animation-duration: 1s;
    animation-duration: 1s
}

.user-profile .up-head-w {
    background-size: cover;
    background-position: center center;
    position: relative;
    color: #fff;
    border-radius: 4px 4px 0px 0px
}

.user-profile .up-head-w .decor {
    position: absolute;
    bottom: -1px;
    right: 0px;
    max-width: 100%;
    z-index: 3
}

.user-profile .up-head-w .decor .decor-path {
    fill: #fff
}

.user-profile .up-head-w:before {
    z-index: 1;
    content: '';
    position: absolute;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    background-image: -webkit-gradient(linear, left top, left bottom, from(transparent), color-stop(rgba(0, 0, 0, 0.2)), color-stop(70%), to(rgba(0, 0, 0, 0.5)));
    background-image: linear-gradient(transparent, rgba(0, 0, 0, 0.2), 70%, rgba(0, 0, 0, 0.5))
}

.user-profile .up-head-w .up-social {
    position: absolute;
    top: 15px;
    right: 25px;
    z-index: 5
}

.user-profile .up-head-w .up-social a {
    color: rgba(255, 255, 255, 0.8);
    display: inline-block;
    font-size: 30px;
    margin-left: 10px
}

.user-profile .up-head-w .up-social a:hover {
    color: #fff;
    text-decoration: none
}

.user-profile .up-main-info {
    padding: 20% 5% 5% 5%;
    position: relative;
    z-index: 4
}

.user-profile .up-main-info .user-avatar-w {
    margin-bottom: 1rem
}

.user-profile .up-main-info .user-avatar-w .user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 40px;
    border: 3px solid #fff;
    overflow: hidden;
    -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.4);
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.4)
}

.user-profile .up-main-info .user-avatar-w .user-avatar img {
    max-width: 100%;
    height: auto
}

.user-profile .up-header {
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
    color: #fff;
    font-size: 3.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.4);
    padding-bottom: 10px;
    display: inline-block;
    margin-bottom: 10px
}

.user-profile .up-sub-header {
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.4);
    color: rgba(255, 255, 255, 0.8)
}

.user-profile .up-controls {
    padding: 20px 5%
}

.user-profile .btn+.btn,
.user-profile .all-wrapper .fc-button+.btn,
.all-wrapper .user-profile .fc-button+.btn,
.user-profile .all-wrapper .btn+.fc-button,
.all-wrapper .user-profile .btn+.fc-button,
.user-profile .all-wrapper .fc-button+.fc-button,
.all-wrapper .user-profile .fc-button+.fc-button {
    margin-left: 20px
}

.user-profile .up-controls+.up-contents {
    border-top: 1px solid rgba(0, 0, 0, 0.05)
}

.user-profile .up-contents {
    padding: 20px 5%;
    padding-top: 3rem
}

.user-profile.compact .up-contents {
    padding-top: 0px
}

.user-profile.compact .up-main-info {
    padding-top: 35%
}

.user-profile.compact .up-head-w .up-social a {
    font-size: 20px
}

.user-profile.compact .up-header {
    font-size: 1.5rem;
    margin-bottom: 10px;
    padding-bottom: 5px
}

.user-profile.compact .up-sub-header {
    font-size: .81rem
}

.user-profile+.element-wrapper {
    margin-top: 2rem
}

.value-pair {
    display: inline-block
}

.value-pair .label {
    color: rgba(0, 0, 0, 0.4);
    font-size: .63rem;
    text-transform: uppercase;
    display: inline-block
}

.value-pair .value {
    display: inline-block;
    font-size: .63rem;
    text-transform: uppercase
}

.value-pair+.value-pair {
    margin-left: 10px;
    padding-left: 10px;
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

body.pages {
    min-height: 100%;
    position: relative;
    padding: 50px;
    overflow-x: hidden
}

body.pages:before {
    content: "";
    position: absolute;
    z-index: -1;
    background: -webkit-gradient(linear, left top, right bottom, from(#D7BBEA), to(#65A8F1));
    background: linear-gradient(to bottom right, #D7BBEA, #65A8F1);
    top: 0px;
    left: 0px;
    bottom: 0px;
    right: 0px
}

b,
strong {
    font-weight: 500
}

.all-wrapper {
    -webkit-box-shadow: 0px 0px 40px rgba(0, 0, 0, 0.1);
    box-shadow: 0px 0px 40px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    max-width: 1600px;
    margin: 0px auto;
    position: relative;
    min-height: 100%
}

.all-wrapper.with-pattern {
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 0px;
    background-image: url("../img/bg-pattern2.png");
    background-size: contain;
    background-repeat: repeat;
    background-position: 0 0
}

.all-wrapper.no-padding-content .content-box {
    padding: 0px
}

.all-wrapper.white-bg-content .content-w {
    background-color: #fff;
    background-image: none
}

.all-wrapper.solid-bg-all {
    background-color: #f2f4f8
}

.all-wrapper.solid-bg-all .content-w {
    background-image: none
}

.auth-wrapper .all-wrapper {
    padding: 100px
}

.section-heading {
    padding: 5% 10%;
    font-size: 1.26rem;
    color: rgba(0, 0, 0, 0.5)
}

.section-heading.centered {
    text-align: center
}

.section-heading h1 {
    position: relative;
    margin-bottom: 40px
}

.section-heading h1:after {
    position: absolute;
    bottom: -25px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    content: "";
    background-color: #047bf8;
    height: 5px;
    width: 40px;
    border-radius: 4px
}

.menu-side .layout-w {
    display: table;
    width: 100%;
    table-layout: fixed
}

.menu-side .content-w {
    border-radius: 0px 4px 4px 0px;
    display: table-cell
}

.menu-top .content-w {
    border-radius: 0px 0px 4px 4px
}

.menu-side-w {
    display: table-cell;
    vertical-align: top
}

.menu-side-compact-w {
    display: table-cell;
    vertical-align: top
}

.content-w {
    background-color: #f2f4f8;
    background-image: url("../img/bg-pattern.png");
    background-repeat: no-repeat;
    background-position: 20px 50px;
    vertical-align: top
}

.content-box {
    vertical-align: top;
    padding: 2rem 3rem
}

.content-panel {
    vertical-align: top;
    width: 400px;
    border-left: 1px solid rgba(0, 0, 0, 0.05);
    padding: 2rem 3rem;
    display: table-cell
}

.content-panel .content-panel-close {
    display: none;
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 14px;
    color: #047bf8;
    z-index: 999
}

.content-panel-toggler {
    background-color: #047bf8;
    padding: 4px 10px;
    border-radius: 4px;
    color: #fff;
    font-size: .72rem;
    text-transform: uppercase;
    display: none;
    position: absolute;
    top: 4px;
    right: 4px;
    z-index: 4
}

.content-panel-toggler i {
    display: inline-block;
    vertical-align: middle;
    font-size: 12px
}

.content-panel-toggler span {
    margin-left: 10px;
    display: inline-block;
    vertical-align: middle
}

.with-side-panel .content-box {
    display: table-cell
}

.with-side-panel .content-i {
    display: table;
    width: 100%;
    table-layout: fixed
}

.with-side-panel .menu-mobile .mm-logo-buttons-w .content-panel-open {
    display: block
}

.demo-icons-list {
    list-style: none;
    padding: 0px;
    margin: 0px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.demo-icons-list li {
    width: 80px;
    text-align: center;
    display: inline-block;
    font-size: 24px;
    vertical-align: middle;
    padding: 20px 15px;
    border-right: 1px solid rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.demo-icons-list li a {
    position: relative;
    color: #333
}

.demo-icons-list li a i {
    font-style: normal
}

.demo-icons-list li a span {
    display: inline-block;
    position: absolute;
    background-color: #047bf8;
    color: #fff;
    padding: 4px 7px;
    border-radius: 4px;
    font-size: .81rem;
    white-space: nowrap;
    top: -30px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    display: none
}

.demo-icons-list li a:hover {
    text-decoration: none
}

.demo-icons-list li a:hover span {
    display: block
}

.menu-mobile {
    background-color: #fff;
    -webkit-box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1);
    box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1);
    position: relative;
    display: none
}

.menu-mobile .mm-logo-buttons-w {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 1rem
}

.menu-mobile .mm-logo-buttons-w .mm-logo {
    vertical-align: middle;
    text-align: left;
    text-decoration: none
}

.menu-mobile .mm-logo-buttons-w .mm-logo img {
    width: 40px;
    display: inline-block
}

.menu-mobile .mm-logo-buttons-w .mm-logo span {
    display: inline-block;
    color: #3E4B5B;
    margin-left: 1rem
}

.menu-mobile .mm-logo-buttons-w .mm-logo:hover {
    text-decoration: none
}

.menu-mobile .mm-logo-buttons-w .mm-buttons {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.menu-mobile .mm-logo-buttons-w .content-panel-open {
    display: none;
    font-size: 18px;
    cursor: pointer;
    margin-right: 20px;
    padding-right: 20px;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    color: #047bf8
}

.menu-mobile .mm-logo-buttons-w .mobile-menu-trigger {
    vertical-align: middle;
    text-align: right;
    font-size: 18px;
    cursor: pointer;
    color: #047bf8
}

.menu-mobile .menu-and-user {
    display: none;
    padding-bottom: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

.menu-mobile .logged-user-w {
    text-align: left;
    padding: 1rem;
    padding-left: 35px
}

.menu-mobile .logged-user-w .avatar-w {
    vertical-align: middle
}

.menu-mobile .logged-user-w .avatar-w img {
    width: 40px
}

.menu-mobile .logged-user-w .logged-user-info-w {
    display: inline-block;
    vertical-align: middle;
    margin-left: 15px
}

.menu-mobile .mobile-menu-magic {
    background-image: linear-gradient(-154deg, #1643A3 8%, #2E1170 90%);
    border-radius: 6px;
    padding: 40px;
    text-align: center;
    margin: 10px 20px
}

.menu-mobile .mobile-menu-magic:last-child {
    margin-bottom: 0px
}

.menu-mobile .mobile-menu-magic h1,
.menu-mobile .mobile-menu-magic h2,
.menu-mobile .mobile-menu-magic h3,
.menu-mobile .mobile-menu-magic h4,
.menu-mobile .mobile-menu-magic h5 {
    color: #fff;
    margin-bottom: 5px
}

.menu-mobile .mobile-menu-magic p {
    color: rgba(255, 255, 255, 0.6)
}

.menu-mobile .mobile-menu-magic .btn-w {
    margin-top: 15px
}

.menu-mobile .mobile-menu-magic .btn-white,
.menu-mobile .mobile-menu-magic .all-wrapper .fc-button,
.all-wrapper .menu-mobile .mobile-menu-magic .fc-button {
    border: none;
    -webkit-box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.3);
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.3)
}

.menu-mobile ul.main-menu {
    list-style: none;
    padding: 10px 1rem;
    margin-bottom: 0px
}

.menu-mobile ul.main-menu>li {
    display: block;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.menu-mobile ul.main-menu>li:last-child {
    border-bottom: none
}

.menu-mobile ul.main-menu>li.has-sub-menu>a:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e91c";
    font-size: 7px;
    color: rgba(0, 0, 0, 0.5);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    position: absolute;
    top: 50%;
    right: 10px
}

.menu-mobile ul.main-menu>li.has-sub-menu.active .sub-menu {
    display: block
}

.menu-mobile ul.main-menu>li>a {
    color: #3E4B5B;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative
}

.menu-mobile ul.main-menu>li>a:focus {
    text-decoration: none
}

.menu-mobile ul.main-menu>li>a:hover {
    text-decoration: none
}

@media (min-width: 1025px) {
    .menu-mobile ul.main-menu>li>a:hover .icon-w {
        -webkit-transform: translateX(10px);
        transform: translateX(10px)
    }
    .menu-mobile ul.main-menu>li>a:hover span {
        -webkit-transform: translateX(5px);
        transform: translateX(5px)
    }
}

.menu-mobile ul.main-menu>li .icon-w {
    color: #0073ff;
    font-size: 27px;
    display: block;
    padding: 1rem;
    width: 80px;
    text-align: center;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-mobile ul.main-menu>li span {
    padding: 1rem;
    display: block;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-mobile ul.main-menu>li .icon-w+span {
    padding-left: 0px
}

.menu-mobile ul.sub-menu {
    padding: 1rem 0px;
    padding-left: 55px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: none
}

.menu-mobile ul.sub-menu li {
    padding: 0.4rem 10px 0.4rem 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.menu-mobile ul.sub-menu li:last-child {
    border-bottom: none
}

.menu-mobile ul.sub-menu li a {
    font-size: .81rem
}

.menu-mobile.color-scheme-dark {
    background-image: -webkit-gradient(linear, left top, left bottom, from(#3D4D75), to(#31395B));
    background-image: linear-gradient(to bottom, #3D4D75 0%, #31395B 100%);
    background-repeat: repeat-x;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#1c4cc3), to(#1c2e7b));
    background-image: linear-gradient(to bottom, #1c4cc3 0%, #1c2e7b 100%);
    background-repeat: repeat-x;
    color: rgba(255, 255, 255, 0.9)
}

.menu-mobile.color-scheme-dark .side-menu-magic {
    background-image: linear-gradient(-154deg, #6d16a3 8%, #5211e6 90%)
}

.menu-mobile.color-scheme-dark ul.sub-menu li {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.menu-mobile.color-scheme-dark ul.sub-menu li a {
    color: #fff
}

.menu-mobile.color-scheme-dark ul.main-menu .icon-w {
    color: #9db2ff
}

.menu-mobile.color-scheme-dark ul.main-menu>li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05)
}

.menu-mobile.color-scheme-dark ul.main-menu>li>a {
    color: #fff
}

.menu-mobile.color-scheme-dark ul.main-menu>li>a:before {
    color: #fff
}

.menu-mobile.color-scheme-dark .sub-menu-w {
    -webkit-box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2);
    box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2)
}

.menu-mobile.color-scheme-dark .mm-logo-buttons-w {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.menu-mobile.color-scheme-dark .mm-logo-buttons-w span {
    color: #fff
}

.menu-mobile.color-scheme-dark .mm-logo-buttons-w .content-panel-open {
    border-right-color: rgba(255, 255, 255, 0.1)
}

.menu-mobile.color-scheme-dark .mm-logo-buttons-w .content-panel-open,
.menu-mobile.color-scheme-dark .mm-logo-buttons-w .mobile-menu-trigger {
    color: #fff
}

.menu-mobile.color-scheme-dark .logged-user-w {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.menu-mobile.color-scheme-dark .logged-user-w .avatar-w {
    border-color: #fff
}

.menu-mobile.color-scheme-dark .logged-user-w .logged-user-role {
    color: rgba(255, 255, 255, 0.4)
}

.menu-mobile.color-scheme-dark .mobile-menu-magic {
    background-image: linear-gradient(-154deg, #6d16a3 8%, #5211e6 90%)
}

.menu-side-w {
    background-color: #fff;
    -webkit-box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1);
    box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1);
    position: relative;
    width: 300px;
    border-radius: 4px 0px 0px 4px
}

.menu-side-w .logged-user-w {
    position: relative
}

.menu-side-w .logged-user-w .logged-user-i {
    display: inline-block
}

.menu-side-w .logged-user-w .logged-user-i:hover {
    cursor: pointer
}

.menu-side-w .logged-user-w .logged-user-i:hover .logged-user-menu {
    visibility: visible;
    opacity: 1;
    -webkit-transform: translateY(0px);
    transform: translateY(0px)
}

.menu-side-w .logged-user-menu {
    background: #0061da;
    -webkit-box-shadow: 0 2px 30px 0 rgba(45, 130, 255, 0.75);
    box-shadow: 0 2px 30px 0 rgba(45, 130, 255, 0.75);
    position: absolute;
    top: 0px;
    left: -10px;
    right: -10px;
    overflow: hidden;
    padding: 1rem;
    z-index: 999;
    visibility: hidden;
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 4px
}

.menu-side-w .logged-user-menu .avatar-w {
    border-color: #fff
}

.menu-side-w .logged-user-menu .logged-user-info-w {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1)
}

.menu-side-w .logged-user-menu .logged-user-info-w .logged-user-name {
    color: #fff
}

.menu-side-w .logged-user-menu .logged-user-info-w .logged-user-role {
    color: rgba(255, 255, 255, 0.6)
}

.menu-side-w .logged-user-menu ul {
    list-style: none;
    text-align: left;
    margin: 0px;
    padding: 0px 40px;
    padding-bottom: 20px
}

.menu-side-w .logged-user-menu ul li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05)
}

.menu-side-w .logged-user-menu ul li a {
    display: block;
    padding: 10px;
    color: #fff
}

.menu-side-w .logged-user-menu ul li a i {
    vertical-align: middle;
    margin-right: 15px;
    font-size: 20px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    display: inline-block
}

.menu-side-w .logged-user-menu ul li a span {
    vertical-align: middle;
    font-size: .9rem;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    display: inline-block
}

.menu-side-w .logged-user-menu ul li a:hover {
    text-decoration: none
}

.menu-side-w .logged-user-menu ul li a:hover i {
    -webkit-transform: translateX(5px);
    transform: translateX(5px)
}

.menu-side-w .logged-user-menu ul li a:hover span {
    -webkit-transform: translateX(8px);
    transform: translateX(8px)
}

.menu-side-w .logged-user-menu ul li:last-child {
    border-bottom: none
}

.menu-side-w .logged-user-menu .bg-icon {
    font-size: 100px;
    color: rgba(255, 255, 255, 0.1);
    position: absolute;
    bottom: -40px;
    right: -20px
}

.menu-side-w .side-menu-magic {
    background-image: linear-gradient(-154deg, #1643A3 8%, #2E1170 90%);
    border-radius: 6px;
    padding: 40px;
    text-align: center;
    margin: 20px
}

.menu-side-w .side-menu-magic h1,
.menu-side-w .side-menu-magic h2,
.menu-side-w .side-menu-magic h3,
.menu-side-w .side-menu-magic h4,
.menu-side-w .side-menu-magic h5 {
    color: #fff;
    margin-bottom: 5px
}

.menu-side-w .side-menu-magic p {
    color: rgba(255, 255, 255, 0.6)
}

.menu-side-w .side-menu-magic .btn-w {
    margin-top: 15px
}

.menu-side-w .side-menu-magic .btn-white,
.menu-side-w .side-menu-magic .all-wrapper .fc-button,
.all-wrapper .menu-side-w .side-menu-magic .fc-button {
    border: none;
    -webkit-box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.3);
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.3)
}

.menu-side-w .logo-w {
    text-align: center;
    padding: 2rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.menu-side-w .logo-w a {
    display: inline-block
}

.menu-side-w .logo-w .logo {
    display: inline-block
}

.menu-side-w .logo-w img {
    width: 40px;
    height: auto;
    display: inline-block;
    vertical-align: middle
}

.menu-side-w .logo-w span {
    vertical-align: middle;
    display: inline-block;
    color: #334152;
    margin-left: 1rem
}

.menu-side-w ul.main-menu {
    list-style: none;
    padding: 2rem;
    margin-bottom: 0px
}

.menu-side-w ul.main-menu>li {
    display: block;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.menu-side-w ul.main-menu>li:last-child {
    border-bottom: none
}

.menu-side-w ul.main-menu>li.has-sub-menu>a:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e91c";
    font-size: 7px;
    color: rgba(0, 0, 0, 0.5);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    position: absolute;
    top: 50%;
    right: 10px
}

.menu-side-w ul.main-menu>li.has-sub-menu.active .sub-menu {
    display: block
}

.menu-side-w ul.main-menu>li>a {
    color: #3E4B5B;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative
}

.menu-side-w ul.main-menu>li>a:focus {
    text-decoration: none
}

.menu-side-w ul.main-menu>li>a:hover {
    text-decoration: none
}

@media (min-width: 1025px) {
    .menu-side-w ul.main-menu>li>a:hover .icon-w {
        -webkit-transform: translateX(10px);
        transform: translateX(10px)
    }
    .menu-side-w ul.main-menu>li>a:hover span {
        -webkit-transform: translateX(5px);
        transform: translateX(5px)
    }
}

.menu-side-w ul.main-menu>li .icon-w {
    color: #0073ff;
    font-size: 27px;
    display: block;
    padding: 1rem;
    width: 80px;
    text-align: center;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-side-w ul.main-menu>li span {
    padding: 1rem;
    display: block;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-side-w ul.main-menu>li .icon-w+span {
    padding-left: 0px
}

.menu-side-w ul.sub-menu {
    padding: 1rem 0px;
    padding-left: 55px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: none;
    list-style: none
}

.menu-side-w ul.sub-menu li {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.menu-side-w ul.sub-menu li:last-child {
    border-bottom: none
}

.menu-side-w ul.sub-menu li a {
    padding: 0.4rem 10px 0.4rem 10px;
    display: block;
    position: relative;
    font-size: .81rem;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-side-w ul.sub-menu li a:before {
    content: "";
    width: 5px;
    height: 5px;
    border: 1px solid #047bf8;
    position: absolute;
    left: -10px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    background-color: #fff;
    display: block;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    border-radius: 6px
}

.menu-side-w ul.sub-menu li a:hover {
    text-decoration: none;
    cursor: pointer;
    -webkit-transform: translateX(-5px);
    transform: translateX(-5px)
}

.menu-side-w ul.sub-menu li a:hover:before {
    -webkit-transform: translate(-5px, -50%);
    transform: translate(-5px, -50%);
    border-radius: 6px;
    background-color: #047bf8;
    border-color: #047bf8
}

.menu-side-w.color-scheme-dark {
    background-image: -webkit-gradient(linear, left top, left bottom, from(#3D4D75), to(#31395B));
    background-image: linear-gradient(to bottom, #3D4D75 0%, #31395B 100%);
    background-repeat: repeat-x;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#1c4cc3), to(#0f296a));
    background-image: linear-gradient(to bottom, #1c4cc3 0%, #0f296a 100%);
    background-repeat: repeat-x;
    color: rgba(255, 255, 255, 0.9)
}

.menu-side-w.color-scheme-dark .logged-user-menu {
    background-color: #5e00da;
    -webkit-box-shadow: 0 2px 30px 0 rgba(0, 0, 0, 0.25);
    box-shadow: 0 2px 30px 0 rgba(0, 0, 0, 0.25)
}

.menu-side-w.color-scheme-dark .side-menu-magic {
    background-image: linear-gradient(-154deg, #6d16a3 8%, #5211e6 90%)
}

.menu-side-w.color-scheme-dark ul.sub-menu li {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.menu-side-w.color-scheme-dark ul.sub-menu li a {
    color: #fff
}

.menu-side-w.color-scheme-dark ul.main-menu .icon-w {
    color: #9db2ff
}

.menu-side-w.color-scheme-dark ul.main-menu>li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05)
}

.menu-side-w.color-scheme-dark ul.main-menu>li>a {
    color: #fff
}

.menu-side-w.color-scheme-dark ul.main-menu>li>a:before {
    color: #fff
}

.menu-side-w.color-scheme-dark .sub-menu-w {
    -webkit-box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2);
    box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2)
}

.menu-side-w.color-scheme-dark .logo-w {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.menu-side-w.color-scheme-dark .logo-w span {
    color: #fff
}

.menu-side-w.color-scheme-dark .logged-user-w {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.menu-side-w.color-scheme-dark .logged-user-w .avatar-w {
    border-color: #fff
}

.menu-side-w.color-scheme-dark .logged-user-w .logged-user-role {
    color: rgba(255, 255, 255, 0.4)
}

.logged-user-w {
    text-align: center;
    padding: 1rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.logged-user-w .avatar-w {
    padding: 5px;
    border: 1px solid #262626;
    border-radius: 50px;
    overflow: hidden;
    display: inline-block
}

.logged-user-w .avatar-w img {
    width: 50px;
    height: auto;
    border-radius: 50px
}

.logged-user-w .logged-user-role {
    display: block;
    font-size: .63rem;
    text-transform: uppercase;
    color: rgba(0, 0, 0, 0.4)
}

.menu-side-compact-w {
    background-color: #fff;
    -webkit-box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1);
    box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1);
    position: relative;
    width: 90px;
    border-radius: 4px 0px 0px 4px
}

.menu-side-compact-w .logo-w {
    text-align: center;
    padding: 19px 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.menu-side-compact-w .logo-w a {
    display: inline-block
}

.menu-side-compact-w .logo-w .logo {
    display: inline-block
}

.menu-side-compact-w .logo-w img {
    width: 40px;
    height: auto;
    display: inline-block;
    vertical-align: middle
}

.menu-side-compact-w .logo-w span {
    vertical-align: middle;
    display: block;
    color: #334152;
    margin-top: 10px
}

.menu-side-compact-w a {
    text-decoration: none
}

.menu-side-compact-w .menu-and-user {
    padding-bottom: 50px
}

.menu-side-compact-w ul.main-menu {
    list-style: none;
    padding: 0px;
    text-align: center
}

.menu-side-compact-w ul.main-menu>li {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative
}

.menu-side-compact-w ul.main-menu>li>a {
    display: block;
    padding: 15px 10px;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease
}

.menu-side-compact-w ul.main-menu>li>a .icon-w {
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease
}

.menu-side-compact-w ul.main-menu>li:last-child {
    border-bottom: none
}

.menu-side-compact-w ul.main-menu>li.active>a {
    background-color: #0061da
}

.menu-side-compact-w ul.main-menu>li.active>a .icon-w {
    color: #fff
}

.menu-side-compact-w ul.main-menu>li.active .sub-menu-w {
    visibility: visible;
    opacity: 1;
    -webkit-transform: translateX(0px) scale(1);
    transform: translateX(0px) scale(1)
}

.menu-side-compact-w ul.main-menu .icon-w {
    font-size: 30px;
    color: #0073ff
}

.menu-side-compact-w .sub-menu-w {
    z-index: 9999;
    visibility: hidden;
    opacity: 0;
    -webkit-transform: translateX(40px) scale(0.95);
    transform: translateX(40px) scale(0.95);
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    position: absolute;
    background-color: #0061da;
    color: #fff;
    left: 100%;
    top: -100px;
    overflow: hidden;
    text-align: left;
    -webkit-box-shadow: 0px 2px 40px 0px rgba(26, 138, 255, 0.5);
    box-shadow: 0px 2px 40px 0px rgba(26, 138, 255, 0.5);
    padding-bottom: 20px
}

.menu-side-compact-w .sub-menu-w .sub-menu-title {
    font-size: 2.4rem;
    color: rgba(255, 255, 255, 0.2);
    padding: 5px 50px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 15px;
    letter-spacing: -0.5px;
    white-space: nowrap;
    overflow: hidden;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500
}

.menu-side-compact-w .sub-menu-w .sub-menu-icon {
    position: absolute;
    font-size: 120px;
    color: rgba(255, 255, 255, 0.1);
    bottom: -50px;
    right: -10px
}

.menu-side-compact-w .sub-menu-w .sub-menu-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.menu-side-compact-w .sub-menu-w .sub-menu-i .sub-menu+.sub-menu {
    border-left: 1px solid rgba(255, 255, 255, 0.1)
}

.menu-side-compact-w .sub-menu-w>ul,
.menu-side-compact-w .sub-menu-w .sub-menu-i>ul {
    list-style: none;
    padding: 0px;
    min-width: 250px
}

.menu-side-compact-w .sub-menu-w>ul>li,
.menu-side-compact-w .sub-menu-w .sub-menu-i>ul>li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1)
}

.menu-side-compact-w .sub-menu-w>ul>li:last-child,
.menu-side-compact-w .sub-menu-w .sub-menu-i>ul>li:last-child {
    border-bottom: none
}

.menu-side-compact-w .sub-menu-w>ul>li>a,
.menu-side-compact-w .sub-menu-w .sub-menu-i>ul>li>a {
    color: #fff;
    display: block;
    padding: 12px 50px;
    font-size: .99rem;
    position: relative;
    white-space: nowrap
}

.menu-side-compact-w .sub-menu-w>ul>li>a:before,
.menu-side-compact-w .sub-menu-w .sub-menu-i>ul>li>a:before {
    content: "";
    display: block;
    width: 8px;
    height: 8px;
    border-radius: 10px;
    background-color: #FBB463;
    position: absolute;
    left: 25px;
    top: 50%;
    -webkit-transform: translate(-10px, -50%);
    transform: translate(-10px, -50%);
    opacity: 0;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-side-compact-w .sub-menu-w>ul>li>a:hover:before,
.menu-side-compact-w .sub-menu-w .sub-menu-i>ul>li>a:hover:before {
    opacity: 1;
    -webkit-transform: translate(0px, -50%);
    transform: translate(0px, -50%)
}

.menu-side-compact-w .logged-user-w {
    position: relative
}

.menu-side-compact-w .logged-user-w .avatar-w img {
    width: 40px
}

.menu-side-compact-w .logged-user-w .logged-user-i {
    display: inline-block
}

.menu-side-compact-w .logged-user-w .logged-user-i:hover {
    cursor: pointer
}

.menu-side-compact-w .logged-user-w .logged-user-i:hover .logged-user-menu {
    visibility: visible;
    opacity: 1;
    -webkit-transform: translateY(0px);
    transform: translateY(0px)
}

.menu-side-compact-w .logged-user-w .logged-user-menu {
    background: #0061da;
    -webkit-box-shadow: 0 2px 30px 0 rgba(45, 130, 255, 0.75);
    box-shadow: 0 2px 30px 0 rgba(45, 130, 255, 0.75);
    position: absolute;
    top: 0px;
    left: -10px;
    overflow: hidden;
    padding: 1rem;
    z-index: 999;
    visibility: hidden;
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    text-align: left;
    border-radius: 4px
}

.menu-side-compact-w .logged-user-w .logged-user-menu .logged-user-avatar-info {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    margin-bottom: 20px;
    padding: 0px 30px 20px 13px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1)
}

.menu-side-compact-w .logged-user-w .logged-user-menu .avatar-w {
    border-color: #fff;
    vertical-align: middle;
    margin-right: 20px
}

.menu-side-compact-w .logged-user-w .logged-user-menu .logged-user-info-w {
    vertical-align: middle
}

.menu-side-compact-w .logged-user-w .logged-user-menu .logged-user-info-w .logged-user-name {
    color: #fff;
    white-space: nowrap
}

.menu-side-compact-w .logged-user-w .logged-user-menu .logged-user-info-w .logged-user-role {
    color: rgba(255, 255, 255, 0.6);
    white-space: nowrap
}

.menu-side-compact-w .logged-user-w .logged-user-menu ul {
    list-style: none;
    text-align: left;
    margin: 0px;
    padding: 0px;
    padding-bottom: 20px
}

.menu-side-compact-w .logged-user-w .logged-user-menu ul li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05)
}

.menu-side-compact-w .logged-user-w .logged-user-menu ul li a {
    display: block;
    padding: 10px 20px;
    color: #fff
}

.menu-side-compact-w .logged-user-w .logged-user-menu ul li a i {
    vertical-align: middle;
    margin-right: 15px;
    font-size: 20px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    display: inline-block
}

.menu-side-compact-w .logged-user-w .logged-user-menu ul li a span {
    vertical-align: middle;
    font-size: .9rem;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    display: inline-block
}

.menu-side-compact-w .logged-user-w .logged-user-menu ul li a:hover {
    text-decoration: none
}

.menu-side-compact-w .logged-user-w .logged-user-menu ul li a:hover i {
    -webkit-transform: translateX(5px);
    transform: translateX(5px)
}

.menu-side-compact-w .logged-user-w .logged-user-menu ul li a:hover span {
    -webkit-transform: translateX(8px);
    transform: translateX(8px)
}

.menu-side-compact-w .logged-user-w .logged-user-menu ul li:last-child {
    border-bottom: none
}

.menu-side-compact-w .logged-user-w .logged-user-menu .bg-icon {
    font-size: 100px;
    color: rgba(255, 255, 255, 0.1);
    position: absolute;
    bottom: -40px;
    right: -20px
}

.menu-side-compact-w.color-scheme-dark {
    color: rgba(255, 255, 255, 0.9);
    background-image: -webkit-gradient(linear, left top, left bottom, from(#1c4cc3), to(#0f296a));
    background-image: linear-gradient(to bottom, #1c4cc3 0%, #0f296a 100%);
    background-repeat: repeat-x
}

.menu-side-compact-w.color-scheme-dark ul.main-menu .icon-w {
    color: #9db2ff
}

.menu-side-compact-w.color-scheme-dark ul.main-menu>li {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.menu-side-compact-w.color-scheme-dark .sub-menu-w {
    -webkit-box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2);
    box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2)
}

.menu-side-compact-w.color-scheme-dark .logo-w {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.menu-side-compact-w.color-scheme-dark .logged-user-w {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.menu-side-compact-w.color-scheme-dark .logged-user-w .avatar-w {
    border-color: #fff
}

.menu-top-w {
    -webkit-box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1);
    box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1);
    background-color: #fff;
    border-radius: 4px 4px 0px 0px;
    position: relative
}

.menu-top-w.color-scheme-dark {
    background-color: #242B3F
}

.menu-top-w.color-scheme-dark .menu-top-i ul.main-menu>li {
    border-right-color: rgba(255, 255, 255, 0.1)
}

.menu-top-w.color-scheme-dark .menu-top-i ul.main-menu>li .icon-w {
    color: rgba(255, 255, 255, 0.7)
}

.menu-top-w.color-scheme-dark .menu-top-i ul.main-menu>li>a {
    color: #fff
}

.menu-top-w.color-scheme-dark .menu-top-i ul.main-menu>li.has-sub-menu>a:before {
    color: rgba(255, 255, 255, 0.7)
}

.menu-top-w .menu-top-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.menu-top-w .menu-top-i .sub-menu {
    position: absolute;
    left: 0px;
    right: 0px;
    list-style: none;
    display: none;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    top: 100%;
    padding: 15px 20px;
    background-color: #0061da;
    -webkit-box-shadow: 0 2px 40px 0 rgba(26, 138, 255, 0.5);
    box-shadow: 0 2px 40px 0 rgba(26, 138, 255, 0.5)
}

.menu-top-w .menu-top-i .sub-menu li {
    border-right: 1px solid rgba(255, 255, 255, 0.1)
}

.menu-top-w .menu-top-i .sub-menu li a {
    color: #fff;
    display: inline-block;
    padding: 5px 25px;
    position: relative
}

.menu-top-w .menu-top-i .sub-menu li a:before {
    content: "";
    display: block;
    width: 6px;
    height: 6px;
    border-radius: 10px;
    background-color: #FBB463;
    position: absolute;
    left: 10px;
    top: 50%;
    -webkit-transform: translate(-10px, -50%);
    transform: translate(-10px, -50%);
    opacity: 0;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-top-w .menu-top-i .sub-menu li a:hover {
    text-decoration: none
}

.menu-top-w .menu-top-i .sub-menu li a:hover:before {
    opacity: 1;
    -webkit-transform: translate(0px, -50%);
    transform: translate(0px, -50%)
}

.menu-top-w .menu-top-i .sub-menu li:last-child {
    border-right: none
}

.menu-top-w .menu-top-i .logged-user-w {
    white-space: nowrap;
    border-bottom: none;
    padding: 0px 2rem 0px 1rem
}

.menu-top-w .menu-top-i .logged-user-w .avatar-w,
.menu-top-w .menu-top-i .logged-user-w .logged-user-info-w {
    display: inline-block;
    vertical-align: middle
}

.menu-top-w .menu-top-i .logged-user-w .avatar-w {
    padding: 3px
}

.menu-top-w .menu-top-i .logged-user-w .avatar-w img {
    width: 30px
}

.menu-top-w .menu-top-i .logged-user-w .logged-user-info-w {
    margin-left: 20px;
    text-align: left
}

.menu-top-w .menu-top-i .logo-w {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100px;
    flex: 0 0 100px;
    text-align: center;
    padding: 0rem 2rem;
    border-right: 1px solid rgba(0, 0, 0, 0.05)
}

.menu-top-w .menu-top-i .logo-w a {
    display: inline-block;
    white-space: nowrap
}

.menu-top-w .menu-top-i .logo-w .logo {
    display: inline-block
}

.menu-top-w .menu-top-i .logo-w img {
    width: 40px;
    height: auto;
    display: inline-block;
    vertical-align: middle
}

.menu-top-w .menu-top-i .logo-w span {
    vertical-align: middle;
    display: inline-block;
    color: #334152;
    margin-left: 1rem
}

.menu-top-w .menu-top-i ul.main-menu {
    list-style: none;
    padding: 0px 1rem;
    margin-bottom: 0px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.menu-top-w .menu-top-i ul.main-menu>li {
    display: block;
    border-right: 1px solid rgba(0, 0, 0, 0.05)
}

.menu-top-w .menu-top-i ul.main-menu>li:last-child {
    border-bottom: none
}

.menu-top-w .menu-top-i ul.main-menu>li.has-sub-menu>a:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e91c";
    font-size: 7px;
    color: rgba(0, 0, 0, 0.5);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    position: absolute;
    top: 50%;
    right: 15px
}

.menu-top-w .menu-top-i ul.main-menu>li.has-sub-menu.active .sub-menu {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.menu-top-w .menu-top-i ul.main-menu>li.has-sub-menu.active>a {
    background-color: #0061da;
    color: #fff
}

.menu-top-w .menu-top-i ul.main-menu>li.has-sub-menu.active>a .icon-w {
    color: #9db2ff
}

.menu-top-w .menu-top-i ul.main-menu>li>a {
    color: #3E4B5B;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative
}

.menu-top-w .menu-top-i ul.main-menu>li>a:focus {
    text-decoration: none
}

.menu-top-w .menu-top-i ul.main-menu>li>a:hover {
    text-decoration: none;
    background-color: #0061da;
    color: #fff
}

.menu-top-w .menu-top-i ul.main-menu>li>a:hover .icon-w {
    color: #9db2ff
}

.menu-top-w .menu-top-i ul.main-menu>li .icon-w {
    color: #0073ff;
    font-size: 20px;
    display: block;
    padding: 1rem;
    width: 45px;
    text-align: center;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-top-w .menu-top-i ul.main-menu>li span {
    padding: 10px;
    padding-right: 30px;
    display: block;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    white-space: nowrap
}

.menu-top-w .menu-top-i ul.main-menu>li .icon-w+span {
    padding-left: 0px
}

.menu-top-image-w {
    min-height: 300px;
    background-size: cover;
    background-position: center center;
    position: relative
}

.menu-top-image-w:after {
    content: "";
    position: absolute;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0)), color-stop(63%, rgba(95, 95, 95, 0.06)), to(rgba(0, 0, 0, 0.73)));
    background-image: linear-gradient(-180deg, rgba(255, 255, 255, 0) 0%, rgba(95, 95, 95, 0.06) 63%, rgba(0, 0, 0, 0.73) 100%);
    z-index: 1
}

.menu-top-image-w .top-part-w {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    position: relative;
    z-index: 2
}

.menu-top-image-w .top-part-w .logo-w {
    text-align: center;
    padding: 1rem 2rem
}

.menu-top-image-w .top-part-w .logo-w a {
    display: inline-block
}

.menu-top-image-w .top-part-w .logo-w .logo {
    display: inline-block
}

.menu-top-image-w .top-part-w .logo-w img {
    width: 40px;
    height: auto;
    display: inline-block;
    vertical-align: middle
}

.menu-top-image-w .top-part-w .logo-w span {
    vertical-align: middle;
    display: inline-block;
    color: #fff;
    margin-left: 1rem
}

.menu-top-image-w .top-part-w .user-and-search {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.menu-top-image-w .top-part-w .element-search {
    position: relative
}

.menu-top-image-w .top-part-w .element-search:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    left: 15px;
    top: 48%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    font-size: 16px;
    content: "\e92c";
    color: rgba(0, 0, 0, 0.4)
}

.menu-top-image-w .top-part-w .element-search input {
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    background-color: transparent;
    border-radius: 30px;
    padding: 8px 15px 8px 40px;
    display: block;
    width: 250px;
    outline: none;
    border: none;
    -webkit-box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.05);
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.05);
    background: #fff
}

.menu-top-image-w .top-part-w .element-search input::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.5)
}

.menu-top-image-w .top-part-w .element-search input:-ms-input-placeholder {
    color: rgba(0, 0, 0, 0.5)
}

.menu-top-image-w .top-part-w .element-search input::placeholder {
    color: rgba(0, 0, 0, 0.5)
}

.menu-top-image-w .top-part-w .logged-user-w {
    padding: 1rem 2rem;
    border-bottom: none
}

.menu-top-image-w .top-part-w .logged-user-w .avatar-w img {
    width: 40px;
    display: block
}

.menu-top-image-w .page-menu-header {
    position: relative;
    z-index: 2;
    font-size: 3.75rem;
    color: #fff;
    padding: 2rem;
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.6);
    margin: 0px
}

.menu-top-image-w .menu-top-image-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: absolute;
    z-index: 2;
    bottom: 0px;
    left: 0px;
    right: 0px;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.menu-top-image-w .sub-menu {
    position: absolute;
    left: 0px;
    right: 0px;
    list-style: none;
    display: none;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    top: 100%;
    padding: 25px 20px;
    background-color: #0061da;
    -webkit-box-shadow: 0 2px 40px 0 rgba(26, 138, 255, 0.5);
    box-shadow: 0 2px 40px 0 rgba(26, 138, 255, 0.5)
}

.menu-top-image-w .sub-menu li {
    border-right: 1px solid rgba(255, 255, 255, 0.1)
}

.menu-top-image-w .sub-menu li a {
    color: #fff;
    display: inline-block;
    padding: 5px 25px;
    position: relative
}

.menu-top-image-w .sub-menu li a:before {
    content: "";
    display: block;
    width: 6px;
    height: 6px;
    border-radius: 10px;
    background-color: #FBB463;
    position: absolute;
    left: 10px;
    top: 50%;
    -webkit-transform: translate(-10px, -50%);
    transform: translate(-10px, -50%);
    opacity: 0;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-top-image-w .sub-menu li a:hover {
    text-decoration: none
}

.menu-top-image-w .sub-menu li a:hover:before {
    opacity: 1;
    -webkit-transform: translate(0px, -50%);
    transform: translate(0px, -50%)
}

.menu-top-image-w .sub-menu li:last-child {
    border-right: none
}

.menu-top-image-w ul.main-menu {
    list-style: none;
    padding: 0rem;
    margin-bottom: 0px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.menu-top-image-w ul.main-menu>li {
    display: block;
    font-size: 1rem
}

.menu-top-image-w ul.main-menu>li:last-child {
    border-bottom: none
}

.menu-top-image-w ul.main-menu>li.has-sub-menu>a:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e91c";
    font-size: 7px;
    color: rgba(255, 255, 255, 0.5);
    color: transparent;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    position: absolute;
    top: 50%;
    right: 15px
}

.menu-top-image-w ul.main-menu>li.has-sub-menu.active .sub-menu {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.menu-top-image-w ul.main-menu>li.has-sub-menu.active>a {
    background-color: #0061da;
    color: #fff
}

.menu-top-image-w ul.main-menu>li.has-sub-menu.active>a .icon-w {
    color: #9db2ff
}

.menu-top-image-w ul.main-menu>li>a {
    color: #fff;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative
}

.menu-top-image-w ul.main-menu>li>a:after {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 0px;
    background-color: rgba(255, 255, 255, 0.3);
    content: "";
    height: 16px;
    width: 1px
}

.menu-top-image-w ul.main-menu>li>a:focus {
    text-decoration: none
}

.menu-top-image-w ul.main-menu>li>a:hover {
    text-decoration: none;
    background-color: #0061da;
    color: #fff
}

.menu-top-image-w ul.main-menu>li>a:hover:after {
    background-color: transparent
}

.menu-top-image-w ul.main-menu>li>a:hover .icon-w {
    color: #9db2ff
}

.menu-top-image-w ul.main-menu>li .icon-w {
    color: #fff;
    font-size: 22px;
    display: block;
    padding: 15px 15px 15px 30px;
    text-align: center;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-top-image-w ul.main-menu>li span {
    padding: 10px;
    padding-right: 30px;
    display: block;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    white-space: nowrap
}

.menu-top-image-w ul.main-menu>li .icon-w+span {
    padding-left: 0px
}

.all-wrapper>.top-menu-secondary {
    border-radius: 4px 4px 0px 0px
}

.top-menu-secondary {
    background-color: #fff;
    -webkit-box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1);
    box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1);
    padding: 0px 20px;
    text-align: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    border-radius: 0px 4px 0px 0px
}

.top-menu-secondary>ul {
    list-style: none;
    margin: 0px;
    padding: 0px;
    text-transform: uppercase;
    color: #3E4B5B;
    font-weight: 500;
    letter-spacing: 2px;
    font-size: .99rem
}

.top-menu-secondary>ul li {
    display: inline-block
}

.top-menu-secondary>ul li a {
    display: inline-block;
    padding: 20px 25px;
    color: rgba(0, 0, 0, 0.25);
    position: relative;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.top-menu-secondary>ul li a:after {
    content: "";
    background-color: #047bf8;
    position: absolute;
    bottom: 0px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 0px;
    height: 5px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.top-menu-secondary>ul li.active a,
.top-menu-secondary>ul li:hover a {
    color: #3E4B5B;
    text-decoration: none
}

.top-menu-secondary>ul li.active a:after,
.top-menu-secondary>ul li:hover a:after {
    width: 100%
}

.top-menu-secondary .fancy-selector-w {
    margin-left: auto;
    -ms-flex-item-align: stretch;
    align-self: stretch
}

.top-menu-secondary .logo-w.menu-size+.fancy-selector-w {
    margin-left: 0px
}

.top-menu-secondary .logo-w {
    text-align: left
}

.top-menu-secondary .logo-w a {
    display: inline-block
}

.top-menu-secondary .logo-w img {
    height: 35px;
    width: auto;
    display: inline-block;
    vertical-align: middle
}

.top-menu-secondary .logo-w span {
    display: inline-block;
    vertical-align: middle
}

.top-menu-secondary .logo-w img+span {
    margin-left: 20px;
    color: #3E4B5B;
    letter-spacing: 2px;
    text-transform: uppercase;
    font-size: .81rem;
    font-weight: 500
}

.top-menu-secondary .logo-w.menu-size {
    width: 280px
}

.top-menu-secondary .top-menu-controls {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-left: auto;
	padding: 7px 0px;
	position: relative;
}

.top-menu-secondary .top-menu-controls .element-search {
    position: relative;
    margin: 0px 1rem
}

.top-menu-secondary .top-menu-controls .element-search:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    left: 15px;
    top: 48%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    font-size: 16px;
    content: "\e92c";
    color: rgba(0, 0, 0, 0.4)
}

.top-menu-secondary .top-menu-controls .element-search input {
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    background-color: transparent;
    border-radius: 30px;
    padding: 8px 15px 8px 40px;
    display: block;
    width: 220px;
    outline: none;
    border: none;
    box-shadow: none;
    background: #eee
}

.top-menu-secondary .top-menu-controls .element-search input::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.5)
}

.top-menu-secondary .top-menu-controls .element-search input:-ms-input-placeholder {
    color: rgba(0, 0, 0, 0.5)
}

.top-menu-secondary .top-menu-controls .element-search input::placeholder {
    color: rgba(0, 0, 0, 0.5)
}

.top-menu-secondary .messages-notifications {
    margin: 0px 1rem;
    position: relative;
    font-size: 24px;
    color: #047bf8
}

.top-menu-secondary .messages-notifications .new-messages-count {
    background-color: #e65252;
    color: #fff;
    border-radius: 6px;
    font-weight: 500;
    position: absolute;
    top: -5px;
    right: -12px;
    padding: 4px 4px;
    vertical-align: middle;
    font-size: .72rem;
    line-height: 1
}

.top-menu-secondary .top-icon {
    margin: 0px 1rem;
    color: #047bf8;
    font-size: 26px
}

.top-menu-secondary .logged-user-w {
    position: relative;
    padding: 0px 1rem;
    border-bottom: none
}

.top-menu-secondary .logged-user-w .avatar-w {
    padding: 0px;
    border: none
}

.top-menu-secondary .logged-user-w .avatar-w img {
    width: 40px
}

.top-menu-secondary .logged-user-w .logged-user-i {
    display: inline-block
}

.top-menu-secondary .logged-user-w .logged-user-i:hover {
    cursor: pointer
}

.top-menu-secondary .logged-user-w .logged-user-i:hover .logged-user-menu {
    visibility: visible;
    opacity: 1;
    -webkit-transform: translateY(0px);
    transform: translateY(0px)
}

.top-menu-secondary .logged-user-w .logged-user-menu {
    background: #0061da;
    -webkit-box-shadow: 0 2px 30px 0 rgba(45, 130, 255, 0.75);
    box-shadow: 0 2px 30px 0 rgba(45, 130, 255, 0.75);
    position: absolute;
    top: -10px;
    right: -20px;
    overflow: hidden;
    padding: 1rem;
    z-index: 999;
    visibility: hidden;
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    text-align: left;
    border-radius: 4px
}

.top-menu-secondary .logged-user-w .logged-user-menu .logged-user-avatar-info {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    margin-bottom: 10px;
    padding: 0px 30px 10px 13px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1)
}

.top-menu-secondary .logged-user-w .logged-user-menu .avatar-w {
    border-color: #fff;
    vertical-align: middle;
    margin-right: 20px
}

.top-menu-secondary .logged-user-w .logged-user-menu .logged-user-info-w {
    vertical-align: middle
}

.top-menu-secondary .logged-user-w .logged-user-menu .logged-user-info-w .logged-user-name {
    color: #fff;
    white-space: nowrap
}

.top-menu-secondary .logged-user-w .logged-user-menu .logged-user-info-w .logged-user-role {
    color: rgba(255, 255, 255, 0.6);
    white-space: nowrap
}

.top-menu-secondary .logged-user-w .logged-user-menu ul {
    list-style: none;
    text-align: left;
    margin: 0px;
    padding: 0px;
    padding-bottom: 0px
}

.top-menu-secondary .logged-user-w .logged-user-menu ul li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05)
}

.top-menu-secondary .logged-user-w .logged-user-menu ul li a {
    display: block;
    padding: 10px 20px;
    color: #fff
}

.top-menu-secondary .logged-user-w .logged-user-menu ul li a i {
    vertical-align: middle;
    margin-right: 15px;
    font-size: 20px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    display: inline-block
}

.top-menu-secondary .logged-user-w .logged-user-menu ul li a span {
    vertical-align: middle;
    font-size: .9rem;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    display: inline-block
}

.top-menu-secondary .logged-user-w .logged-user-menu ul li a:hover {
    text-decoration: none
}

.top-menu-secondary .logged-user-w .logged-user-menu ul li a:hover i {
    -webkit-transform: translateX(5px);
    transform: translateX(5px)
}

.top-menu-secondary .logged-user-w .logged-user-menu ul li a:hover span {
    -webkit-transform: translateX(8px);
    transform: translateX(8px)
}

.top-menu-secondary .logged-user-w .logged-user-menu ul li:last-child {
    border-bottom: none
}

.top-menu-secondary .logged-user-w .logged-user-menu .bg-icon {
    font-size: 100px;
    color: rgba(255, 255, 255, 0.1);
    position: absolute;
    bottom: -40px;
    right: -20px
}

.top-menu-secondary.color-scheme-dark {
    background-color: #1c4cc3
}

.top-menu-secondary.color-scheme-dark .top-menu-controls .element-search input {
    background-color: rgba(0, 0, 0, 0.2);
    color: #fff
}

.top-menu-secondary.color-scheme-dark .top-menu-controls .element-search input::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 0.3)
}

.top-menu-secondary.color-scheme-dark .top-menu-controls .element-search input:-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.3)
}

.top-menu-secondary.color-scheme-dark .top-menu-controls .element-search input::placeholder {
    color: rgba(255, 255, 255, 0.3)
}

.top-menu-secondary.color-scheme-dark .top-menu-controls .element-search:before {
    color: rgba(255, 255, 255, 0.7)
}

.top-menu-secondary.color-scheme-dark .messages-notifications,
.top-menu-secondary.color-scheme-dark .top-icon {
    color: #fff
}

.top-menu-secondary.color-scheme-dark>ul li a {
    color: rgba(255, 255, 255, 0.4)
}

.top-menu-secondary.color-scheme-dark>ul li a:after {
    background-color: #FBB463
}

.top-menu-secondary.color-scheme-dark>ul li.active a,
.top-menu-secondary.color-scheme-dark>ul li:hover a {
    color: #fff
}

.top-menu-secondary.color-scheme-dark .logo-w img+span {
    color: #fff
}

.top-menu-secondary.color-scheme-dark .fancy-selector-w .fancy-selector-current .fs-selector-trigger {
    background-color: #3572ed
}

.menu-side-v2-w {
    position: relative;
    width: 300px;
    border-radius: 4px 0px 0px 4px;
    display: table-cell;
    border-right: 1px solid rgba(0, 0, 0, 0.05)
}

.menu-side-v2-w .logged-user-w {
    position: relative
}

.menu-side-v2-w .logged-user-w .logged-user-i {
    display: inline-block
}

.menu-side-v2-w .logged-user-w .logged-user-i:hover {
    cursor: pointer
}

.menu-side-v2-w .logged-user-w .logged-user-i:hover .logged-user-menu {
    visibility: visible;
    opacity: 1;
    -webkit-transform: translateY(0px);
    transform: translateY(0px)
}

.menu-side-v2-w .logged-user-menu {
    background: #0061da;
    -webkit-box-shadow: 0 2px 30px 0 rgba(45, 130, 255, 0.75);
    box-shadow: 0 2px 30px 0 rgba(45, 130, 255, 0.75);
    position: absolute;
    top: 0px;
    left: -10px;
    right: -10px;
    overflow: hidden;
    padding: 1rem;
    z-index: 999;
    visibility: hidden;
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 4px
}

.menu-side-v2-w .logged-user-menu .avatar-w {
    border-color: #fff
}

.menu-side-v2-w .logged-user-menu .logged-user-info-w {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1)
}

.menu-side-v2-w .logged-user-menu .logged-user-info-w .logged-user-name {
    color: #fff
}

.menu-side-v2-w .logged-user-menu .logged-user-info-w .logged-user-role {
    color: rgba(255, 255, 255, 0.6)
}

.menu-side-v2-w .logged-user-menu ul {
    list-style: none;
    text-align: left;
    margin: 0px;
    padding: 0px 40px;
    padding-bottom: 20px
}

.menu-side-v2-w .logged-user-menu ul li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05)
}

.menu-side-v2-w .logged-user-menu ul li a {
    display: block;
    padding: 10px;
    color: #fff
}

.menu-side-v2-w .logged-user-menu ul li a i {
    vertical-align: middle;
    margin-right: 15px;
    font-size: 20px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    display: inline-block
}

.menu-side-v2-w .logged-user-menu ul li a span {
    vertical-align: middle;
    font-size: .9rem;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    display: inline-block
}

.menu-side-v2-w .logged-user-menu ul li a:hover {
    text-decoration: none
}

.menu-side-v2-w .logged-user-menu ul li a:hover i {
    -webkit-transform: translateX(5px);
    transform: translateX(5px)
}

.menu-side-v2-w .logged-user-menu ul li a:hover span {
    -webkit-transform: translateX(8px);
    transform: translateX(8px)
}

.menu-side-v2-w .logged-user-menu ul li:last-child {
    border-bottom: none
}

.menu-side-v2-w .logged-user-menu .bg-icon {
    font-size: 100px;
    color: rgba(255, 255, 255, 0.1);
    position: absolute;
    bottom: -40px;
    right: -20px
}

.menu-side-v2-w .side-menu-magic {
    background-image: linear-gradient(-154deg, #1643A3 8%, #2E1170 90%);
    border-radius: 6px;
    padding: 40px;
    text-align: center;
    margin: 20px
}

.menu-side-v2-w .side-menu-magic h1,
.menu-side-v2-w .side-menu-magic h2,
.menu-side-v2-w .side-menu-magic h3,
.menu-side-v2-w .side-menu-magic h4,
.menu-side-v2-w .side-menu-magic h5 {
    color: #fff;
    margin-bottom: 5px
}

.menu-side-v2-w .side-menu-magic p {
    color: rgba(255, 255, 255, 0.6)
}

.menu-side-v2-w .side-menu-magic .btn-w {
    margin-top: 15px
}

.menu-side-v2-w .side-menu-magic .btn-white,
.menu-side-v2-w .side-menu-magic .all-wrapper .fc-button,
.all-wrapper .menu-side-v2-w .side-menu-magic .fc-button {
    border: none;
    -webkit-box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.3);
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.3)
}

.menu-side-v2-w .logo-w {
    text-align: center;
    padding: 2rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.menu-side-v2-w .logo-w a {
    display: inline-block
}

.menu-side-v2-w .logo-w .logo {
    display: inline-block
}

.menu-side-v2-w .logo-w img {
    width: 40px;
    height: auto;
    display: inline-block;
    vertical-align: middle
}

.menu-side-v2-w .logo-w span {
    vertical-align: middle;
    display: inline-block;
    color: #334152;
    margin-left: 1rem
}

.menu-side-v2-w ul.main-menu {
    list-style: none;
    padding: 2rem;
    margin-bottom: 0px
}

.menu-side-v2-w ul.main-menu>li {
    display: block;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.menu-side-v2-w ul.main-menu>li:last-child {
    border-bottom: none
}

.menu-side-v2-w ul.main-menu>li.has-sub-menu>a:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e91c";
    font-size: 7px;
    color: rgba(0, 0, 0, 0.5);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    position: absolute;
    top: 50%;
    right: 10px
}

.menu-side-v2-w ul.main-menu>li.has-sub-menu.active .sub-menu {
    display: block
}

.menu-side-v2-w ul.main-menu>li.menu-sub-header {
    text-transform: uppercase;
    color: #448eef;
    font-size: .72rem;
    letter-spacing: 1px;
    padding-top: 20px;
    padding-bottom: 0px;
    border-bottom: none
}

.menu-side-v2-w ul.main-menu>li>a {
    color: #3E4B5B;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    font-size: .99rem
}

.menu-side-v2-w ul.main-menu>li>a:focus {
    text-decoration: none
}

.menu-side-v2-w ul.main-menu>li>a:hover {
    text-decoration: none
}

@media (min-width: 1025px) {
    .menu-side-v2-w ul.main-menu>li>a:hover .icon-w {
        -webkit-transform: translateX(10px);
        transform: translateX(10px)
    }
    .menu-side-v2-w ul.main-menu>li>a:hover span {
        -webkit-transform: translateX(5px);
        transform: translateX(5px)
    }
}

.menu-side-v2-w ul.main-menu>li .icon-w {
    color: #0073ff;
    font-size: 27px;
    display: block;
    padding: 1rem;
    width: 80px;
    text-align: center;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-side-v2-w ul.main-menu>li span {
    padding: 1rem;
    display: block;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-side-v2-w ul.main-menu>li .icon-w+span {
    padding-left: 0px
}

.menu-side-v2-w ul.sub-menu {
    padding: 1rem 0px;
    padding-left: 55px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: none;
    list-style: none
}

.menu-side-v2-w ul.sub-menu li {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.menu-side-v2-w ul.sub-menu li:last-child {
    border-bottom: none
}

.menu-side-v2-w ul.sub-menu li a {
    padding: 0.4rem 10px 0.4rem 10px;
    display: block;
    position: relative;
    font-size: .81rem;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-side-v2-w ul.sub-menu li a:before {
    content: "";
    width: 5px;
    height: 5px;
    border: 1px solid #047bf8;
    position: absolute;
    left: -10px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    background-color: #fff;
    display: block;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    border-radius: 6px
}

.menu-side-v2-w ul.sub-menu li a:hover {
    text-decoration: none;
    cursor: pointer;
    -webkit-transform: translateX(-5px);
    transform: translateX(-5px)
}

.menu-side-v2-w ul.sub-menu li a:hover:before {
    -webkit-transform: translate(-5px, -50%);
    transform: translate(-5px, -50%);
    border-radius: 6px;
    background-color: #047bf8;
    border-color: #047bf8
}

.menu-side-v2-w.color-scheme-dark {
    background-image: -webkit-gradient(linear, left top, left bottom, from(#3D4D75), to(#31395B));
    background-image: linear-gradient(to bottom, #3D4D75 0%, #31395B 100%);
    background-repeat: repeat-x;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#1c4cc3), to(#0f296a));
    background-image: linear-gradient(to bottom, #1c4cc3 0%, #0f296a 100%);
    background-repeat: repeat-x;
    color: rgba(255, 255, 255, 0.9)
}

.menu-side-v2-w.color-scheme-dark .logged-user-menu {
    background-color: #5e00da;
    -webkit-box-shadow: 0 2px 30px 0 rgba(0, 0, 0, 0.25);
    box-shadow: 0 2px 30px 0 rgba(0, 0, 0, 0.25)
}

.menu-side-v2-w.color-scheme-dark .side-menu-magic {
    background-image: linear-gradient(-154deg, #6d16a3 8%, #5211e6 90%)
}

.menu-side-v2-w.color-scheme-dark ul.sub-menu li {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.menu-side-v2-w.color-scheme-dark ul.sub-menu li a {
    color: #fff
}

.menu-side-v2-w.color-scheme-dark ul.main-menu .icon-w {
    color: #9db2ff
}

.menu-side-v2-w.color-scheme-dark ul.main-menu>li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05)
}

.menu-side-v2-w.color-scheme-dark ul.main-menu>li>a {
    color: #fff
}

.menu-side-v2-w.color-scheme-dark ul.main-menu>li>a:before {
    color: #fff
}

.menu-side-v2-w.color-scheme-dark .sub-menu-w {
    -webkit-box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2);
    box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2)
}

.menu-side-v2-w.color-scheme-dark .logo-w {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.menu-side-v2-w.color-scheme-dark .logo-w span {
    color: #fff
}

.menu-side-v2-w.color-scheme-dark .logged-user-w {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.menu-side-v2-w.color-scheme-dark .logged-user-w .avatar-w {
    border-color: #fff
}

.menu-side-v2-w.color-scheme-dark .logged-user-w .logged-user-role {
    color: rgba(255, 255, 255, 0.4)
}

.menu-side-v2-w.flying-menu .main-menu>li.has-sub-menu {
    position: relative
}

.menu-side-v2-w.flying-menu .main-menu>li.has-sub-menu>a:before {
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-side-v2-w.flying-menu .main-menu>li.has-sub-menu.active>a:before {
    -webkit-transform: translateY(-50%) rotate(-90deg);
    transform: translateY(-50%) rotate(-90deg)
}

.menu-side-v2-w.flying-menu .main-menu>li.has-sub-menu.active .sub-menu-w {
    visibility: visible;
    opacity: 1;
    -webkit-transform: translate(100%, -50%) scale(1);
    transform: translate(100%, -50%) scale(1)
}

.menu-side-v2-w.flying-menu .sub-menu-w {
    position: absolute;
    background-color: #fff;
    padding: 10px 30px 20px;
    border-radius: 4px;
    right: 0px;
    top: 50%;
    -webkit-transform: translate(110%, -50%) scale(0.8);
    transform: translate(110%, -50%) scale(0.8);
    z-index: 10000;
    -webkit-box-shadow: 0px 15px 50px rgba(0, 0, 0, 0.1);
    box-shadow: 0px 15px 50px rgba(0, 0, 0, 0.1);
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease
}

.menu-side-v2-w.flying-menu .sub-menu-header {
    font-size: 1.35rem;
    font-weight: 500;
    color: #047bf8;
    text-align: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 10px;
    margin-bottom: 10px
}

.menu-side-v2-w.flying-menu .sub-menu-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.menu-side-v2-w.flying-menu ul.sub-menu {
    display: block;
    padding-left: 0px;
    border: none;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 200px;
    flex: 0 0 200px;
    padding: 0px 10px
}

.menu-side-v2-w.flying-menu ul.sub-menu li {
    min-width: 200px
}

.menu-side-v2-w.flying-menu ul.sub-menu li a {
    color: #3E4B5B;
    white-space: nowrap;
    font-size: .9rem;
    padding: 10px 20px
}

.menu-side-v2-w.flying-menu ul.sub-menu li a:before {
    opacity: 0;
    -webkit-transform: translate(10px, -50%);
    transform: translate(10px, -50%);
    background-color: #047bf8
}

.menu-side-v2-w.flying-menu ul.sub-menu li a:hover {
    color: #047bf8
}

.menu-side-v2-w.flying-menu ul.sub-menu li a:hover:before {
    opacity: 1;
    -webkit-transform: translate(20px, -50%);
    transform: translate(20px, -50%)
}

.menu-side-v2-w.flying-menu ul.sub-menu li .badge {
    font-size: .72rem;
    padding: 2px 5px;
    margin-left: 5px
}

.menu-side-v2-w.flying-menu ul.sub-menu+ul.sub-menu {
    border-left: 1px solid rgba(0, 0, 0, 0.05)
}

.element-wrapper {
    padding-bottom: 2rem
}

.element-wrapper.compact {
    padding-bottom: 2rem
}

.element-wrapper .element-info {
    padding-bottom: 1rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.element-wrapper .element-info .element-info-with-icon {
    margin-bottom: 0px
}

.element-wrapper .element-info-with-icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 2rem
}

.element-wrapper .element-info-with-icon .element-info-icon {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 70px;
    flex: 0 0 70px;
    color: #047bf8;
    font-size: 30px
}

.element-wrapper .element-info-with-icon.smaller .element-info-icon {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50px;
    flex: 0 0 50px;
    font-size: 20px
}

.element-wrapper .element-info-with-icon.smaller .element-info-text .element-inner-header {
    margin-bottom: 0px
}

.element-wrapper .element-actions {
    float: right;
    position: relative;
    z-index: 2
}

.element-wrapper .element-actions .btn+.btn,
.element-wrapper .element-actions .all-wrapper .fc-button+.btn,
.all-wrapper .element-wrapper .element-actions .fc-button+.btn,
.element-wrapper .element-actions .all-wrapper .btn+.fc-button,
.all-wrapper .element-wrapper .element-actions .btn+.fc-button,
.element-wrapper .element-actions .all-wrapper .fc-button+.fc-button,
.all-wrapper .element-wrapper .element-actions .fc-button+.fc-button {
    margin-left: 10px
}

.element-wrapper .element-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 1rem;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1
}

.element-wrapper .element-header:after {
    content: "";
    background-color: #047bf8;
    width: 22px;
    height: 6px;
    border-radius: 2px;
    display: block;
    position: absolute;
    bottom: -3px;
    left: 0px
}

.element-wrapper .element-inner-header {
    margin-bottom: 0.5rem;
    margin-top: 0px;
    display: block
}

.element-wrapper .element-inner-desc {
    color: #999;
    font-weight: 300;
    font-size: .81rem;
    display: block
}

.element-wrapper .element-search {
    position: relative
}

.element-wrapper .element-search:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    left: 15px;
    top: 48%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    font-size: 20px;
    content: "\e92c";
    color: rgba(0, 0, 0, 0.2)
}

.element-wrapper .element-search input {
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    background-color: #f1f1f1;
    border-radius: 30px;
    padding: 10px 15px 10px 50px;
    display: block;
    width: 100%;
    outline: none
}

.element-wrapper .element-search input::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.3)
}

.element-wrapper .element-search input:-ms-input-placeholder {
    color: rgba(0, 0, 0, 0.3)
}

.element-wrapper .element-search input::placeholder {
    color: rgba(0, 0, 0, 0.3)
}

.box-style,
.user-profile,
.element-box,
.invoice-w,
.big-error-w,
.activity-boxes-w .activity-box,
.projects-list .project-box {
    border-radius: 4px;
    background-color: #fff;
    -webkit-box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1);
    box-shadow: 0px 3px 8px rgba(69, 101, 173, 0.1)
}

.element-box,
.invoice-w,
.big-error-w {
    padding: 1.5rem 2rem;
    margin-bottom: 1rem
}

.element-box .os-tabs-controls,
.invoice-w .os-tabs-controls,
.big-error-w .os-tabs-controls {
    margin-left: -2rem;
    margin-right: -2rem
}

.element-box .os-tabs-controls .nav,
.invoice-w .os-tabs-controls .nav,
.big-error-w .os-tabs-controls .nav {
    padding-left: 2rem;
    padding-right: 2rem
}

.element-box .centered-header,
.invoice-w .centered-header,
.big-error-w .centered-header {
    text-transform: uppercase;
    letter-spacing: 2px;
    text-align: center;
    margin-bottom: 1rem
}

.element-box .element-box-header,
.invoice-w .element-box-header,
.big-error-w .element-box-header {
    color: #046fdf;
    margin-bottom: 1rem
}

.element-box-content+.form-header {
    margin-top: 2rem
}

.element-box+.element-box,
.invoice-w+.element-box,
.big-error-w+.element-box,
.element-box+.invoice-w,
.invoice-w+.invoice-w,
.big-error-w+.invoice-w,
.element-box+.big-error-w,
.invoice-w+.big-error-w,
.big-error-w+.big-error-w {
    margin-top: 2rem
}

.element-box-tp .input-search-w,
.element-box .input-search-w,
.invoice-w .input-search-w,
.big-error-w .input-search-w {
    margin-bottom: 1rem
}

.el-tablo {
    padding-right: 5px
}

.el-tablo .label {
    display: block;
    font-size: .63rem;
    text-transform: uppercase;
    color: rgba(0, 0, 0, 0.4)
}

.el-tablo .value {
    font-size: 1.5rem;
    font-weight: 500;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    letter-spacing: -1px;
    line-height: 1.2;
    display: inline-block;
    vertical-align: middle
}

.el-tablo .trending {
    padding: 3px 10px;
    border-radius: 30px;
    display: inline-block;
    font-size: .72rem;
    vertical-align: middle;
    margin-left: 1rem
}

.el-tablo .trending .os-icon {
    margin-left: 5px;
    vertical-align: middle;
    font-size: 14px
}

.el-tablo .trending span {
    display: inline-block;
    vertical-align: middle
}

.el-tablo .trending-up {
    color: #fff;
    background-color: #579D1B
}

.el-tablo .trending-down {
    color: #fff;
    background-color: #e65252
}

.el-tablo .trending-up-basic {
    color: #579D1B;
    padding: 0px
}

.el-tablo .trending-down-basic {
    color: #e65252;
    padding: 0px
}

.el-tablo.centered {
    text-align: center
}

.el-tablo.padded {
    padding: 2rem
}

.el-tablo.bigger .value {
    font-size: 3.33rem
}

.el-tablo.bigger .label {
    font-size: .9rem
}

.el-tablo.highlight .value {
    color: #047bf8
}

.el-buttons-list .btn,
.el-buttons-list .all-wrapper .fc-button,
.all-wrapper .el-buttons-list .fc-button {
    margin-bottom: 0.5rem
}

.el-buttons-list.full-width .btn,
.el-buttons-list.full-width .all-wrapper .fc-button,
.all-wrapper .el-buttons-list.full-width .fc-button {
    display: block
}

.el-tablo+.el-chart-w {
    margin-top: 1rem
}

.el-chart-w {
    position: relative;
    text-align: center
}

.el-chart-w .inside-donut-chart-label {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;
    font-size: .63rem;
    text-transform: uppercase;
    z-index: 1
}

.el-chart-w .inside-donut-chart-label strong {
    display: block;
    font-size: 1.26rem
}

.el-chart-w .inside-donut-chart-label span {
    display: block;
    color: rgba(0, 0, 0, 0.4)
}

.el-chart-w canvas {
    position: relative;
    z-index: 2
}

.el-chart-w+.el-legend {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 1.5rem;
    margin-top: 2rem
}

.el-legend .legend-value-w {
    margin-bottom: 0.5rem
}

.el-legend .legend-pin {
    width: 10px;
    height: 10px;
    border-radius: 4px;
    display: inline-block;
    vertical-align: middle;
    margin-right: 0.7rem
}

.el-legend .legend-value {
    display: inline-block;
    vertical-align: middle
}

.profile-tile {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.profile-tile .profile-tile-box {
    width: 110px;
    text-align: center;
    border-radius: 4px;
    padding: 1.5rem 0.5rem 0.5rem;
    background-color: #fff;
    -webkit-box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.05);
    box-shadow: 0px 5px 20px rgba(0, 0, 0, 0.05)
}

.profile-tile .pt-avatar-w {
    display: inline-block;
    border-radius: 50px;
    overflow: hidden
}

.profile-tile .pt-avatar-w img {
    width: 60px;
    height: auto
}

.profile-tile .pt-user-name {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 0.5rem;
    margin-top: 0.5rem;
    font-size: .63rem;
    text-transform: uppercase
}

.profile-tile .profile-tile-meta {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    padding-left: 30px
}

.profile-tile .profile-tile-meta ul {
    list-style: none;
    font-size: .63rem;
    text-transform: uppercase;
    color: rgba(0, 0, 0, 0.4);
    margin: 0px;
    padding: 0px
}

.profile-tile .profile-tile-meta ul li {
    margin-bottom: 0.3rem
}

.profile-tile .profile-tile-meta ul strong {
    color: #3E4B5B;
    margin-left: 5px
}

.status-pill {
    width: 12px;
    height: 12px;
    border-radius: 30px;
    background-color: #eee;
    display: inline-block;
    vertical-align: middle
}

.status-pill.yellow {
    background-color: #f8bc34
}

.status-pill.red {
    background-color: #c21a1a
}

.status-pill.green {
    background-color: #71c21a
}

.status-pill.smaller {
    width: 8px;
    height: 8px
}

.status-pill+span {
    margin-left: 10px;
    display: inline-block;
    vertical-align: middle
}

.users-list-w .user-w {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 12px 0px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.users-list-w .user-w .user-avatar-w {
    width: 50px
}

.users-list-w .user-w .user-avatar-w .user-avatar {
    border-radius: 40px;
    overflow: hidden
}

.users-list-w .user-w .user-avatar-w .user-avatar img {
    max-width: 100%;
    height: auto
}

.users-list-w .user-w.with-status .user-avatar-w {
    position: relative
}

.users-list-w .user-w.with-status .user-avatar-w:before {
    content: "";
    width: 10px;
    height: 10px;
    position: absolute;
    top: 2px;
    right: 2px;
    border-radius: 10px;
    -webkit-box-shadow: 0px 0px 0px 3px #fff;
    box-shadow: 0px 0px 0px 3px #fff
}

.users-list-w .user-w.with-status.status-green .user-avatar-w:before {
    background-color: #579D1B
}

.users-list-w .user-w.with-status.status-red .user-avatar-w:before {
    background-color: #e65252
}

.users-list-w .user-w .user-name {
    padding-left: 20px;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.users-list-w .user-w .user-title {
    margin-bottom: 4px
}

.users-list-w .user-w .user-role {
    font-size: .63rem;
    text-transform: uppercase;
    color: rgba(0, 0, 0, 0.4)
}

.users-list-w .user-w .user-action {
    width: 50px;
    color: #047bf8;
    font-size: 18px
}

.os-tabs-controls {
    margin-bottom: 2rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.os-tabs-controls .nav-pills {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.os-tabs-controls .nav-tabs {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.activity-boxes-w .activity-box-w {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 0.8rem 0px;
    position: relative
}

.activity-boxes-w .activity-box-w:before {
    content: "";
    display: block;
    width: 3px;
    background-color: #c0cadd;
    top: 0px;
    bottom: 0px;
    left: 75px;
    position: absolute
}

.activity-boxes-w .activity-box-w:after {
    content: "";
    display: block;
    width: 20px;
    background-color: #c0cadd;
    top: 50%;
    height: 1px;
    left: 80px;
    position: absolute;
    z-index: 1
}

.activity-boxes-w .activity-box-w:first-child:before {
    top: 50%
}

.activity-boxes-w .activity-box-w:last-child:before {
    bottom: 50%
}

.activity-boxes-w .activity-time {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100px;
    flex: 0 0 100px;
    font-size: .63rem;
    text-transform: uppercase;
    color: rgba(0, 0, 0, 0.4);
    text-align: right;
    padding-right: 40px
}

.activity-boxes-w .activity-box {
    padding: 0.8rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    position: relative;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.activity-boxes-w .activity-box:before {
    position: absolute;
    top: 50%;
    left: -30px;
    content: "";
    width: 12px;
    height: 12px;
    border: 2px solid #60769f;
    background-color: #f2f4f8;
    border-radius: 20px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 2
}

.activity-boxes-w .activity-avatar {
    width: 50px;
    border-radius: 50px;
    overflow: hidden;
    margin-right: 1.5rem
}

.activity-boxes-w .activity-avatar img {
    max-width: 100%;
    height: auto
}

.activity-boxes-w .activity-info {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.activity-boxes-w .activity-role {
    font-size: .63rem;
    text-transform: uppercase;
    color: rgba(0, 0, 0, 0.4);
    margin-bottom: 0.2rem
}

.activity-boxes-w .activity-title {
    font-size: .63rem;
    text-transform: uppercase;
    display: block
}

.note-box {
    padding: 15%;
    background-color: #F8ECC7;
    margin-bottom: 2rem
}

.note-box .note-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 1rem;
    margin-bottom: 1rem
}

.note-box .note-content {
    font-weight: 300;
    color: rgba(0, 0, 0, 0.6);
    font-size: .81rem
}

.step-contents .step-content {
    padding: 0px 5%;
    display: none
}

.step-contents .step-content.active {
    display: block
}

.step-triggers {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 2.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.step-triggers .step-trigger {
    padding-bottom: 1rem;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    text-align: center;
    font-size: 1.08rem;
    color: rgba(0, 0, 0, 0.3);
    position: relative;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease
}

.step-triggers .step-trigger:focus {
    text-decoration: none
}

.step-triggers .step-trigger:hover {
    text-decoration: none;
    color: #3E4B5B;
    cursor: pointer
}

.step-triggers .step-trigger:before {
    position: absolute;
    content: "";
    height: 4px;
    background-color: #047bf8;
    left: 0px;
    right: 100%;
    bottom: -2px;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease
}

.step-triggers .step-trigger.complete:before {
    right: 0px
}

.step-triggers .step-trigger.active {
    color: #3E4B5B
}

.step-triggers .step-trigger.active:before {
    right: 0px
}

.invoice-w {
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    max-width: 800px;
    position: relative;
    overflow: hidden;
    padding: 100px;
    padding-bottom: 20px
}

.invoice-w:before {
    width: 140%;
    height: 450px;
    background-color: #faf9f3;
    position: absolute;
    top: -15%;
    left: -24%;
    -webkit-transform: rotate(-27deg);
    transform: rotate(-27deg);
    content: "";
    z-index: 1
}

.invoice-w .infos {
    position: relative;
    z-index: 2;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.invoice-w .infos .info-1 {
    font-size: 1.08rem
}

.invoice-w .infos .info-1 .company-name {
    font-size: 2.25rem;
    margin-bottom: 0.5rem;
    margin-top: 10px
}

.invoice-w .infos .info-1 .company-extra {
    font-size: .81rem;
    color: rgba(0, 0, 0, 0.4);
    margin-top: 1rem
}

.invoice-w .infos .info-2 {
    padding-top: 140px;
    text-align: right
}

.invoice-w .infos .info-2 .company-name {
    margin-bottom: 1rem;
    font-size: 1.26rem
}

.invoice-w .infos .info-2 .company-address {
    color: rgba(0, 0, 0, 0.6)
}

.invoice-w .terms {
    font-size: .81rem;
    margin-top: 2.5rem
}

.invoice-w .terms .terms-header {
    font-size: .9rem;
    margin-bottom: 10px
}

.invoice-w .terms .terms-content {
    color: rgba(0, 0, 0, 0.4)
}

.invoice-table thead th {
    border-bottom: 2px solid #333
}

.invoice-table tbody tr td {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.invoice-table tbody tr:last-child td {
    padding-bottom: 40px
}

.invoice-table tfoot tr td {
    border-top: 3px solid #333;
    font-size: 1.26rem
}

.invoice-heading {
    margin-bottom: 4rem;
    margin-top: 7rem;
    position: relative;
    z-index: 2
}

.invoice-heading h3 {
    margin-bottom: 0px
}

.invoice-footer {
    padding-top: 1rem;
    padding-bottom: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-top: 6rem
}

.invoice-footer .invoice-logo img {
    vertical-align: middle;
    height: 20px;
    width: auto;
    display: inline-block
}

.invoice-footer .invoice-logo span {
    vertical-align: middle;
    margin-left: 10px;
    display: inline-block
}

.invoice-footer .invoice-info span {
    display: inline-block
}

.invoice-footer .invoice-info span+span {
    margin-left: 1rem;
    padding-left: 1rem;
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

.invoice-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.invoice-body .invoice-desc {
    -webkit-box-flex: 0;
    -ms-flex: 0 1 250px;
    flex: 0 1 250px;
    font-size: 1.17rem
}

.big-error-w {
    width: 450px;
    text-align: center;
    padding: 50px;
    margin: 0px auto;
    margin-top: 100px
}

.big-error-w h1 {
    font-size: 4.75rem;
    margin-bottom: 0px
}

.big-error-w h5 {
    color: #047bf8;
    margin-bottom: 3rem
}

.big-error-w h4 {
    margin-bottom: 0px
}

.big-error-w form {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 1.5rem;
    margin-top: 1.5rem
}

.auth-box-wh {
    max-width: 450px;
    margin: 0px auto;
    background-color: #fff;
    border-radius: 4px;
    -webkit-box-shadow: 0px 0px 40px rgba(0, 0, 0, 0.1);
    box-shadow: 0px 0px 40px rgba(0, 0, 0, 0.1)
}

.auth-box-wh.wider {
    max-width: 500px
}

.auth-box-wh .logged-user-w {
    padding-top: 0px;
    padding-bottom: 10px
}

.auth-box-wh .logo-w {
    text-align: center;
    padding: 20%
}

.auth-box-wh .auth-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 1rem;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
    padding-left: 80px
}

.auth-box-wh .auth-header:after {
    content: "";
    background-color: #047bf8;
    width: 32px;
    height: 7px;
    border-radius: 2px;
    display: block;
    position: absolute;
    bottom: -4px;
    left: 80px
}

.auth-box-wh form {
    padding: 20px 80px;
    padding-bottom: 60px
}

.auth-box-wh form .form-check-inline {
    margin-left: 1rem
}

.auth-box-wh form .form-group {
    position: relative
}

.auth-box-wh form .form-group .pre-icon {
    position: absolute;
    bottom: 5px;
    font-size: 24px;
    color: #047bf8;
    left: -38px
}

.auth-box-wh .buttons-w {
    padding-top: 1rem;
    margin-top: 0.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05)
}

.auth-box-wh.centered {
    text-align: center
}

.auth-box-wh.centered .auth-header {
    padding-left: 0px
}

.auth-box-wh.centered .auth-header:after {
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    left: 50%
}

.timed-activities {
    margin-bottom: 1rem
}

.timed-activity {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 2rem;
    font-size: 1.08rem
}

.timed-activity .ta-date {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 130px;
    flex: 0 0 130px;
    color: rgba(0, 0, 0, 0.4);
    position: relative;
    text-align: center
}

.timed-activity .ta-date span {
    position: relative;
    display: inline-block
}

.timed-activity .ta-date span:after {
    content: "";
    position: absolute;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    bottom: -30px;
    width: 16px;
    height: 16px;
    border: 3px solid #C76DC8;
    border-radius: 8px;
    background-color: #fff
}

.timed-activity .ta-date:before {
    content: "";
    position: absolute;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    bottom: -20px;
    width: 1px;
    top: 70px;
    background-color: rgba(0, 0, 0, 0.1)
}

.timed-activity .ta-record-w {
    padding-left: 40px
}

.timed-activity .ta-record {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 10px;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.timed-activity .ta-timestamp {
    position: relative;
    padding-right: 40px;
    color: rgba(0, 0, 0, 0.4);
    width: 110px;
    white-space: nowrap
}

.timed-activity .ta-timestamp strong {
    color: #3E4B5B
}

.timed-activity .ta-timestamp:after {
    content: '\e910';
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: rgba(0, 0, 0, 0.3);
    font-size: 10px;
    position: absolute;
    top: 50%;
    right: 15px;
    -webkit-transform: translateY(-40%);
    transform: translateY(-40%);
    display: block
}

.timed-activity .ta-activity a {
    display: inline-block;
    padding-bottom: 1px;
    border-bottom: 1px solid #3b75e3
}

.timed-activity .ta-activity a:hover {
    text-decoration: none;
    color: #1747a1;
    border-bottom-color: #1747a1
}

.timed-activities.compact .timed-activity {
    display: block;
    padding-left: 30px;
    position: relative
}

.timed-activities.compact .timed-activity:before {
    position: absolute;
    width: 1px;
    background-color: rgba(0, 0, 0, 0.1);
    bottom: 0px;
    top: 45px;
    content: "";
    left: 8px
}

.timed-activities.compact .timed-activity .ta-date {
    -webkit-box-flex: 0;
    -ms-flex: 0;
    flex: 0;
    text-align: left;
    margin-bottom: 1.5rem
}

.timed-activities.compact .timed-activity .ta-date:before {
    display: none
}

.timed-activities.compact .timed-activity .ta-date span:after {
    left: -30px;
    top: 50%;
    bottom: auto;
    right: auto;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.timed-activities.compact .timed-activity .ta-record-w {
    padding-left: 20px
}

.timed-activities.compact .timed-activity .ta-record {
    display: block
}

.timed-activities.compact .timed-activity .ta-timestamp {
    font-size: .81rem;
    margin-bottom: 5px
}

.timed-activities.compact .timed-activity .ta-timestamp:after {
    right: auto;
    left: -20px;
    font-size: 8px
}

.timed-activities.compact .timed-activity .ta-activity {
    font-size: .9rem;
    margin-bottom: 10px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.os-progress-bar {
    margin-bottom: 1rem
}

.os-progress-bar .bar-labels {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 5px
}

.os-progress-bar .bar-labels span {
    font-size: .72rem
}

.os-progress-bar .bar-label-left span {
    margin-right: 5px
}

.os-progress-bar .bar-label-left span.positive {
    color: #619B2E
}

.os-progress-bar .bar-label-left span.negative {
    color: #D83536
}

.os-progress-bar .bar-label-right span {
    margin-left: 5px
}

.os-progress-bar .bar-label-right span.info {
    color: #456CF9
}

.os-progress-bar .bar-level-1,
.os-progress-bar .bar-level-2,
.os-progress-bar .bar-level-3 {
    border-radius: 12px;
    height: 6px
}

.os-progress-bar.blue .bar-level-1,
.os-progress-bar.primary .bar-level-1 {
    background-color: #F2F2F2
}

.os-progress-bar.blue .bar-level-2,
.os-progress-bar.primary .bar-level-2 {
    background-color: #98c9fd
}

.os-progress-bar.blue .bar-level-3,
.os-progress-bar.primary .bar-level-3 {
    background-color: #047bf8
}

.os-progress-bar.floated {
    float: right;
    width: 220px
}

.element-balances {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 20px 0px;
    -ms-flex-pack: distribute;
    justify-content: space-around
}

.element-balances .balance {
    padding: 10px 20px
}

.element-balances .balance+.balance {
    border-left: 1px solid rgba(0, 0, 0, 0.03)
}

.element-balances .balance-title {
    color: rgba(90, 99, 126, 0.49);
    font-size: 1.08rem
}

.element-balances .balance-value {
    font-size: 2.07rem;
    font-weight: 500;
    color: #303a46;
    white-space: nowrap
}

.element-balances .balance-value.danger {
    color: #b71b1b
}

.element-balances .balance-value span {
    display: inline-block;
    vertical-align: middle;
    line-height: 1.1
}

.element-balances .balance-value i {
    line-height: 1.1
}

.element-balances .balance-value .balance-value-success {
    color: #579D1B
}

.element-balances span.trending {
    padding: 3px 10px;
    border-radius: 30px;
    display: inline-block;
    font-size: .99rem;
    vertical-align: middle;
    margin-left: 10px
}

.element-balances span.trending .os-icon {
    margin-left: 5px;
    vertical-align: middle;
    font-size: 14px
}

.element-balances span.trending span {
    display: inline-block;
    vertical-align: middle
}

.element-balances span.trending-up {
    color: #fff;
    background-color: #579D1B
}

.element-balances span.trending-down {
    color: #fff;
    background-color: #e65252
}

.element-balances span.trending-up-basic {
    color: #579D1B;
    padding: 0px
}

.element-balances span.trending-down-basic {
    color: #e65252;
    padding: 0px
}

.example-column {
    padding: 12px 15px;
    background: #EEF5FF;
    border: 1px solid #AEC8FF;
    margin-bottom: 15px;
    text-align: center
}

.example-column+.example-column {
    border-left: none
}

.example-content .progress+.progress {
    margin-top: 1rem
}

.floated-chat-btn {
    z-index: 9999;
    position: fixed;
    bottom: 10px;
    right: 10px;
    background: #097CFF;
    -webkit-box-shadow: 0 2px 20px 0 rgba(46, 130, 255, 0.75);
    box-shadow: 0 2px 20px 0 rgba(46, 130, 255, 0.75);
    border-radius: 75px;
    color: #fff;
    padding: 12px 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    vertical-align: middle;
    font-size: 1.08rem;
    cursor: pointer;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.floated-chat-btn i {
    vertical-align: middle;
    display: inline-block;
    font-size: 24px
}

.floated-chat-btn span {
    vertical-align: middle;
    display: inline-block;
    font-weight: 500
}

.floated-chat-btn i+span {
    margin-left: 15px
}

.floated-chat-btn:hover {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
    background-color: #0064d5;
    -webkit-box-shadow: 0 2px 30px 0 rgba(46, 130, 255, 0.8);
    box-shadow: 0 2px 30px 0 rgba(46, 130, 255, 0.8)
}

.floated-chat-w {
    z-index: 9999;
    position: fixed;
    bottom: 70px;
    right: 10px;
    visibility: hidden;
    opacity: 0;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease
}

.floated-chat-w.active {
    visibility: visible;
    opacity: 1;
    -webkit-transform: translateY(0px);
    transform: translateY(0px)
}

.floated-chat-w .floated-chat-i {
    background-color: #fff;
    -webkit-box-shadow: 0 2px 40px 0 rgba(43, 132, 210, 0.41);
    box-shadow: 0 2px 40px 0 rgba(43, 132, 210, 0.41);
    border-radius: 10px;
    width: 320px;
    position: relative
}

.floated-chat-w .floated-chat-i .chat-close {
    position: absolute;
    top: 10px;
    right: 10px;
    color: rgba(0, 0, 0, 0.8);
    font-size: 10px;
    cursor: pointer
}

.floated-chat-w .chat-head {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.floated-chat-w .chat-head .user-w {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.floated-chat-w .chat-head .user-w .user-avatar-w {
    width: 50px
}

.floated-chat-w .chat-head .user-w .user-avatar-w .user-avatar {
    border-radius: 40px;
    overflow: hidden
}

.floated-chat-w .chat-head .user-w .user-avatar-w .user-avatar img {
    max-width: 100%;
    height: auto
}

.floated-chat-w .chat-head .user-w.with-status .user-avatar-w {
    position: relative
}

.floated-chat-w .chat-head .user-w.with-status .user-avatar-w:before {
    content: "";
    width: 10px;
    height: 10px;
    position: absolute;
    top: 2px;
    right: 2px;
    border-radius: 10px;
    -webkit-box-shadow: 0px 0px 0px 3px #fff;
    box-shadow: 0px 0px 0px 3px #fff
}

.floated-chat-w .chat-head .user-w.with-status.status-green .user-avatar-w:before {
    background-color: #579D1B
}

.floated-chat-w .chat-head .user-w.with-status.status-red .user-avatar-w:before {
    background-color: #e65252
}

.floated-chat-w .chat-head .user-w .user-name {
    padding-left: 20px;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.floated-chat-w .chat-head .user-w .user-title {
    margin-bottom: 2px;
    color: #047bf8
}

.floated-chat-w .chat-head .user-w .user-role {
    font-weight: 500;
    font-size: .81rem
}

.floated-chat-w .chat-head .user-w .user-action {
    width: 50px;
    color: #047bf8;
    font-size: 18px
}

.floated-chat-w .chat-messages {
    padding: 20px;
    height: 300px;
    position: relative;
    overflow: hidden
}

.floated-chat-w .chat-messages .message {
    margin-bottom: 12px
}

.floated-chat-w .chat-messages .message .message-content {
    color: #594939;
    padding: 10px 20px;
    background-color: #fcf6ee;
    border-radius: 20px 20px 20px 0px;
    max-width: 80%;
    display: inline-block;
    text-align: left
}

.floated-chat-w .chat-messages .message.self {
    text-align: right
}

.floated-chat-w .chat-messages .message.self .message-content {
    border-radius: 20px 20px 0px 20px;
    background-color: #e2efff;
    color: #2A4E7F
}

.floated-chat-w .chat-messages .date-break {
    text-align: center;
    margin-bottom: 10px;
    color: rgba(0, 0, 0, 0.4)
}

.floated-chat-w .chat-controls {
    padding: 10px;
    padding-top: 0px;
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

.floated-chat-w .chat-controls .message-input {
    border: 1px solid transparent;
    background-color: #fff;
    padding: 10px;
    width: 100%;
    display: block;
    border-radius: 0px
}

.floated-chat-w .chat-controls .message-input:focus {
    outline: none;
    border-bottom: 1px solid #047bf8
}

.floated-chat-w .chat-controls .chat-extra {
    text-align: left;
    padding-left: 0px;
    padding-top: 10px
}

.floated-chat-w .chat-controls .chat-extra a {
    display: inline-block;
    margin-left: 10px;
    font-size: 16px;
    position: relative
}

.floated-chat-w .chat-controls .chat-extra a .extra-tooltip {
    background-color: rgba(0, 0, 0, 0.9);
    color: #fff;
    font-weight: 500;
    font-size: .63rem;
    text-transform: uppercase;
    display: inline-block;
    padding: 2px 7px;
    border-radius: 4px;
    position: absolute;
    top: -20px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    white-space: nowrap;
    display: none
}

.floated-chat-w .chat-controls .chat-extra a:hover {
    text-decoration: none
}

.floated-chat-w .chat-controls .chat-extra a:hover .extra-tooltip {
    display: block
}

.app-email-w a:focus,
.app-email-w a:hover {
    text-decoration: none
}

.app-email-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    background-color: #fff;
    border-radius: 4px
}

.ae-side-menu {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 170px;
    flex: 0 0 170px;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #fff
}

.ae-side-menu .aem-head {
    padding: 10px 20px;
    height: 50px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 10px
}

.ae-side-menu .ae-main-menu {
    list-style: none;
    padding: 0px;
    margin: 0px
}

.ae-side-menu .ae-main-menu li {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative
}

.ae-side-menu .ae-main-menu li a {
    display: block;
    padding: 20px
}

.ae-side-menu .ae-main-menu li a i {
    font-size: 20px;
    display: inline-block;
    vertical-align: middle;
    color: #047bf8;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.ae-side-menu .ae-main-menu li a span {
    margin-left: 20px;
    display: inline-block;
    vertical-align: middle;
    color: #3E4B5B;
    font-weight: 500;
    text-transform: uppercase
}

.ae-side-menu .ae-main-menu li:after {
    content: "";
    position: absolute;
    right: 0px;
    top: -1px;
    bottom: -1px;
    width: 5px;
    opacity: 0;
    background-color: #047bf8;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease
}

.ae-side-menu .ae-main-menu li:hover a i {
    -webkit-transform: translateX(5px);
    transform: translateX(5px)
}

.ae-side-menu .ae-main-menu li:hover:after,
.ae-side-menu .ae-main-menu li.active:after {
    opacity: 1
}

.ae-side-menu .ae-labels {
    margin-top: 20px
}

.ae-side-menu .ae-labels .ae-labels-header {
    padding: 20px
}

.ae-side-menu .ae-labels .ae-labels-header i {
    color: #047bf8;
    font-size: 20px;
    vertical-align: middle;
    display: inline-block
}

.ae-side-menu .ae-labels .ae-labels-header span {
    margin-left: 20px;
    text-transform: uppercase;
    font-weight: 500;
    vertical-align: middle;
    display: inline-block
}

.ae-side-menu .ae-labels .ae-label {
    display: block;
    padding: 10px;
    padding-left: 30px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    color: #3E4B5B;
    white-space: nowrap
}

.ae-side-menu .ae-labels .ae-label .label-pin {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 10px;
    background-color: #047bf8;
    vertical-align: middle
}

.ae-side-menu .ae-labels .ae-label .label-value {
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px
}

.ae-side-menu .ae-labels .ae-label:before {
    content: "";
    position: absolute;
    left: 10px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.ae-side-menu .ae-labels .ae-label.ae-label-red .label-pin {
    background-color: #e65252
}

.ae-side-menu .ae-labels .ae-label.ae-label-green .label-pin {
    background-color: #579D1B
}

.ae-side-menu .ae-labels .ae-label.ae-label-yellow .label-pin {
    background-color: #F9F1D9
}

.ae-list-w {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 370px;
    flex: 0 0 370px;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #fff
}

.ae-list-w .ael-head {
    padding: 10px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 50px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.ae-list-w .ael-head a {
    display: inline-block;
    vertical-align: middle
}

.ae-list-w .ael-head a i {
    color: #047bf8;
    font-size: 20px
}

.ae-list-w .ael-head a:hover {
    text-decoration: none
}

.ae-list-w .ael-head a+a {
    margin-left: 15px
}

.ae-list-w .ae-list .ae-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    cursor: pointer
}

.ae-list-w .ae-list .ae-item:after {
    content: "";
    position: absolute;
    right: 0px;
    top: -1px;
    bottom: -1px;
    width: 5px;
    opacity: 0;
    background-color: #047bf8;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease
}

.ae-list-w .ae-list .ae-item:hover:after,
.ae-list-w .ae-list .ae-item.active:after {
    opacity: 1
}

.ae-list-w .ae-list .ae-item.with-status .user-avatar-w {
    position: relative
}

.ae-list-w .ae-list .ae-item.with-status .user-avatar-w:before {
    content: "";
    width: 10px;
    height: 10px;
    position: absolute;
    top: 2px;
    right: 2px;
    border-radius: 10px;
    -webkit-box-shadow: 0px 0px 0px 3px #fff;
    box-shadow: 0px 0px 0px 3px #fff
}

.ae-list-w .ae-list .ae-item.with-status.status-green .user-avatar-w:before {
    background-color: #579D1B
}

.ae-list-w .ae-list .ae-item.with-status.status-red .user-avatar-w:before {
    background-color: #e65252
}

.ae-list-w .ae-list .ae-item.with-status.status-blue .user-avatar-w:before {
    background-color: #047bf8
}

.ae-list-w .ae-list .ae-item.with-status.status-yellow .user-avatar-w:before {
    background-color: #F9F1D9
}

.ae-list-w .ae-list .aei-image {
    margin-right: 20px
}

.ae-list-w .ae-list .aei-image .user-avatar-w {
    width: 50px
}

.ae-list-w .ae-list .aei-image .user-avatar-w img {
    border-radius: 50px;
    display: block;
    width: 50px;
    height: auto
}

.ae-list-w .ae-list .aei-content {
    position: relative
}

.ae-list-w .ae-list .aei-content .aei-timestamp {
    position: absolute;
    top: -10px;
    right: 0px;
    color: rgba(0, 0, 0, 0.3);
    font-size: .63rem
}

.ae-list-w .ae-list .aei-content .aei-sub-title {
    color: #047bf8;
    text-transform: uppercase;
    font-size: .72rem;
    font-weight: 500
}

.ae-list-w .ae-list .aei-content .aei-text {
    color: rgba(0, 0, 0, 0.5);
    font-size: .81rem
}

.ae-content-w {
    background-color: #f6f7f8
}

.ae-content-w .aec-head {
    height: 50px;
    padding: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    background-color: #fff
}

.ae-content-w .aec-head .separate {
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    padding-right: 15px;
    margin-right: 5px
}

.ae-content-w .aec-head a {
    display: inline-block;
    vertical-align: middle
}

.ae-content-w .aec-head a i {
    color: #047bf8;
    font-size: 20px
}

.ae-content-w .aec-head a.highlight i {
    color: #CB9E48
}

.ae-content-w .aec-head a:hover {
    text-decoration: none
}

.ae-content-w .aec-head a+a {
    margin-left: 15px
}

.ae-content-w .aec-head .actions-left {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.ae-content-w .aec-head .actions-right {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.ae-content-w .aec-head .user-avatar {
    margin-left: 40px;
    padding-left: 10px;
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

.ae-content-w .aec-head .user-avatar img {
    display: block;
    width: 30px;
    height: auto;
    border-radius: 40px
}

.ae-content {
    padding: 40px
}

.aec-full-message-w {
    position: relative;
    z-index: 2;
    margin-bottom: 40px
}

.aec-full-message-w .more-messages {
    position: absolute;
    left: 50%;
    top: -55px;
    color: rgba(0, 0, 0, 0.4);
    font-size: .72rem;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    display: inline-block;
    border-bottom: 1px solid transparent;
    padding-bottom: 1px
}

.aec-full-message-w .more-messages:hover {
    cursor: pointer;
    color: #047bf8;
    border-bottom: 1px solid #047bf8
}

.aec-full-message-w.show-pack {
    margin-top: 40px
}

.aec-full-message-w.show-pack:before {
    z-index: -1;
    content: "";
    position: absolute;
    bottom: 30px;
    left: 30px;
    right: 30px;
    top: -30px;
    background-color: rgba(255, 255, 255, 0.2);
    -webkit-box-shadow: 0 0px 15px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 0px 15px 0 rgba(0, 0, 0, 0.1);
    border-radius: 2px
}

.aec-full-message-w.show-pack:after {
    z-index: -1;
    content: "";
    position: absolute;
    bottom: 15px;
    left: 15px;
    right: 15px;
    top: -15px;
    background-color: rgba(255, 255, 255, 0.7);
    -webkit-box-shadow: 0 0px 15px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 0px 15px 0 rgba(0, 0, 0, 0.1);
    border-radius: 2px
}

.aec-full-message-w .aec-full-message {
    background-color: #fff;
    -webkit-box-shadow: 0 0px 15px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 0px 15px 0 rgba(0, 0, 0, 0.1);
    border-radius: 2px
}

.aec-full-message-w .message-head {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.aec-full-message-w .message-head .user-w {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.aec-full-message-w .message-head .user-w .user-avatar-w {
    width: 50px
}

.aec-full-message-w .message-head .user-w .user-avatar-w .user-avatar {
    border-radius: 40px;
    overflow: hidden
}

.aec-full-message-w .message-head .user-w .user-avatar-w .user-avatar img {
    max-width: 100%;
    height: auto
}

.aec-full-message-w .message-head .user-w.with-status .user-avatar-w {
    position: relative
}

.aec-full-message-w .message-head .user-w.with-status .user-avatar-w:before {
    content: "";
    width: 10px;
    height: 10px;
    position: absolute;
    top: 2px;
    right: 2px;
    border-radius: 10px;
    -webkit-box-shadow: 0px 0px 0px 3px #fff;
    box-shadow: 0px 0px 0px 3px #fff
}

.aec-full-message-w .message-head .user-w.with-status.status-green .user-avatar-w:before {
    background-color: #579D1B
}

.aec-full-message-w .message-head .user-w.with-status.status-red .user-avatar-w:before {
    background-color: #e65252
}

.aec-full-message-w .message-head .user-w .user-name {
    padding-left: 20px;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.aec-full-message-w .message-head .user-w .user-title {
    margin-bottom: 2px;
    color: #047bf8
}

.aec-full-message-w .message-head .user-w .user-role {
    font-weight: 500;
    font-size: .81rem
}

.aec-full-message-w .message-head .user-w .user-role span {
    display: inline-block;
    margin-left: 5px;
    color: rgba(0, 0, 0, 0.4)
}

.aec-full-message-w .message-head .user-w .user-action {
    width: 50px;
    color: #047bf8;
    font-size: 18px
}

.aec-full-message-w .message-head .message-info {
    color: rgba(0, 0, 0, 0.3);
    font-size: .72rem;
    text-align: right
}

.aec-full-message-w .message-content {
    padding: 6% 10% 8% 10%
}

.aec-full-message-w .message-attachments {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-top: 20px;
    margin-top: 20px
}

.aec-full-message-w .message-attachments .attachments-heading {
    text-transform: uppercase;
    font-size: .72rem;
    color: rgba(0, 0, 0, 0.4)
}

.aec-full-message-w .message-attachments .attachments-docs {
    margin-top: 15px
}

.aec-full-message-w .message-attachments .attachments-docs a {
    display: inline-block
}

.aec-full-message-w .message-attachments .attachments-docs a i {
    display: inline-block;
    vertical-align: middle;
    margin-right: 10px
}

.aec-full-message-w .message-attachments .attachments-docs a span {
    display: inline-block;
    vertical-align: middle
}

.aec-full-message-w .message-attachments .attachments-docs a:hover {
    text-decoration: none
}

.aec-full-message-w .message-attachments .attachments-docs a+a {
    padding-left: 15px;
    margin-left: 15px;
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

.older-pack {
    display: none
}

.aec-reply {
    padding: 40px;
    background-color: #fff;
    -webkit-box-shadow: 0 0px 15px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 0px 15px 0 rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    overflow: hidden
}

.aec-reply .reply-header {
    position: relative;
    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
    padding-bottom: 30px;
    margin-bottom: 0px
}

.aec-reply .reply-header i {
    font-size: 130px;
    color: rgba(0, 86, 255, 0.05);
    position: absolute;
    top: -70px;
    left: -70px
}

.aec-reply .reply-header h5 {
    margin: 0px;
    font-size: 1.08rem
}

.aec-reply .reply-header h5 span {
    color: #047bf8;
    border-bottom: 1px dotted #047bf8;
    display: inline-block
}

.aec-reply .buttons-w {
    padding-top: 20px;
    margin-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.aec-reply .buttons-w .btn,
.aec-reply .buttons-w .all-wrapper .fc-button,
.all-wrapper .aec-reply .buttons-w .fc-button {
    text-transform: uppercase;
    padding: 5px 10px;
    font-size: 0.7rem
}

.aec-reply .buttons-w .btn .os-icon,
.aec-reply .buttons-w .all-wrapper .fc-button .os-icon,
.all-wrapper .aec-reply .buttons-w .fc-button .os-icon {
    margin-right: 10px
}

.aec-reply .cke_chrome {
    border: none
}

.aec-reply .cke_bottom {
    border: none
}

.app-email-w.compact-side-menu .ae-side-menu {
    -webkit-box-flex: 0;
    -ms-flex: 0 1 60px;
    flex: 0 1 60px;
    text-align: center
}

.app-email-w.compact-side-menu .ae-side-menu .aem-head {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.app-email-w.compact-side-menu .ae-side-menu .ae-main-menu li a span {
    display: none
}

.app-email-w.compact-side-menu .ae-side-menu .ae-labels .ae-label {
    padding-left: 10px
}

.app-email-w.compact-side-menu .ae-side-menu .ae-labels .ae-label span.label-value {
    display: none
}

.app-email-w.compact-side-menu .ae-side-menu .ae-labels-header span {
    display: none
}

.projects-list {
    margin-bottom: 2rem
}

.projects-list .project-head {
    padding: 20px 40px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.projects-list .project-head .project-title {
    text-transform: uppercase;
    letter-spacing: 2px
}

.projects-list .project-head .project-title h5 {
    margin-bottom: 0
}

.projects-list .project-head .project-users {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 220px;
    flex: 0 0 220px;
    text-align: right
}

.projects-list .project-head .project-users .avatar {
    width: 35px;
    border-radius: 35px;
    -webkit-box-shadow: 0px 0px 0px 5px #fff;
    box-shadow: 0px 0px 0px 5px #fff;
    display: inline-block;
    overflow: hidden;
    vertical-align: middle
}

.projects-list .project-head .project-users .avatar img {
    height: auto;
    width: 35px
}

.projects-list .project-head .project-users .more {
    background-color: #047bf8;
    display: inline-block;
    vertical-align: middle;
    position: relative;
    left: -10px;
    padding: 2px 7px;
    border-radius: 12px;
    color: #fff;
    font-size: .63rem
}

.projects-list .project-info {
    padding: 20px 40px
}

.projects-list .project-info .os-progress-bar {
    margin-bottom: 0px
}

.projects-list .project-info .os-progress-bar+.os-progress-bar {
    margin-top: 1rem
}

.projects-list .project-box+.project-box {
    margin-top: 2rem
}

.full-chat-w .full-chat-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    background-color: #fff;
    padding: 0px
}

.full-chat-w .full-chat-left {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 340px;
    flex: 0 0 340px;
    background-color: #f6f7f8;
    border-radius: 4px 0px 0px 4px;
    padding: 20px 0px
}

.full-chat-w .full-chat-left .os-tabs-w .nav {
    padding: 0px 20px
}

.full-chat-w .full-chat-left .chat-search {
    padding: 20px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.full-chat-w .full-chat-left .element-search {
    position: relative
}

.full-chat-w .full-chat-left .element-search:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    left: 15px;
    top: 48%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    font-size: 20px;
    content: "\e92c";
    color: rgba(0, 0, 0, 0.2)
}

.full-chat-w .full-chat-left .element-search input {
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    background-color: #fff;
    border-radius: 30px;
    padding: 10px 15px 10px 50px;
    display: block;
    width: 100%;
    outline: none
}

.full-chat-w .full-chat-left .element-search input::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.3)
}

.full-chat-w .full-chat-left .element-search input:-ms-input-placeholder {
    color: rgba(0, 0, 0, 0.3)
}

.full-chat-w .full-chat-left .element-search input::placeholder {
    color: rgba(0, 0, 0, 0.3)
}

.full-chat-w .full-chat-left .user-list .user-w {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 20px 30px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.full-chat-w .full-chat-left .user-list .user-w .avatar {
    margin-right: 20px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50px;
    flex: 0 0 50px
}

.full-chat-w .full-chat-left .user-list .user-w .avatar img {
    width: 50px;
    height: auto;
    border-radius: 50px;
    display: block
}

.full-chat-w .full-chat-left .user-list .user-w .user-info {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto
}

.full-chat-w .full-chat-left .user-list .user-w .user-name {
    font-weight: 500;
    font-size: .99rem;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.full-chat-w .full-chat-left .user-list .user-w .last-message {
    color: rgba(0, 0, 0, 0.4);
    font-size: .81rem;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.full-chat-w .full-chat-left .user-list .user-w .user-date {
    float: right;
    padding: 2px 7px;
    background-color: #fff;
    border-radius: 12px;
    font-size: .72rem;
    color: rgba(0, 0, 0, 0.3);
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.full-chat-w .full-chat-left .user-list .user-w:hover {
    background-color: #047bf8;
    cursor: pointer
}

.full-chat-w .full-chat-left .user-list .user-w:hover .user-name {
    color: #fff
}

.full-chat-w .full-chat-left .user-list .user-w:hover .last-message {
    color: rgba(255, 255, 255, 0.5)
}

.full-chat-w .full-chat-left .user-list .user-w:hover .user-date {
    background-color: #046fdf;
    color: rgba(255, 255, 255, 0.3)
}

.full-chat-w .full-chat-middle {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    background-color: #fff
}

.full-chat-w .full-chat-middle .chat-head {
    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
    padding: 10px 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.full-chat-w .full-chat-middle .user-info {
    font-size: 1.08rem
}

.full-chat-w .full-chat-middle .user-info span {
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px
}

.full-chat-w .full-chat-middle .user-info a {
    display: inline-block;
    vertical-align: middle;
    border-bottom: 1px dotted #047bf8
}

.full-chat-w .full-chat-middle .user-info a:hover {
    text-decoration: none
}

.full-chat-w .full-chat-middle .user-actions a {
    margin-left: 1rem;
    font-size: 24px;
    display: inline-block;
    vertical-align: middle
}

.full-chat-w .chat-content-w {
    height: 600px;
    overflow: hidden;
    position: relative
}

.full-chat-w .chat-content-w .chat-content {
    padding: 50px;
    min-height: 600px
}

.full-chat-w .chat-content-w .chat-date-separator {
    text-align: center;
    color: rgba(0, 0, 0, 0.3);
    font-size: .81rem;
    position: relative;
    margin: 40px 0px
}

.full-chat-w .chat-content-w .chat-date-separator:before {
    content: "";
    background-color: rgba(0, 0, 0, 0.1);
    height: 1px;
    width: 100%;
    position: absolute;
    top: 50%;
    left: 0px;
    right: 0px
}

.full-chat-w .chat-content-w .chat-date-separator span {
    display: inline-block;
    background-color: #fff;
    padding: 0px 10px;
    position: relative
}

.full-chat-w .chat-content-w .chat-message {
    margin-bottom: 20px
}

.full-chat-w .chat-content-w .chat-message .chat-message-content {
    padding: 15px 35px;
    background-color: #fff9f0;
    color: #594939;
    max-width: 400px;
    display: inline-block;
    margin-bottom: -20px;
    margin-left: 20px;
    border-radius: 20px;
    text-align: left
}

.full-chat-w .chat-content-w .chat-message .chat-message-avatar {
    display: inline-block;
    vertical-align: bottom
}

.full-chat-w .chat-content-w .chat-message .chat-message-avatar img {
    width: 40px;
    height: auto;
    border-radius: 30px;
    display: inline-block;
    -webkit-box-shadow: 0px 0px 0px 10px #fff;
    box-shadow: 0px 0px 0px 10px #fff
}

.full-chat-w .chat-content-w .chat-message .chat-message-date {
    display: inline-block;
    vertical-align: bottom;
    margin-left: 10px;
    margin-right: 10px;
    font-size: .72rem;
    color: rgba(0, 0, 0, 0.3)
}

.full-chat-w .chat-content-w .chat-message.self {
    text-align: right
}

.full-chat-w .chat-content-w .chat-message.self .chat-message-content {
    background-color: #f0f9ff;
    color: #2A4E7F;
    margin-right: 20px;
    margin-left: 0px
}

.full-chat-w .chat-controls {
    padding: 20px;
    padding-top: 0px;
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

.full-chat-w .chat-input input[type="text"] {
    padding: 20px 20px 20px 0px;
    border: none;
    display: block;
    width: 100%;
    outline: none
}

.full-chat-w .chat-input-extra {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.full-chat-w .chat-input-extra .chat-extra-actions a {
    margin-right: 10px;
    display: inline-block
}

.full-chat-w .full-chat-right {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 260px;
    flex: 0 0 260px;
    background-color: #fff;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px
}

.full-chat-w .user-intro {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center
}

.full-chat-w .user-intro .avatar {
    width: 90px;
    height: 90px;
    display: inline-block
}

.full-chat-w .user-intro .avatar img {
    width: 90px;
    border-radius: 60px;
    height: auto
}

.full-chat-w .user-intro .user-intro-info {
    margin-top: 1rem
}

.full-chat-w .user-intro .user-intro-info .user-name {
    margin-top: 0px;
    margin-bottom: 0px;
    color: #047bf8
}

.full-chat-w .user-intro .user-intro-info .user-sub {
    color: rgba(0, 0, 0, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: .72rem;
    margin-top: 5px
}

.full-chat-w .user-intro .user-intro-info .user-social {
    margin-top: 1rem
}

.full-chat-w .user-intro .user-intro-info .user-social a {
    display: inline-block;
    margin: 0px 6px;
    font-size: 24px
}

.full-chat-w .user-intro .user-intro-info .user-social a:hover {
    text-decoration: none
}

.full-chat-w .user-intro .user-intro-info .user-social i.os-icon.os-icon-twitter {
    color: #31a7f3
}

.full-chat-w .user-intro .user-intro-info .user-social i.os-icon.os-icon-facebook {
    color: #175dc5
}

.full-chat-w .chat-info-section {
    padding: 20px
}

.full-chat-w .chat-info-section .ci-header i {
    color: #047bf8;
    font-size: 20px;
    margin-right: 10px;
    display: inline-block;
    vertical-align: middle
}

.full-chat-w .chat-info-section .ci-header span {
    text-transform: uppercase;
    color: rgba(0, 0, 0, 0.5);
    letter-spacing: 2px;
    display: inline-block;
    vertical-align: middle
}

.full-chat-w .chat-info-section .ci-content {
    padding: 20px
}

.full-chat-w .chat-info-section .ci-content .ci-file-list ul {
    list-style-type: square;
    color: #98c9fd;
    margin-left: 0px;
    margin-bottom: 0px;
    padding-left: 10px
}

.full-chat-w .chat-info-section .ci-content .ci-file-list ul li {
    margin: 5px
}

.full-chat-w .chat-info-section .ci-content .ci-file-list ul li a {
    font-size: .81rem;
    border-bottom: 1px solid #047bf8
}

.full-chat-w .chat-info-section .ci-content .ci-file-list ul li a:hover {
    text-decoration: none
}

.full-chat-w .chat-info-section .ci-content .ci-photos-list {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.full-chat-w .chat-info-section .ci-content .ci-photos-list img {
    margin: 2%;
    border-radius: 6px;
    width: 45%;
    display: inline-block;
    height: auto
}

.full-chat-w .chat-info-section+.chat-info-section {
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

.pricing-plans {
    background-color: #fff
}

.pricing-plan+.pricing-plan {
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

.pricing-plan {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
    padding-bottom: 20px;
    background-color: #f9f9f9;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.pricing-plan.with-hover-effect:hover,
.pricing-plan.highlight {
    -webkit-box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
    -webkit-box-shadow: 0 2px 30px 0 rgba(54, 88, 206, 0.2);
    box-shadow: 0 2px 30px 0 rgba(54, 88, 206, 0.2)
}

.pricing-plan.with-hover-effect:hover .plan-price-w .price-value,
.pricing-plan.highlight .plan-price-w .price-value {
    color: #047bf8
}

.pricing-plan.with-hover-effect:hover .plan-head,
.pricing-plan.highlight .plan-head {
    -webkit-box-shadow: inset 0px 5px 0px #047bf8;
    box-shadow: inset 0px 5px 0px #047bf8
}

.pricing-plan.with-hover-effect:hover .plan-name {
    color: #3E4B5B
}

.pricing-plan.highlight {
    margin-top: -20px;
    margin-bottom: -20px
}

.pricing-plan .plan-image img {
    width: 80px
}

.pricing-plan .plan-head {
    background-color: #fff;
    padding-top: 40px
}

.pricing-plan .plan-body {
    background-color: #fff
}

.pricing-plan .plan-name {
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: 1.5rem;
    color: #047bf8;
    font-weight: 500;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    padding-bottom: 40px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.pricing-plan .plan-image+.plan-name {
    margin-top: 20px
}

.pricing-plan .plan-price-w {
    padding-top: 40px;
    margin-bottom: 40px
}

.pricing-plan .plan-price-w .price-value {
    font-size: 3.6rem;
    line-height: 1;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.pricing-plan .plan-price-w .price-label {
    text-transform: uppercase;
    letter-spacing: 2px;
    color: rgba(0, 0, 0, 0.3);
    padding-top: 5px;
    margin-top: 5px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    display: inline-block
}

.pricing-plan .plan-btn-w {
    padding-bottom: 40px
}

.pricing-plan .plan-description {
    text-align: left;
    padding: 30px 15%;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    color: rgba(0, 0, 0, 0.5)
}

.pricing-plan .plan-description h6 {
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-top: 30px;
    font-size: .9rem
}

.pricing-plan .plan-description ul {
    list-style: none;
    padding-left: 30px;
    margin-top: 20px
}

.pricing-plan .plan-description ul li {
    position: relative;
    margin-bottom: 10px
}

.pricing-plan .plan-description ul li:before {
    content: '\e961';
    color: #047bf8;
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    position: absolute;
    left: -30px;
    top: 5px
}

.pipeline {
    border-radius: 4px;
    background-color: #fff;
    -webkit-box-shadow: 0 2px 30px 0 rgba(16, 37, 133, 0.5);
    box-shadow: 0 2px 30px 0 rgba(16, 37, 133, 0.5);
    padding: 20px;
    margin-bottom: 20px
}

.pipeline.blue {
    background-color: #1D54EF;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#1D54EF), to(#4327DF));
    background-image: linear-gradient(-180deg, #1D54EF 0%, #4327DF 100%);
    -webkit-box-shadow: 0 2px 30px 0 rgba(16, 37, 133, 0.5);
    box-shadow: 0 2px 30px 0 rgba(16, 37, 133, 0.5)
}

.pipeline.teal {
    background-color: #07B77F;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#07B77F), color-stop(98%, #2767DF));
    background-image: linear-gradient(-180deg, #07B77F 0%, #2767DF 98%);
    -webkit-box-shadow: 0 2px 30px 0 rgba(7, 186, 171, 0.5);
    box-shadow: 0 2px 30px 0 rgba(7, 186, 171, 0.5)
}

.pipeline.purple {
    background-color: #28428c;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#28428c), to(#510d8c));
    background-image: linear-gradient(-180deg, #28428c 0%, #510d8c 100%);
    -webkit-box-shadow: 0 2px 30px 0 rgba(42, 16, 133, 0.5);
    box-shadow: 0 2px 30px 0 rgba(42, 16, 133, 0.5)
}

.pipeline.green {
    background-color: #7BC10C;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#7BC10C), color-stop(95%, #057051));
    background-image: linear-gradient(-180deg, #7BC10C 0%, #057051 95%);
    -webkit-box-shadow: 0 2px 30px 0 rgba(31, 219, 78, 0.5);
    box-shadow: 0 2px 30px 0 rgba(31, 219, 78, 0.5)
}

.pipeline.red {
    background-color: #960644;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#960644), color-stop(95%, #af760b));
    background-image: linear-gradient(-180deg, #960644 0%, #af760b 95%);
    -webkit-box-shadow: 0 2px 30px 0 rgba(154, 7, 58, 0.5);
    box-shadow: 0 2px 30px 0 rgba(154, 7, 58, 0.5)
}

.pipeline.pink {
    background-color: #a0008f;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#a0008f), color-stop(98%, #2767DF));
    background-image: linear-gradient(-180deg, #a0008f 0%, #2767DF 98%);
    -webkit-box-shadow: 0 2px 30px 0 rgba(7, 8, 186, 0.5);
    box-shadow: 0 2px 30px 0 rgba(7, 8, 186, 0.5)
}

.pipeline.white {
    background-color: #fff;
    -webkit-box-shadow: 0 2px 30px 0 rgba(16, 37, 133, 0.1);
    box-shadow: 0 2px 30px 0 rgba(16, 37, 133, 0.1)
}

.pipeline.white .pipeline-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 15px
}

.pipeline.white .pipeline-header .pipeline-name {
    color: #334152
}

.pipeline.white .pipeline-header .pipeline-count {
    color: rgba(0, 0, 0, 0.5)
}

.pipeline.white .pipeline-header .pipeline-settings {
    color: rgba(0, 0, 0, 0.4)
}

.pipeline.white .pipeline-header .pipeline-value {
    color: #047bf8
}

.pipeline.white .pipeline-item {
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 4px;
    margin-bottom: 15px;
    border: 1px solid #d1d4e8;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    cursor: move
}

.pipeline.white .pipeline-item:last-child {
    margin-bottom: 0px
}

.pipeline.white .pipeline-item:hover {
    border: 1px solid #9ea4cf;
    -webkit-box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1)
}

.pipeline.white .pipeline-item .pi-foot {
    border-radius: 0px 0px 4px 4px
}

.pipeline.white.lined-primary {
    border-top: 5px solid #047bf8;
    border-radius: 0px 0px 4px 4px
}

.pipeline.white.lined-success {
    border-top: 5px solid #579D1B;
    border-radius: 0px 0px 4px 4px
}

.pipeline.white.lined-danger {
    border-top: 5px solid #e65252;
    border-radius: 0px 0px 4px 4px
}

.pipeline.white.lined-warning {
    border-top: 5px solid #F9F1D9;
    border-radius: 0px 0px 4px 4px
}

.pipeline-header {
    position: relative;
    margin-bottom: 20px
}

.pipeline-header .pipeline-header-numbers {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.pipeline-header .pipeline-name {
    text-transform: uppercase;
    letter-spacing: 3px;
    color: #fff;
    margin-bottom: 5px;
    line-height: 1;
    padding-right: 30px
}

.pipeline-header .pipeline-value {
    color: #FFD038;
    letter-spacing: 3px;
    font-size: 1.17rem
}

.pipeline-header .pipeline-count {
    font-size: .81rem;
    color: rgba(255, 255, 255, 0.7)
}

.pipeline-header .pipeline-settings {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    position: absolute;
    top: 0px;
    right: 0px
}

.pipeline-body {
    min-height: 100px
}

.pipeline-body.empty {
    border: 1px dashed rgba(0, 0, 0, 0.2)
}

.pipeline-item {
    background-color: #fff;
    margin-bottom: 20px;
    border-radius: 4px;
    position: relative;
    -webkit-box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.4);
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.4)
}

.pipeline-item:last-child {
    margin-bottom: 0px
}

.pipeline-item .pi-controls {
    position: absolute;
    top: 5px;
    right: 10px;
    line-height: 1
}

.pipeline-item .pi-controls a {
    font-size: 16px;
    line-height: 14px;
    color: #047bf8;
    display: inline-block;
    vertical-align: middle
}

.pipeline-item .pi-controls .pi-settings {
    display: inline-block;
    color: #047bf8;
    vertical-align: middle
}

.pipeline-item .pi-controls .pi-settings>i {
    font-size: 15px
}

.pipeline-item .pi-controls .status {
    display: inline-block;
    vertical-align: middle;
    margin-left: 5px;
    width: 11px;
    height: 11px;
    border-radius: 10px;
    cursor: pointer
}

.pipeline-item .pi-controls .status.status-green {
    background-color: #579D1B
}

.pipeline-item .pi-controls .status.status-red {
    background-color: #e65252
}

.pipeline-item .pi-controls .status.status-yellow {
    background-color: #F9F1D9
}

.pipeline-item .pi-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 10px 15px
}

.pipeline-item .pi-body .avatar {
    width: 50px;
    margin-right: 15px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50px;
    flex: 0 0 50px
}

.pipeline-item .pi-body .avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%
}

.pipeline-item .pi-body .pi-name {
    margin-bottom: 0px
}

.pipeline-item .pi-body .pi-sub {
    color: rgba(0, 0, 0, 0.5);
    font-size: .81rem
}

.pipeline-item .pi-foot {
    background-color: #F1F4F8;
    padding: 10px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 0px 0px 4px 4px
}

.pipeline-item .pi-foot .tags {
    line-height: 1
}

.pipeline-item .pi-foot .tags .tag {
    padding: 3px 6px;
    line-height: 1;
    display: inline-block;
    background-color: #fff;
    border: 1px solid #047bf8;
    color: #047bf8;
    margin-right: 2px;
    font-size: .72rem;
    border-radius: 4px;
    margin-bottom: 2px;
    margin-top: 2px
}

.pipeline-item .pi-foot .extra-info {
    margin-right: 5px;
    white-space: nowrap;
    text-transform: uppercase;
    color: rgba(0, 0, 0, 0.6);
    font-size: .63rem;
    letter-spacing: 1px
}

.pipeline-item .pi-foot .extra-info i {
    margin-right: 5px;
    font-size: 16px;
    vertical-align: middle;
    color: #047bf8;
    display: inline-block
}

.pipeline-item .pi-foot .extra-info span {
    vertical-align: middle;
    display: inline-block
}

.control-header {
    padding-bottom: 10px;
    margin-bottom: 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.control-header select.form-control {
    font-weight: 400;
    border-color: rgba(0, 0, 0, 0.5)
}

.control-header label {
    text-transform: uppercase;
    font-size: .72rem;
    letter-spacing: 2px
}

.control-header .form-group {
    margin-bottom: 0px
}

.os-dropdown-trigger {
    position: relative;
    cursor: pointer
}

.os-dropdown-trigger.over .os-dropdown {
    visibility: visible;
    opacity: 1;
    -webkit-transform: translate(99%, 99%);
    transform: translate(99%, 99%)
}

.os-dropdown-trigger.os-dropdown-center .os-dropdown {
    right: 50%;
    -webkit-transform: translate(50%, 115%);
    transform: translate(50%, 115%)
}

.os-dropdown-trigger.os-dropdown-center.over .os-dropdown {
    -webkit-transform: translate(50%, 99%);
    transform: translate(50%, 99%)
}

.os-dropdown {
    background-color: #047bf8;
    color: #fff;
    padding: 10px 20px;
    position: absolute;
    z-index: 999;
    bottom: 0px;
    right: 0px;
    visibility: hidden;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    -webkit-transform: translate(99%, 115%);
    transform: translate(99%, 115%);
    opacity: 0;
    border-radius: 6px;
    -webkit-box-shadow: 0px 5px 25px 0px rgba(4, 123, 248, 0.5);
    box-shadow: 0px 5px 25px 0px rgba(4, 123, 248, 0.5);
    overflow: hidden;
    font-size: .9rem;
    text-align: left
}

.os-dropdown>.icon-w {
    position: absolute;
    top: -30px;
    right: -30px;
    color: rgba(0, 0, 0, 0.1);
    font-size: 100px
}

.os-dropdown ul {
    list-style: none;
    margin: 0px;
    padding: 0px;
    position: relative
}

.os-dropdown ul li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1)
}

.os-dropdown ul li:last-child {
    border-bottom: none
}

.os-dropdown ul li a {
    display: block;
    white-space: nowrap;
    padding: 10px 10px 10px 0px;
    line-height: 1;
    color: #fff;
    font-size: .9rem
}

.os-dropdown ul li a:hover {
    text-decoration: none
}

.os-dropdown ul li a i {
    color: rgba(255, 255, 255, 0.5);
    display: inline-block;
    vertical-align: middle;
    margin-right: 15px;
    font-size: 18px
}

.os-dropdown ul li a span {
    display: inline-block;
    vertical-align: middle;
    color: #fff;
    font-size: .9rem
}

.os-dropdown ul li a:hover i {
    color: #FBB463
}

.os-dropdown.message-list {
    padding: 10px 0px
}

.os-dropdown.message-list li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2)
}

.os-dropdown.message-list li:last-child {
    border-bottom: none
}

.os-dropdown.message-list li>a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 12px 25px
}

.os-dropdown.message-list li>a:hover {
    background-color: rgba(124, 137, 234, 0.07)
}

.os-dropdown.message-list li>a .user-avatar-w {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 45px;
    flex: 0 0 45px;
    margin-right: 20px
}

.os-dropdown.message-list li>a .user-avatar-w img {
    width: 45px;
    border-radius: 30px;
    height: auto
}

.os-dropdown.message-list li>a .message-content .message-from {
    color: #fff;
    margin-bottom: 5px
}

.os-dropdown.message-list li>a .message-content .message-title {
    color: rgba(255, 255, 255, 0.6);
    font-size: .63rem;
    text-transform: uppercase;
    margin-top: 2px;
    margin-bottom: 0px
}

.os-dropdown.light {
    background-color: #fff;
    -webkit-box-shadow: 0px 5px 75px 0px rgba(12, 76, 140, 0.21), 0px 3px 7px 0px rgba(12, 76, 140, 0.14);
    box-shadow: 0px 5px 75px 0px rgba(12, 76, 140, 0.21), 0px 3px 7px 0px rgba(12, 76, 140, 0.14)
}

.os-dropdown.light.message-list>.icon-w {
    color: rgba(4, 123, 248, 0.1)
}

.os-dropdown.light.message-list li {
    border-bottom: 1px solid rgba(113, 133, 171, 0.09)
}

.os-dropdown.light.message-list li:last-child {
    border-bottom: none
}

.os-dropdown.light.message-list li>a .message-content .message-from {
    color: #334152
}

.os-dropdown.light.message-list li>a .message-content .message-title {
    color: #047bf8
}

.todo-app-w {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.todo-app-w .todo-sidebar {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 350px;
    flex: 0 0 350px;
    background-color: #F9F9F9;
    padding: 40px
}

.todo-app-w .todo-sidebar .todo-sidebar-section {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px
}

.todo-app-w .todo-sidebar .todo-sidebar-section:first-child {
    padding-top: 0px
}

.todo-app-w .todo-sidebar .todo-sidebar-section-header {
    color: #047bf8;
    position: relative
}

.todo-app-w .todo-sidebar .todo-sidebar-section-header span {
    display: inline-block;
    vertical-align: middle
}

.todo-app-w .todo-sidebar .todo-sidebar-section-header span+.os-icon {
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
    font-size: 22px
}

.todo-app-w .todo-sidebar .todo-sidebar-section-header span+.os-icon.starred {
    color: #E7AD10
}

.todo-app-w .todo-sidebar .todo-sidebar-section-header span+.os-icon.fire {
    color: #ff1b1b
}

.todo-app-w .todo-sidebar .todo-sidebar-section-contents {
    margin-top: 20px
}

.todo-app-w .todo-sidebar .todo-sidebar-section-toggle {
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    left: -30px;
    color: #047bf8;
    font-size: 16px;
    position: absolute;
    text-decoration: none
}

.todo-app-w .todo-sidebar .todo-sidebar-section-toggle:hover {
    color: #024994
}

.todo-app-w .todo-sidebar ul {
    margin-left: 0px;
    padding-left: 30px
}

.todo-app-w .todo-sidebar ul.projects-list {
    list-style: none;
    font-size: .99rem;
    margin-bottom: 0px
}

.todo-app-w .todo-sidebar ul.projects-list li {
    position: relative;
    margin-bottom: 10px
}

.todo-app-w .todo-sidebar ul.projects-list li:before {
    content: "\e981";
    color: #047bf8;
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 18px;
    position: absolute;
    left: -30px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.todo-app-w .todo-sidebar ul.projects-list li a {
    color: #3E4B5B;
    display: block
}

.todo-app-w .todo-sidebar ul.projects-list li.add-new-project {
    margin-top: 20px
}

.todo-app-w .todo-sidebar ul.projects-list li.add-new-project:before {
    content: "\e969"
}

.todo-app-w .todo-sidebar ul.projects-list li.add-new-project a {
    color: #047bf8;
    border-bottom: 1px solid #047bf8;
    display: inline-block;
    font-size: .9rem
}

.todo-app-w .todo-sidebar ul.projects-list li.add-new-project a:hover {
    text-decoration: none
}

.todo-app-w .todo-sidebar ul.tasks-list {
    list-style: none;
    padding-left: 25px
}

.todo-app-w .todo-sidebar ul.tasks-list li {
    position: relative;
    margin-bottom: 10px
}

.todo-app-w .todo-sidebar ul.tasks-list li strong {
    color: #3E4B5B;
    display: block
}

.todo-app-w .todo-sidebar ul.tasks-list li span {
    color: rgba(0, 0, 0, 0.4);
    font-size: .72rem;
    display: inline-block
}

.todo-app-w .todo-sidebar ul.tasks-list li:before {
    content: "";
    width: 10px;
    height: 10px;
    position: absolute;
    left: -25px;
    top: 7px;
    border-radius: 10px
}

.todo-app-w .todo-sidebar ul.tasks-list li a {
    color: #3E4B5B;
    display: block
}

.todo-app-w .todo-sidebar ul.tasks-list li.danger:before {
    background-color: #e65252
}

.todo-app-w .todo-sidebar ul.tasks-list li.warning:before {
    background-color: #F9F1D9
}

.todo-app-w .todo-sidebar ul.tasks-list li.success:before {
    background-color: #579D1B
}

.todo-app-w .todo-sidebar .todo-sidebar-section-sub-section {
    padding-left: 30px;
    position: relative;
    margin-bottom: 15px
}

.todo-app-w .todo-sidebar .todo-sidebar-section-sub-section .todo-sidebar-section-sub-section-toggler {
    position: absolute;
    z-index: 2;
    top: 5px;
    right: 0px;
    font-size: 14px;
    cursor: pointer
}

.todo-app-w .todo-sidebar .todo-sidebar-section-sub-section .todo-sidebar-section-sub-header {
    position: relative;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 10px;
    padding-top: 10px
}

.todo-app-w .todo-sidebar .todo-sidebar-section-sub-section .todo-sidebar-section-sub-header i.os-icon {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    left: -30px;
    font-size: 18px;
    color: #047bf8
}

.todo-app-w .todo-sidebar .todo-sidebar-section-sub-section .todo-sidebar-section-sub-header h6 {
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: .72rem;
    margin: 0px;
    color: #047bf8
}

.todo-app-w .todo-sidebar .todo-sidebar-section-sub-section .todo-sidebar-section-sub-section-content ul {
    list-style: none;
    padding: 10px 0px;
    margin: 0px
}

.todo-app-w .todo-sidebar .todo-sidebar-section-sub-section .todo-sidebar-section-sub-section-content ul li {
    padding: 3px 0px;
    margin: 0px
}

.todo-app-w .todo-sidebar .todo-sidebar-section-sub-section .todo-sidebar-section-sub-section-content ul li a {
    color: #3E4B5B;
    display: inline-block;
    font-size: .9rem;
    line-height: 1.2;
    height: 1.08rem;
    overflow: hidden
}

.todo-app-w .todo-content {
    background-color: #fff;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    padding: 40px
}

.todo-app-w .todo-content .todo-content-header {
    margin-bottom: 20px;
    color: #047bf8
}

.todo-app-w .todo-content .todo-content-header i {
    margin-right: 10px;
    font-size: 22px;
    display: inline-block;
    vertical-align: middle
}

.todo-app-w .todo-content .todo-content-header span {
    display: inline-block;
    vertical-align: middle
}

.todo-app-w .todo-content .all-tasks-w {
    padding: 20px 30px
}

.todo-app-w .todo-content .tasks-header-w {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 10px 0px;
    position: relative;
    margin-bottom: 30px;
    margin-top: 20px
}

.todo-app-w .todo-content .tasks-header-w .tasks-header-toggler {
    position: absolute;
    color: #047bf8;
    font-size: 18px;
    position: absolute;
    left: -30px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    text-decoration: none
}

.todo-app-w .todo-content .tasks-header-w .tasks-header-toggler:hover {
    color: #024994
}

.todo-app-w .todo-content .tasks-header-w .tasks-header {
    display: inline-block;
    margin-bottom: 0px
}

.todo-app-w .todo-content .tasks-header-w .tasks-sub-header {
    display: inline-block;
    margin-left: 10px;
    color: rgba(0, 0, 0, 0.3);
    font-size: .81rem
}

.todo-app-w .todo-content .tasks-header-w .add-task-btn {
    float: right
}

.todo-app-w .todo-content .tasks-header-w .add-task-btn span,
.todo-app-w .todo-content .tasks-header-w .add-task-btn i {
    display: inline-block;
    vertical-align: middle
}

.todo-app-w .todo-content .tasks-header-w .add-task-btn span {
    border-bottom: 1px solid #047bf8
}

.todo-app-w .todo-content .tasks-header-w .add-task-btn i.os-icon {
    margin-right: 5px;
    font-size: 20px
}

.todo-app-w .todo-content .tasks-list-header {
    text-transform: uppercase;
    color: #047bf8;
    letter-spacing: 1px;
    font-size: .81rem;
    font-weight: 500;
    margin-bottom: 5px
}

.todo-app-w .todo-content .tasks-list {
    font-size: 1.08rem;
    padding: 0px;
    list-style: none;
    border-radius: 4px;
    margin-left: -10px;
    padding: 5px 0px
}

.todo-app-w .todo-content .tasks-list li.draggable-task {
    margin: 0px;
    padding: 6px 40px;
    position: relative;
    border: 1px solid transparent
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task-drag {
    color: #111;
    position: absolute;
    top: 9px;
    left: -10px;
    font-size: 8px;
    cursor: move;
    display: none;
    padding: 5px
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task-media {
    padding-top: 10px
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task-media img {
    display: inline-block;
    border-radius: 4px;
    height: 30px;
    width: auto;
    margin-right: 5px
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task-buttons {
    position: absolute;
    right: -10px;
    top: 50%;
    -webkit-transform: translate(100%, -50%);
    transform: translate(100%, -50%);
    display: none
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task-buttons a {
    font-size: 18px;
    display: inline-block;
    position: relative;
    vertical-align: middle;
    text-decoration: none
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task-buttons a span {
    position: absolute;
    top: -28px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    white-space: nowrap;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: #111;
    color: #fff;
    font-weight: 500;
    font-size: .72rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    display: inline-block;
    vertical-align: middle
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task-buttons a:hover {
    text-decoration: none
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task-buttons a:hover span {
    -webkit-transform: translate(-50%, 8px);
    transform: translate(-50%, 8px);
    visibility: visible;
    opacity: 1
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task-buttons a+a {
    margin-left: 8px
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task-buttons .task-btn-done {
    color: #4d9121
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task-buttons .task-btn-edit {
    color: #047bf8
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task-buttons .task-btn-delete {
    color: #e65252
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task-buttons .task-btn-star {
    color: #c89302
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task {
    position: relative;
    display: inline-block
}

.todo-app-w .todo-content .tasks-list li.draggable-task .todo-task span {
    outline: none
}

.todo-app-w .todo-content .tasks-list li.draggable-task:hover .todo-task-drag,
.todo-app-w .todo-content .tasks-list li.draggable-task:hover .todo-task-buttons {
    display: block
}

.todo-app-w .todo-content .tasks-list li.draggable-task:before {
    content: "";
    width: 8px;
    height: 8px;
    border-radius: 10px;
    background-color: #e1e1e1;
    position: absolute;
    left: 20px;
    top: 15px
}

.todo-app-w .todo-content .tasks-list li.draggable-task.favorite {
    background-color: #fffaea;
    border-radius: 6px
}

.todo-app-w .todo-content .tasks-list li.draggable-task.gu-transit {
    opacity: 0.8;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.05)
}

.todo-app-w .todo-content .tasks-list li.draggable-task.pre-removed {
    background-color: #fff5f5;
    border-radius: 6px
}

.todo-app-w .todo-content .tasks-list li.draggable-task.pre-removed .todo-task {
    opacity: 0.3
}

.todo-app-w .todo-content .tasks-list li.draggable-task.pre-removed .task-btn-undelete {
    position: absolute;
    right: 10px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    background-color: #e65252;
    color: #fff;
    font-weight: 500;
    font-size: .72rem;
    padding: 1px 10px;
    border-radius: 10px;
    text-decoration: none
}

.todo-app-w .todo-content .tasks-list li.draggable-task.pre-removed .task-btn-undelete:hover {
    background-color: #111;
    text-decoration: none
}

.todo-app-w .todo-content .tasks-list li.draggable-task.pre-removed .todo-task-drag,
.todo-app-w .todo-content .tasks-list li.draggable-task.pre-removed .todo-task-buttons {
    display: none !important
}

.todo-app-w .todo-content .tasks-list li.draggable-task.complete {
    color: #999
}

.todo-app-w .todo-content .tasks-list li.draggable-task.complete .todo-task {
    text-decoration: line-through
}

.todo-app-w .todo-content .tasks-list li.draggable-task.complete:before {
    background-color: #e1e1e1 !important
}

.todo-app-w .todo-content .tasks-list li.draggable-task.danger:before {
    background-color: #e65252
}

.todo-app-w .todo-content .tasks-list li.draggable-task.warning:before {
    background-color: #F9F1D9
}

.todo-app-w .todo-content .tasks-list li.draggable-task.success:before {
    background-color: #579D1B
}

.draggable-task.gu-mirror {
    list-style: none;
    padding: 10px 40px;
    background-color: #fff;
    -webkit-box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.1);
    box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.1);
    margin: 0px;
    font-size: 1.08rem;
    position: relative;
    border-radius: 4px
}

.draggable-task.gu-mirror .todo-task-drag {
    color: #111;
    position: absolute;
    top: 50%;
    left: -10px;
    font-size: 8px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    cursor: move;
    padding: 5px
}

.draggable-task.gu-mirror .todo-task-buttons {
    display: none
}

.draggable-task.gu-mirror .todo-task-media {
    padding-top: 10px
}

.draggable-task.gu-mirror .todo-task-media img {
    display: inline-block;
    border-radius: 4px;
    height: 30px;
    width: auto;
    margin-right: 5px
}

.attached-media-w img {
    display: inline-block;
    border-radius: 4px;
    height: 30px;
    width: auto;
    margin-right: 5px
}

.attached-media-w .attach-media-btn {
    display: inline-block;
    margin-left: 10px
}

.attached-media-w .attach-media-btn span,
.attached-media-w .attach-media-btn i {
    display: inline-block;
    vertical-align: middle
}

.attached-media-w .attach-media-btn span {
    border-bottom: 1px solid #047bf8
}

.attached-media-w .attach-media-btn i.os-icon {
    margin-right: 5px;
    font-size: 18px
}

.fancy-selector-w {
    position: relative
}

.fancy-selector-w .fancy-selector-current {
    position: relative;
    z-index: 999;
    height: 100%
}

.fancy-selector-w .fancy-selector-current,
.fancy-selector-w .fancy-selector-option {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    background-color: #1e62eb;
    color: #fff;
    text-align: left;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.fancy-selector-w .fancy-selector-current .fs-img,
.fancy-selector-w .fancy-selector-option .fs-img {
    padding: 10px 15px;
    vertical-align: middle
}

.fancy-selector-w .fancy-selector-current .fs-img img,
.fancy-selector-w .fancy-selector-option .fs-img img {
    display: inline-block;
    vertical-align: middle;
    height: 30px;
    border-radius: 4px;
    -webkit-box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1)
}

.fancy-selector-w .fancy-selector-current .fs-main-info,
.fancy-selector-w .fancy-selector-option .fs-main-info {
    padding: 10px;
    padding-right: 20px
}

.fancy-selector-w .fancy-selector-current .fs-main-info .fs-name,
.fancy-selector-w .fancy-selector-option .fs-main-info .fs-name {
    font-size: .99rem;
    font-weight: 500;
    line-height: 1;
    letter-spacing: 1px;
    margin-bottom: 5px
}

.fancy-selector-w .fancy-selector-current .fs-main-info .fs-sub,
.fancy-selector-w .fancy-selector-option .fs-main-info .fs-sub {
    color: rgba(255, 255, 255, 0.6);
    letter-spacing: 0.5px;
    font-size: .72rem
}

.fancy-selector-w .fancy-selector-current .fs-main-info .fs-sub strong,
.fancy-selector-w .fancy-selector-option .fs-main-info .fs-sub strong {
    color: #F6DB77;
    margin-left: 5px
}

.fancy-selector-w .fancy-selector-current .fs-extra-info,
.fancy-selector-w .fancy-selector-option .fs-extra-info {
    padding: 10px 20px;
    border-left: 1px solid rgba(255, 255, 255, 0.05);
    text-align: center;
    color: rgba(255, 255, 255, 0.6)
}

.fancy-selector-w .fancy-selector-current .fs-extra-info strong,
.fancy-selector-w .fancy-selector-option .fs-extra-info strong {
    font-size: .99rem;
    margin-bottom: 5px;
    font-weight: 500;
    display: block;
    letter-spacing: 1px;
    line-height: 1
}

.fancy-selector-w .fancy-selector-current .fs-extra-info span,
.fancy-selector-w .fancy-selector-option .fs-extra-info span {
    text-transform: uppercase;
    color: rgba(255, 255, 255, 0.4);
    font-size: .54rem;
    letter-spacing: 2px;
    line-height: 1;
    display: block
}

.fancy-selector-w .fancy-selector-current .fs-selector-trigger,
.fancy-selector-w .fancy-selector-option .fs-selector-trigger {
    background-color: #114dc5;
    padding: 10px 10px;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    -ms-flex-item-align: stretch;
    align-self: stretch;
    position: relative;
    width: 60px;
    cursor: pointer
}

.fancy-selector-w .fancy-selector-current .fs-selector-trigger i,
.fancy-selector-w .fancy-selector-option .fs-selector-trigger i {
    vertical-align: middle;
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.fancy-selector-w .fancy-selector-current .fs-selector-trigger:hover,
.fancy-selector-w .fancy-selector-option .fs-selector-trigger:hover {
    background-color: #0d3b96;
    color: #fff
}

.fancy-selector-w .fancy-selector-current .fs-selector-trigger:hover i,
.fancy-selector-w .fancy-selector-option .fs-selector-trigger:hover i {
    -webkit-transform: translate(-50%, -40%);
    transform: translate(-50%, -40%)
}

.fancy-selector-w .fancy-selector-options {
    background-color: #1456dc;
    position: absolute;
    left: 0px;
    min-width: 100%;
    padding: 15px 0px;
    padding-top: 25px;
    z-index: 9998;
    border-radius: 0px 0px 4px 4px;
    visibility: hidden;
    -webkit-transform: translateY(-30px) scale(1);
    transform: translateY(-30px) scale(1);
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    opacity: 0
}

.fancy-selector-w .fancy-selector-options .fancy-selector-actions {
    padding: 15px 15px 0px 15px
}

.fancy-selector-w .fancy-selector-options .fancy-selector-option {
    padding: 8px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    color: rgba(255, 255, 255, 0.7);
    position: relative;
    background-color: transparent;
    cursor: pointer;
    border-radius: 0px
}

.fancy-selector-w .fancy-selector-options .fancy-selector-option .fs-extra-info {
    padding-right: 10px
}

.fancy-selector-w .fancy-selector-options .fancy-selector-option .fs-img {
    padding-left: 20px
}

.fancy-selector-w .fancy-selector-options .fancy-selector-option .fs-img img {
    height: 37px
}

.fancy-selector-w .fancy-selector-options .fancy-selector-option .fs-main-info {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.fancy-selector-w .fancy-selector-options .fancy-selector-option.active {
    color: #fff
}

.fancy-selector-w .fancy-selector-options .fancy-selector-option.active:before {
    content: "";
    width: 8px;
    height: 8px;
    background-color: #ffb900;
    position: absolute;
    top: 50%;
    left: 15px;
    border-radius: 4px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.fancy-selector-w .fancy-selector-options .fancy-selector-option:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #fff
}

.fancy-selector-w.opened .fancy-selector-options {
    visibility: visible;
    -webkit-transform: translateY(-5px) scale(1);
    transform: translateY(-5px) scale(1);
    opacity: 1
}

.fancy-selector-w.opened .fs-selector-trigger i {
    -webkit-transform: translate(-50%, -50%) rotate(180deg);
    transform: translate(-50%, -50%) rotate(180deg)
}

.fancy-selector-w.opened .fs-selector-trigger:hover i {
    -webkit-transform: translate(-50%, -60%) rotate(180deg);
    transform: translate(-50%, -60%) rotate(180deg)
}

@media (max-width: 1650px) {
    body.pages,
    body.auth-wrapper .all-wrapper {
        padding: 40px
    }
    .content-panel {
        padding: 2rem;
        width: 350px
    }
    .content-box {
        padding: 2rem
    }
    .all-wrapper {
        max-width: 100%
    }
}

@media (max-width: 1550px) {
    body.pages,
    body.auth-wrapper .all-wrapper {
        padding: 20px
    }
    .menu-side-w ul.main-menu {
        padding: 1.5rem 1rem
    }
    .menu-side-w ul.main-menu>li .icon-w {
        padding-left: 1rem;
        width: 75px
    }
    .menu-side-w ul.sub-menu {
        padding-left: 45px
    }
    .menu-side-w {
        width: 260px
    }
    .menu-side-w .logged-user-menu ul {
        padding-left: 30px;
        padding-right: 30px
    }
}

@media (max-width: 1350px) {
    .top-menu-secondary .fs-extra-info {
        display: none
    }
    .menu-side-w .logo-w {
        padding: 1.5rem 1rem
    }
    .menu-top-w .menu-top-i .logo-w {
        padding: 0rem 1rem;
        width: 50px
    }
    .menu-top-w .menu-top-i ul.main-menu {
        padding-left: 0px
    }
}

@media (min-width: 1100px) and (max-width: 1350px) {
    .content-panel {
        width: 300px;
        padding: 1.5rem
    }
    .padded {
        padding: 0.5rem 1rem
    }
    .content-box {
        padding: 1.5rem
    }
    .activity-boxes-w .activity-avatar {
        margin-right: 1rem;
        width: 40px
    }
    .activity-boxes-w .activity-time {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 90px;
        flex: 0 0 90px
    }
    .activity-boxes-w .activity-box-w:before {
        left: 65px
    }
    .activity-boxes-w .activity-box-w:after {
        left: 70px
    }
    .profile-tile .profile-tile-box {
        width: 95px
    }
    .profile-tile .pt-avatar-w img {
        width: 50px
    }
    .profile-tile .profile-tile-meta {
        padding-left: 15px
    }
}

@media (max-width: 1400px) {
    .menu-side-w {
        width: 230px
    }
    .menu-side-w .side-menu-magic {
        padding: 30px 20px
    }
    .menu-side-w .logged-user-menu ul {
        padding-left: 15px;
        padding-right: 15px
    }
    .menu-top-image-w ul.main-menu>li .icon-w {
        padding-left: 20px
    }
    .menu-top-image-w ul.main-menu>li span {
        padding-right: 20px
    }
    .full-chat-w .chat-content-w .chat-message .chat-message-content {
        max-width: 320px
    }
}

@media (max-width: 1150px) {
    .ae-content {
        padding: 0px
    }
    .aec-full-message-w {
        margin-bottom: 0px
    }
    .ae-content-w {
        background-image: none;
        background-color: #fff
    }
    .aec-full-message-w .more-messages {
        top: -32px;
        background-color: #fff;
        padding: 3px 5px;
        border-radius: 4px;
        color: #047bf8
    }
    .ae-list-w {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 300px;
        flex: 0 0 300px
    }
    .ae-list-w .ae-list .aei-image {
        display: none
    }
    .layout-w {
        -webkit-transition: all 0.6s ease;
        transition: all 0.6s ease
    }
    .content-box:after {
        display: block;
        position: absolute;
        top: 0px;
        left: 0px;
        right: 0px;
        bottom: 0px;
        background: rgba(4, 36, 113, 0.6);
        content: "";
        z-index: 10;
        visibility: hidden;
        opacity: 0;
        -webkit-transition: all 0.4s ease;
        transition: all 0.4s ease
    }
    .content-panel {
        position: absolute;
        top: 0px;
        bottom: 0px;
        right: 0px;
        background-color: #fff;
        background-image: -webkit-gradient(linear, right top, left top, color-stop(8%, #EFF2F9), color-stop(90%, #fff));
        background-image: linear-gradient(-90deg, #EFF2F9 8%, #fff 90%);
        z-index: 4;
        border: none;
        -webkit-transform: translateX(100%);
        transform: translateX(100%);
        -webkit-transition: all 0.6s ease;
        transition: all 0.6s ease;
        visibility: hidden;
        z-index: 11
    }
    .content-panel .content-panel-close {
        display: block
    }
    .content-panel-toggler {
        display: block
    }
    .content-i,
    .with-side-panel .content-i {
        display: block
    }
    .content-i .content-box,
    .with-side-panel .content-i .content-box {
        display: block
    }
    .content-panel-active .content-panel {
        -webkit-transform: translateX(80px);
        transform: translateX(80px);
        visibility: visible;
        -webkit-box-shadow: 0 2px 80px 0 rgba(0, 0, 0, 0.4);
        box-shadow: 0 2px 80px 0 rgba(0, 0, 0, 0.4)
    }
    .content-panel-active .layout-w {
        -webkit-transform: translateX(-80px);
        transform: translateX(-80px)
    }
    .content-panel-active .content-box:after {
        opacity: 1;
        visibility: visible
    }
}

@media (max-width: 1250px) {
    .element-box,
    .invoice-w,
    .big-error-w,
    .invoice-w,
    .big-error-w {
        padding: 1rem 1.5rem
    }
    .element-box .os-tabs-controls,
    .invoice-w .os-tabs-controls,
    .big-error-w .os-tabs-controls,
    .invoice-w .os-tabs-controls,
    .big-error-w .os-tabs-controls {
        margin-left: -1.5rem;
        margin-right: -1.5rem
    }
    body.pages,
    body.auth-wrapper .all-wrapper {
        padding: 0px
    }
    .content-w,
    .menu-side-w,
    .all-wrapper {
        border-radius: 0px !important;
        overflow: hidden
    }
    .menu-side-w .logged-user-menu {
        left: 0px;
        right: 0px
    }
}

@media (max-width: 1024px) {
    .invoice-w {
        padding: 50px
    }
    .ae-list-w {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 230px;
        flex: 0 0 230px
    }
    .ae-list-w .ae-list .ae-item {
        padding: 15px
    }
    .table th,
    .table td {
        padding: 0.7rem 0.5rem
    }
    .content-w {
        border-radius: 0px !important
    }

    .desktop-lmenu {display: none;}
}

@media (min-width: 768px) and (max-width: 1024px) {
    .top-menu-secondary .logo-w {
        display: none
    }
    .top-menu-secondary .fs-extra-info {
        display: none
    }
    .element-wrapper {
        padding-bottom: 2rem
    }
    .element-balances {
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between
    }
    .full-chat-w .full-chat-right {
        display: none
    }
    .full-chat-w .chat-content-w .chat-content {
        padding: 30px
    }
    .top-menu-secondary {
        overflow: scroll;
        padding: 0px
    }
    .top-menu-secondary>ul {
        white-space: nowrap
    }
    .top-menu-secondary>ul li a {
        padding: 15px 10px;
        margin: 0px 10px
    }
    .top-menu-secondary.with-overflow {
        overflow: visible
    }
    .nav.upper.nav-tabs .nav-link {
        margin-right: 0.7rem;
        padding-bottom: 10px;
        font-size: .72rem
    }
    .display-type {
        content: "tablet"
    }
    .content-box {
        padding: 1.5rem
    }
    .padded {
        padding: 0.3rem 0.8rem
    }
    .element-box,
    .invoice-w,
    .big-error-w,
    .invoice-w,
    .big-error-w {
        padding: 1rem 1rem
    }
    .element-box .os-tabs-controls,
    .invoice-w .os-tabs-controls,
    .big-error-w .os-tabs-controls,
    .invoice-w .os-tabs-controls,
    .big-error-w .os-tabs-controls {
        margin-left: -1rem;
        margin-right: -1rem
    }
    .layout-w {
        display: table;
        table-layout: fixed;
        width: 100%
    }
    .content-w {
        display: table-cell
    }
    .menu-mobile {
        width: 92px;
        display: table-cell
    }
    .menu-mobile .menu-and-user {
        display: block
    }
    .menu-mobile ul.sub-menu {
        padding: 1rem 1.5rem
    }
    .menu-mobile .mobile-menu-magic {
        display: none
    }
    .menu-mobile .logged-user-w {
        padding: 1rem;
        text-align: center
    }
    .menu-mobile .logged-user-w .avatar-w img {
        width: 35px
    }
    .menu-mobile .logged-user-w .logged-user-info-w {
        display: none
    }
    .menu-mobile ul.main-menu {
        padding: 1rem 0.5rem
    }
    .menu-mobile .mm-logo-buttons-w {
        text-align: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center
    }
    .menu-mobile .mm-logo-buttons-w .mm-buttons {
        display: none
    }
    .menu-mobile .mm-logo-buttons-w .mm-logo {
        display: block;
        text-align: center
    }
    .menu-mobile .mm-logo-buttons-w .mm-logo span {
        display: none
    }
    .menu-mobile.color-scheme-dark ul.main-menu>li>a>span {
        background-color: #fff;
        color: #111
    }
    .menu-mobile.color-scheme-dark ul.sub-menu {
        background-image: -webkit-gradient(linear, left top, left bottom, from(#1c4cc3), to(#1c2e7b));
        background-image: linear-gradient(to bottom, #1c4cc3 0%, #1c2e7b 100%)
    }
    .menu-mobile.color-scheme-dark ul.sub-menu:before {
        border-right-color: #203c9b
    }
    .menu-mobile ul.main-menu>li>a>span {
        position: absolute;
        top: -15px;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.9);
        border-radius: 4px;
        color: #fff;
        font-size: .81rem;
        padding: 1px 5px !important;
        display: none;
        white-space: nowrap
    }
    .menu-mobile ul.main-menu li {
        position: relative
    }
    .menu-mobile .sub-menu {
        position: absolute;
        top: 50%;
        left: 100px;
        background-color: #fff;
        min-width: 200px;
        z-index: 999;
        list-style: none;
        padding: 1rem 1.5rem;
        -webkit-box-shadow: 0px 5px 30px rgba(0, 0, 0, 0.1);
        box-shadow: 0px 5px 30px rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%)
    }
    .menu-mobile .sub-menu:before {
        right: 100%;
        top: 50%;
        border: solid transparent;
        content: " ";
        height: 0;
        width: 0;
        position: absolute;
        pointer-events: none;
        border-color: rgba(255, 255, 255, 0);
        border-right-color: #fff;
        border-width: 10px;
        margin-top: -10px
    }
    .menu-mobile .main-menu li>a:hover>span {
        display: block
    }
    .menu-mobile .has-sub-menu.active .icon-w {
        color: #3E4B5B
    }
    .menu-mobile .has-sub-menu.active .sub-menu {
        display: block
    }
    .user-profile .up-header {
        font-size: 2.5rem
    }
    .user-profile .up-sub-header {
        font-size: 1rem
    }
    .todo-app-w .todo-sidebar {
        padding: 30px;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 280px;
        flex: 0 0 280px
    }
    .todo-app-w .todo-sidebar .todo-sidebar-section {
        padding: 10px
    }
    .todo-app-w .todo-content {
        padding: 30px
    }
}

@media (max-width: 768px) {
    .todo-app-w .todo-sidebar {
        padding: 30px 20px 20px 45px
    }
    .todo-app-w .todo-sidebar .todo-sidebar-section {
        padding: 10px 0px
    }
    .todo-app-w .todo-sidebar .todo-sidebar-section-toggle {
        left: -22px
    }
    .todo-app-w .todo-sidebar .todo-sidebar-section-header {
        font-size: 1.25rem
    }
    .todo-app-w .todo-sidebar .todo-sidebar-section-sub-section,
    .todo-app-w .todo-sidebar ul.projects-list {
        padding-left: 10px
    }
    .todo-app-w .todo-content {
        padding: 30px
    }
    .todo-app-w .todo-content .all-tasks-w {
        padding-left: 5px
    }
    .todo-app-w .todo-content .tasks-header-w {
        padding-left: 20px
    }
    .todo-app-w .todo-content .tasks-header-w .tasks-header-toggler {
        left: -5px
    }
    .todo-app-w .todo-content .todo-content-header {
        font-size: 1.25rem
    }
    .todo-app-w .todo-content .all-tasks-w {
        padding-top: 0px;
        padding-right: 0px
    }
    .todo-app-w .todo-content .tasks-list li.draggable-task {
        padding-right: 10px
    }
}

@media (max-width: 767px) {
    .hidden-mobile {
        display: none !important
    }
    .top-menu-secondary .logo-w {
        display: none
    }
    .top-menu-secondary.with-overflow {
        -webkit-box-orient: vertical;
        -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse
    }
    .top-menu-secondary.with-overflow .top-menu-controls {
        display: none
    }
    .top-menu-secondary .fs-extra-info {
        display: none
    }
    .element-balances {
        -ms-flex-wrap: wrap;
        flex-wrap: wrap
    }
    .element-balances .balance {
        border: none !important
    }
    .cell-with-media {
        text-align: center
    }
    .cell-with-media img {
        margin-bottom: 5px
    }
    .cell-with-media img+span {
        margin-left: 0px !important
    }
    .element-wrapper .element-actions {
        float: none;
        margin-bottom: 20px
    }
    .todo-app-w {
        display: block
    }
    .todo-app-w .todo-sidebar {
        padding: 30px 15px 20px 40px;
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1
    }
    .todo-app-w .todo-sidebar .todo-sidebar-section {
        padding: 10px 0px
    }
    .todo-app-w .todo-sidebar .todo-sidebar-section-toggle {
        left: -22px
    }
    .todo-app-w .todo-sidebar .todo-sidebar-section-header {
        font-size: 1.25rem
    }
    .todo-app-w .todo-sidebar .todo-sidebar-section-sub-section,
    .todo-app-w .todo-sidebar ul.projects-list {
        padding-left: 10px
    }
    .todo-app-w .todo-content {
        padding: 30px 20px
    }
    .todo-app-w .todo-content .todo-content-header {
        font-size: 1.25rem
    }
    .todo-app-w .todo-content .all-tasks-w {
        padding-top: 0px;
        padding-right: 0px
    }
    .todo-app-w .todo-content .tasks-list li.draggable-task {
        padding-right: 10px
    }
    .full-chat-w .full-chat-i {
        display: block
    }
    .projects-list .project-head {
        padding: 20px 30px;
        display: block;
        text-align: center
    }
    .projects-list .project-head .project-users {
        text-align: center;
        margin-top: 2rem
    }
    .projects-list .project-info {
        padding: 20px 30px
    }
    .projects-list .project-info .el-tablo {
        text-align: center;
        margin-bottom: 0.5rem
    }
    .display-type {
        content: "phone"
    }
    .ae-list-w {
        -webkit-box-flex: 1;
        -ms-flex: 1 1 230px;
        flex: 1 1 230px
    }
    .ae-content-w {
        display: none
    }
    .top-menu-secondary {
        overflow: scroll;
        padding: 0px
    }
    .top-menu-secondary>ul {
        white-space: nowrap
    }
    .top-menu-secondary>ul li a {
        padding: 15px 10px;
        margin: 0px 10px
    }
    .top-menu-secondary.with-overflow {
        overflow: visible
    }
    .nav.upper.nav-tabs .nav-link {
        margin-right: 0.7rem;
        padding-bottom: 10px;
        font-size: .72rem
    }
    .app-email-w.forse-show-content .ae-content-w {
        display: block
    }
    .app-email-w.forse-show-content .ae-list-w {
        display: none
    }
    .aec-full-message-w .message-content {
        padding: 15px
    }
    .aec-full-message-w .message-attachments {
        text-align: center
    }
    .aec-full-message-w .message-head .user-w .user-role span {
        margin-left: 0
    }
    .aec-reply {
        padding: 15px
    }
    .aec-reply .reply-header {
        padding-bottom: 15px;
        padding-top: 15px;
        text-align: center
    }
    .aec-reply .buttons-w {
        display: block;
        text-align: center
    }
    .aec-reply .buttons-w .btn,
    .aec-reply .buttons-w .all-wrapper .fc-button,
    .all-wrapper .aec-reply .buttons-w .fc-button {
        margin-bottom: 10px
    }
    .floated-chat-btn i+span {
        display: none
    }
    .floated-chat-btn {
        padding: 10px 12px
    }
    .padded {
        padding: 1rem
    }
    .user-profile .up-head-w .up-social {
        top: 5px;
        right: 10px
    }
    .user-profile .up-head-w .up-social a {
        font-size: 24px
    }
    .user-profile .up-main-info {
        padding-bottom: 10%
    }
    .user-profile .up-header {
        font-size: 1.75rem;
        margin-bottom: 5px;
        padding-bottom: 5px
    }
    .user-profile .up-sub-header {
        font-size: 1rem
    }
    .user-profile .up-controls {
        text-align: center
    }
    .user-profile .up-controls .value-pair {
        margin-bottom: 1rem
    }
    .user-profile .up-controls .text-right {
        text-align: center !important
    }
    .timed-activities {
        padding: 0
    }
    .timed-activity {
        display: block;
        font-size: .9rem
    }
    .timed-activity .ta-date {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        margin-bottom: 3rem;
        text-align: left
    }
    .timed-activity .ta-date span:after {
        -webkit-transform: none;
        transform: none;
        left: 0px
    }
    .timed-activity .ta-record-w {
        padding-left: 0px
    }
    .timed-activity .ta-record {
        display: block;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05)
    }
    .timed-activity .ta-activity {
        font-size: .81rem
    }
    .content-panel-toggler {
        display: none
    }
    .menu-mobile {
        display: block
    }
    .element-info .element-search {
        margin-top: 1rem
    }
    .element-box .os-tabs-controls,
    .invoice-w .os-tabs-controls,
    .big-error-w .os-tabs-controls,
    .invoice-w .os-tabs-controls,
    .big-error-w .os-tabs-controls {
        display: block;
        margin-left: -1rem;
        margin-right: -1rem
    }
    .element-box .os-tabs-controls .nav+.nav,
    .invoice-w .os-tabs-controls .nav+.nav,
    .big-error-w .os-tabs-controls .nav+.nav,
    .invoice-w .os-tabs-controls .nav+.nav,
    .big-error-w .os-tabs-controls .nav+.nav {
        margin-top: 1rem
    }
    .element-box.el-tablo,
    .el-tablo.invoice-w,
    .el-tablo.big-error-w {
        text-align: center
    }
    .invoice-w .infos {
        display: block
    }
    .invoice-w .infos .info-2 {
        text-align: left;
        padding-top: 30px
    }
    .invoice-heading {
        margin-bottom: 2rem;
        margin-top: 4rem
    }
    .invoice-body {
        display: block
    }
    .invoice-body .invoice-desc {
        margin-bottom: 2rem
    }
    .invoice-footer {
        display: block;
        text-align: center
    }
    .invoice-footer .invoice-logo {
        margin-bottom: 1rem
    }
    .controls-above-table .form-control {
        width: 110px;
        display: inline-block
    }
    .controls-above-table .btn,
    .controls-above-table .all-wrapper .fc-button,
    .all-wrapper .controls-above-table .fc-button {
        margin-bottom: 0.5rem
    }
    .layout-w {
        display: block
    }
    .content-w,
    .menu-side .content-w {
        display: block
    }
    .content-i {
        display: block
    }
    .content-i .content-box {
        display: block;
        padding: 15px
    }
    .content-i .content-panel {
        padding: 15px;
        border-left: none;
        display: block;
        width: auto
    }
    .big-error-w {
        padding: 1rem
    }
    .element-wrapper {
        padding-bottom: 1.5rem
    }
    .element-box,
    .invoice-w,
    .big-error-w {
        padding: 1rem
    }
    canvas {
        max-width: 100%
    }
    table {
        max-width: 100%
    }
    .invoice-w {
        padding: 30px
    }
    .breadcrumb {
        margin-bottom: 1rem
    }
}
