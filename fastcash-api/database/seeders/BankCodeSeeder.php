<?php

namespace Database\Seeders;

use App\Models\BankCode;
use Illuminate\Database\Seeder;

class BankCodeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $codes = [
            array('name' => 'CITIBANK NIG LTD', 'code' => '023'),
            array('name' => 'FIRST CITY MONUMENT BANK PLC', 'code' => '214'),
            array('name' => 'FIRST BANK OF NIGERIA PLC', 'code' => '011'),
            array('name' => 'WEMA BANK PLC', 'code' => '035'),
            array('name' => 'STANBIC IBTC BANK PLC', 'code' => '039'),
            array('name' => 'UBA PLC', 'code' => '033'),
            array('name' => 'PROVIDUS BANK PLC', 'code' => '101'),
            array('name' => 'ACCESS BANK PLC', 'code' => '044'),
            array('name' => 'ECOBANK NIGERIA PLC', 'code' => '050'),
            array('name' => 'ZENITH BANK PLC', 'code' => '057'),
            array('name' => 'DIAMOND BANK PLC', 'code' => '063'),
            array('name' => 'SKYE BANK PLC', 'code' => '076'),
            array('name' => 'KEYSTONE BANK', 'code' => '082'),
            array('name' => 'STERLING BANK PLC', 'code' => '232'),
            array('name' => 'UNION BANK OF NIGERIA PLC', 'code' => '032'),
            array('name' => 'HERITAGE BANK', 'code' => '030'),
            array('name' => 'JAIZ BANK PLC', 'code' => '301'),
            array('name' => 'GUARANTY TRUST BANK PLC', 'code' => '058'),
            array('name' => 'FIDELITY BANK PLC', 'code' => '070'),
            array('name' => 'STANDARD CHARTERED BANK NIGERIA LTD', 'code' => '068'),
            array('name' => 'SUNTRUST BANK NIG LTD', 'code' => '100'),
            array('name' => 'UNITY BANK PLC', 'code' => '215'),
            array('name' => 'STANDARD CHARTERED BANK NIGERIA LTD', 'code' => '068'),
            array('name' => 'SUNTRUST BANK NIG LTD', 'code' => '100'),
            array('name' => 'UNITY BANK PLC', 'code' => '215'),
        ];

        foreach ($codes as $code){
            $bc = new BankCode();
            $bc->name = $code['name'];
            $bc->code = $code['code'];
            $bc->save();
        }
    }
}
