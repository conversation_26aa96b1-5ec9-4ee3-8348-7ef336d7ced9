<?php

namespace Database\Seeders;

use App\Models\PayslipUpload;
use Illuminate\Database\Seeder;

class PayslipUploadSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $slips = DB::table('payslips')
            ->select('employer', DB::raw('SUBSTRING_INDEX(GROUP_CONCAT(month ORDER BY year DESC, month DESC), \',\', 1) AS month'), DB::raw('max(year) AS year'))
            ->whereNotNull('employer')
            ->groupBy('employer')
            ->get();

        foreach ($slips as $data) {
            PayslipUpload::create([
                'employer' => $data->employer,
                'month' => $data->month,
                'year' => $data->year,
            ]);
        }
    }
}
