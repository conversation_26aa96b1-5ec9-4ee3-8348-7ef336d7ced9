<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Accountno;
use App\Models\BlackListedNumber;
use App\Models\Lender;
use App\Models\Loan;
use App\Models\PaymentDate;
use App\Models\Payslip;
use App\Models\Setting;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UssdLevel;
use App\Models\UssdSession;
use App\Models\UssdSetting;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Log;
use stdClass;

class UssdController extends Controller
{
    public function index(Request $request)
    {
        error_log('------------------start-------------------------');
        $sessionId = $request->get('sessionId');
        $phoneNumber = $request->get('phoneNumber');
        $serviceCode = $request->get('serviceCode');
        $networkCode = $request->get('networkCode');
        $text = $request->get('text');

        $level = 0;
        $limitNo = 0;
        $textArray = explode("*", $text);
        $ussd_string = trim(end($textArray));

        $ussdSetting = UssdSetting::where('id', 1)->first();

        if ($ussdSetting) {
            $limitNo = $ussdSetting->limitNo;
        }


        $phone = str_replace('+234', '0', $phoneNumber);
        $user = User::where('phone', $phone)->first();

        $blackList = BlackListedNumber::where('phone_number', $phone)->first();

        if ($blackList && $blackList->isBlacklisted && Carbon::parse($blackList->dateOfBlackListing)->lte(Carbon::now()->subMonth())) {

            BlackListedNumber::where('phone_number', $phone)->update([
                'repeat' => 1,
                'todaysDate' => Carbon::now(),
                'dateOfBlackListing' => null,
                'isBlacklisted' => false,
            ]);

        }

        $payslip = null;
        $lender = null;
        if ($user) {
            $lender = Lender::where('id', $user->lender_id)->first();
            $payslip = Payslip::where('id', $user->payslip_id)->first();
        }

        $loan = null;
        if ($user && $user->loan_id != null) {
            $loan = Loan::where('id', $user->loan_id)->first();
        }

        $session = UssdSession::where('phone', $phone)->where('session_id', $sessionId)->first();
        if (!$session) {
            $session = UssdSession::create([
                'session_id' => $sessionId,
                'phone' => $phone,
                'user_id' => $user ? $user->id : null,
                'service_code' => $serviceCode,
                'network_code' => $networkCode,
            ]);
        }

        $ussd_level = UssdLevel::where('ussd_session_id', $session->id)->first();
        if (!$ussd_level) {
            $ussd_level = UssdLevel::create(['ussd_session_id' => $session->id, 'level' => $level]);
            $level = $ussd_level->level;
        } else {
            if ((count($textArray) == 1 && $ussd_string == "") || $ussd_level->level == 0) {
                if (strlen($ussd_string) > 2) {
                    $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                } else {
                    $ussd_string = (int) $ussd_string;
                    UssdLevel::where('ussd_session_id', $session->id)->update(['level' => $ussd_string]);
                    $level = (int) $ussd_string;
                    $level = $ussd_level->level;

                    if ($user) {
                        $level = (int) $ussd_string;
                    }
                }
            } else {
                $level = $ussd_level->level;
            }
        }

        $snooze_period = Setting::where('name', 'snooze_period')->first();
        $eligibility_gap = Setting::where('name', 'eligibility_gap')->first();

        $response = "CON Invalid selection, press 0 to go back to the main menu";


        $blackList = BlackListedNumber::where('phone_number', $phone)->first();

        if ($blackList && $blackList->repeat >= $limitNo) {

            $response = 'END ' . $ussdSetting->messageToDisplay;

        } else if (!$user) {

            $level = 0;

            if ($level == 0) {

                switch ($ussd_level->sub_level) {

                    case 2:
                        //find and register user using bank details
                        if ($ussd_string == '22') {

                            $response = "CON Select a valid bank\n";
                            $response .= "1. First Bank\n";
                            $response .= "2. Access (Diamond)\n";
                            $response .= "3. Access\n";
                            $response .= "4. Zenith Bank\n";
                            $response .= "5. UBA\n";
                            $response .= "6. Ecobank\n";
                            $response .= "7. Polaris Bank\n";
                            $response .= "8. FCMB\n";
                            $response .= "9. Guaranty Trust Bank\n";
                            $response .= "10. Fidelity Bank\n";
                            $response .= "11. next\n";
                            $response .= "0. exit\n";

                        } else if ($ussd_string == '11' || $ussd_string == '33') {

                            $response = "CON Select a valid bank\n";
                            $response .= "12. Stanbic\n";
                            $response .= "13. Sterling Bank\n";
                            $response .= "14. Union Bank\n";
                            $response .= "15. Wema Bank\n";
                            $response .= "16. STD CHARTD\n";
                            $response .= "17. Citi Bank\n";
                            $response .= "18. Providus Bank\n";
                            $response .= "19. Parallex Bank\n";
                            $response .= "20. Premium Trust Bank\n";
                            $response .= "21. next\n";
                            $response .= "22. previous\n";
                            $response .= "0. exit\n";

                        } else if ($ussd_string == '21' || $ussd_string == '47') {
                            $response = "CON Select a valid bank\n";
                            $response .= "23. Unity Bank\n";
                            $response .= "24. Keystone Bank\n";
                            $response .= "25. Heritage Bank\n";
                            $response .= "26. Aso Savings & Loans\n";
                            $response .= "27. Jaiz Bank\n";
                            $response .= "28. Jubilee Life Mortgage Bank\n";
                            $response .= "29. Suntrust Bank\n";
                            $response .= "30. Coronation Merchant Bank\n";
                            $response .= "31. Rand Merchant Bank (RMB)\n";
                            $response .= "32. next\n";
                            $response .= "33. previous\n";
                            $response .= "0. exit\n";

                        } else if ($ussd_string == '32') {

                            $response = "CON Select a valid bank\n";
                            $response .= "34. Optimus Bank\n";
                            $response .= "35. Prudential Bank\n";
                            $response .= "36. Imperial Homes Mortgage Bank\n";
                            $response .= "37. FSDH Merchant Bank\n";
                            $response .= "38. Gateway Mortgage Bank\n";
                            $response .= "39. Titan Trust Bank\n";
                            $response .= "40. Taj Bank\n";
                            $response .= "41. Globus Bank\n";
                            $response .= "42. M36\n";
                            $response .= "43. Hope PSB\n";
                            $response .= "44. 9PSB\n";
                            $response .= "45. Lotus Bank\n";
                            $response .= "46. Signature Bank\n";
                            $response .= "47. previous\n";
                            $response .= "0. exit\n";

                        } else if ($ussd_string == "0") {
                            UssdLevel::where('ussd_session_id', $session->id)->update(['level' => 0, 'sub_level' => 0, 'repeat' => 0]);
                            $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                        } else {

                            $repeat = $ussd_level->repeat + 1;
                            UssdLevel::where('ussd_session_id', $session->id)->update(['repeat' => $repeat]);

                            $bankName = getBankNameBySerialNumber($ussd_string);
                            if ($bankName != null) {

                                $bankCode = validateBankCode($bankName);
                                if ($bankCode) {

                                    $loan_data = json_decode($ussd_level->loan_data);
                                    $loan_data->bankCode = $bankCode;

                                    $accountNumber = $loan_data->accountNumber;
                                    $bankCode = $loan_data->bankCode;

                                    // error_log('the account details:  ' . $accountNumber . "  " . $bankCode);
                                    //find user on remita
                                    $user = findAccountNumberViaRemita($accountNumber, $bankCode);

                                    $isAccountNumber = Accountno::where([
                                        ['account_number', $accountNumber],
                                        ['bank_code', $bankCode]
                                    ])->exists();

                                    if ($isAccountNumber) {

                                        $response = "END This account number is already in use. Press 0";

                                    } else if (is_array($user)) {

                                        $payslip = Payslip::where('phone', $phone)->orderBy('created_at', 'DESC')->first();
                                        $lender = Lender::where('id', 1)->first();

                                        $slip = (object) null;
                                        $slip->payslip_id = $payslip ? $payslip->id : null;
                                        $slip->platform = $payslip ? $payslip->platform : 'remita';
                                        $slip->lender_id = $lender ? $lender->id : null;
                                        $slip->name = $user['name'];
                                        $slip->phone = $phone;

                                        $slip->customer_id = $user['customer_id'];
                                        $slip->bankCode = $user['bank_code'];
                                        $slip->accountNumber = $user['account_number'];
                                        $slip->salaryPaymentDetails = $user['salaries'];
                                        $slip->authCode = $user['auth_code'];

                                        $slip->password = null;
                                        $slip->email = null;
                                        $slip->employer = $user['company'];
                                        $slip->bvn = $user['bvn'];

                                        $new_user = createUser($slip);

                                        $repeat = $ussd_level->repeat + 1;
                                        UssdLevel::where('ussd_session_id', $session->id)->update(['repeat' => $repeat]);

                                        if ($new_user) {
                                            UssdLevel::where('ussd_session_id', $session->id)->update(['level' => 0, 'sub_level' => 0, 'repeat' => 0]);

                                            $response = "CON Registration successful \n";
                                            $response .= "1. New Loan Request \n";
                                            $response .= "2. Loan Topup Request \n";
                                            $response .= "3. Loan Balance \n";
                                            $response .= "4. Repay Loan \n";
                                            $response .= "5. Exit \n";
                                        } else {
                                            $response = "END We encountered a problem during registration, please try again later.\nFor enquiries contact us at *********** or <EMAIL>";
                                        }
                                    } else {
                                        if ($ussd_level->repeat >= 2) {
                                            $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                                        } else {

                                            $blackList = BlackListedNumber::where('phone_number', $phone)->first();

                                            if ($blackList) {
                                                //reset repeat if date is not today
                                                if (Carbon::parse($blackList->todaysDate)->toDateString() != Carbon::now()->toDateString())
                                                    BlackListedNumber::where('phone_number', $phone)->update(['repeat' => 1, 'todaysDate' => Carbon::now()]);
                                                else
                                                    BlackListedNumber::where('phone_number', $phone)->update(['repeat' => $blackList->repeat + 1, 'todaysDate' => Carbon::now()]);

                                                if ($blackList->repeat >= $limitNo)
                                                    BlackListedNumber::where('phone_number', $phone)->update(['isBlacklisted' => true, 'dateOfBlackListing' => Carbon::now()]);

                                                // if ($blackList->repeat >= 2)
                                                //     $response = "END You are spamming this service. Any further attempts will result in a ban.";
                                                // else
                                                $response = "END Sorry the phone number and account details provided can't obtain a loan at the moment.";

                                            } else {
                                                BlackListedNumber::create([
                                                    'phone_number' => $phone,
                                                    'repeat' => 1,
                                                    'isBlacklisted' => false,
                                                    'todaysDate' => Carbon::now()
                                                ]);

                                                $response = "END Sorry the phone number and account details provided can't obtain a loan at the moment.";
                                            }

                                        }
                                    }
                                } else {
                                    if ($ussd_level->repeat >= 3) {
                                        $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                                    } else {
                                        $response = "CON Select a valid bank\n";
                                        $response .= "1. First Bank\n";
                                        $response .= "2. Access (Diamond)\n";
                                        $response .= "3. Access\n";
                                        $response .= "4. Zenith Bank\n";
                                        $response .= "5. UBA\n";
                                        $response .= "6. Ecobank\n";
                                        $response .= "7. Polaris Bank\n";
                                        $response .= "8. FCMB\n";
                                        $response .= "9. Guaranty Trust Bank\n";
                                        $response .= "10. Fidelity Bank\n";
                                        $response .= "11. next\n";
                                        $response .= "0. exit\n";

                                    }
                                }
                            }
                        }
                        break;

                    case 1:

                        $accountNumber = validateAccountNumber($ussd_string);

                        $repeat = $ussd_level->repeat + 1;
                        UssdLevel::where('ussd_session_id', $session->id)->update(['repeat' => $repeat]);

                        if ($accountNumber != null) {

                            $loan_data = new stdClass();
                            $loan_data->accountNumber = $accountNumber;

                            UssdLevel::where('ussd_session_id', $session->id)->update(['level' => 0, 'sub_level' => 2, 'loan_data' => json_encode($loan_data)]);

                            $response = "CON Select a valid bank\n";
                            $response .= "1. First Bank\n";
                            $response .= "2. Access (Diamond)\n";
                            $response .= "3. Access\n";
                            $response .= "4. Zenith Bank\n";
                            $response .= "5. UBA\n";
                            $response .= "6. Ecobank\n";
                            $response .= "7. Polaris Bank\n";
                            $response .= "8. FCMB\n";
                            $response .= "9. Guaranty Trust Bank\n";
                            $response .= "10. Fidelity Bank\n";
                            $response .= "11. next\n";
                            $response .= "0. exit\n";

                        } else {
                            if ($ussd_level->repeat >= 3) {
                                $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                            } else {
                                $response = "CON Enter a valid salary bank account number";
                            }
                        }
                        break;

                    default:

                        $user = findViaRemita($phone);


                        if ($user === null) {
                            $response = "END We encountered a problem during registration, please try again later.\nFor enquiries contact us at *********** or <EMAIL>";
                        } else if (is_array($user)) {

                            $payslip = Payslip::where('phone', $phone)->orderBy('created_at', 'DESC')->first();
                            $lender = Lender::where('id', 1)->first();

                            $slip = (object) null;
                            $slip->payslip_id = $payslip ? $payslip->id : null;
                            $slip->platform = $payslip ? $payslip->platform : 'remita';
                            $slip->lender_id = $lender ? $lender->id : null;
                            $slip->name = $user['name'];
                            $slip->phone = $phone;

                            $slip->customer_id = $user['customer_id'];
                            $slip->bankCode = $user['bank_code'];
                            $slip->accountNumber = $user['account_number'];
                            $slip->salaryPaymentDetails = $user['salaries'];
                            $slip->authCode = $user['auth_code'];

                            $slip->password = null;
                            $slip->email = null;
                            $slip->employer = $user['company'];
                            $slip->bvn = $user['bvn'];

                            $new_user = createUser($slip);

                            $repeat = $ussd_level->repeat + 1;
                            UssdLevel::where('ussd_session_id', $session->id)->update(['repeat' => $repeat]);

                            if ($new_user) {
                                UssdLevel::where('ussd_session_id', $session->id)->update(['level' => 0, 'sub_level' => 0, 'repeat' => 0]);

                                $response = "CON Registration successful \n";
                                $response .= "1. New Loan Request \n";
                                $response .= "2. Loan Topup Request \n";
                                $response .= "3. Loan Balance \n";
                                $response .= "4. Repay Loan \n";
                                $response .= "5. Exit \n";
                            } else {
                                $response = "END We encountered a problem during registration, please try again later.\nFor enquiries contact us at *********** or <EMAIL>";
                            }
                        } else {
                            if ($ussd_level->repeat >= 3) {
                                $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                            } else {
                                UssdLevel::where('ussd_session_id', $session->id)->update(['level' => 0, 'sub_level' => 1, 'repeat' => 0]);
                                $response = "CON Enter your salary account number";
                            }
                        }
                }
            }


        } else {

            if ($level == 0) {
                $response = "CON Welcome to fastcash.ng \n";
                $response .= "1. New Loan Request \n";
                $response .= "2. Loan Topup Request \n";
                $response .= "3. Loan Balance \n";
                $response .= "4. Repay Loan \n";
                $response .= "5. Exit \n";

            } else if ($level == 1) {
                // loan request

                switch ($ussd_string) {
                    case "0":
                        UssdLevel::where('ussd_session_id', $session->id)->update(['level' => 0, 'sub_level' => 0]);

                        $response = "CON Welcome to fastcash.ng \n";
                        $response .= "1. New Loan Request \n";
                        $response .= "2. Loan Topup Request \n";
                        $response .= "3. Loan Balance \n";
                        $response .= "4. Repay Loan \n";
                        $response .= "5. Exit \n";
                        break;
                    default:
                        if ($loan == null) {

                            switch ($ussd_level->sub_level) {
                                // case 5:
                                //     $repeat = $ussd_level->repeat + 1;
                                //     UssdLevel::where('ussd_session_id', $session->id)->update(['repeat' => $repeat]);

                                //     $loan_data = json_decode($ussd_level->loan_data);

                                // if (!Hash::check($ussd_string, $user->password)) {
                                //     if ($ussd_level->repeat >= 3) {
                                //         $response = "END Thank you for using fastcash.ng. \nFor enquiries contact us at *********** or <EMAIL>";
                                //     } else {
                                //         $s = $loan_data->tenure > 1 ? "s" : "";
                                //         $response = "CON You have requested for a loan of N" . number_format($loan_data->amount, 2) . " for " . $loan_data->tenure . "month$s \n";
                                //         $response .= "Your password is not correct, please try again.";
                                //     }
                                // } else {
                                //     $loan_data->topup = 0;
                                //     $loan_data->disburse_amount = 0;
                                //     $loan_data->status = 0;

                                //     $new_loan = loanRequest($loan_data, $user, $lender);
                                //     if ($new_loan) {
                                //         UssdLevel::where('ussd_session_id', $session->id)->update([
                                //             'loan_data' => json_encode($loan_data),
                                //             'repeat' => 0,
                                //         ]);

                                //         $response = "END Your loan has been logged in for processing. It will take between 2 to 24hrs.";
                                //     } else {
                                //         $response = "END We encountered a problem while booking your loan request, please try again later.";
                                //     }
                                // }
                                // break;
                                // case 4:
                                //     $repeat = $ussd_level->repeat + 1;
                                //     UssdLevel::where('ussd_session_id', $session->id)->update(['repeat' => $repeat]);

                                //     $loan_data = json_decode($ussd_level->loan_data);

                                //     $code = checkMerchantCode($ussd_string);

                                //     if (($code != null && $ussd_string != '#') || $ussd_string == '#') {
                                //         $loan_data->merchant_code = $ussd_string == '#' ? '' : $code;

                                //         UssdLevel::where('ussd_session_id', $session->id)->update([
                                //             'sub_level' => 5,
                                //             'loan_data' => json_encode($loan_data),
                                //             'repeat' => 0
                                //         ]);

                                //         $s = $loan_data->tenure > 1 ? "s" : "";
                                //         $response = "CON You have requested for a loan of N" . number_format($loan_data->amount, 2) . " for " . $loan_data->tenure . "month$s \n";
                                //         $response .= "Enter your password to complete your loan request";
                                //     } else {
                                //         if ($ussd_level->repeat >= 3) {
                                //             $response = "END Thank you for using fastcash.ng. \nFor enquiries contact us at *********** or <EMAIL>";
                                //         } else {
                                //             $response = "CON The merchant code is incorrect, Please enter the correct merchant code or # to continue";
                                //         }
                                //     }
                                //     break;
                                // case 3:
                                //     $repeat = $ussd_level->repeat + 1;
                                //     UssdLevel::where('ussd_session_id', $session->id)->update(['repeat' => $repeat]);

                                //     $loan_data = json_decode($ussd_level->loan_data);

                                //     if (is_numeric($ussd_string) && strlen($ussd_string) == 11) {
                                //         $loan_data->bvn = $ussd_string;

                                //         UssdLevel::where('ussd_session_id', $session->id)->update([
                                //             'sub_level' => 4,
                                //             'loan_data' => json_encode($loan_data),
                                //             'repeat' => 0
                                //         ]);

                                //         $s = $loan_data->tenure > 1 ? "s" : "";
                                //         $response = "CON You have requested for a loan of N" . number_format($loan_data->amount, 2) . " for " . $loan_data->tenure . "month$s \n";
                                //         $response .= "Enter merchant code (optional) or Enter # to continue";
                                //     } else {
                                //         if ($ussd_level->repeat >= 3) {
                                //             $response = "END Thank you for using fastcash.ng. \nFor enquiries contact us at *********** or <EMAIL>";
                                //         } else {
                                //             $response = "CON Please enter the correct BVN";
                                //         }
                                //     }
                                //     break;

                                case 4:
                                    switch ($ussd_string) {
                                        case "1":

                                            // $checkUser = User::where('phone', $phone);

                                            if ($user->bvn != null && $user->bvn != '') {

                                                $loan_data = json_decode($ussd_level->loan_data);

                                                $loan_data->bvn = $user->bvn;

                                                $loan_data->topup = 0;
                                                $loan_data->disburse_amount = 0;
                                                $loan_data->status = 0;

                                                $new_loan = loanRequest($loan_data, $user, $lender);
                                                if ($new_loan) {
                                                    UssdLevel::where('ussd_session_id', $session->id)->update([
                                                        'loan_data' => json_encode($loan_data),
                                                        'repeat' => 0,
                                                    ]);

                                                    $response = "END Your loan has been logged in for processing. It will take between 2 to 24hrs.\nFor enquiries contact us at *********** or <EMAIL>";
                                                } else {
                                                    $response = "END We encountered a problem while booking your loan request, please try again later.";
                                                }
                                            } else {
                                                UssdLevel::where('ussd_session_id', $session->id)->update(['level' => 1, 'sub_level' => 4, 'repeat' => 0]);
                                                $response = 'Enter your bvn';
                                            }
                                            break;

                                        case "2":
                                            UssdLevel::where('ussd_session_id', $session->id)->update(['level' => 0, 'sub_level' => 0, 'repeat' => 0,]);
                                            $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                                            break;

                                        default:
                                            UssdLevel::where('ussd_session_id', $session->id)->update(['level' => 0, 'sub_level' => 0, 'repeat' => 0,]);
                                            $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                                    }

                                    break;

                                case 3:
                                    $repeat = $ussd_level->repeat + 1;
                                    UssdLevel::where('ussd_session_id', $session->id)->update(['repeat' => $repeat]);

                                    $loan_data = json_decode($ussd_level->loan_data);

                                    if (is_numeric($ussd_string) && strlen($ussd_string) == 11) {
                                        // $loan_data->bvn = $ussd_string;
                                        User::where('id', $user->id)->update(['bvn' => $ussd_string]);

                                    } else {
                                        if ($ussd_level->repeat >= 3) {
                                            $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                                            break;
                                        } else {
                                            $response = "CON Please enter the correct BVN";
                                            break;
                                        }
                                    }

                                case 2:
                                    $repeat = $ussd_level->repeat + 1;
                                    UssdLevel::where('ussd_session_id', $session->id)->update(['repeat' => $repeat]);

                                    $loan_data = json_decode($ussd_level->loan_data);
                                    $eligible_amount = number_format($loan_data->eligible_amount, 2);

                                    $amount = (float) str_replace(',', '', $ussd_string);
                                    if ($amount < 10000 && strlen($ussd_string) != 11) {
                                        if ($ussd_level->repeat >= 3) {
                                            $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                                        } else {
                                            $response = "CON You should enter an amount greater than N10,000.00. \n";
                                            $response .= "Your eligible amount is: N" . $eligible_amount . " \n";
                                            $response .= "Enter Loan Amount";
                                        }
                                    } else if ($amount > $loan_data->eligible_amount) {
                                        if ($ussd_level->repeat >= 3) {
                                            $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                                        } else {
                                            $response = "CON You should enter an amount less than your eligible amount. \n";
                                            $response .= "Your eligible amount is: N" . $eligible_amount . " \n";
                                            $response .= "Enter Loan Amount";
                                        }
                                    } else {
                                        $monthly_deduction = round($amount * ((1 / $loan_data->tenure) + ($loan_data->interest_rate / 100)), 2);
                                        $monthly_principal = round($amount / $loan_data->tenure, 2);
                                        $monthly_interest = round($monthly_deduction - $monthly_principal, 2);

                                        $total_repayment = round($monthly_deduction * $loan_data->tenure, 2);

                                        $loan_data->amount = $amount;
                                        $loan_data->monthly_deduction = $monthly_deduction;
                                        $loan_data->monthly_principal = $monthly_principal;
                                        $loan_data->monthly_interest = $monthly_interest;

                                        // $repayment_summary = "Loan Details: $monthly_deduction\n\n";
                                        $repayment_summary = "Loan Amount: $amount\n";
                                        $repayment_summary .= "Monthly Repayment: $monthly_deduction\n";
                                        $repayment_summary .= "Tenure: " . $loan_data->tenure . " months\n\n";
                                        $repayment_summary .= "Select: \n";
                                        $repayment_summary .= "1. Accept \n";
                                        $repayment_summary .= "2. Exit ";

                                        UssdLevel::where('ussd_session_id', $session->id)->update(['level' => 1, 'sub_level' => 4, 'repeat' => 0, 'loan_data' => json_encode($loan_data)]);
                                        $response = "CON Loan Details:\n\n$repayment_summary";
                                    }

                                    break;

                                case 1:
                                    $min_tenure = $lender->min_loan_tenure;
                                    $max_tenure = $lender->max_loan_tenure;

                                    $tenure = intval($ussd_string) >= $min_tenure && intval($ussd_string) <= $max_tenure ? intval($ussd_string) : 1;
                                    $loan_data = json_decode($ussd_level->loan_data);

                                    $loan_data->interest_rate = $lender->interest_rate;
                                    $loan_data->tenure = $tenure;

                                    $eligible_amount = getEligibleAmount($loan_data);

                                    $loan_data->eligible_amount = $eligible_amount;

                                    UssdLevel::where('ussd_session_id', $session->id)->update(['sub_level' => 2, 'loan_data' => json_encode($loan_data)]);

                                    $response = "CON Your eligible amount is: N" . number_format($eligible_amount, 2) . " \n";
                                    $response .= "Enter Loan Amount";
                                    break;

                                default:
                                    if ($user->enabled == 0) {
                                        $response = "END You are not allowed to request a loan at this time. Your account has been disabled, please contact support.";
                                    } else if ($user->is_snoozed == 1) {
                                        $snooze_duration = $snooze_period->value;
                                        $snoozed_date = $user->snoozed_at;
                                        $end_of_snooze = Carbon::parse($snoozed_date)->addDays($snooze_duration)->diffInDays();

                                        $response = "END You are not allowed to request a loan at this time. Try again after $end_of_snooze days";
                                    } else {
                                        $min_tenure = $lender->min_loan_tenure;
                                        $max_tenure = $lender->max_loan_tenure;

                                        $date_diff = $payslip ? Carbon::parse($payslip->retire_expected_at)->diffInDays() : 0;
                                        $eligibility = $date_diff - $eligibility_gap->value;

                                        if ($eligibility <= 0 && $user->platform == 'ippis') {
                                            $response = "END You are not legible to take any loans at this moment";
                                        } else {

                                            $auth_code = null;
                                            $remitaId = null;
                                            $platform = 'sme';

                                            if ($user->platform == 'sme') {
                                                $net_earnings = [];
                                                $net_earning = $payslip->eligible_amt;
                                            } else {
                                                $platform = 'remita';
                                                $remita_data = getRemitaNetEarnings($user, $platform);

                                                $net_earnings = $remita_data ? $remita_data->earnings : [];
                                                $remitaId = $remita_data ? $remita_data->remita_id : null;

                                                if (count($net_earnings) > 0) {
                                                    $auth_code = $net_earnings[0]->auth_code;
                                                }

                                                if (count($net_earnings) == 0) {
                                                    $platform = 'ippis';
                                                    $net_earnings = getIppisNetEarnings($user, $platform);
                                                    $remitaId = null;
                                                }
                                            }

                                            if (count($net_earnings) == 0 && $user->platform != 'sme') {
                                                $response = "END Sorry you can’t get a loan at the moment. Try again after your next salary payment.";
                                            } else {
                                                if ($platform != 'sme') {
                                                    $net_earning = getNetEarning($net_earnings, $platform);
                                                }

                                                $loan_data = (object) null;
                                                $loan_data->platform = $platform;
                                                $loan_data->net_earning = $net_earning;
                                                $loan_data->net_earnings = $net_earnings;
                                                $loan_data->auth_code = $auth_code;
                                                $loan_data->old_loan = null;
                                                $loan_data->remita_id = $remitaId;

                                                UssdLevel::where('ussd_session_id', $session->id)->update(['sub_level' => 1, 'loan_data' => json_encode($loan_data)]);
                                                $response = "CON Enter Loan Tenure: $min_tenure to $max_tenure months";
                                            }
                                        }
                                    }
                                    break;
                            }
                        } else {
                            $response = "CON You already have an active loan. Press 0 to go back or drop the call.\nFor enquiries contact us at *********** or <EMAIL>";
                        }
                        break;
                }
            } else if ($level == 2) {
                // topup loan request
                switch ($ussd_string) {
                    case "0":
                        UssdLevel::where('ussd_session_id', $session->id)->update(['level' => 0, 'sub_level' => 0]);

                        $response = "CON Welcome to fastcash.ng \n";
                        $response .= "1. New Loan Request \n";
                        $response .= "2. Loan Topup Request \n";
                        $response .= "3. Loan Balance \n";
                        $response .= "4. Repay Loan \n";
                        $response .= "5. Exit \n";
                        break;
                    default:
                        if ($loan) {
                            $total_paid = PaymentDate::where('loan_id', $loan->id)->where('paid', 1)->count();
                            $should_have_paid = round($loan->tenure / 2);
                            $has_ended = $loan->disbursed == 1 ? hasLoanEnded($loan) : true;
                            $eligible = $total_paid >= $should_have_paid && $has_ended == false ? 1 : 0;

                            if ($eligible == 1) {
                                switch ($ussd_level->sub_level) {
                                    case 3:
                                        $repeat = $ussd_level->repeat + 1;
                                        UssdLevel::where('ussd_session_id', $session->id)->update(['repeat' => $repeat]);

                                        $loan_data = json_decode($ussd_level->loan_data);

                                        if (!Hash::check($ussd_string, $user->password)) {
                                            if ($ussd_level->repeat >= 3) {
                                                $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                                            } else {
                                                $s = $loan_data->tenure > 1 ? "s" : "";
                                                $response = "CON You have requested for a topup loan of N" . number_format($loan_data->amount, 2) . " for " . $loan_data->tenure . "month$s \n";
                                                $response .= "Your password is not correct, please try again.";
                                            }
                                        } else {
                                            $loan_data->topup = 1;
                                            $loan_data->status = 0;
                                            $loan_data->bvn = $user->bvn;

                                            $new_loan = loanRequest($loan_data, $user, $lender);
                                            if ($new_loan) {
                                                UssdLevel::where('ussd_session_id', $session->id)->update([
                                                    'loan_data' => json_encode($loan_data),
                                                    'repeat' => 0,
                                                ]);

                                                $response = "END Your topup loan has been logged in for processing. It will take between 2 to 24hrs.";
                                            } else {
                                                $response = "END We encountered a problem while booking your loan request, please try again later.";
                                            }
                                        }
                                        break;
                                    case 2:
                                        $repeat = $ussd_level->repeat + 1;
                                        UssdLevel::where('ussd_session_id', $session->id)->update(['repeat' => $repeat]);

                                        $loan_data = json_decode($ussd_level->loan_data);
                                        $eligible_amount = number_format($loan_data->eligible_amount, 2);
                                        $outstanding_amount = number_format($loan_data->outstanding_amount, 2);

                                        $amount = (float) str_replace(',', '', $ussd_string);
                                        if ($amount < 10000) {
                                            if ($ussd_level->repeat >= 3) {
                                                $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                                            } else {
                                                $response = "CON You should enter an amount greater than N10,000.00. \n";
                                                $response .= "Your eligible amount is: N" . $eligible_amount . " \n";
                                                $response .= "Your outstanding deduction is: N" . $outstanding_amount . " \n";
                                                $response .= "Enter Loan Amount";
                                            }
                                        } else if ($amount > $loan_data->eligible_amount) {
                                            if ($ussd_level->repeat >= 3) {
                                                $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                                            } else {
                                                $response = "CON You should enter an amount less than your eligible amount. \n";
                                                $response .= "Your eligible amount is: N" . $eligible_amount . " \n";
                                                $response .= "Your outstanding deduction is: N" . $outstanding_amount . " \n";
                                                $response .= "Enter Loan Amount";
                                            }
                                        } else if ($amount < $loan_data->outstanding_amount) {
                                            if ($ussd_level->repeat >= 3) {
                                                $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                                            } else {
                                                $response = "CON Your eligible amount is: N" . $eligible_amount . " \n";
                                                $response .= "Your outstanding deduction is: N" . $outstanding_amount . " \n";
                                                $response .= "Enter Loan Amount (must be above your outstanding amount)";
                                            }
                                        } else {
                                            $monthly_deduction = round($amount * ((1 / $loan_data->tenure) + ($loan_data->interest_rate / 100)), 2);
                                            $monthly_principal = round($amount / $loan_data->tenure, 2);
                                            $monthly_interest = round($monthly_deduction - $monthly_principal, 2);
                                            $balance_amount = getLoanBalance($loan);
                                            $disburse_amount = $amount - $balance_amount;

                                            $loan_data->amount = $amount;
                                            $loan_data->monthly_deduction = $monthly_deduction;
                                            $loan_data->monthly_principal = $monthly_principal;
                                            $loan_data->monthly_interest = $monthly_interest;
                                            $loan_data->balance_amount = $balance_amount;
                                            $loan_data->disburse_amount = $disburse_amount < 0 ? 0 : $disburse_amount;

                                            UssdLevel::where('ussd_session_id', $session->id)->update([
                                                'sub_level' => 3,
                                                'loan_data' => json_encode($loan_data),
                                                'repeat' => 0
                                            ]);

                                            $s = $loan_data->tenure > 1 ? "s" : "";
                                            $response = "CON You have requested for a loan of N" . number_format($amount, 2) . " for " . $loan_data->tenure . "month$s \n";
                                            $response .= "Enter your password to complete your loan request";
                                        }
                                        break;
                                    case 1:
                                        $min_tenure = $lender->min_loan_tenure;
                                        $max_tenure = $lender->max_loan_tenure;

                                        $tenure = intval($ussd_string) >= $min_tenure && intval($ussd_string) <= $max_tenure ? intval($ussd_string) : 1;
                                        $loan_data = json_decode($ussd_level->loan_data);

                                        $loan_data->interest_rate = $lender->interest_rate;
                                        $loan_data->tenure = $tenure;

                                        $eligible_amount = getEligibleAmount($loan_data);

                                        $loan_data->eligible_amount = $eligible_amount;

                                        UssdLevel::where('ussd_session_id', $session->id)->update(['sub_level' => 2, 'loan_data' => json_encode($loan_data)]);

                                        $response = "CON Your eligible amount is: N" . number_format($eligible_amount, 2) . " \n";
                                        $response .= "Your outstanding deduction is: N" . number_format($loan_data->outstanding_amount, 2) . " \n";
                                        $response .= "Enter Loan Amount (must be above your outstanding amount)";
                                        break;
                                    case 0:
                                    default:
                                        if ($user->enabled == 0) {
                                            $response = "END You are not allowed to request a loan at this time. Your account has been disabled, please contact support.";
                                        } else if ($user->is_snoozed == 1) {
                                            $snooze_duration = $snooze_period->value;
                                            $snoozed_date = $user->snoozed_at;
                                            $end_of_snooze = Carbon::parse($snoozed_date)->addDays($snooze_duration)->diffInDays();

                                            $response = "END You are not allowed to request a loan at this time. Try again after $end_of_snooze days";
                                        } else {
                                            $min_tenure = $lender->min_loan_tenure;
                                            $max_tenure = $lender->max_loan_tenure;

                                            $auth_code = null;
                                            $remitaId = null;
                                            $platform = $loan->platform;
                                            if ($platform == 'sme') {
                                                $net_earnings = [];
                                                $net_earning = $payslip->eligible_amt;
                                            } else {
                                                $platform = 'remita';
                                                $remita_data = getRemitaNetEarnings($user, $platform);

                                                $net_earnings = $remita_data ? $remita_data->earnings : [];
                                                $remitaId = $remita_data ? $remita_data->remita_id : null;

                                                if (count($net_earnings) > 0) {
                                                    $auth_code = $net_earnings[0]->auth_code;
                                                }

                                                if (count($net_earnings) == 0) {
                                                    $platform = 'ippis';
                                                    $net_earnings = getIppisNetEarnings($user, $platform);
                                                    $remitaId = null;
                                                }
                                            }

                                            if (count($net_earnings) == 0 && $user->platform != 'sme') {
                                                $response = "END You are not eligible to take a loan at this moment. Please contact support.";
                                            } else {
                                                if ($platform != 'sme') {
                                                    $net_earning = getNetEarning($net_earnings, $platform);
                                                }

                                                $loan_data = (object) null;
                                                $loan_data->platform = $platform;
                                                $loan_data->net_earning = $net_earning;
                                                $loan_data->net_earnings = $net_earnings;
                                                $loan_data->auth_code = $auth_code;
                                                $loan_data->old_loan = $loan->id;
                                                $loan_data->remita_id = $remitaId;

                                                $transactions = Transaction::where('loan_id', $loan->id)->where('transaction_flag', 'deduction')->where('approved', 1)->get(['amount', 'principal', 'interest']);
                                                $amt_paid = [];
                                                foreach ($transactions as $item) {
                                                    $amt_paid[] = $item->amount;
                                                }

                                                $outstanding_balance = $loan->total_deduction - array_sum($amt_paid);

                                                $loan_data->outstanding_amount = $outstanding_balance;
                                                $loan_data->tenure_balance = $loan->tenure - count($transactions);

                                                UssdLevel::where('ussd_session_id', $session->id)->update(['sub_level' => 1, 'loan_data' => json_encode($loan_data)]);
                                                $response = "CON Enter Loan Tenure: $min_tenure to $max_tenure months";
                                            }
                                        }
                                        break;
                                }
                            } else {
                                $response = "CON You are not eligible for a topup loan. Press 0 to go back or drop the call.\nFor enquiries contact us at *********** or <EMAIL>";
                            }
                        } else {
                            $response = "CON You dont have an active loan. Press 0 to go back or drop the call.\nFor enquiries contact us at *********** or <EMAIL>";
                        }
                        break;
                }
            } else if ($level == 3) {
                // loan balance 
                switch ($ussd_string) {
                    case "0":
                        UssdLevel::where('ussd_session_id', $session->id)->update(['level' => 0]);

                        $response = "CON Welcome to fastcash.ng \n";
                        $response .= "1. New Loan Request \n";
                        $response .= "2. Loan Topup Request \n";
                        $response .= "3. Loan Balance \n";
                        $response .= "4. Repay Loan \n";
                        $response .= "5. Exit \n";
                        break;
                    case "1":
                        $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                        break;
                    default:
                        if ($loan == null) {
                            $info = 'You do not have an active loan.';
                            $response = "CON Loan Balance: " . $info . " \n";
                        } else {
                            if ($loan->disbursed == 0) {
                                $info = 'Your loan has not been approved and disbursed yet.';
                                $response = "CON Loan Balance: " . $info . " \n";
                            } else {
                                $balance = getLoanBalance($loan);
                                $info = 'NGN' . number_format($balance, 2);

                                $response = "CON Loan Balance: " . $info . " \n";
                            }
                        }

                        $response .= "0. Go Back \n";
                        $response .= "1. Exit";
                        break;
                }

            } else if ($level == 4) {
                // Exit
                switch ($ussd_string) {
                    case "0":
                        UssdLevel::where('ussd_session_id', $session->id)->update(['level' => 0]);

                        $response = "CON Welcome to fastcash.ng \n";
                        $response .= "1. New Loan Request \n";
                        $response .= "2. Loan Topup Request \n";
                        $response .= "3. Loan Balance \n";
                        $response .= "4. Repay Loan \n";
                        $response .= "5. Exit \n";
                        break;
                    case "1":
                        try {
                            $balance = getLoanBalance($loan);
                            $amount = 'NGN' . number_format($balance, 2);

                            $result = ussdLiquidateLoan($loan->id, 'bank', $balance);
                            if ($result->message != '') {
                                $response = "END Kindly transfer " . $amount . " to \n";
                                $response .= "Account Name: Consumer Microfinance Bank Limited \n";
                                $response .= "Bank Name: FIRST BANK \n";
                                $response .= "Account Number: **********";
                            } else {
                                $response = "END " . $result->error . "\n";
                            }
                        } catch (\Exception $e) {
                            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                            $response = "END We encountered an error while trying to liquidate your loan. please try again later.";
                        }
                        break;
                    case "2":
                        $response = "CON Please login to https://fastcash.ng to liquidate via your debit card \n";
                        $response .= "1. Exit \n";
                        $response .= "0. Go Back To Main Menu";
                        break;
                    case "3":
                        $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
                        break;
                    default:
                        if ($loan == null) {
                            $response = "CON You do not have an active loan. Press 0 to go back or drop the call.\nFor enquiries contact us at *********** or <EMAIL>";
                        } else if ($loan->liquidate_approve == 0 && $loan->liquidated == 1) {
                            $response = "CON Your previous liquidation request is awaiting approval.";
                            $response .= "Press 0 to go back or drop the call.\nFor enquiries contact us at *********** or <EMAIL>";
                        } else if ($loan->disbursed == 0) {
                            $response = "CON Your loan amount has not been disbursed yet.";
                            $response .= "Press 0 to go back or drop the call.\nFor enquiries contact us at *********** or <EMAIL>";
                        } else {
                            $response = "CON Liquidate your loan \n";
                            $response .= "1. Liquidate via bank transfer \n";
                            $response .= "2. Liquidate via card \n";
                            $response .= "3. Exit \n";
                            $response .= "0. Go Back";
                        }
                        break;
                }
            } else {
                $response = "END Thank you for using fastcash.ng.\nFor enquiries contact us at *********** or <EMAIL>";
            }
        }

        echo $response;
    }

    public function createOrUpdateSetting(Request $request)
    {
        try {
            $validated = $request->validate([
                'limitNo' => 'required|integer',
                'messageToDisplay' => 'required|string',
            ]);

            $setting = UssdSetting::find(1);

            if ($setting) {
                $setting->update([
                    'limitNo' => $validated['limitNo'],
                    'messageToDisplay' => $validated['messageToDisplay'],
                ]);
            } else {
                $setting = UssdSetting::create([
                    'id' => 1,
                    'limitNo' => $validated['limitNo'],
                    'messageToDisplay' => $validated['messageToDisplay'],
                ]);
            }

            return response()->json([
                'message' => 'USSD settings updated successfully',
                'data' => $setting
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating USSD settings: ' . $e->getMessage());

            return response()->json([
                'error' => 'Failed to update USSD settings',
            ], 500);
        }
    }



    public function getSetting()
    {

        $setting = UssdSetting::find(1);

        if ($setting) {

            return response()->json([
                'data' => $setting,
            ]);
        } else {

            return response()->json([
                'message' => 'USSd settings not found',
            ], 404);
        }
    }
}

