import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import { doNotify, notifyDone } from '../actions/general';

import { baseURL, httpRequest, userAPI } from '../config/constants';
import loading from '../assets/img/loading.gif';

const validate = values => {
    const errors = {}
    if (!values.firstname) {
        errors.firstname = 'Enter the first name';
    }
    if (!values.lastname) {
        errors.lastname = 'Enter the last name';
    }
    if (!values.email) {
        errors.email = 'Enter email address';
    }
    if (!values.phone) {
        errors.phone = 'Please give us the phone number';
    }
    if (!values.gender) {
        errors.gender = 'Select the gender';
    }
    if (!values.role) {
        errors.role = 'Select the role of the user';
    }
    return errors;
};

const genders = [
    {id: 'male', name: 'Male'},
    {id: 'female', name: 'Female'},
];

const renderField = ({input, id, label, disabled, type, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

const renderSelectField = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label>{label}</label>
        <select {...input} className="form-control">
            <option value="">{`Select ${label}`}</option>
            {data.map((c, i) => <option value={c.id} key={i}>{c.name}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class EditUser extends Component {
    updateAdminUser = data => {
        const { user, edit_user } = this.props;
        const details = { ...data, user_id: user.id };
		return httpRequest(`${baseURL}${userAPI}/${edit_user.id}`, 'PUT', true, details)
			.then(_ => {
                this.props.reset('editadmin');
                this.props.notifyDone(true);
                this.props.editUser(null);
                this.notify('', 'admin user saved!', 'success');
			})
			.catch(error => {
				const message = error.message || 'could not save admin user';
				this.notify('', message, 'error');
				throw new SubmissionError({
					_error: message,
				});
			});
    };
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

    render() {
        const { handleSubmit, error, submitting, pristine, roleList, profile } = this.props;
        const roles = roleList.map(r => ({id: r.id, name: r.title}));

        return (
            <div className="element-wrapper">
                <h6 className="element-header">Edit user</h6>
                <div className="element-box">
                    {error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                    <form onSubmit={handleSubmit(this.updateAdminUser)}>
                        <div className="row">
                            <div className="col-sm-4">
                                <Field
                                    name="firstname"
                                    id="firstname"
                                    type="text"
                                    component={renderField}
                                    label="First Name"
                                />
                            </div>
                            <div className="col-sm-4">
                                <Field
                                    name="lastname"
                                    id="lastname"
                                    type="text"
                                    component={renderField}
                                    label="Last Name"
                                />
                            </div>
                            <div className="col-sm-4">
                                <Field
                                    name="email"
                                    id="email"
                                    type="email"
                                    component={renderField}
                                    label="Email"
                                />
                            </div>
                            <div className="col-sm-4">
                                <Field
                                    name="gender"
                                    component={renderSelectField}
                                    label="Gender"
                                    data={genders}
                                />
                            </div>
                            <div className="col-sm-4">
                                <Field
                                    name="phone"
                                    id="phone"
                                    type="text"
                                    component={renderField}
                                    label="Phone Number"
                                />
                            </div>
                            <div className="col-sm-4">
								{profile.role_name === 'super' ? (
                                    <Field
										name="norole"
										id="norole"
										type="text"
										component={renderField}
										label="Role"
                                        disabled={true}
									/>
								) : (
									<Field name="role" component={renderSelectField} label="Role" data={roles} />
								)}
                            </div>
                        </div>
                        <div className="form-buttons-w">
                            <button className="btn btn-dark" onClick={() => this.props.editUser(null)}>Cancel</button>
                            <button className="btn btn-primary" disabled={pristine || submitting} type="submit">
                                {submitting? <img src={loading} alt=""/> : 'Save'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    }
}

EditUser = reduxForm({
    form: 'editadmin',
    validate,
})(EditUser);

const mapStateToProps = (state, ownProps) => {
    const roles = state.general.roles;
    const edit_user = ownProps.edit_user;
    const names = edit_user.name.split(' ');
    const role = roles.find(r => r.title === edit_user.role_category);
    
    return {
        initialValues: {
            firstname: names[0],
            lastname: names[1],
            email: edit_user.email,
            gender: edit_user.gender,
            phone: edit_user.phone,
            role: role ? role.id : null,
            norole: edit_user.role_category,
        },
        user: state.user.user,
        roleList: roles,
        profile: edit_user,
    }
}

export default connect(mapStateToProps, { reset, doNotify, notifyDone })(EditUser);