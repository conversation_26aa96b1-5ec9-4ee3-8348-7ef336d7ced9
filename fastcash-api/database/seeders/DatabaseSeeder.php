<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        if(env('APP_DEBUG') === 'true'){
            $this->call(EarningTableSeeder::class);
            $this->call(PayslipTableSeeder::class);
        }
        $this->call(RoleTableSeeder::class);
        $this->call(SettingsTableSeeder::class);
        $this->call(UserTableSeeder::class);
        $this->call(StateLGASeeder::class);
        $this->call(OfficeSeeder::class);
        $this->call(ActionsSeeder::class);
        $this->call(BankCodeSeeder::class);
    }
}
