<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Migrations\Migration;

class CreateUsersLoanView extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement($this->dropView());
        DB::statement($this->createView());
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement($this->dropView());
    }

    private function createView()
    {
        return <<<SQL
            CREATE VIEW `customers` AS
            SELECT
                users.*,
                roles.name AS role_name,
                loans.approved AS loan_approved,
                loans.verified AS loan_verified,
                loans.disbursed AS loan_disbursed,
                loans.status
            FROM
                users
            LEFT JOIN loans ON loans.id = users.loan_id
            LEFT JOIN user_roles ON user_roles.user_id = users.id
            LEFT JOIN roles ON roles.id = user_roles.role_id;
SQL;
    }

    private function dropView()
    {
        return <<<SQL
DROP VIEW IF EXISTS `customers`;
SQL;
    }
}
