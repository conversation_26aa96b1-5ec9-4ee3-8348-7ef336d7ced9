import React, { Component, Fragment } from 'react';
import moment from 'moment';

import { httpRequest, baseURL, merchantAPI, currency } from '../config/constants';
import { connect } from 'react-redux';
import { doNotify } from '../actions/general';
import Loading from '../components/Loading';

class Merchant extends Component {
	state = {
		merchant: null,
		amount: 0,
		percent1: 0,
		percent2: 0,
		percent3: 0,
		loans: [],
	};

	componentDidMount() {
		const { match } = this.props;
		this.fetch(match.params.id);
	}

	fetch = async id => {
		const { lender } = this.props;
		this.setState({ fetching: true });
		try {
			const rs = await httpRequest(`${baseURL}${merchantAPI}/${id}`, 'GET', true);

			const { loans, ...merchant } = rs.merchant;
			const amount = merchant.count * parseInt(lender.merchant_commission, 10);
			const percent1 = (parseInt(merchant.count, 10) / 20) * 100;
			const percent2 = (parseInt(merchant.count, 10) / 20) * 100 * 1.5;
			const percent3 = percent2 > 100 ? 100 : percent2;
			
			this.setState({ merchant, fetching: false, amount, percent1, percent2, percent3, loans });
		}
		catch (error) {
			const message = error.message || 'error, failed loading merchant';
			this.props.doNotify({ message, level: 'error', title: '' });
			this.setState({ fetching: false });
		}
	};
	
	render() {
		const { fetching, merchant, amount, percent1, percent3, loans } = this.state;
			
		return (
			<div className="content-i">
				<div className="content-box">
					{fetching && <Loading />}
					{!fetching && merchant && (
						<Fragment>
							<div className="row">
								<div className="col-sm-12">
									<div className="padded-lg">
										<div className="element-wrapper">
											<div className="element-box">
												<div className="row">
													<div className="col-sm-3">
														<div className="element-box el-tablo" style={{marginBottom: '0'}}>
															<div className="el-tablo highlight bigger">
																<div className="lead">{merchant.user}</div>
															</div>
															<div className="label">{merchant.lender.name}</div>
														</div>
													</div>
													<div className="col-sm-6">
														<div className="element-box el-tablo" style={{marginBottom: '0'}}>
															<div className="label">Office</div>
															<div className="lead">{merchant.office}</div>
														</div>
													</div>
													<div className="col-sm-3">
														<div className="element-box el-tablo" style={{marginBottom: '0'}}>
															<div className="label">Merchant ID</div>
															<div className="lead">{merchant.merchant_code}</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div className="row">
								<div className="col-lg-7">
									<div className="padded-lg">
										<div className="element-wrapper">
											<h6 className="element-header">Transaction Statistics</h6>
											<div className="element-box">
												<div className="table-responsive">
													<table className="table table-striped">
														<thead>
														<tr>
															<th>Employee Name</th>
															<th>Loan Amount</th>
															<th className="text-center">Date</th>
														</tr>
														</thead>
														<tbody>
														{loans.map(l => {
															return (
																<tr key={l.id}>
																	<td>{l.user && l.user.name}</td>
																	<td>{currency(l.amount)}</td>
																	<td>{moment(l.created_at).format('D.MMM.YYYY')}</td>
																</tr>
															)
														})}
														</tbody>
													</table>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div className="col-lg-5 b-l-lg">
									<div className="padded-lg">
										<div className="element-wrapper">
											<h6 className="element-header">Project Statistics</h6>
											<div className="element-box">
												<div className="padded m-b">
													<div className="centered-header"><h6>Count & Earnings</h6></div>
													<div className="row">
														<div className="col-6 b-r b-b">
															<div className="el-tablo centered padded-v-big highlight bigger">
																<div className="label">Transaction Count</div>
																{merchant && <div className="value">{merchant.count}</div>}
															</div>
														</div>
														<div className="col-6 b-b">
															<div className="el-tablo centered padded-v-big highlight bigger">
																<div className="label">Amount Earned</div>
																<div className="value">{currency(amount)}</div>
															</div>
														</div>
													</div>
												</div>
												{merchant && (
													<div className="padded m-b">
														<div className="centered-header"><h6> Transaction Target</h6></div>
														<div className="os-progress-bar primary">
															<div className="bar-labels">
																<div className="bar-label-left"><span>Progress</span><span className="positive">{merchant.count}</span></div>
																<div className="bar-label-right"><span className="info">{merchant.count}/20</span></div>
															</div>
															<div className="bar-level-1" style={{width: '100%'}}>
																<div className="bar-level-2" style={{width: `${percent3}%`}}>
																	<div className="bar-level-3" style={{width: `${percent1}%`}}></div>
																</div>
															</div>
														</div>
													</div>
												)}
											</div>
										</div>
									</div>
								</div>
							</div>
						</Fragment>
					)}
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
    return {
        lender: state.user.lender,
    }
}

export default connect(mapStateToProps, { doNotify })(Merchant);
