<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdatePayslip extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('payslips', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('name');
            $table->string('biometric')->nullable()->after('salary_account_number');
            $table->string('email')->nullable()->after('phone');
            $table->string('address')->nullable()->after('birth_date');
            $table->string('nature_of_business')->nullable()->after('employer');
            $table->string('employment_status')->nullable()->after('nature_of_business');
            $table->string('turnover')->nullable()->after('trade_union');
            $table->decimal('eligible_amount', 20, 2)->nullable()->after('turnover');
            $table->string('platform')->nullable()->after('lender_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('payslips', function (Blueprint $table) {
            $table->dropColumn(['phone', 'biometric', 'email', 'address', 'nature_of_business', 'employment_status', 'turnover', 'eligible_amount']);
        });
    }
}
