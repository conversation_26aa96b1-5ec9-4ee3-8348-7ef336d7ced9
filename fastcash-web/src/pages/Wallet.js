/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import qs from 'querystring';

import { currency, httpRequest, baseURL, withdrawAPI } from '../config/constants';
import { doNotify } from '../actions/general';
import Withdraw from '../components/Withdraw';
import WithdrawalRequests from '../components/WithdrawalRequests';

const pageSize = 12;

class Wallet extends Component {
	state = {
		withdrawals: [],
		currentPage: 1,
		totalPage: 0,
		fetching: false,
	};

	componentDidMount() {
		const { location } = this.props;
		const query = qs.parse(location.search.replace('?', ''));
		
		const page = query && query.p ? parseInt(query.p, 10) : 1;

		this.setState({
			currentPage: page,
			location: location.pathname,
		});

		this.fetch(page);
	}

	fetch = async pageNumber => {
		this.setState({ fetching: true });
		try {
			const { user } = this.props;
			const rs = await httpRequest(`${baseURL}${withdrawAPI}/${user.id}?page=${pageNumber}&pagesize=${pageSize}`, 'GET', true);
            const result = rs.result;
			this.setState({
				currentPage: pageNumber,
				totalPage: result.total,
				withdrawals: result.data,
				fetching: false,
			});
		} catch (e) {
			const message = e.message || 'could not load withdrawal requests';
			this.props.doNotify({ message, level: 'error' });
			this.setState({ fetching: false });
		}
	};

	componentWillUpdate(nextProps, nextState) {
		const query = qs.parse(nextProps.location.search.replace('?', ''));
		const pageNumber = query && query.p ? parseInt(query.p, 10) : 1;
		if(nextState.currentPage !== pageNumber || nextProps.location.pathname !== nextState.location){
			this.setState({
				location: nextProps.location.pathname,
				currentPage: pageNumber,
			});

			this.fetch(pageNumber);
		}
	}

	handleChange = pageNumber => {
		console.log(pageNumber);
		this.props.history.push(`/wallet?p=${pageNumber}`);
	};

	transfer = () => {};

	referFriend = () => {};

	render() {
		const { user } = this.props;
		const { withdrawals, currentPage, totalPage, fetching } = this.state;
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="element-wrapper compact pt-4">
						<h6 className="element-header">Wallet Balance</h6>
						<div className="element-box-tp">
							<div className="row">
								<div className="col-lg-7 col-xxl-6">
									<div className="element-balances">
										<div className="balance">
											<div className="balance-title">Wallet Balance</div>
											<div className="balance-value">
												<span>{currency(user.wallet)}</span>
											</div>
											{user.has_request && (
												<div className="balance-link">
													<a className="btn btn-link btn-underlined btn-red">
														<span>Pending Requests</span>
														<i className="os-icon os-icon-alert-circle" style={{fontSize:'17px'}}/>
													</a>
												</div>
											)}
										</div>
										<div className="balance">
											<div className="balance-title">Referrals</div>
											<div className="balance-value">{user.referrals}</div>
										</div>
										<div className="balance">
											<div className="balance-title">Investment Returns</div>
											<div className="balance-value">{currency(user.investment)}</div>
											<div className="balance-link">
												<a className="btn btn-link btn-underlined btn-gold" role="button" tabIndex="0" onClick={this.transfer}>
													<span>Transfer to Wallet</span>
													<i className="os-icon os-icon-arrow-right4"/>
												</a>
											</div>
										</div>
									</div>
								</div>
								<div className="col-lg-5 col-xxl-6">
									{/* <div className="alert alert-warning borderless">
										<h5 className="alert-heading">Refer Friends. Get Rewarded</h5>
										<p>You can earn: 15,000 Membership Rewards points for each approved referral – up to 55,000 Membership Rewards points per calendar year.</p>
										<div className="alert-btn mt-4">
											<a className="btn btn-white-gold" role="button" tabIndex="0" onClick={this.referFriend}>
												<i className="os-icon os-icon-ui-92" />
												<span>Send Referral</span>
											</a>
										</div>
									</div> */}
								</div>
							</div>
						</div>
					</div>
					<div className="row">
						<div className="col-lg-7 col-xxl-6">
							<div className="element-wrapper">
								<Withdraw fetch={this.fetch} currentPage={currentPage} />
							</div>
						</div>
						<div className="col-lg-5 col-xxl-6" />
					</div>
					<WithdrawalRequests
						withdrawals={withdrawals}
						currentPage={currentPage}
						totalPage={totalPage}
						fetching={fetching}
						handleChange={this.handleChange}
					/>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
	};
};

export default connect(mapStateToProps, { doNotify })(Wallet);
