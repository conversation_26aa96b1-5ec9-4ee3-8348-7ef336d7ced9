<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class MultiFundSetting extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $settings = [
            array('name' => 'multifund_expire', 'value' => '14'),
        ];

        foreach ($settings as $setting){
            $s = new Setting();
            $s->name = $setting['name'];
            $s->value = $setting['value'];
            $s->lender_id = 1;
            $s->save();
        }
    }
}
