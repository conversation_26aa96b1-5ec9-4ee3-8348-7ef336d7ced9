import React, { Component } from 'react';
import moment from 'moment';
import { connect } from 'react-redux';

import { httpRequest, baseURL, officeAPI } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import { doNotify } from '../actions/general';

class ScheduleDate extends Component {
	state = {
		month: moment().month(),
		year: moment().format('YYYY'),
		mda: '',
	};
	
	selectMonth = e => this.setState({ month: e.target.value });
	
	selectYear = e => this.setState({ year: e.target.value });

    generateSchedule = () => {
		const { office, lender } = this.props;
		const { month, year } = this.state;
		const nMonth = parseInt(month, 10) + 1;
		if(!office){ return; }
        const action = window.confirm("Do you want to generate schedule?") ? true : false;
        if(action){
            this.props.startBlock();
            this.setState({ mda: office.name });
            return httpRequest(`${baseURL}${officeAPI}/schedule/${office.id}?month=${nMonth}&year=${year}&lender=${lender.id}`, 'GET', true)
                .then((response) => {
					this.props.stopBlock();
					const message = response.message === '' ? `schedule created for ${this.state.mda}. check your email and download it to print the schedule.` : response.message;
					this.notify('', message, 'success');
					if(response.data !== ''){
						window.open(response.data, '_blank');
					}
                })
                .catch(error => {
					this.props.stopBlock();
					const message = error.message || `schedule could not be created for ${this.state.mda}`;
					this.notify('', message, 'error');
                });
        }
    }
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	render() {
		const { month, year } = this.state;
		return (
			<div className="schedule_date" style={{padding: '12px'}}>
				<div>
					<label>month</label>
					<select className="form-control" onChange={this.selectMonth} defaultValue={month}>
						{[ ...Array(12).keys() ].map(m => {
							return (
								<option key={m} value={m}>{moment().month(m).format('MMMM')}</option>
							)
						})}
					</select>
				</div>
				<div>
					<label>year</label>
					<select className="form-control" onChange={this.selectYear} defaultValue={year}>
						<option value="2020">2020</option>
						<option value="2019">2019</option>
						<option value="2018">2018</option>
					</select>
				</div>
				<div style={{marginTop: '4px',textAlign:'center',}}>
					<button style={{width: '100%'}} type="button" className="btn btn-primary" onClick={this.generateSchedule}>Generate</button>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		lender: state.lender.profile,
	}
};

export default connect(mapStateToProps, { startBlock, stopBlock, doNotify })(ScheduleDate);