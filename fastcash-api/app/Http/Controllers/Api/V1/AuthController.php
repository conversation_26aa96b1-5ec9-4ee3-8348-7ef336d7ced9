<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Accountno;
use App\Models\BankCode;
use App\Models\Earning;
use App\Models\Lender;
use App\Models\Loan;
use App\Models\Merchant;
use App\Models\MerchantPay;
use App\Models\Office;
use App\Models\PaymentDate;
use App\Models\Payslip;
use App\Models\PayslipUpload;
use App\Models\Role;
use App\Models\Setting;
use App\Models\Support;
use App\Models\SupportMail;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Withdraw;
use App\Services\MailHandler;
use DB;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Log;

class AuthController extends Controller
{
    /**
     * API Admin Login
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function signin(Request $request)
    {
        try {
            $input = $request->all();

            $user = User::where('username', $input['username'])->first();
            if ($user) {
                $credentials = array('email' => $user->email, 'password' => $input['password']);
                if (!$token = auth()->attempt($credentials)) {
                    return response()->json(['error' => 'invalid credentials'], 500);
                }

                $lender_id = $user->lender_id;
                $lender = Lender::where('id', $lender_id)->first();

                $user->lender = $lender;

                if ($user->role == 'admin' || $user->role == 'super') {
                    if ($user->enabled == 0) {
                        return response()->json(['error' => 'your account has been disabled, please contact support'], 500);
                    }

                    $details = getFinancialOverview($lender_id, $lender_id);

                    $banks = BankCode::whereRaw('id = (SELECT MIN(id) FROM bank_codes t2 WHERE bank_codes.code = t2.code)')->orderBy('name')->get(['id', 'name', 'code']);

                    $lenders = Lender::get(['id', 'name']);

                    $roles = Role::where('name', '<>', 'user')->where('name', '<>', 'super')->where('name', '<>', 'merchant')->get();

                    $slips = PayslipUpload::where('lender_id', $lender_id)->orderBy('year', 'desc')->orderBy('month', 'desc')->orderBy('created_at', 'desc')->get();

                    $mails = SupportMail::where('user_id', $user->id)->where('lender_id', $lender_id)->orderBy('created_at', 'desc')->get();
                    $mails = $mails->map(function ($item, $key) {
                        $item->user = User::where('id', $item->user_id)->first(['id', 'name', 'email']);
                        $item->receiver = User::where('id', $item->receiver_id)->first(['id', 'name', 'email']);
                        return $item;
                    });

                    $settings = Setting::where('lender_id', 1)->get();

                    $unread_support = Support::where('lender_id', $lender_id)->where('top_query', 1)->where('status', 0)->where('lender_id', $lender_id)->count();

                    // all un approved transactions
                    $unapproved_transactions = Transaction::where('approved', '<', 1)->whereNull('description')->count();

                    // all liquidated loans
                    $unliquidated_loans = Loan::where('liquidated', 1)->where('liquidate_approve', 0)->count();

                    // all withdrawal requests
                    $withdrawals = Withdraw::where('approved', 0)->count();

                    // all loan cancel requests
                    $cancel_requests = Loan::where('cancel_request', -1)->count();

                    return response()->json(['user' => $user, 'token' => $token, 'expires_in' => auth()->factory()->getTTL() * 60, 'lender' => $lender, 'loan_details' => $details, 'slips' => $slips, 'mails' => $mails, 'banks' => $banks, 'lenders' => $lenders, 'roles' => $roles, 'settings' => $settings, 'unread_support' => $unread_support, 'unapproved_transactions' => $unapproved_transactions, 'unliquidated_loans' => $unliquidated_loans, 'withdrawals' => $withdrawals, 'cancel_requests' => $cancel_requests], 200);
                }
            }

            return response()->json(['error' => 'invalid credentials'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile() . ': ' . $e->getTraceAsString());
            return response()->json(['error' => 'invalid credentials'], 500);
        }
    }

    /**
     * API Merchant Login
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function signinMerchant(Request $request)
    {
        try {
            $input = $request->all();

            $user = User::where('username', $input['username'])->first();
            if ($user) {
                if (Hash::check($input['password'], $user->merchant_password)) {
                    $token = auth()->login($user);

                    $lender_id = $user->lender_id;
                    $lender = Lender::where('id', $lender_id)->first();

                    $user->lender = $lender;

                    if ($user->role == 'merchant' || $user->role == 'u-merchant') {
                        $merchant = Merchant::where('user_id', $user->id)->first();

                        if ($merchant) {
                            $office = Office::find($merchant->office_id);

                            $merchant->user = $user->name;
                            $merchant->office = ($office ? $office->name : null);
                            $merchant->count = MerchantPay::where('merchant_id', $merchant->id)->where('paid', 0)->count();

                            $user->merchant_code = $merchant->merchant_code;
                            $user->merchant = $merchant;

                            $loans = Loan::where('merchant_code', $merchant->merchant_code)->get(['id', 'user_id', 'amount', 'created_at']);
                            $loans = $loans->map(function ($item, $key) {
                                $item->user = User::where('id', $item->user_id)->first(['id', 'name', 'email']);
                                return $item;
                            });

                            $settings = Setting::where('lender_id', 1)->get();

                            return response()->json(['user' => $user, 'token' => $token, 'expires_in' => auth()->factory()->getTTL() * 60, 'loans' => $loans, 'settings' => $settings], 200);
                        }
                    }
                }
            }

            return response()->json(['error' => 'invalid credentials'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile() . ': ' . $e->getTraceAsString());
            return response()->json(['error' => 'invalid credentials'], 500);
        }
    }

    /**
     * API Verify
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verify(Request $request)
    {
        try {
            $method_type = $request->methodType ?? '';
            if ($method_type == '') {
                return response()->json(['error' => 'Network error, try again'], 500);
            }

            $data = $request->all();

            $lenders = Lender::get(['id', 'name']);

            if ($method_type == 'phone') {
                $user = User::where('phone', $data['phone'])->first();

                if ($user) {
                    if ($user->enabled == 0) {
                        return response()->json(['error' => 'your account has been disabled, please contact support'], 500);
                    }

                    $lender = Lender::where('id', $user->lender_id)->first();
                    $settings = Setting::where('lender_id', 1)->get();

                    $reset_password = DB::table('password_resets')->where('email', $user->email)->first();
                    $has_reset_password = $reset_password ? 1 : 0;

                    // login with password
                    return response()->json(['el' => 13, 'method_type' => $data['methodType'], 'phone' => $data['phone'], 'email' => $user->email, 'lender' => $lender, 'lenders' => $lenders, 'settings' => $settings, 'has_reset_password' => $has_reset_password], 200);
                }

                try {
                    $result = fetchRemitaHistoryPhone($data['phone'], true);
                    $lh = $result ? $result->data : null;

                    if ($lh != null && $lh->responseCode == '00') {
                        $customerId = $lh->data->customerId;

                        $remitaDatum = (object)null;
                        $remitaDatum->customer_id = $lh->data->customerId;
                        $remitaDatum->name = $lh->data->customerName;
                        $remitaDatum->bank_code = $lh->data->bankCode;
                        $remitaDatum->account_number = $lh->data->accountNumber;
                        $remitaDatum->salaries = $lh->data->salaryPaymentDetails;
                        $remitaDatum->auth_code = $result->auth_code;

                        // goto setup with remita
                        return response()->json(['el' => 12, 'method_type' => $data['methodType'], 'phone' => $data['phone'], 'lenders' => $lenders, 'customer_id' => $customerId, 'remitaDatum' => $remitaDatum], 200);
                    }
                } catch (\Exception $e) {
                    Log::debug($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                }

                // restart with account number
                return response()->json(['el' => 10, 'phone' => $data['phone'], 'message' => 'your account does not exist on our platform, please enter your salary account number to register.'], 200);
            } else if($method_type == 'account_number') {
                $bank_account = Accountno::where('account_number', $data['account_number'])->where('bank_code_id', $data['bank'])->where('status', 'primary')->first();
                if ($bank_account != null) {
                    $user = User::where('id', $bank_account->user_id)->first();

                    if ($user) {
                        if ($user->enabled == 0) {
                            return response()->json(['error' => 'your account has been disabled, please contact support'], 500);
                        }
    
                        $lender = Lender::where('id', $user->lender_id)->first();
                        $settings = Setting::where('lender_id', 1)->get();
    
                        $reset_password = DB::table('password_resets')->where('email', $user->email)->first();
                        $has_reset_password = $reset_password ? 1 : 0;
    
                        // login with password
                        return response()->json(['el' => 13, 'method_type' => $data['methodType'], 'account_number' => $data['account_number'], 'email' => $user->email, 'lender' => $lender, 'lenders' => $lenders, 'settings' => $settings, 'has_reset_password' => $has_reset_password], 200);
                    }
                }

                try {
                    $bank = BankCode::where('id', $data['bank'])->first();
                    $result = fetchRemitaHistoryAccount($data['account_number'], $bank->code, true);

                    $lh = $result ? $result->data : null;

                    if ($lh != null && $lh->responseCode == '00') {
                        $customerId = $lh->data->customerId;

                        $remitaDatum = (object)null;
                        $remitaDatum->customer_id = $lh->data->customerId;
                        $remitaDatum->name = $lh->data->customerName;
                        $remitaDatum->bank_code = $lh->data->bankCode;
                        $remitaDatum->account_number = $lh->data->accountNumber;
                        $remitaDatum->salaries = $lh->data->salaryPaymentDetails;
                        $remitaDatum->auth_code = $result->auth_code;

                        // goto setup with remita
                        return response()->json(['el' => 12, 'method_type' => $data['methodType'], 'phone' => $data['phone'], 'account_number' => $data['account_number'], 'lenders' => $lenders, 'customer_id' => $customerId, 'remitaDatum' => $remitaDatum], 200);
                    }
                } catch (\Exception $e) {
                    Log::debug($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                }
            }

            return response()->json(['error' => 'your '.str_replace('_', ' ', $method_type).' does not exist on our platform or remita, please contact support.'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Network error, please try again'], 500);
        }
    }

    /**
     * API Login
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        try {
            // login
            $credentials = $request->only('email', 'password');
            // error_log(json_encode($credentials));

            if (!$token = auth()->attempt($credentials)) {
                return response()->json(['error' => 'invalid credentials'], 500);
            }

            $user = User::where('email', $credentials['email'])->first();

            if ($user->verified == 0) {
                return response()->json(['error' => 'your account has not been verified, please check your email to verify your account', 'self' => 16, 'user_id' => $user->id, 'user_email' => $user->email], 200);
            }

            if ($user->enabled == 0) {
                return response()->json(['error' => 'your account has been disabled, please contact support'], 500);
            }

            if ($user->role == 'user' || $user->role == 'u-merchant') {
                $user = loadUserData($user);

                $settings = Setting::where('lender_id', 1)->get();

                return response()->json(['user' => $user, 'token' => $token, 'expires_in' => auth()->factory()->getTTL() * 60, 'self' => 15, 'settings' => $settings], 200);
            }

            return response()->json(['error' => 'invalid credentials'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Network error, please try again'], 500);
        }
    }

    /**
     * API Find User
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function findUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|numeric',
        ], [
            'phone.required' => 'Enter phone number or ippis'
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        $data = $request->all();

        $customerId = null;
        $remitaDatum = (object)null;

        try {
            $payslip = Payslip::where('phone', $data['phone'])->orWhere('ippis', $data['phone'])->orderBy('created_at', 'DESC')->first();

            $user = User::where('enabled', 1)->where(function ($query) use ($data) {
                $query->where('phone', $data['phone'])->orWhere('ippis', $data['phone']);
            })->first();

            if ($payslip == null && $user) {
                $payslip = Payslip::where('id', $user->payslip_id)->first();
            }

            if ($data['type'] == 'loan') {
                if ($user == null) {
                    return response()->json(['error' => 'User has not registered on this platform'], 500);
                }

                switch ($data['platform']) {
                    case 'ippis':
                        if (!$payslip) {
                            return response()->json(['error' => 'User account does not exist on ippis. It could be that he has an ippis number but his office payslip is not uploaded yet, try searching with remita'], 500);
                        }
                        break;
                    case 'sme':
                        if ($user->platform != 'sme') {
                            return response()->json(['error' => 'User account does not exist on sme, try searching with ippis'], 500);
                        }
                        break;
                    case 'remita':
                        try {
                            $result = fetchRemitaHistoryAccount($data['account_number'], $data['bank_code'], true, $user);

                            $lh = $result ? $result->data : null;

                            if ($lh != null && $lh->responseCode == '00') {
                                $customerId = $lh->data->customerId;

                                $remitaDatum->customer_id = $lh->data->customerId;
                                $remitaDatum->name = $lh->data->customerName;
                                $remitaDatum->bank_code = $lh->data->bankCode;
                                $remitaDatum->account_number = $lh->data->accountNumber;
                                $remitaDatum->salaries = $lh->data->salaryPaymentDetails;
                                $remitaDatum->auth_code = $result->auth_code;
                                $remitaDatum->remita_id = $result->remita_id;

                                $salaries = $lh->data->salaryPaymentDetails;
                                $customerId = $lh->data->customerId;
    
                                if ($customerId) {
                                    foreach ($salaries as $earning) {
                                        $d = explode('+', $earning->paymentDate);
    
                                        $month = Carbon::parse($d[0])->format('n');
                                        $year = Carbon::parse($d[0])->format('Y');
    
                                        $earn = Earning::where('customer_id', $customerId)->where('month', $month)->where('year', $year)->where('net_earning', $earning->amount)->where('platform', 'remita')->first();
    
                                        if (!$earn) {
                                            $earn = Earning::create([
                                                'customer_id' => $customerId,
                                                'month' => $month,
                                                'year' => $year,
                                                'net_earning' => $earning->amount,
                                                'platform' => 'remita',
                                                'auth_code' => $result->auth_code,
                                            ]);
                                        }
                                    }
                                }
                            }
                        } catch (\Exception $e) {
                            Log::debug($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                        }

                        if ($customerId == null) {
                            return response()->json(['error' => 'User account does not exist on remita'], 500);
                        }
                        break;
                }
            } else if ($data['type'] == 'new-user') {
                if ($user) {
                    return response()->json(['error' => 'User has is already registered'], 500);
                }

                try {
                    $result = fetchRemitaHistoryAccount($data['account_number'], $data['bank_code'], true, $user);

                    $lh = $result ? $result->data : null;

                    if ($lh != null && $lh->responseCode == '00') {
                        $customerId = $lh->data->customerId;

                        $remitaDatum->customer_id = $lh->data->customerId;
                        $remitaDatum->name = $lh->data->customerName;
                        $remitaDatum->bank_code = $lh->data->bankCode;
                        $remitaDatum->account_number = $lh->data->accountNumber;
                        $remitaDatum->salaries = $lh->data->salaryPaymentDetails;
                        $remitaDatum->auth_code = $result->auth_code;
                        $remitaDatum->remita_id = $result->remita_id;
                    } else {
                        try {
                            $result = fetchRemitaHistoryPhone($data['phone']);
        
                            $lh = $result ? $result->data : null;
        
                            if ($lh != null && $lh->responseCode == '00') {
                                $customerId = $lh->data->customerId;
        
                                $remitaDatum->customer_id = $lh->data->customerId;
                                $remitaDatum->name = $lh->data->customerName;
                                $remitaDatum->bank_code = $lh->data->bankCode;
                                $remitaDatum->account_number = $lh->data->accountNumber;
                                $remitaDatum->salaries = $lh->data->salaryPaymentDetails;
                                $remitaDatum->auth_code = $result->auth_code;
                                $remitaDatum->remita_id = $result->remita_id;
                            }
                        } catch (\Exception $e) {
                            Log::debug($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                        }
                    }
                } catch (\Exception $e) {
                    Log::debug($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                }

                if ($payslip == null && $customerId == null) {
                    return response()->json(['error' => 'User account does not exist on our platform and user is not remita'], 500);
                }
            }

            if ($payslip) {
                try {
                    $max_age = Setting::where('lender_id', 1)->where('name', 'max_age')->first();
                    $era = Carbon::parse($payslip->birth_date)->addYears($max_age->value)->format('Y-m-d');
                    $max_served = Setting::where('lender_id', 1)->where('name', 'max_served')->first();

                    if ($payslip->first_appointment) {
                        $ers = Carbon::parse($payslip->first_appointment)->addYears($max_served->value)->format('Y-m-d');

                        $dates = [$era, $ers];
                        $expected_date_of_retirement = min(array_map('strtotime', $dates));
                        $payslip->retire_expected_at = date('Y-m-d', $expected_date_of_retirement);
                    }

                    $payslip->save();
                } catch (\Exception $e) {
                }

                $payslip->lender = Lender::where('id', $payslip->lender_id)->first();
                $payslip->settings = Setting::where('lender_id', 1)->get();
            }

            $loan = null;
            $earnings = null;
            $bank_accounts = null;

            if ($user) {
                $user->lender = Lender::where('id', $user->lender_id)->first();
                $user->settings = Setting::where('lender_id', 1)->get();
                $user->office = Office::find($user->office_id);

                $loan = Loan::where('id', $user->loan_id)->first();
                if ($loan) {
                    $total_paid = PaymentDate::where('loan_id', $loan->id)->where('paid', 1)->count();
                    $should_have_paid = round($loan->tenure / 2);
                    $has_ended = $loan->disbursed == 1 ? hasLoanEnded($loan) : true;
                    $eligible = $total_paid >= $should_have_paid && $has_ended == false ? 1 : 0;

                    $loan->topup_eligible = $eligible;
                    $loan->has_topup = Loan::where('topup', $loan->id)->first();
                    $loan->total_paid = $total_paid;
                }

                $platform = $data['platform'];

                $earnings = $data['platform'] == 'ippis' ?
                    DB::table('earnings')
                        ->select(DB::raw('DISTINCT ippis, month, year, net_earning, platform, gross_earning, auth_code, created_at'))
                        ->where('ippis', $user->ippis)->where('platform', $platform)->where('net_earning', '!=', '0.00')
                        ->orderBy('year', 'desc')->orderBy('month', 'desc')
                        ->take(12)->get()
                    :
                    DB::table('earnings')
                        ->select(DB::raw('DISTINCT customer_id, month, year, net_earning, platform, gross_earning, auth_code, created_at'))
                        ->where('customer_id', $user->customer_id)->where('platform', $platform)->where('net_earning', '!=', '0.00')
                        ->orderBy('year', 'desc')->orderBy('month', 'desc')
                        ->take(12)->get();

                $bank_accounts = Accountno::where('user_id', $user->id)->get();
            }

            $lenders = Lender::get(['id', 'name']);

            $result = (object)null;
            $result->profile = $user;
            $result->payslip = $payslip;
            $result->lenders = $lenders;
            $result->loan = $loan;
            $result->earnings = $earnings;
            $result->bank_accounts = $bank_accounts;
            $result->has_remita = $customerId;
            $result->remitaDatum = $remitaDatum;
            $result->loan_platform = $data['platform'];
            $result->phone = $data['platform'] != 'ippis' ? $data['phone'] : null;
            $result->remita_id = $remitaDatum->remita_id;

            return response()->json(['result' => $result], 200);

        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Network error, please try again'], 500);
        }
    }

    /**
     * API Forgot Password send email
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function forgot(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        $input = $request->all();
        $source = isset($input['source']) ? $input['source'] : '';

        try {
            $platform = isset($input['platform']) ? $input['platform'] : '';

            $user = User::where('email', strtolower($input['email']))->first();

            if ($user && $user->enabled == 0) {
                return response()->json(['error' => 'your account has been disabled, please contact support'], 500);
            }

            if ($user && $user->verified == 0) {
                return response()->json(['error' => 'your account has not been verified, please check your email to verify your account', 'user_id' => $user->id, 'user_email' => $user->email], 200);
            }

            $role = $user && $user->role == 'u-merchant' ? 'u-merchant' : $input['role'];

            if (!$user || ($user && $user->role != $role)) {
                // Log::error('AuthController 600: user not found: ' . json_encode($input) . ' - ' . json_encode($user));
                return response()->json(['error' => 'user not found'], 500);
            }

            if ($user) {
                $activate = DB::table('password_resets')->where('email', $user->email)->first();

                $reset_token = $token = hash_hmac('sha256', Str::random(40), config('app.key'));
                $code = floor(random() * 1101233);

                if (!$activate) {
                    DB::table('password_resets')->insert([
                        'email' => $user->email,
                        'token' => $token,
                        'code' => $code,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                } else {
                    DB::table('password_resets')->where('email', $user->email)->update([
                        'token' => $reset_token,
                        'code' => $code,
                    ]);
                }

                $mail = (object)null;
                $mail->from_name = 'FastCash';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $user->name);
                $mail->to_email = $user->email;
                $mail->template = 'email.forgot_password';
                $mail->code = $code;
                $mail->platform = isset($input['__umtp']) && $input['__umtp'] != '' ? $input['__umtp'] : '';

                if (($user->role == 'user' || $user->role == 'u-merchant') && $platform == 'user') {
                    $mail->login = env('MAIN_URL');
                    $mail->reset_url = env('MAIN_URL') . '/reset-password/' . $reset_token;
                } else if ($user->role == 'admin' || $user->role == 'super') {
                    $mail->login = env('ADMIN_URL');
                    $mail->reset_url = env('ADMIN_URL') . '/reset-password/' . $reset_token;
                } else if (($user->role == 'merchant' || $user->role == 'u-merchant') && $platform == 'merchant') {
                    $mail->login = env('MERCHANT_URL');
                    $mail->reset_url = env('MERCHANT_URL') . '/reset-password/' . $reset_token;
                } else {
                    $mail->login = '';
                    $mail->reset_url = '';
                }

                $mail->subject = 'Reset Password';

                $send = (new MailHandler())->sendMail($mail);

                return response()->json(compact('user'), 200);
            }

            return response()->json(['error' => 'user not found'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile() . ' ' . $source);
            return response()->json(['error' => 'we could not send password recovery link please try again'], 500);
        }
    }

    /**
     * API Check Token
     *
     * @param string $token
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkToken($token)
    {
        try {
            $reset = DB::table('password_resets')->where('token', $token)->first();

            if ($reset) {
                return response()->json(compact('reset'), 200);
            }

            return response()->json(['error' => 'invalid token'], 422);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'network error please try again'], 422);
        }
    }

    /**
     * API Check Code
     *
     * @param string $code
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkCode($code)
    {
        try {
            $reset = DB::table('password_resets')->where('code', $code)->first(['code', 'email']);

            if ($reset) {
                return response()->json(compact('reset'), 200);
            }

            return response()->json(['error' => 'invalid token'], 422);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'network error please try again'], 422);
        }
    }

    /**
     * @param Request $request
     * @param string $token
     * @return \Illuminate\Http\JsonResponse
     */
    public function resetPswd(Request $request, $token)
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|confirmed',
            'password_confirmation' => 'required',
        ], [
            'password.confirmed' => 'your passwords does not match'
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 422);
        }

        try {
            $input = $request->all();
            $getPR = DB::table('password_resets')->where('email', $input['email'])->where(function ($query) use ($token) {
                $query->where('token', $token)->orWhere('code', $token);
            })->first();

            if ($getPR) {
                $usr = User::where('email', $getPR->email)->first(['id', 'email', 'phone']);

                if ($usr && ($usr->role == 'user' || $usr->role == 'u-merchant')) {
                    $usr->password = bcrypt($input['password_confirmation']);
                    $usr->save();

                    // login
                    $user = User::where('id', $usr->id)->first();
                    $auth_token = auth()->login($user);

                    DB::table('password_resets')->where('email', $input['email'])->where(function ($query) use ($token) {
                        $query->where('token', $token)->orWhere('code', $token);
                    })->delete();

                    $user = loadUserData($user);

                    $settings = Setting::where('lender_id', 1)->get();

                    return response()->json(['user' => $user, 'token' => $auth_token, 'expires_in' => auth()->factory()->getTTL() * 60, 'settings' => $settings], 200);
                }
            }

            return response()->json(['error' => 'user not found'], 422);

        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not change password'], 422);
        }
    }

    /**
     * API Admin Reset Password
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function adminResetPswd(Request $request, $token)
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|confirmed',
            'password_confirmation' => 'required',
        ], [
            'password.confirmed' => 'your passwords does not match'
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 422);
        }

        try {
            $input = $request->all();
            $getPR = DB::table('password_resets')->where('token', $token)->first();
            $usr = User::where('email', $getPR->email)->first(['id', 'email', 'phone']);

            if ($usr && ($usr->role == 'admin' || $usr->role == 'super')) {
                $usr->password = bcrypt($input['password_confirmation']);
                $usr->save();

                DB::table('password_resets')->where('token', $token)->orWhere('email', $usr->email)->delete();

                // login
                $lender_id = $usr->lender_id;
                $lender = Lender::where('id', $lender_id)->first();

                $user = User::where('id', $usr->id)->first();
                $user->lender = $lender;

                $auth_token = auth()->login($user);

                $banks = BankCode::whereRaw('id = (SELECT MIN(id) FROM bank_codes t2 WHERE bank_codes.code = t2.code)')->orderBy('name')->get(['id', 'name', 'code']);

                $lenders = Lender::get(['id', 'name']);

                $roles = Role::where('name', '<>', 'user')->where('name', '<>', 'super')->where('name', '<>', 'merchant')->get();

                $details = getFinancialOverview($lender_id, $lender_id);

                $slips = PayslipUpload::where('lender_id', $lender_id)->orderBy('year', 'desc')->orderBy('month', 'desc')->orderBy('created_at', 'desc')->get();

                $mails = SupportMail::where('user_id', $user->id)->where('lender_id', $lender_id)->orderBy('created_at', 'desc')->get();
                $mails = $mails->map(function ($item, $key) {
                    $item->user = User::where('id', $item->user_id)->first(['id', 'name', 'email']);
                    $item->receiver = User::where('id', $item->receiver_id)->first(['id', 'name', 'email']);
                    return $item;
                });

                $settings = Setting::where('lender_id', 1)->get();

                $unread_support = Support::where('lender_id', $lender_id)->where('top_query', 1)->where('status', 0)->count();

                // all un approved transactions
                $unapproved_transactions = Transaction::where('approved', '<', 1)->whereNull('description')->count();

                // all liquidated loans
                $unliquidated_loans = Loan::where('liquidated', 1)->where('liquidate_approve', 0)->count();

                // all withdrawal requests
                $withdrawals = Withdraw::where('approved', 0)->count();

                return response()->json(['user' => $user, 'token' => $auth_token, 'expires_in' => auth()->factory()->getTTL() * 60, 'loan_details' => $details, 'slips' => $slips, 'mails' => $mails, 'banks' => $banks, 'lender' => $lender, 'lenders' => $lenders, 'roles' => $roles, 'settings' => $settings, 'unread_support' => $unread_support, 'unapproved_transactions' => $unapproved_transactions, 'unliquidated_loans' => $unliquidated_loans, 'withdrawals' => $withdrawals], 200);
            }

            return response()->json(['error' => 'user not found'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'invalid credentials'], 500);
        }
    }

    /**
     * API Merchant Reset Password
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function merchantResetPswd(Request $request, $token)
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|confirmed',
            'password_confirmation' => 'required',
        ], [
            'password.confirmed' => 'your passwords does not match'
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 422);
        }

        try {
            $input = $request->all();
            $getPR = DB::table('password_resets')->where('token', $token)->first();
            $usr = User::where('email', $getPR->email)->first(['id', 'email', 'phone']);

            if ($usr && ($usr->role == 'merchant' || $usr->role == 'u-merchant')) {
                $usr->merchant_password = bcrypt($input['password_confirmation']);
                $usr->save();

                DB::table('password_resets')->where('token', $token)->orWhere('email', $usr->email)->delete();

                // login
                $user = User::where('id', $usr->id)->first();
                $user->lender = Lender::where('id', $user->lender_id)->first();

                $auth_token = auth()->login($user);

                $merchant = Merchant::where('user_id', $user->id)->first();
                if ($merchant) {
                    $office = Office::find($merchant->office_id);

                    $merchant->user = $user->name;
                    $merchant->office = ($office ? $office->name : null);
                    $merchant->count = MerchantPay::where('merchant_id', $merchant->id)->where('paid', 0)->count();

                    $user->merchant_code = $merchant->merchant_code;
                    $user->merchant = $merchant;

                    $loans = Loan::where('merchant_code', $merchant->merchant_code)->get(['id', 'user_id', 'amount', 'created_at']);
                    $loans = $loans->map(function ($item, $key) {
                        $item->user = User::where('id', $item->user_id)->first(['id', 'name', 'email']);
                        return $item;
                    });

                    $settings = Setting::where('lender_id', 1)->get();

                    return response()->json(['user' => $user, 'token' => $auth_token, 'expires_in' => auth()->factory()->getTTL() * 60, 'loans' => $loans, 'settings' => $settings], 200);
                }
            }

            return response()->json(['error' => 'user not found'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'invalid credentials'], 500);
        }
    }

    /**
     * API Refresh
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function refresh()
    {
        try {
            $token = auth()->refresh();
            return response()->json(compact('token'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'invalid token'], 500);
        }
    }

    /**
     * API User
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function user(Request $request)
    {
        $user = auth()->user();
        return response()->json($user, 200);
    }

    /**
     * API SignUp
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function signUp(Request $request)
    {
        $isAdmin = $request->has('focus') && $request->has('focus') == 'admin';

        if ($isAdmin) {
            $validator = Validator::make($request->all(), [
                'phone' => 'required|digits:11|unique:users',
                'bvn' => 'required|digits:11',
                'gender' => 'required',
                'lender_id' => 'required',
                'employer' => 'required',
            ]);
        } else {
            $validator = Validator::make($request->all(), [
                'lga' => 'required',
                'state' => 'required',
                'phone' => 'required|digits:11|unique:users',
                'bvn' => 'required|digits:11',
                'gender' => 'required',
                'password' => 'required|string|min:6|confirmed',
                'address1' => 'required',
                'nok_firstname' => 'required',
                'nok_lastname' => 'required',
                'nok_relationship' => 'required',
                'nok_phone' => 'required',
                'nok_address1' => 'required',
                'lender_id' => 'required',
                'employer' => 'required',
            ]);
        }

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        DB::beginTransaction();
        try {
            $input = $request->all();

            if (isset($input['isAdminCreated']) && $input['isAdminCreated'] == 0 && $input['email'] == '') {
                return response()->json(['error' => 'enter email address.'], 500);
            }

            $email = User::where('email', $input['email'])->first();
            if ($email && (!isset($input['isAdminCreated']) || (isset($input['isAdminCreated']) && $input['isAdminCreated'] == 0))) {
                return response()->json(['error' => 'Could not create user account, your email already exists.'], 500);
            }

            $hasBvn = User::where('bvn', $input['bvn'])->first();
            if ($hasBvn) {
                return response()->json(['error' => 'Could not create user account, your bvn already exists.'], 500);
            }

            $checkphone = User::where('phone', $input['phone'])->first();
            if ($checkphone) {
                return response()->json(['error' => 'Your phone number already exists.'], 500);
            }

            $payslip = isset($input['id']) ? Payslip::where('id', $input['id'])->first() : null;

            if ($input['platform'] == 'remita') {
                $office = Office::where('lender_id', $input['lender_id'])->where('name', 'Remita')->first();
            } else if ($input['platform'] == 'sme') {
                $office = Office::where('lender_id', $input['lender_id'])->where('name', 'SME')->first();
            } else {
                if ($payslip) {
                    $office = Office::where('name', 'LIKE', '%' . $payslip->employer . '%')->first();
                } else {
                    $office = Office::where('name', 'LIKE', '%' . $input['employer'] . '%')->first();
                    if (!$office) {
                        $office = Office::create([
                            'name' => $input['employer'],
                            'lender_id' => $input['lender_id'],
                        ]);
                    }
                }
            }

            $tkn_hash = hash_hmac('sha256', Str::random(40), config('app.key'));

            $fullname = $input['name'];

            $_user = User::create([
                'password' => bcrypt($input['password']),
                'name' => str_replace(',', '', $fullname),
                'email' => isset($input['email']) && $input['email'] != '' ? $input['email'] : null,
                'phone' => $input['phone'],
                'bvn' => $input['bvn'],
                'state_of_origin' => isset($input['state']) ? $input['state'] : null,
                'lga_of_origin' => isset($input['lga']) ? $input['lga'] : null,
                'address1' => isset($input['address1']) ? $input['address1'] : null,
                'address2' => (isset($input['address2']) ? $input['address2'] : null),
                'nok_firstname' => isset($input['nok_firstname']) ? $input['nok_firstname'] : null,
                'nok_lastname' => isset($input['nok_lastname']) ? $input['nok_lastname'] : null,
                'nok_relationship' => isset($input['nok_relationship']) ? $input['nok_relationship'] : null,
                'nok_phone' => isset($input['nok_phone']) ? $input['nok_phone'] : null,
                'nok_address1' => isset($input['nok_address1']) ? $input['nok_address1'] : null,
                'nok_address2' => (isset($input['nok_address2']) ? $input['nok_address2'] : null),
                'payslip_id' => ($payslip ? $payslip->id : null),
                'office_id' => ($office ? $office->id : null),
                'platform' => $input['platform'],
                'gender' => $input['gender'],
                'customer_id' => $input['customer_id'],
                'lender_id' => $input['lender_id'],
                'employer' => $input['employer'],
                'is_admin_created' => isset($input['isAdminCreated']) ? $input['isAdminCreated'] : 0,
                'email_token' => $input['verified'] == 1 ? null : $tkn_hash,
                'verified' => $input['verified'],
            ]);
            $_user->roles()->attach(Role::where('name', 'user')->first());

            $user = User::with('loan', 'office')->where('id', $_user->id)->first();

            if ($input['platform'] == 'remita') {
                $bank_code = $input['bank_code'];
                $bank = BankCode::where('code', $bank_code)->first();

                $account_number = $input['account_number'];
                $bank_name = $bank ? $bank->name : null;
                $bank_id = $bank ? $bank->id : null;

                createbankAccount($user->id, $account_number, $bank_name, $bank_code, 1, $input['phone'], $bank_id, $input['bvn']);
            } else {
                $bankname = str_replace('- ', '', $payslip->salary_bank);
                $bank = BankCode::where('name', $bankname)->first();
                info('bank name: ' . $bankname . ', bank: ' . json_encode($bank));

                $bank_code = $bank ? $bank->code : null;
                $bank_id = $bank ? $bank->id : null;

                createbankAccount($user->id, $payslip->salary_account_number, $payslip->salary_bank, $bank_code, 1, $input['phone'], $bank_id, $input['bvn']);
            }

            $notify = doNotify($user->id, 'users', $user->id, 'user_create', 'success', null, $user->lender_id, null);

            DB::commit();

            if (!$isAdmin) {
                try {
                    $mail = (object)null;
                    $mail->from_name = 'FastCash';
                    $mail->from_email = '<EMAIL>';
                    $mail->to_name = str_replace(',', '', $user->name);
                    $mail->to_email = $user->email;
                    $mail->login = env('MAIN_URL');
                    $mail->platform = $user->platform;

                    if ($user->verified == 0) {
                        $mail->template = 'email.verify_account';
                        $mail->subject = 'Verify Your Email';
                        $mail->activation_url = env('MAIN_URL') . '/activate/' . $user->email_token;
                    } else {
                        $mail->template = 'email.new_customer';
                        $mail->subject = 'Welcome to FastCash';
                        $mail->ippis = $user->ippis ?? '';
                        $mail->phone = $user->phone;
                    }

                    $send = (new MailHandler())->sendMail($mail);
                } catch (\Exception $e) {
                    Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                }
            }

            $user->user_id = $user->id;
            $user->user_email = $user->email;

            return response()->json(['user' => $user], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'Could not create user or your data was not found on our platform'], 500);
        }
    }

    /**
     * API Verify Email
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyEmail($token)
    {
        try {
            $user = User::where('email_token', $token)->first();
            if ($user && ($user->role == 'user' || $user->role == 'u-merchant')) {
                $user->verified = 1;
                $user->email_token = null;
                $user->email_code = null;
                $user->save();

                $auth_token = auth()->login($user);

                try {
                    $mail = (object)null;
                    $mail->from_name = 'FastCash';
                    $mail->from_email = '<EMAIL>';
                    $mail->to_name = str_replace(',', '', $user->name);
                    $mail->to_email = $user->email;
                    $mail->template = 'email.new_customer';
                    $mail->subject = 'Welcome to FastCash';
                    $mail->login = env('MAIN_URL');
                    $mail->platform = $user->platform;
                    $mail->ippis = $user->ippis ?? '';
                    $mail->phone = $user->phone;

                    $send = (new MailHandler())->sendMail($mail);
                } catch (\Exception $e) {
                    Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                }

                return response()->json(['token' => $auth_token, 'expires_in' => auth()->factory()->getTTL() * 60], 200);
            }

            return response()->json(['error' => 'invalid activation token'], 422);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'invalid activation token'], 422);
        }
    }

    /**
     * API Verify Email Code
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyEmailCode(Request $request)
    {
        try {
            $input = $request->all();

            $user = User::where('email_code', $input['code'])->first();
            if ($user && ($user->role == 'user' || $user->role == 'u-merchant')) {
                $user->verified = 1;
                $user->email_token = null;
                $user->email_code = null;
                $user->save();

                $auth_token = auth()->login($user);

                try {
                    $mail = (object)null;
                    $mail->from_name = 'FastCash';
                    $mail->from_email = '<EMAIL>';
                    $mail->to_name = str_replace(',', '', $user->name);
                    $mail->to_email = $user->email;
                    $mail->template = 'email.new_customer';
                    $mail->subject = 'Welcome to FastCash';
                    $mail->login = env('MAIN_URL');
                    $mail->platform = $user->platform;
                    $mail->ippis = $user->ippis ?? '';
                    $mail->phone = $user->phone;

                    $send = (new MailHandler())->sendMail($mail);
                } catch (\Exception $e) {
                    Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                }

                $user = loadUserData($user);

                $settings = Setting::where('lender_id', 1)->get();

                return response()->json(['user' => $user, 'token' => $auth_token, 'expires_in' => auth()->factory()->getTTL() * 60, 'settings' => $settings], 200);
            }

            return response()->json(['error' => 'invalid activation code'], 422);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'invalid activation code'], 422);
        }
    }

    /**
     * API Resend Email
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function resendEmail(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
            ]);

            if ($validator->fails()) {
                $error = validatorMessage($validator->errors());
                return response()->json(['error' => $error], 422);
            }

            $input = $request->only('email', 'user_id', '__umtp');

            $user = User::where('id', $input['user_id'])->where('email', $input['email'])->first(['name', 'email', 'platform', 'email_token', 'verified', 'id']);

            $email_code = hash_hmac('sha256', Str::random(40), config('app.key'));
            $code = floor(random() * 1101233);

            if ($user) {
                if ($user->verified == 0) {
                    $user->email_token = $email_code;
                    $user->email_code = $code;
                    $user->save();

                    $mail = (object)null;
                    $mail->from_name = 'FastCash';
                    $mail->from_email = '<EMAIL>';
                    $mail->to_name = str_replace(',', '', $user->name);
                    $mail->to_email = $user->email;
                    $mail->template = 'email.verify_account';
                    $mail->subject = 'Verify Your Email';
                    $mail->login = env('MAIN_URL');
                    $mail->platform = $user->platform;
                    $mail->activation_url = env('MAIN_URL') . '/activate/' . $user->email_token;
                    $mail->code = $code;
                    $mail->platform = isset($input['__umtp']) && $input['__umtp'] != '' ? $input['__umtp'] : '';

                    $send = (new MailHandler())->sendMail($mail);

                    return response()->json(compact('user'), 200);
                }

                return response()->json(['error' => 'your account has been verified'], 422);
            }

            return response()->json(['error' => 'user not found'], 422);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'network error please try again'], 422);
        }
    }

    /**
     * API Change Email
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function changeEmail(Request $request)
    {
        DB::beginTransaction();
        try {
            $validator = Validator::make($request->all(), [
                'email' => 'required|email',
            ]);

            if ($validator->fails()) {
                $error = validatorMessage($validator->errors());
                return response()->json(['error' => $error], 422);
            }

            $input = $request->only('email', 'user_id', 'user_email');

            $user = User::where('id', $input['user_id'])->where('email', $input['user_email'])->first(['name', 'email', 'platform', 'email_token', 'verified', 'id']);

            // check new email
            $check = User::where('email', $input['email'])->first(['id']);

            if ($user) {
                if ($user->verified == 0) {
                    if ($user->email == $input['email'] || $check != null) {
                        return response()->json(['error' => 'email has been used, pick another email'], 422);
                    }

                    if (!$user->email_token) {
                        $tkn = hash_hmac('sha256', Str::random(40), config('app.key'));
                        $user->email_token = $tkn;
                    }

                    $user->email = $input['email'];
                    $user->save();

                    $mail = (object)null;
                    $mail->from_name = 'FastCash';
                    $mail->from_email = '<EMAIL>';
                    $mail->to_name = str_replace(',', '', $user->name);
                    $mail->to_email = $user->email;
                    $mail->template = 'email.verify_account';
                    $mail->subject = 'Verify Your Email';
                    $mail->login = env('MAIN_URL');
                    $mail->platform = $user->platform;
                    $mail->activation_url = env('MAIN_URL') . '/activate/' . $user->email_token;

                    $send = (new MailHandler())->sendMail($mail);

                    DB::commit();

                    return response()->json(compact('user'), 200);
                }

                DB::rollBack();
                return response()->json(['error' => 'your account has been verified'], 422);
            }

            DB::rollBack();
            return response()->json(['error' => 'user not found'], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'network error email not sent'], 422);
        }
    }

    /**
     * API Logout
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout()
    {
        auth()->logout();

        return response()->json(['auth' => true], 200);
    }
}
