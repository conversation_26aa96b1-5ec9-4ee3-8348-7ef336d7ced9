/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Fragment } from 'react';
import moment from 'moment';
import Tooltip from 'antd/lib/tooltip';
import numeral from 'numeral';
import Popover from 'antd/lib/popover';
import { Link, withRouter } from 'react-router-dom';

import loading from '../assets/img/loading.gif';
import { rootURL, currency, superLenderID } from '../config/constants';
import EditLoanInsurance from './EditLoanInsurance';

const FundLoan = ({ doHide, startFundLoan, loan, amount, onChangeAmount, funding }) => {
	return (
		<div className="element-box p-3 m-0">
			<form onSubmit={(e) => startFundLoan(e, loan, amount)}>
				<div className="row">
					<div className="col-sm-12">
						<div className="form-group">
							<label htmlFor="amount">Amount</label>
							<div className="input-group">
								<input name="amount" id="amount" placeholder="Amount" type="number" className="form-control" onChange={(e) => onChangeAmount(e)} value={amount} />
								<div className="input-group-append">
									<div className="input-group-text">₦</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div className="text-center">
					<button className="mr-2 btn btn-primary" disabled={funding} type="submit">
						{funding ? <img src={loading} alt=""/> : 'Fund'}
					</button>
					<button className="btn btn-default" type="button" onClick={doHide}>Cancel</button>
				</div>
			</form>
		</div>
	);
};

const LoanRepayments = ({ doHide, repayments }) => {
	return (
		<div className="element-box p-3 m-0">
			<button className="close no-outline" type="button" onClick={doHide}>
				<span className="os-icon os-icon-close"/>
			</button>
			<div className="table-responsive">
				<table className="table table-striped">
					<thead>
						<tr>
							<th>Description</th>
							<th>Amount</th>
							<th>Oust. Amount</th>
							<th>Interest Earned</th>
							<th>Date</th>
						</tr>
					</thead>
					<tbody>
						{repayments.map((item, i) => {
							return (
								<tr key={i}>
									<td>no repayments</td>
									<td>no repayments</td>
									<td>no repayments</td>
									<td>no repayments</td>
								</tr>
							)
						})}
						{repayments.length === 0 && (
							<tr>
								<td colSpan="4">no repayments</td>
							</tr>
						)}
					</tbody>
				</table>
			</div>
		</div>
	);
};

const Funders = ({ doHide, funders }) => {
	const totalFunds = funders.reduce((total, fund) => parseFloat(fund.amount) + total, 0);
	return (
		<div className="element-box p-3 m-0">
			<button className="close no-outline" type="button" onClick={doHide}>
				<span className="os-icon os-icon-close"/>
			</button>
			<div className="table-responsive">
				<table className="table table-striped">
					<thead>
						<tr>
							<th>Lender</th>
							<th>Amount</th>
							<th>Date</th>
						</tr>
					</thead>
					<tbody>
						{funders.map((item, i) => {
							return (
								<tr key={i}>
									<td>{item.lender.name}</td>
									<td>{currency(item.amount)}</td>
									<td>{moment(item.created_at, 'YYYY-MM-DD HH:mm:ss').format('D.MMM.YYYY')}</td>
								</tr>
							)
						})}
						{funders.length > 0 && (
							<tr>
								<td/>
								<td>{currency(totalFunds)}</td>
								<td/>
							</tr>
						)}
						{funders.length === 0 && (
							<tr>
								<td colSpan="4">no funds</td>
							</tr>
						)}
					</tbody>
				</table>
			</div>
		</div>
	);
};

const LoanData = ({ location, loan, showUserProfile, user, doVerifyLoan, doApproveLoan, openModal, category, verifyPayment, disburse, bypass, startFundLoan, doMultiFund, doHide, visible, editVisible, showFundLoan, showEditLoan, loanID, amount, onChangeAmount, funding, stopFundLoan, visibleRepays, showRepayments, showFunders, visibleFunders, showLender, liquidateLoan, sendConsentSMS, doSendMail, onSaveLoanInsurance }) => {
	const disbursed = loan && loan.disbursement ? JSON.parse(loan.disbursement) : null;
	const paymentStatus = loan && loan.payment_status ? JSON.parse(loan.payment_status) : null;
	const cid = loan.user.platform === 'sme' ? `Phone: ${loan.user.phone}` : (loan.user.ippis && !isNaN(loan.user.ippis) ? `IPPIS: ${loan.user.ippis}` : `CID: ${loan.user.customer_id}`);
	const width = loan.funds ? loan.funds.reduce((total, fund) => {
		return parseFloat(fund.percentage) + total;
	}, 0) : 0;
	const myFund = loan.funds ? loan.funds.find(f => f.lender_id === user.lender_id) : null;
	const _search = location.search.replace('?', '');
	const url = encodeURIComponent(`${location.pathname}${_search !== '' ? `?${_search}` : ''}`);

	return loan && (
		<Fragment>
			<tr>
				<td>
					{loan.children && loan.children.length > 0 && (
						<i className="os-icon os-icon-plus-square"/>
					)}
				</td>
				{showLender && <td>{loan.lender.name}</td>}
				<td>{loan.user.name}</td>
				<td>
					{loan.is_multifund === 0 || (loan.is_multifund === 1 && loan.cancel_multifund === 1) ? (
						<a onClick={() => showUserProfile(loan.user.id)} className="cursor link">{cid}</a>
					) : (
						user.role_name === 'super' ? <a onClick={() => showUserProfile(loan.user.id)} className="cursor link">{cid}</a> : cid
					)}
					<br/>
					<span className="smaller bold">{`${loan.platform.toUpperCase()}`}</span>
				</td>
				{(category !== 'disburse' || category === 'active') && <td>{currency(loan.amount)}</td>}
				{category !== 'multi_fund' && (
					<td nowrap="nowrap">
						{loan ? (
							(loan.is_multifund === 0 || (loan.is_multifund === 1 && loan.cancel_multifund === 1)) ? (
								loan.approved === 1 ? (
									<span>
										<small>{moment(loan.approved_at).format('D.MMM.YYYY')}</small>
										<br />
										<span className="elabel elabel-success">Approved</span>
									</span>
								) : (
									loan.verified === 0 ? (
										(user.role_name === 'admin-v' || user.role_name === 'super' || user.role_name === 'super-l') && loan.lender_id === user.lender_id ? (
											<button
												className="btn btn-sm btn-outline-danger cursor"
												onClick={() => doVerifyLoan(loan.id, user.id)}>
												Verify
											</button>
										) : (
											<span className="elabel elabel-danger">awaiting verification</span>
										)
									) : (
										(user.role_name === 'admin-a' || user.role_name === 'super' || user.role_name === 'super-l') && loan.lender_id === user.lender_id ? (
											!loan.has_approved && <button
												className="btn btn-sm btn-outline-primary cursor"
												onClick={() => doApproveLoan(loan, user.id)}>
												Approve
											</button>
										) : (
											<span className="elabel elabel-danger">awaiting approval</span>
										)
									)
								)
							) : '-'
						) : (
							<span className="elabel elabel-info">No Loan</span>
						)}
					</td>
				)}
				{category === 'multi_fund' && (
					<td nowrap="nowrap">
						<div className="os-progress-bar info mb-1">
							<div className="bar-labels">
								<div className="bar-label-left">
									<span>{width < 100 ? 'Fund Progress' : 'Fully Funded'}</span>
								</div>
								{user.role_name === 'super' ? (
									<div className="bar-label-right">
										<Popover
											title=""
											content={
												<Funders 
													doHide={doHide}
													funders={loan.funds || []}
												/>
											}
											trigger="click"
											visible={visibleFunders && loanID === loan.id}
											onVisibleChange={(status) => showFunders(status, loan)}
										>
											<span className="info cursor">{`${numeral(width).format('0.0')}%`}</span>
										</Popover>
									</div>
								) : (
									<div className="bar-label-right">
										<span className="info">{`${numeral(width).format('0.0')}%`}</span>
									</div>
								)}
							</div>
							<div className="bar-level-1" style={{ width: '100%' }}>
								<div className="bar-level-2 bg-info" style={{ width: `${width}%` }}/>
							</div>
						</div>
						{width < 100 && (
							<Popover
								title=""
								content={
									<FundLoan
										loan={loan}
										amount={amount}
										doHide={doHide}
										funding={funding}
										startFundLoan={startFundLoan}
										onChangeAmount={onChangeAmount}
									/>
								}
								trigger="click"
								visible={visible && loanID === loan.id}
								onVisibleChange={(status) => showFundLoan(status, loan)}
							>
								<span className="elabel elabel-primary cursor">Fund Loan</span>
							</Popover>
						)}
						{user.role_name === 'super' ? (
							loan.fully_funded === 1 && (
								loan.approved === 0 ? (
									!loan.has_approved && <span onClick={() => doApproveLoan(loan, user.id)} className="elabel elabel-success cursor ml-1">Approve</span>
								) : (
									<Fragment>
										{disbursed ? (
											disbursed.status === 'success' ? (
												paymentStatus ? (
													paymentStatus.status === 'success' ? (
														<span>
															{loan && loan.disbursed_at && (
																<small>{moment(loan.disbursed_at).format('D.MMM.YYYY')}</small>
															)}
															{loan && loan.disbursed_at && <br />}
															<span className="elabel elabel-success">Disbursed</span>
														</span>
													) : (
														<span>
															{parseFloat(loan.disburse_amount) > 0 ? (
																<small>{currency(loan.disburse_amount)}</small>
															) : (
																<small>{currency(loan.amount)}</small>
															)}
															<br />
															<span
																className="elabel elabel-danger cursor"
																onClick={() => verifyPayment(loan)}>
																Failed? Retry
															</span>
														</span>
													)
												) : (
													<span>
														{parseFloat(loan.disburse_amount) > 0 ? (
															<small>{currency(loan.disburse_amount)}</small>
														) : (
															<small>{currency(loan.amount)}</small>
														)}
														<br />
														<span
															className="elabel elabel-warning cursor"
															onClick={() => verifyPayment(loan)}>
															Pending
														</span>
													</span>
												)
											) : (
												<span>
													{parseFloat(loan.disburse_amount) > 0 ? (
														<small>{currency(loan.disburse_amount)}</small>
													) : (
														<small>{currency(loan.amount)}</small>
													)}
													<br />
													<span onClick={() => disburse(loan)} className="elabel elabel-danger cursor">
														Failed? Retry
													</span>
												</span>
											)
										) : (
											<span>
												{parseFloat(loan.disburse_amount) > 0 ? (
													<small>{currency(loan.disburse_amount)}</small>
												) : (
													<small>{currency(loan.amount)}</small>
												)}
												<br />
												<span onClick={() => disburse(loan)} className="elabel elabel-info cursor">Disburse</span>
											</span>
										)}{' '}
										<input
											type="checkbox"
											onChange={() => bypass(loan.id)}
											disabled={loan.disbursed === 1}
											checked={loan.disbursed === 1}
										/>
									</Fragment>
								)
							)
						) : (
							loan.fully_funded === 1 && loan.approved === 1 && disbursed && disbursed.status === 'success' && paymentStatus && paymentStatus.status === 'success' && (
								<span>
									{loan && loan.disbursed_at && (
										<small>{moment(loan.disbursed_at).format('D.MMM.YYYY')}</small>
									)}
									{loan && loan.disbursed_at && <br />}
									<span className="elabel elabel-success">Disbursed</span>
								</span>
							)
						)}
						{user.role_name === 'super' && loan.approved === 0 && (
							<span onClick={doMultiFund(loan, 0)} className="elabel elabel-danger cursor ml-1">Cancel</span>
						)}
					</td>
				)}
				{category === 'multi_fund' && (
					<td className="text-center" nowrap="nowrap">
						{myFund ? (
								<Fragment>
									{currency(myFund.amount)}{' '}
									{loan.approved === 0 && (
										<Tooltip title="Remove Fund">
											<span className="elabel elabel-danger cursor" style={{ marginLeft: '2px' }} onClick={stopFundLoan(myFund.id)}><i className="fa fa-trash" /></span>
										</Tooltip>
									)}
								</Fragment>
						) : '-'}
					</td>
				)}
				{category === 'multi_fund' && (
					<td>
						{myFund && loan.approved === 1 ? (
							<Popover
								title=""
								content={
									<LoanRepayments
										doHide={doHide}
										repayments={myFund ? myFund.repayments : []}
									/>
								}
								trigger="click"
								visible={visibleRepays && loanID === loan.id}
								onVisibleChange={(status) => showRepayments(status, loan)}
							>
								<span className="elabel elabel-info cursor">Repayment</span>
							</Popover>
						) : '-'}
					</td>
				)}
				{category === 'default' && (
					<td nowrap="nowrap">{loan && loan.start_date ? moment(loan.start_date).format('D.MMM.YYYY') : '-'}</td>
				)}
				{category === 'default' && (
					<td nowrap="nowrap">{loan && loan.end_date ? moment(loan.end_date).format('D.MMM.YYYY') : '-'}</td>
				)}
				{(category === 'disburse' || category === 'active') && (
					user.lender_id === superLenderID && user.role_name === 'super' ? (
						<td nowrap="nowrap">
							<div>
								{loan && loan.approved === 1 ? (
									disbursed ? (
										disbursed.status === 'success' ? (
											paymentStatus ? (
												paymentStatus.status === 'success' ? (
													<span>
														{loan && loan.disbursed_at && (
															<small>{moment(loan.disbursed_at).format('D.MMM.YYYY')}</small>
														)}
														{loan && loan.disbursed_at && <br />}
														<span className="elabel elabel-success">Disbursed</span>
													</span>
												) : (
													<span>
														{parseFloat(loan.disburse_amount) > 0 ? (
															<small>{currency(loan.disburse_amount)}</small>
														) : (
															<small>{currency(loan.amount)}</small>
														)}
														<br />
														<span className="elabel elabel-danger cursor" onClick={() => verifyPayment(loan)}>Failed? Retry</span>
													</span>
												)
											) : (
												<span>
													{parseFloat(loan.disburse_amount) > 0 ? (
														<small>{currency(loan.disburse_amount)}</small>
													) : (
														<small>{currency(loan.amount)}</small>
													)}
													<br />
													<span className="elabel elabel-warning cursor" onClick={() => verifyPayment(loan)}>Pending</span>
												</span>
											)
										) : (
											<span>
												{parseFloat(loan.disburse_amount) > 0 ? (
													<small>{currency(loan.disburse_amount)}</small>
												) : (
													<small>{currency(loan.amount)}</small>
												)}
												<br />
												<span onClick={() => disburse(loan)} className="elabel elabel-danger cursor">Failed? Retry</span>
											</span>
										)
									) : (
										<span>
											{parseFloat(loan.disburse_amount) > 0 ? (
												<small>{currency(loan.disburse_amount)}</small>
											) : (
												<small>{currency(loan.amount)}</small>
											)}
											<br />
											<span onClick={() => disburse(loan)} className="elabel elabel-info cursor">Disburse</span>
										</span>
									)
								) : (
									'-'
								)}{' '}
								{loan && loan.approved === 1 && (
									<input
										type="checkbox"
										onChange={() => bypass(loan.id)}
										disabled={loan.disbursed === 1}
										checked={loan.disbursed === 1}
									/>
								)}
							</div>
						</td>
					) : (
						<td nowrap="nowrap">
							{loan.approved === 0 ? (
								'-'
							) : (
								loan.disbursed === 1 ? (
									<span>
										<small>{moment(loan.approved_at).format('D.MMM.YYYY')}</small>
										<br />
										<span className="elabel elabel-success">Loan Disbursed</span>
									</span>
								): (
									<span>
										{parseFloat(loan.disburse_amount) > 0 ? (
											<small>{currency(loan.disburse_amount)}</small>
										) : (
											<small>{currency(loan.amount)}</small>
										)}
										<br />
										<span className="elabel elabel-danger">Awaiting Disbursement</span>
									</span>
								)
							)}
						</td>
					)
				)}
				{category === 'approve' && user.role_name === 'super' && (
					loan.lender_id === user.lender_id ? (
						<td>
							{loan.is_multifund === 0 || (loan.is_multifund === 1 && loan.cancel_multifund === 1) ? (
								<span onClick={doMultiFund(loan, 1)} className="elabel elabel-info cursor">Allow Multi Fund</span>
							) : (
								<span onClick={doMultiFund(loan, 0)} className="elabel elabel-danger cursor">Cancel Multi-Fund</span>
							)}
						</td>
					) : (
						<td>-</td>
					)
				)}
				<td nowrap="nowrap">{loan.tenure ? `${loan.tenure}month${loan.tenure > 1 ? 's' : ''}` : '-'}</td>
				<td nowrap="nowrap">
				{loan && !(!loan.consent_otp && !loan.has_consented) && <>
						{loan.has_consented === 1 ? <>
							<small>{moment(loan.consented_at).format('D.MMM.YYYY h:mma')}</small><br/>
							<span className="elabel elabel-success">Loan Consented</span>
						</>: <>
							<span className="elabel elabel-danger">{moment(loan.consent_otp_expire_at).diff(moment(), 'seconds') < 0 ? 'OTP Expired' : 'Awaiting Consent'}</span>
						</>}
					</>}
				</td>
				<td nowrap="nowrap">{moment(loan.created_at).format('D-MMM-YYYY')}</td>
				{category === 'multi_fund' && loan.is_multifund === 1 && (
					<td nowrap="nowrap">{moment(loan.multifund_expire_at).format('D-MMM-YYYY')}</td>
				)}
				<td className="row-actions">
					<Tooltip title="View Loan Profile">
						<a className="cursor" onClick={() => openModal(loan.user, loan)}>
							<i className="os-icon os-icon-arrow-2-up" />
						</a>
					</Tooltip>
					{loan && loan.verified === 0 && loan.is_topup === 0 && (
						<Tooltip title="Edit Loan">
							<Link className="cursor" to={`/edit-loan/${loan.id}?_url=${url}`}>
								<i className="os-icon os-icon-edit" />
							</Link>
						</Tooltip>
					)}
					{loan && (category === 'active' || category === 'approve' || category === 'verify' || category === 'verify' || category === 'disburse') && (
						<Popover
							title=""
							content={
								<EditLoanInsurance
									loan={loan}
									doHide={doHide}
									saveLoanInsurance={onSaveLoanInsurance}
								/>
							}
							trigger="click"
							visible={editVisible && loanID === loan.id}
							onVisibleChange={(status) => showEditLoan(status, loan)}
						>
							<Tooltip title="edit insurance">
								<a className="text-primary cursor">
									<i className="os-icon os-icon-text-input" />
								</a>
							</Tooltip>
						</Popover>
					)}
					{loan && (loan.oletter_updated || loan.oletter) && (
						<Tooltip title="Download Offer Letter">
							<a target="_blank" rel="noopener noreferrer" href={`${rootURL}/oletter/${loan.oletter_updated || loan.oletter}`}>
								<i className="icon-paper-clip" />
							</a>
						</Tooltip>
					)}
					{loan && loan.disbursed === 1 && user.role === 'super' && (
						<Tooltip title="Liquidate Loan">
							<a className="cursor" onClick={liquidateLoan(loan)}>
								<i className="os-icon os-icon-cancel-square" />
							</a>
						</Tooltip>
					)}
					{loan && loan.has_consented === 0 && loan.approved === 1 && loan.disbursed === 0 && (
						<Tooltip title="Send Consent SMS">
							<a className="cursor text-gray-dark" onClick={sendConsentSMS(loan)}>
								<i className="os-icon os-icon-send" />
							</a>
						</Tooltip>
					)}
					{loan && loan.approved === 1 && loan.disbursed === 0 && (
						<Tooltip title="Generate Offer Letter">
							<a className="cursor text-success" onClick={doSendMail(loan.id)}>
								<i className="os-icon os-icon-settings" />
							</a>
						</Tooltip>
					)}
				</td>
			</tr>
			{loan.children && loan.children.length > 0 && (
				<tr>
					<td colSpan="">
						<table>
							extra loans
						</table>
					</td>
				</tr>
			)}
		</Fragment>
	);
};

export default withRouter(LoanData);
