import React, { Component } from 'react';
import { connect } from 'react-redux';
import moment from 'moment';
import truncate from 'lodash.truncate';

import { rootURL } from '../config/constants';
import Empty from '../components/Empty';

class SentMail extends Component {
	render() {
		const { mail } = this.props;
		const attachments = mail && mail.attachments && mail.attachments !== '' ? JSON.parse(mail.attachments) : [];
		return (
			<div className="aec-full-message-w show-pack">
				{mail ? (
					<div className="aec-full-message">
						<div className="message-head">
							<div className="user-w">
								<div className="user-name">
									<h6 className="user-title">{mail.receiver.name}</h6>
									<div className="user-role">
										<span>&lt;{mail.receiver.email}&gt;</span>
									</div>
								</div>
							</div>
							<div className="message-info">{moment(mail.created_at).format('D MMM, YYYY')}<br />{moment(mail.created_at).format('h:ma')}</div>
						</div>
						<div className="message-content">
							<div dangerouslySetInnerHTML={{__html: mail.message}} />
							{attachments.length > 0 && (
								<div className="message-attachments">
									<div className="attachments-heading">Attachments</div>
									<div className="attachments-docs">
										{attachments.map((file, i) => {
											return (
												<a target="_blank" href={`${rootURL}/attachment/${file.name}`} key={i} rel="noopener noreferrer">
													<i className={`os-icon ${file.icon}`} />
													<span>{truncate(file.name, { 'length': 20, })}</span>
												</a>
											);
										})}
									</div>
								</div>
							)}
						</div>
					</div>
				) : (
					<Empty />
				)}
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		mail: state.mail.mail,
	}
};

export default connect(mapStateToProps)(SentMail);