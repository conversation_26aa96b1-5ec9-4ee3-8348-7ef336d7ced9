/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import Modal from 'react-modal';

import FinancialOverview from '../components/FinancialOverview';
import TransItem from '../components/TransItem';
import LoanProfile from '../components/LoanProfile';
import { httpRequest, baseURL, loanAPI } from '../config/constants';
import Loading from '../components/Loading';
import { setLoanAnalytics } from '../actions/loan';
import TransferLoans from '../modals/TransferLoans';

class Dashboard extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			loans: [],
			fetching: false,
			hoveredLenders: false,
			retrieving: false,
			isTransferLoans: false,
		};
		this.openModal = this.openModal.bind(this);
		this.closeModal = this.closeModal.bind(this);
	}

	componentWillMount() {
		Modal.setAppElement('body');
	}

	openModal = (customer, loan) => {
		document.body.className = 'pages modal-open';
		this.setState({ modalIsOpen: true, customer, loan });
	};

	closeModal = () => {
		this.setState({ modalIsOpen: false, customer: null, loan: null });
		document.body.className = 'pages';
	};

	componentDidMount() {
		this.fetch();
	}

	fetch = async () => {
		try {
			const { lender } = this.props;
			this.setState({ fetching: true });
			const url = `${baseURL}${loanAPI}?q=recent&lender=${lender.id}`;
			const rs = await httpRequest(url, 'GET', true);
			this.setState({ loans: rs.loans, fetching: false });
		} catch (e) {
			console.log(e);
			this.setState({ fetching: false });
		}
	};

	toggleLenders = () => {
		const { hoveredLenders } = this.state;
		this.setState({ hoveredLenders: !hoveredLenders });
	};
	
	switchLender = item => async () => {
		this.setState({ hoveredLenders: false });
		try {
			const { lender } = this.props;
			this.setState({ retrieving: true });
			const rs = await httpRequest(`${baseURL}${loanAPI}/overview/${item.id}/${lender.id}`, 'GET', true);
			this.props.setLoanAnalytics(rs.analytics);
			this.setState({ retrieving: false });
		} catch (e) {
			this.props.setLoanAnalytics(null);
			this.setState({ retrieving: false });
		}
	};

	transferLoans = () => {
		document.body.className = 'pages modal-open';
		this.setState({ isTransferLoans: true });
	};

	closeTransferLoans = () => {
		this.setState({ isTransferLoans: false });
		document.body.className = 'pages';
		this.switchLender({id: 0});
		this.fetch();
	};

	render() {
		const { analytics, user, lenders } = this.props;
		const { modalIsOpen, customer, loan, loans, fetching, hoveredLenders, retrieving, isTransferLoans } = this.state;
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="element-wrapper compact pt-4">
						<div className="element-actions" style={{textAlign: 'right'}}>
							{user && user.role_name === 'super' && (
								<a className="btn btn-secondary btn-sm cursor mr-2 no-outline" role="button" tabIndex="1" onClick={this.transferLoans}><span>Sell Loans</span></a>
							)}
							{user && user.role_name === 'super' && (
								<div className={`messages-notifications os-dropdown-trigger os-dropdown-position-left lender-switch ${hoveredLenders ? 'over':''} inline-block`} onClick={this.toggleLenders}>
									<a
										className="btn btn-primary btn-sm cursor"
										style={{ color: '#ffffff' }}>
										<span>Change Lender</span>
									</a>
									<div className="os-dropdown light message-list">
										<ul>
											<li>
												<a onClick={this.switchLender({id: 0})}>
													<div className="message-content">
														<h6 className="message-from">Total Summary</h6>
													</div>
												</a>
											</li>
											{lenders.map((lender, i) => (
												<li key={i}>
													<a onClick={this.switchLender(lender)}>
														<div className="message-content">
															<h6 className="message-from">{lender.name}</h6>
														</div>
													</a>
												</li>
											))}
										</ul>
									</div>
								</div>
							)}
						</div>
						<h6 className="element-header">{`Financial Overview - ${analytics ? analytics.name : '-'}`}</h6>
						{analytics && (
							<FinancialOverview
								details={analytics}
								user={user}
								retrieving={retrieving}
							/>
						)}
					</div>
					<div className="element-wrapper">
						<div className="element-box">
							<h5 className="form-header">Recent Loans</h5>
							<div className="table-responsive">
								{fetching ? <Loading /> :
								(
									<table className="table table-striped">
										<thead>
											<tr>
												<th>Date</th>
												<th>Platform</th>
												<th>Borrower</th>
												<th>Loan Amount</th>
												<th>Repayment</th>
												<th className="text-center">Status</th>
												<th className="text-right">Actions</th>
											</tr>
										</thead>
										<tbody>
											{loans.map(loan => {
												return (
													<TransItem
														key={loan.id}
														platform={loan.platform}
														user={loan.user}
														loan={loan}
														amount={loan.amount}
														repayment={loan.monthly_deduction}
														openModal={this.openModal}
														office={loan.office}
													/>
												);
											})}
											{loans.length === 0 && (
												<tr>
													<td colSpan="7">No loan transactions available</td>
												</tr>
											)}
										</tbody>
									</table>
								)}
								<Modal
									isOpen={modalIsOpen}
									onRequestClose={this.closeModal}
									contentLabel="User Profile"
									shouldCloseOnOverlayClick={false}
									overlayClassName="modal-dialog modal-lg modal-extra-lg"
									className="modal-content"
									portalClassName="ReactModalPortall"
									bodyOpenClassName="ReactModal__Body--openl"
								>
									<LoanProfile
										closeModal={() => this.closeModal()}
										user={customer}
										loan={loan}
									/>
								</Modal>
								{modalIsOpen ? <div className="modal-backdrop fade show" /> : ''}

								<Modal
									isOpen={isTransferLoans}
									onRequestClose={this.closeTransferLoans}
									contentLabel="Transfer Loans"
									shouldCloseOnOverlayClick={false}
									overlayClassName="modal-dialog modal-lg modal-extra-lg"
									className="modal-content"
								>
									<TransferLoans closeModal={() => this.closeTransferLoans()} />
								</Modal>
								{isTransferLoans ? <div className="modal-backdrop fade show" /> : ''}
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = state => {
	return {
		analytics: state.loan.analytics,
		user: state.user.user,
		lenders: state.lender.list,
		lender: state.lender.profile,
	};
};

export default connect(mapStateToProps, { setLoanAnalytics })(Dashboard);
