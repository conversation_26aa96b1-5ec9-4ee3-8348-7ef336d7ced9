<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\BankCode;
use App\Http\Controllers\Controller;
use Log;

class BankController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        try {
            $banks = BankCode::whereRaw('id = (SELECT MIN(id) FROM bank_codes t2 WHERE bank_codes.code = t2.code)')->orderBy('name')->get(['id', 'name', 'code']);

            return response()->json(['banks' => $banks], 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'error, could not get banks'], 500);
        }
    }
}
