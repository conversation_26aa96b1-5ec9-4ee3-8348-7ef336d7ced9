import React from 'react';

import profile_bg1 from '../assets/img/profile_bg1.jpg';

const ProfileData = ({ user, lender }) => {
    return (
        user && <div className="user-profile compact">
            <div className="up-head-w" style={{backgroundImage: "url("+profile_bg1+")"}}>
                <div className="up-main-info">
                    <h2 className="up-header">{user.name}</h2>
                    <h6 className="up-sub-header">{user.employer}</h6>
                </div>
                <svg className="decor" width="842px" height="219px" viewBox="0 0 842 219"
                    preserveAspectRatio="xMaxYMax meet" version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlnsXlink="http://www.w3.org/1999/xlink">
                    <g transform="translate(-381.000000, -362.000000)" fill="#FFFFFF">
                        <path className="decor-path"
                            d="M1223,362 L1223,581 L381,581 C868.912802,575.666667 1149.57947,502.666667 1223,362 Z"></path>
                    </g>
                </svg>
            </div>

            <div className="up-contents">
                <div className="element-wrapper">
                    <h6 className="element-header" style={{marginTop: '1.5rem'}}>&nbsp;</h6>
                    <div className="element-box-tp">
                        <div className="users-list-w">
                            {user.platform !== 'sme' && (
                                <div className="user-w with-status status-green">
                                    <div className="user-name">
                                        <h6 className="user-title">{user.ippis || '-'}</h6>
                                        <div className="user-role">IPPIS NUMBER</div>
                                    </div>
                                    <div className="user-action">
                                        <div className="os-icon os-icon-tasks-checked"/>
                                    </div>
                                </div>
                            )}
                            {lender && (
                                <div className="user-w with-status status-green">
                                    <div className="user-name">
                                        <h6 className="user-title">{lender.name || '-'}</h6>
                                        <div className="user-role">LENDING PLATFORM</div>
                                    </div>
                                    <div className="user-action">
                                        <div className="os-icon os-icon-tasks-checked"/>
                                    </div>
                                </div>
                            )}
                            <div className="user-w with-status status-green">
                                <div className="user-name">
                                    <h6 className="user-title">{user.employer || '-'}</h6>
                                    <div className="user-role">EMPLOYER</div>
                                </div>
                                <div className="user-action">
                                    <div className="os-icon os-icon-tasks-checked"/>
                                </div>
                            </div>
                            {user.platform !== 'sme' && (
                                <div className="user-w with-status status-red">
                                    <div className="user-name">
                                        <h6 className="user-title">{user.designation}</h6>
                                        <div className="user-role">DESIGNATION</div>
                                    </div>
                                    <div className="user-action">
                                        <div className="os-icon os-icon-tasks-checked"/>
                                    </div>
                                </div>
                            )}
                            {user.platform !== 'sme' && (
                                <div className="user-w with-status status-green">
                                    <div className="user-name">
                                        <h6 className="user-title">{user.gender}</h6>
                                        <div className="user-role">GENDER</div>
                                    </div>
                                    <div className="user-action">
                                        <div className="os-icon os-icon-tasks-checked"/>
                                    </div>
                                </div>
                            )}
                            {user.platform !== 'sme' && (
                                <div className="user-w with-status status-green">
                                    <div className="user-name">
                                        <h6 className="user-title">{user.first_appointment}</h6>
                                        <div className="user-role">DATE OF FIRST APPOINTMENT</div>
                                    </div>
                                    <div className="user-action">
                                        <div className="os-icon os-icon-tasks-checked"/>
                                    </div>
                                </div>
                            )}
                            {user.platform !== 'sme' && (
                                <div className="user-w with-status status-green">
                                    <div className="user-name">
                                        <h6 className="user-title">{user.retire_expected_at}</h6>
                                        <div className="user-role">EXPECTED DATE OF RETIREMENT</div>
                                    </div>
                                    <div className="user-action">
                                        <div className="os-icon os-icon-tasks-checked"/>
                                    </div>
                                </div>
                            )}
                            <div className="user-w with-status status-green">
                                <div className="user-name">
                                    <h6 className="user-title">{user.salary_bank}</h6>
                                    <div className="user-role">BANK NAME</div>
                                </div>
                                <div className="user-action">
                                    <div className="os-icon os-icon-tasks-checked"/>
                                </div>
                            </div>
                            <div className="user-w with-status status-green">
                                <div className="user-name">
                                    <h6 className="user-title">{user.salary_account_number}</h6>
                                    <div className="user-role">SALARY ACCOUNT NUMBER</div>
                                </div>
                                <div className="user-action">
                                    <div className="os-icon os-icon-tasks-checked"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="m-b">
                    <div className="padded">
                        <div className="os-progress-bar primary">
                            <div className="bar-labels">
                                <div className="bar-label-left">
                                </div>
                            </div>
                        </div>
                        <div className="os-progress-bar primary">
                        </div>
                        <div className="os-progress-bar primary">
                            <div className="bar-labels">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ProfileData;