import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import { withRouter } from 'react-router-dom';

import { baseURL, httpRequest, signupAPI } from '../config/constants';
import { doNotify } from '../actions/general';
import { setUserPayslip } from '../actions/user';
import loading from '../assets/img/loading.gif';

const validate = values => {
    const errors = {};
    if (!values.phone) {
        errors.phone = 'Enter your phone number';
    }
    if (!values.bvn) {
        errors.bvn = 'Enter your bvn number';
    }
    if (!values.lender_id) {
        errors.lender_id = 'Select lending platform';
    }
    return errors;
};
const genders = [
    {id: 'male', name: 'Male'},
    {id: 'female', name: 'Female'},
];

const renderField = ({input, id, label, type, disabled, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
        {touched &&
            ((error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>) ||
                (warning && <span className="help-block form-text with-errors form-control-feedback">{warning}</span>))}
    </div>
);

const renderSelectField = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label>{label}</label>
        <select {...input} className="form-control">
            <option value="">{`Select ${label}`}</option>
            {data.map((c, i) => <option value={c.id} key={i}>{c.name}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class ProfileForm extends Component {
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};
    
    createUser = async data => {
        const { user, bvn, remitaDatum } = this.props;

        const details = {
            ...user,
            ...data,
            platform: user ? user.platform : 'remita',
            password: 'mAoJ6BdO@ve1YT%chVmf',
            name: remitaDatum ? remitaDatum.name : user.name,
            customer_id: remitaDatum ? remitaDatum.customer_id : null,
            bank_code: remitaDatum ? remitaDatum.bank_code : null,
            account_number: remitaDatum ? remitaDatum.account_number : null,
            isAdminCreated: 1,
            verified: 1,
        };

        if(bvn !== details.bvn && user && user.platform === 'sme') {
            window.scrollTo(0, 0);
            const message = 'Error, incorrect bvn';
            this.notify('', message, 'error');
            throw new SubmissionError({
                _error: message,
            });
        }
        
        try {
            const url = `${baseURL}${signupAPI}?focus=admin`;
            await httpRequest(url, 'POST', false, details);
            this.props.reset('newcustomer');
            this.props.setUserPayslip(null);
            this.notify('', 'New user created!', 'success');
            window.scrollTo(0, 0);
        }
        catch (error) {
            window.scrollTo(0, 0);
            const info = error.message || 'Error, check your connection and try again';
            this.notify('', info, 'error');
            throw new SubmissionError({
                _error: info,
            });
        }
    };

    render() {
        const { handleSubmit, error, pristine, submitting, lenders, ippis, user, lender } = this.props;
        return (
            <div className={user ? "col-sm-8" : "col-sm-12"}>
                <div className="element-wrapper">
                    <div className="element-box">
                        <div className="element-info">
                            <div className="element-info-with-icon">
                                <div className="element-info-icon">
                                    <div className="os-icon os-icon-wallet-loaded"/>
                                </div>
                                <div className="element-info-text">
                                    <h5 className="element-inner-header">Profile Setup</h5>
                                    <div className="element-inner-desc">
                                        Please Take time to fill out the form below correctly. The phone number on this page will be used for notifications on all necessary transactions. <a href="http://consumermfb.com.ng" target="_blank" rel="noopener noreferrer">Feel free to contact our help desk if you need a  walk-through.</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            {error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                            <form onSubmit={handleSubmit(this.createUser)}>
                                {lender && (
                                    <div className="row">
                                        <div className="col-sm-6">
                                            <Field
                                                name="lender_id"
                                                component={renderSelectField}
                                                label="Lending Office"
                                                data={lenders.filter(l => l.id === lender.id)}
                                            />
                                        </div>
                                        <div className="col-sm-6"/>
                                    </div>
                                )}
                                <div className="row">
                                    {(!user || (user && user.platform === 'ippis')) && (
                                        <div className="col-sm-6">
                                            <Field
                                                name="ippis"
                                                id="ippis"
                                                type="text"
                                                component={renderField}
                                                label="IPPIS Number"
                                                disabled={ippis !== ''}
                                            />
                                        </div>
                                    )}
                                    <div className="col-sm-6">
                                        <Field
                                            name="employer"
                                            id="employer"
                                            type="text"
                                            component={renderField}
                                            label="Employer Office"
                                        />
                                    </div>
                                </div>
                                <div className="row">
                                    <div className="col-sm-6">
                                        <Field
                                            name="phone"
                                            id="phone"
                                            type="text"
                                            component={renderField}
                                            label="Phone Number"
                                        />
                                    </div>
                                    <div className="col-sm-6">
                                        <Field
                                            name="gender"
                                            component={renderSelectField}
                                            label="Gender"
                                            data={genders}
                                        />
                                    </div>
                                </div>
                                <div className="row">
                                    <div className="col-sm-6">
                                        <Field
                                            name="bvn"
                                            id="bvn"
                                            type="text"
                                            component={renderField}
                                            label="BVN Number"
                                        />
                                    </div>
                                </div>
                                <div className="form-buttons-w">
                                    <button className="btn btn-primary" disabled={pristine || submitting} type="submit">
                                        {submitting? <img src={loading} alt=""/> : 'Submit'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

ProfileForm = reduxForm({
    form: 'newcustomer',
    validate
})(ProfileForm);

const mapStateToProps = (state, ownProps) => {
	return {
		initialValues: {
            phone: ownProps.phone ? ownProps.phone : '',
            lender_id: ownProps.user ? ownProps.user.lender.id : 1,
            ippis: ownProps.user ? ownProps.user.ippis : '',
            employer: ownProps.user ? ownProps.user.employer : '',
            email: ownProps.user ? ownProps.user.email : '',
            address1: ownProps.user ? ownProps.user.address : '',
		},
		phone: ownProps.user ? ownProps.user.phone : '',
		bvn: ownProps.user ? ownProps.user.biometric : '',
        lender: ownProps.user ? ownProps.user.lender : null,
        ippis: ownProps.user ? ownProps.user.ippis : '',
        employer: ownProps.user ? ownProps.user.employer : '',
        lenders: ownProps.lenders,
	}
};

export default withRouter(connect(mapStateToProps, { reset, doNotify, setUserPayslip })(ProfileForm));
