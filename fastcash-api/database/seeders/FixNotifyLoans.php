<?php

namespace Database\Seeders;

use App\Models\Loan;
use App\Models\Notify;
use App\Models\Schedule;
use App\Models\Support;
use Illuminate\Database\Seeder;

class FixNotifyLoans extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $notifies = Notify::get();
            foreach ($notifies as $notify) {
                switch ($notify->action_id) {
                    case 1: // loan request
                        $loan = Loan::where('id', $notify->category_id)->first();
                        if ($loan) {
                            $n = Notify::find($notify->id);
                            $n->user_id = $loan->user_id;
                            $n->staff_id = null;
                            $n->save();
                        }
                        break;
                    case 2: // loan verified
                    case 3: // loan approved
                        $loan = Loan::where('id', $notify->category_id)->first();
                        if ($loan) {
                            $n = Notify::find($notify->id);
                            $n->staff_id = $notify->user_id;
                            $n->user_id = $loan->user_id;
                            $n->save();
                        }
                        break;
                    case 4: // loan paid
                        $schedule = Schedule::where('id', $notify->category_id)->first();
                        if ($schedule) {
                            $ls = Loan::where('id', $schedule->loan_id)->first();
                            if ($ls) {
                                $n = Notify::find($notify->id);
                                $n->user_id = $ls->user_id;
                                $n->staff_id = null;
                                $n->save();
                            }
                        }
                        break;
                    case 5: // deposit
                        $n = Notify::find($notify->id);
                        $n->staff_id = $notify->user_id;
                        $n->save();
                        break;
                    case 6: // admin create user
                    case 8: // loan deleted
                        $n = Notify::find($notify->id);
                        $n->staff_id = 1;
                        $n->save();
                        break;
                }
            }
        } catch (\Exception $e) {
            error_log($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
        }
    }
}
