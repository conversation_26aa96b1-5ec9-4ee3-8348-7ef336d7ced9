<?php

namespace Database\Seeders;

use App\Models\Transaction;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class UpdateLabelTransactions extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $transactions = Transaction::where('description', 'LIKE', '%Taken%')->get();
            foreach ($transactions as $t){
                $transaction = Transaction::find($t->id);
                $transaction->transaction_flag = 'loan';
                $transaction->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }

        try {
            $transactions = Transaction::where('description', 'LIKE', '%taken%')->get();
            foreach ($transactions as $t){
                $transaction = Transaction::find($t->id);
                $transaction->transaction_flag = 'loan';
                $transaction->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }

        try {
            $transactions = Transaction::where('description', 'LIKE', '%Deduction%')->get();
            foreach ($transactions as $t){
                $transaction = Transaction::find($t->id);
                $transaction->transaction_flag = 'deduction';
                $transaction->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }

        try {
            $transactions = Transaction::where('description', 'LIKE', '%Liquidated%')->get();
            foreach ($transactions as $t){
                $transaction = Transaction::find($t->id);
                $transaction->transaction_flag = 'liquidate';
                $transaction->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }

        try {
            $transactions = Transaction::where('description', 'LIKE', '%Rpmt%')->get();
            foreach ($transactions as $t){
                $transaction = Transaction::find($t->id);
                $transaction->transaction_flag = 'deduction';
                $transaction->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }

        try {
            $transactions = Transaction::where('description', 'LIKE', '%Referral Commission%')->get();
            foreach ($transactions as $t){
                $transaction = Transaction::find($t->id);
                $transaction->transaction_flag = 'commission';
                $transaction->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }

        try {
            $transactions = Transaction::where('description', 'LIKE', '%Account Verification%')->get();
            foreach ($transactions as $t){
                $transaction = Transaction::find($t->id);
                $transaction->transaction_flag = 'verify-bvn';
                $transaction->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }

        try {
            $transactions = Transaction::where('description', 'LIKE', '%Excess Repayment%')->get();
            foreach ($transactions as $t){
                $transaction = Transaction::find($t->id);
                $transaction->transaction_flag = 'credit-wallet';
                $transaction->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }

        try {
            $transactions = Transaction::where('channel', 'wallet-withdrawal')->get();
            foreach ($transactions as $t){
                $transaction = Transaction::find($t->id);
                $transaction->transaction_flag = 'withdrawal';
                $transaction->save();
            }
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }
    }
}
