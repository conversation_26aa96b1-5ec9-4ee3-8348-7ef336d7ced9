FROM php:7.3-fpm-alpine

RUN apk add --no-cache \
    libpng \
    libpng-dev \
    libzip-dev \
    git

RUN docker-php-ext-configure zip --with-libzip=/usr/include
RUN docker-php-ext-install gd pdo_mysql zip

# install composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# set working directory
WORKDIR /var/www/html

# copy dependency files to working directory
COPY composer.json .
COPY composer.lock .

# install composer libraries
RUN composer install --no-scripts --no-autoloader --ansi --no-interaction

# copy application code to working directory
COPY . .

# composer regenerate laravel files
RUN composer dump-autoload -o
