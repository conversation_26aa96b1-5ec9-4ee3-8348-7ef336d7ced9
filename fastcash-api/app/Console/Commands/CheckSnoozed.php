<?php

namespace App\Console\Commands;

use App\Http\Controllers\Api\V1\UserController;
use Illuminate\Console\Command;

class CheckSnoozed extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:snoozed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for snoozed users and remove snooze';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $users = app(UserController::class)->checkSnoozed();
        $this->info(json_encode($users));
    }
}
