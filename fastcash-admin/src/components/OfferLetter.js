/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset } from 'redux-form';
import moment from 'moment';
import startCase from 'lodash.startcase';
import converter from 'number-to-words';
import numeral from 'numeral';

import { resetLoanData } from '../actions/loan';
import { baseURL, httpRequest, loanAPI, currency } from '../config/constants';
import loading from '../assets/img/loading.gif';
import logo from '../assets/img/fc-logo.png';
import all_states from '../assets/json/states';
import { doNotify } from '../actions/general';

const validate = values => {
    const errors = {};
    if (!values.agree) {
        errors.agree = 'You have not agreed to our terms and conditions';
    }
    return errors;
};

const renderCheckbox = ({input, label, id, meta: {touched, error}}) => (
    <div className={`form-check ${(touched && error && 'has-error')}`}>
        <label htmlFor={id} className="form-check-label">
            <input {...input} id={id} type="checkbox" className="form-check-input" />{label}
        </label>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class OfferLetter extends Component {
    constructor(props) {
        super(props);
        this.toWords = this.toWords.bind(this);
    }

    toWords = amount => {
        return converter.toWords(amount);
    };

    takeLoan = async _ => {
        try {
            this.props.updateError('');
            const { loanData } = this.props;

            let url;
			if (loanData.section === 'edit') {
				url = `${baseURL}${loanAPI}/${loanData.loan_id}`;
			} else {
				url = `${baseURL}${loanAPI}`;
			}

			const method = loanData.section === 'edit' ? 'PUT' : 'POST';

			await httpRequest(url, method, true, loanData);
            this.props.reset('giveloan');
            this.props.resetLoanData();
            this.props.completed();
            this.notify('Loan Application Changed Successful!', `You have applied for a loan of ₦${numeral(loanData.amount).format('0,0.00')} to be re-payable in ${loanData.tenure}months at ₦${numeral(loanData.monthly_deduction).format('0,0.00')} per month.`, 'success');
        }
        catch (error) {
            const message = error.message || 'Error, check your connection and try again';
            this.props.updateError(message);
        }
    };

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

    render() {
        const { closeModal, handleSubmit, pristine, submitting, user, loanData } = this.props;
        let lga;
        const theState = all_states.find(c => c.state.id == user.state_of_origin);
        if(theState) {
            lga = theState.state.locals.find(l => l.id == user.lga_of_origin);
        }
        const address2 = (!user.address2) ? '' : `<br/>${startCase(user.address2)}`;
        const bankAccount = user.bankAccounts.length > 0 ? user.bankAccounts[0] : null;
        return (
            <div className="offer-block">
                <form onSubmit={handleSubmit(this.takeLoan)}>
                    <div className="invoice-w">
                        <div className="infos">
                            <div className="info-1">
                                <div className="invoice-logo-w" style={{width: "280px"}}><img alt="logo" src={logo}/></div>
                                <div className="company-name">Consumer MFB</div>
                                <div className="company-address">Plot 2087 Herbert Macaulay Way
                                    <br/>Wuse Zone 5, Abuja
                                    <br/>Nigeria
                                </div>
                                <div className="company-extra">tel. 0807.825.5242</div>
                            </div>
                            <div className="info-2">
                                <div className="company-name">{startCase(user.name)}</div>
                                {lga && theState ? (
                                    <div className="company-address" dangerouslySetInnerHTML={{__html: `${startCase(user.address1)} ${address2} <br/>${lga.name}, ${theState.state.name} <br/>Nigeria`}}/>
                                ) : (
                                    <div className="company-address">Nigeria</div>
                                )}
                            </div>
                        </div>
                        <div className="invoice-heading" style={{marginTop: '2rem', marginBottom: '1rem'}}>
                            <h5 style={{fontSize: '0.9rem', fontWeight: '600'}}>LOAN OFFER LETTER</h5>
                            <div className="invoice-date">{moment().format('D MMMM YYYY')}</div>
                            <div className="terms-content" style={{fontWeight: '600', fontSize: 'small'}}>{user.name}</div>
                            <div className="terms-content" style={{fontSize: 'small'}}>{user.office.name}</div>
                            <div className="terms-content" style={{fontSize: 'small'}}>{`BVN: ${user.bvn}`}</div>
                            {bankAccount && (
                                <div className="terms-content" style={{fontSize: 'small'}}>{`Account No: ${bankAccount.account_number}`}</div>
                            )}
                            {bankAccount && (
                                <div className="terms-content" style={{fontSize: 'small'}}>{`Bank: ${bankAccount.bank_name}`}</div>
                            )}
                        </div>
                        <div className="invoice-body">
                            <div className="invoice-table">
                                <div className="terms row">
                                    <div className="col-md-12" style={{marginBottom: '20px'}}>
                                        <div className="terms-content" style={{fontSize:'15px', color:'inherit', lineHeight:'40px'}}>Dear Sir/Madam,</div>
                                        <div className="terms-content" style={{fontWeight: '600', fontSize: 'small', color: 'inherit', lineHeight: '30px'}}>{`OFFER OF LOAN OF ${this.toWords(loanData.amount).toUpperCase()} ONLY.`}</div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="terms-content" style={{textAlign: 'justify'}}>We are pleased to advice you that the management of Consumer MFB has approved your request of {currency(loanData.amount)} ({startCase(this.toWords(loanData.amount))} Only) under the following terms and conditions</div>
                                        <br/>
                                        <div className="terms-content">
                                            <div className="profile-tile-meta">
                                                <ul>
                                                    <li><span className="span">Lender:</span>Consumer MFB</li>
                                                    <li><span className="span">Borrower:</span><strong>{startCase(user.name)}</strong>("The Borrower")</li>
                                                    <li><span className="span">Facility Type:</span>Loan</li>
                                                    <li><span className="span">Facility Amount:</span>{currency(loanData.amount)} ({startCase(this.toWords(loanData.amount))} Only)</li>
                                                    <li><span className="span">Purpose: </span>Consumer/Personal Loan</li>
                                                    <li><span className="span">Tenure:</span>{loanData.tenure} Months</li>
                                                    <li><span className="span">Repayment Plan:</span>Monthly</li>
                                                    <li><span className="span">Repayment Source:</span>Payroll deduction from the borrower monthly salary</li>
                                                    <li><span className="span">Interest Rate:</span>{`${loanData.interest_rate}%`} FLAT/MONTHLY, However this rate is subject to conditions and reciews in line with changes in the money market</li>
                                                    <li><span className="span">Fees:</span>Nil</li>
                                                    <li><span className="span">Insurance:</span>{loanData.insurance}%</li>
                                                    <li><span className="span">Processing Fee:</span>{loanData.processing_fee}%</li>
                                                    <li><span className="span">Monthly Repayment Amount:</span>{currency(loanData.monthly_deduction)}</li>
                                                </ul>
                                            </div>
                                        </div>
                                        <br/>
                                        <div className="terms-content" style={{textAlign: 'justify'}}>The Borrower, hereby authorize Consumer Microfinance Bank Limited (The Bank) or its third party service provider (Service Provider) to make monthly deductions from his/her payroll for the above loan amount and its interest and other fees thereof. In consideration for The Bank or its Service Provider making such deductions I releases the Bank and the Service Provider from any and all liability, and waives all error, if any, made by way of the deduction or failure to make deductions.</div>
										<br/>
                                        <div className="terms-header">SECURITY:</div>
                                        <div className="terms-content" style={{textAlign: 'justify'}}>Direct debit instruction issued by the customer authorising the Bank to debit the payroll at source or via any third party service.  And/or Letter of introduction / confirmation of employment signed by the approved signatories of the organisation.</div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="terms-header">DEFAULT CLAUSE:</div>
                                        <div className="terms-content" style={{textAlign: 'justify'}}>Outstanding repayment after due date shall attract a default fee of 5% and an interest at the rate of 10%/month until payment is received. The Borrower undertakes to be charged the said default fee of 5% and debit interest of 10% per month even after the expiration of the loan term or expiration of any extension granted until the final liquidation of the loan sum inclusive of default fee and debit interest charge. The borrower consents to his/her payment data being shared with necessary third parties to facilitate this loan repayment and the loan amount being directly debited from any of his or her BVN related bank account where funds are not directly available from the pre-specified repayment source or bank account'</div>
										<br/>
                                        <div className="terms-header">REPRESENTATIONS AND WARRANTIES:</div>
                                        <div className="terms-content">The borrower presents and warrants that:
                                            <div className="profile-tile-meta">
                                                <ul style={{textAlign: 'justify'}}>
                                                    <li>The Borrower has the right to accept the facility and has taken all necessary actions to authorise the same upon the terms and conditions herein.</li>
                                                    <li>The Borrower is not in default in respect of any loan obligation to any other financial institution; and shall not obtain any   payroll deductible loan without written consent of The Lender for the duration of this loan.</li>
                                                    <li>That the acceptance of this facility will not be or result in a breach of or default under any provision of any other agreement to which the Borrower is a party.</li>
                                                </ul>
                                            </div>
                                        </div>
                                        <br/>
                                        <div className="terms-header">VALIDITY CLAUSE:</div>
                                        <div className="terms-content">
                                            <div className="profile-tile-meta">
                                                <ol>
                                                    <li>This offer will lapse at the instance of the bank if not accepted within fourteen (14) days from the date of this letter.</li>
                                                    <li>The Borrower will acknowledge acceptance and receipt of the loan by singing this offer and the agreement over leaf or by imprinting his or her left thumbprint on every page of this document and return same to us.</li>
                                                </ol>
                                            </div>
                                        </div>
                                        <br/>
                                        <div className="terms-content">Yours faithfully,</div>
                                        <div className="terms-content"><strong>For: CONSUMER MICROFINANCE BANK LTD</strong></div>
                                    </div>
									<div className="col-md-12" style={{marginTop: '20px'}}>
										<div className="terms-header" style={{textAlign: "center", fontWeight: "bold"}}>LOAN AGREEMENT</div>
									</div>
									<div className="col-md-6">
										<ol style={{padding: "0 0 0 20px"}}>
											<li><strong>THE LOAN</strong>
												<ol style={{padding: "0 0 0 10px", listStyle: "none"}}>
													<li>1.1 The Loan amount overleaf shall be made available by direct payment into borrower's account with The Bank (The Bank).</li>
													<li>1.2 The Borrower will acknowledge receipt of the loan by singing this document or by imprinting his or her left thumb print on this document and the offer overleaf.</li>
												</ol>
											</li>
											<li><strong>INTEREST</strong>
												<ol style={{padding: "0 0 0 10px", listStyle: "none"}}>
													<li>2.1  The Bank will charge interest on the loan amount at the rate stated in the offer letter, and the interest rate will be fixed for the period of the loan. If the Borrower fails to pay any amount which the Borrower owes The Bank in terms of this agreement  on the due date of such amount, The Bank shall be entitle to charge default fee of 5%  and interest of 10% per month on the amount the Borrower has not paid.</li>
												</ol>
											</li>
											<li><strong>PAYMENT</strong>
												<ol style={{padding: "0 0 0 10px", listStyle: "none"}}>
													<li>3.1 The Borrower must repay the loan as shown on the offer letter over leaf.</li>
													<li>3.2 The Borrower agrees that The Bank shall have the rights to deduct the monthly instalment in full as set out in the offer letter directly from the Borrower's salary as a deduction from his or her employer's payroll.</li>
													<li>3.3 The Borrower hereby gives The Bank the right to deduct monies owing to it from any unpaid wages or any other remuneration credits payable to the Borrower if the Borrower leaves the service of his/her employer for any reason before the total amount repayable under this offer has been paid.</li>
													<li>3.4 The Bank can use the money paid by the Borrower to pay first legal and other costs, then interest and then the actual loan amount.</li>
													<li>3.5 Penalty Charges may be applied on pre-liquidated loan; 10% at 1 month and 7.5% from 2nd to the 6th month irrespective of the loan tenure.</li>
													<li>3.6 In the event of the Borrower's death or permanent disability, the Borrower will be liable for the repayment of any outstanding limited to his/her terminal benefit/gratuity or any other disclosed or undisclosed entitlement from his employer (IF ANY).</li>
												</ol>
											</li>
											<li><strong>COSTS AND CHARGES</strong>
												<ol style={{padding: "0 0 0 10px", listStyle: "none"}}>
													<li>4.1 The Borrower agrees that, if The Bank has to use lawyers, Debit Recovery Agents and/or Other Consultants because the Borrower has not carried out any s part of this agreement, the Borrower will pay The Bank all the costs incurred by The Lender.</li>
												</ol>
											</li>
										</ol>
									</div>
									<div className="col-md-6">
										<ol start="5" style={{padding: "0 0 0 10px"}}>
											<li><strong>EVENTS OF DEFAULT</strong>
												<p>The occurrence of any of the following events shall cause all outstanding under this agreement, together with any penalty interest and all other charges and expenses owing to The Bank by the Borrowers shall become immediately due and payable to The Bank.</p>
												<ol style={{padding: "0 0 0 20px", listStyleType: "lower-roman"}}>
													<li>Any failure by the Borrower to pay amount which owes of this agreement in full and on the date he or she has to,</li>
													<li>Any claim that the Borrower has failed to carry his or her duties under this agreement;</li>
													<li>Any situation arises which in the Bank’s opinion makes it inappropriate to continue to extend the Facility to the Borrower</li>
												</ol>
												<p>The Bank shall be entitled to terminate this Agreement and claim and/or recover from the Borrower any damages/losses it may have suffered as consequences.</p>
											</li>
											<li><strong>GENERAL</strong>
												<ol style={{padding: "0 0 0 10px", listStyle: "none"}}>
													<li>6.1 This agreement and the offer over leaf is the whole agreement between The Bank and the Borrower. It cannot be changed unless the change is put into writing and sign by both The Bank and the Borrowers.</li>
													<li>6.2 The Borrower is not in default in respect of any loan obligation to any other financial institution; and shall not obtain any   payroll deductible loan without written consent of The Bank for the duration of this loan.</li>
													<li>6.3 All consents, licenses, approvals, authorisations of any governmental authority, bureau, or agency etc., required in connection with the execution, delivery performance, validity or enforceability of this facility shall be obtained by the Borrower and the originals thereof delivered to the Bank and the conditions contained therein or otherwise applicable thereto shall at the appropriate time be complied with or fulfilled. The costs of obtaining such approvals etc shall be borne by the Borrower.</li>
													<li>6.4 The Borrower gives The Bank permission to register details of the conduct of the Borrower's account with any credit bureau, and the Borrower waives any claims he or she may have against The Bank in respect of such disclosure.</li>
													<li>6.5 Disbursement is subject to provision of letter of employment and confirmation, availability of funds as well as CBN Regulation.</li>
												</ol>
											</li>
										</ol>
									</div>
									<div className="col-md-12" style={{marginTop: '12px'}}>
										<div className="invoice-desc" style={{fontSize: '1.02rem'}}>
                                            <div className="form-check">
                                                <Field
                                                    name="agree"
                                                    id="agree"
                                                    component={renderCheckbox}
                                                    label={`I confirm that I have read, understand and agree to the above terms and conditions. I also authorize my employer to deduct monthly instalments of ₦${numeral(loanData.monthly_deduction).format('0,0.00')} from my salary until the loan has been fully paid and to recover any outstanding instalments against my terminal dues in the event of termination of employment before the loan is fully recovered.`}
                                                />
                                            </div>
                                        </div>
									</div>
                                </div>
                            </div>
                        </div>
                        <div className="invoice-footer">
                            <div className="invoice-logo">
                                <img alt="logo" src={logo}/>
                                <span>Consumer MFB</span>
                            </div>
                            <div className="invoice-info">
                                <span><EMAIL></span>
                                <span>0704.634.6454</span>
                            </div>
                        </div>
                    </div>
                    <div className="modal-footer">
                        <button className="btn btn-secondary" onClick={closeModal} disabled={submitting} type="button"> Cancel</button>
                        <button className="btn btn-primary" type="submit" disabled={pristine || submitting}>
                            {submitting? <img src={loading} alt="loading"/> : 'Proceed'}
                        </button>
                    </div>
                </form>
            </div>
        );
    }
}

OfferLetter = reduxForm({
    form: 'giveloan',
    validate
})(OfferLetter);

export default connect(null, { resetLoanData, reset, doNotify })(OfferLetter);
