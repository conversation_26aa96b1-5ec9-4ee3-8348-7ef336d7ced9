FROM ubuntu:20.04

RUN apt-get update \
  && apt-get install -y --no-install-recommends \
	ssh \
	git \
	ca-certificates \
	curl \
	vim

RUN echo 'root:password' | chpasswd

RUN useradd -ms /bin/bash cmfbweb

WORKDIR /home/<USER>

RUN mkdir /home/<USER>/app
COPY --chown=cmfbweb:cmfbweb . /home/<USER>/app

USER cmfbweb

RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash

ENTRYPOINT [ "/bin/bash" ]
