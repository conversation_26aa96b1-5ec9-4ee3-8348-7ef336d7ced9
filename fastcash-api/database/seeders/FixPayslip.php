<?php

namespace Database\Seeders;

use App\Models\Payslip;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class FixPayslip extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            $users = User::whereNull('ippis')->whereNull('username')->where('platform', 'ippis')->get();
            foreach ($users as $user) {
                $payslip = Payslip::find($user->payslip_id);
                $u = User::find($user->id);

                if($u && $payslip) {
                    $u->ippis = $payslip->ippis;
                    $u->save();
                }
            }


            $transactions = Transaction::where('transaction_flag', 'liquidate')->get();
            foreach ($transactions as $transaction){
                $transaction->repayment_source = $transaction->payment_type;
                $transaction->save();
            }
        }
        catch (\Exception $e){
            Log::error($e->getLine().' '.$e->getMessage().' '.$e->getFile());
        }
    }
}
