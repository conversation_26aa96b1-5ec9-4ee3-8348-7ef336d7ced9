<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateLoansTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->tinyInteger('disbursed')->default(0)->after('payment_status');
            $table->dateTime('disbursed_at')->nullable()->after('disbursed');
            $table->bigInteger('disbursed_by')->nullable()->after('disbursed_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropColumn('disbursed');
            $table->dropColumn('disbursed_at');
            $table->dropColumn('disbursed_by');
        });
    }
}
