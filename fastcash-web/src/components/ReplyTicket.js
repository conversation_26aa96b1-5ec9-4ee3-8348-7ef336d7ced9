/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import truncate from 'lodash.truncate';

import loading from '../assets/img/loading.gif';
import { httpRequest, baseURL, supportAPI, httpRequest2, uploadDocAPI } from '../config/constants';
import { updateTicket } from '../actions/support';
import { setCurrentUser } from '../actions/user';
import { doNotify } from '../actions/general';
import { startBlock, stopBlock } from '../actions/ui-block';

const validate = values => {
    const errors = {}
    if (!values.message) {
        errors.message = 'Enter the message';
    }
    return errors;
};

const renderField = ({input, label, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <div className="input-group mb-2 mr-sm-2 mb-sm-0">
            <textarea {...input} placeholder={label} className="form-control" rows="8" />
        </div>
        {touched &&
            ((error && <span className="help-block">{error}</span>) ||
                (warning && <span className="help-block">{warning}</span>))}
    </div>
);

class ReplyTicket extends Component {
	constructor(props, context) {
		super(props, context);
		this.state = {
			submitting: false,
			files: [],
		};

		this.saveTicket = this.saveTicket.bind(this);
	}

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	saveTicket = data => {
		const { user, id } = this.props;
		const { files } = this.state;
		let details = { ...data, user_id: user.id, response: 'awaiting response,customer-reply', attachment: files, };
		this.setState({ submitting: true });
		return httpRequest(`${baseURL}${supportAPI}/reply/${id}`, 'POST', true, details)
			.then(rs => {
				this.props.updateTicket(rs.support);
				this.props.setCurrentUser(rs.user);
				this.setNotification()
				this.setState({ submitting: false });
				this.props.reset('ticket-frm');
				this.notify('', 'reply sent!', 'success');
			})
			.catch(error => {
				this.setState({ submitting: false });
				const message = error.message || 'could not send ticket';
				this.notify('', message, 'error');
				throw new SubmissionError({
					_error: message,
				});
			});
	};

	onChange = async e => {
		const file = e.target.files[0];
		if (file) {
			this.props.startBlock();

			let formData = new FormData();
			formData.append('file', file);
			formData.append('name', file.name);

			return httpRequest2(`${baseURL}${uploadDocAPI}`, 'POST', formData)
				.then(response => {
					this.setState({ files: [...this.state.files, response.result] });
					this.props.stopBlock();
				})
				.catch(error => {
					this.props.stopBlock();
					if (error.message) {
						this.notify('', error.message, 'error');
					} else {
						this.notify('', 'Error, could not upload file', 'error');
					}
				});
		}
	};

	setNotification = () => {
		let { user } = this.props;
		let hintElem = document.querySelector(".introjs-hint-pulse");
		if(hintElem){
			hintElem.innerHTML = "<span style='font-size: 9px; background: rgba(255, 90, 90, 0.7); font-weight: 600; color: #fff; position: absolute;'>"+user.pending_support+"</span>"			
		}
	}

	remove = file => () => {
		this.setState({ files: [...this.state.files.filter(f => f.name !== file.name)] });
	};

	render() {
		let uploadAttachment;
        const { pristine, handleSubmit, error, title, id } = this.props;
		const { submitting, files } = this.state;
		
		return (
			<form onSubmit={handleSubmit(this.saveTicket)} autoComplete="off">
				{error && <div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
				<h6 className="element-box-header">{`${title} #${id}`}</h6>
				<div className="row">
					<div className="col-sm-12">
						<Field name="message" component={renderField} label="Description" />
					</div>
				</div>
				<div className="actions-left">
					<input className="hidden" onClick={(e) => { e.target.value = null; }} type="file" ref={(el) => { uploadAttachment = el; }} onChange={(e) => this.onChange(e)} />
					<a className="btn btn-link" onClick={() => { uploadAttachment.click() }}>
						<i className="os-icon os-icon-ui-51"/>
						<span>Add Attachment</span>
					</a>
					<div className="message-attachments">
						<div className="attachments-docs">
							{files.map((file, i) => {
								return (
									<a key={i} className="badge badge-info-inverted badge-light ml-2">
										<span>{truncate(file.name, { 'length': 20, })}</span>
										<i onClick={this.remove(file)} className="os-icon os-icon-ui-15 text-danger ml-2" />
									</a>
								);
							})}
						</div>
					</div>
				</div>
				<div className="form-buttons-w text-right compact">
					<button className="mr-2 btn btn-primary" disabled={pristine || submitting} type="submit">
						{submitting? <img src={loading} alt=""/> : 'Reply'}
					</button>
				</div>
			</form>
		);
	}
}

ReplyTicket = reduxForm({
    form: 'ticket-frm',
    validate,
})(ReplyTicket);

export default connect(null, { reset, updateTicket, setCurrentUser, doNotify, startBlock, stopBlock })(ReplyTicket);
