<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddAccountNumberAndBankCodeToRemitaUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('remita_users', function (Blueprint $table) {
            $table->string('account_number')->nullable();
            $table->string('bank_code')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('remita_users', function (Blueprint $table) {
            $table->dropColumn('account_number');
            $table->dropColumn('bank_code');
        });
    }
}

