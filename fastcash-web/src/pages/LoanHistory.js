/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';

import { httpRequest, baseURL, loanAPI } from '../config/constants';
import HistoryItem from '../components/HistoryItem';
import loader from '../assets/img/loader.gif';
import loading from '../assets/img/loading.gif';
import { doNotify } from '../actions/general';

class LoanHistory extends Component {
	state = {
		user: null,
		loans: [],
		fetching: false,
		exporting: false,
	};

	componentDidMount() {
		this.fetchUser();
	}

	fetchUser = async () => {
		this.setState({ fetching: true });
		try {
			const { user } = this.props;
			const url = `${baseURL}/profile/${user.id}`;
			const rs = await httpRequest(url, 'GET', true);
			const { loans, ...profile } = rs.user;
			this.setState({ user: profile, loans, fetching: false });
		} catch (error) {
			this.setState({ fetching: false });
		}
	};

	exportHistory = async e => {
		e.preventDefault();
		try {
			this.setState({ exporting: true });
			const { user } = this.props;
			const url = `${baseURL}${loanAPI}/export/${user.id}`;
			const rs = await httpRequest(url, 'GET', true)
			if(rs.result) {
				this.notify('', rs.result, 'success');
				this.setState({ exporting: false });
			} else {
				this.setState({ exporting: false });
				this.notify('', 'loan history has been exported and sent to your email', 'success');
			}
		} catch(error) {
			this.setState({ exporting: false });
			const message = error.message || 'could not export history';
			this.notify('', message, 'error');
		}
	};
    
    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
    };

	render() {
		const { user, fetching, exporting, loans } = this.state;
		const { current_loan } = this.props;
		return (
			fetching ? <div className="content-i"><div className="content-box" style={{textAlign:'center'}}><img src={loader} alt=""/></div></div> :
			<div className="content-i">
				<div className="content-box">
					<div className="row">
						<div className="col-sm-12">
							<div className="element-wrapper">
								<div className="element-actions">
									{exporting ? (
										<a className="btn btn-primary btn-sm btn-export pointer"><img src={loading} alt=""/></a>
									) : (
										<a className="btn btn-primary btn-sm btn-export pointer" role="button" tabIndex="0" onClick={this.exportHistory}><i className="os-icon os-icon-email-2-at"/><span>Export History</span></a>
									)}
								</div>
								<h5 className="element-header">Loan History</h5>
								<div className="element-box-tp">
									{!fetching && user && (
										<div className="table-responsive">
											<table className="table table-padded">
												<thead>
												<tr>
													<th/>
													<th>Description</th>
													<th className="text-center">Platform</th>
													<th className="text-center">Amount</th>
													<th>Tenure</th>
													<th>Date</th>
													<th className="text-center">Status</th>
												</tr>
												</thead>
												<tbody>
													{loans.map((loan, i) => {
														return (
															<HistoryItem
																key={i}
																loan={loan}
																current_loan={current_loan}
																declined={loan.deleted_at}
																liquidated={loan.liquidated === 1}
															/>
														)
													})}
													{loans.length === 0 && (
														<tr><td colSpan="7">No loans taken yet</td></tr>
													)}
												</tbody>
											</table>
										</div>
									)}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
    return {
		user: state.user.user,
		current_loan: state.loan.current,
    }
}

export default connect(mapStateToProps, { doNotify })(LoanHistory);
