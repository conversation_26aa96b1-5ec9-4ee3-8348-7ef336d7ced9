{"name": "fastash", "version": "2.0.0", "private": true, "scripts": {"start": "react-scripts start", "clean": "sudo rm -rf build", "build": "yarn clean && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "antd": "^3.8.2", "bootstrap": "^4.5.2", "crypto-js": "^3.1.9-1", "draft-js": "^0.11.0", "draftjs-to-html": "^0.8.4", "es6-promise": "^4.2.8", "font-awesome": "^4.7.0", "html-to-draftjs": "^1.4.0", "jquery": "^3.2.1", "jwt-decode": "^2.2.0", "lodash.debounce": "^4.0.8", "lodash.range": "^3.2.0", "lodash.replace": "^4.1.4", "lodash.size": "^4.2.0", "lodash.split": "^4.4.2", "lodash.startcase": "^4.4.0", "lodash.template": "^4.5.0", "lodash.truncate": "^4.4.2", "moment": "^2.22.1", "number-to-words": "^1.2.3", "numeral": "^2.0.6", "react": "^16.13.1", "react-autosuggest": "^9.4.3", "react-block-ui": "^1.3.3", "react-bootstrap-table": "^4.3.1", "react-datepicker": "^1.4.1", "react-dom": "^16.13.1", "react-draft-wysiwyg": "^1.13.2", "react-dropzone": "^4.2.3", "react-loadable": "^5.5.0", "react-modal": "^3.1.0", "react-notification-system": "^0.2.17", "react-redux": "^7.2.1", "react-router-dom": "^5.2.0", "react-router-redux": "^4.0.8", "react-scripts": "3.4.3", "react-tabs": "^3.1.1", "redux": "^4.0.5", "redux-form": "^8.3.6", "redux-thunk": "^2.3.0", "simple-line-icons": "^2.4.1"}, "devDependencies": {"@types/lodash.debounce": "^4.0.6", "@types/lodash.replace": "^4.1.6", "@types/lodash.size": "^4.2.6", "@types/lodash.split": "^4.4.6", "@types/lodash.startcase": "^4.4.6", "@types/lodash.template": "^4.4.6", "@types/lodash.truncate": "^4.4.6", "redux-logger": "^3.0.6"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}