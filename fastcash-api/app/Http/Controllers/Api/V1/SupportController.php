<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Support;
use App\Models\User;
use App\Services\MailHandler;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Log;
use DB;
use Carbon\Carbon;

class SupportController extends Controller
{
    /**
     * Display a listing of the resource.
     * @param \Illuminate\Http\Request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $lender = $request->has('lender') ? $request->input('lender') : '';
            $ticketID = $request->has('ticket') ? $request->input('ticket') : '';

            $limit = $request->has('results') && $request->input('results') != '' ? $request->input('results') : 5;
            $page = $request->has('page') && $request->input('page') != '' ? $request->input('page') : 1;
            $skip = ($page - 1) * $limit;

            $user = $request->has('user_id') && $request->input('user_id') != '' ? $request->input('user_id') : '';
            $status = $request->has('status') && $request->input('status') != '' ? $request->input('status') : '';

            $from = $request->has('from') && $request->input('from') != '' ? $request->input('from') : '';
            $to = $request->has('to') && $request->input('to') != '' ? $request->input('to') : '';
            $_from = $from == '' ? '' : Carbon::createFromFormat('d-m-Y', $from)->toDateTimeString();
            $_to = $to == '' ? '' : Carbon::createFromFormat('d-m-Y', $to)->toDateTimeString();

            $type = $request->has('type') ? $request->input('type') : '';
            $q = $request->has('q') ? $request->input('q') : '';

            $supports = [];
            $openTickets = [];
            $closedTickets = [];
            $answeredTickets = [];
            $count = 0;

            if ($user != '') {
                $supports = Support::where('top_query', 1)
                    ->where(function ($query) use ($user) {
                        $query->where('user_id', $user)->orWhere('receiver_id', $user);
                    })
                    ->where('user_id', $user)
                    ->skip($skip)
                    ->take($limit)
                    ->orderBy('created_at', 'desc')
                    ->get();

                $count = Support::where('top_query', 1)
                    ->where(function ($query) use ($user) {
                        $query->where('user_id', $user)->orWhere('receiver_id', $user);
                    })->count();

                if ($user != '') {
                    $supports = $supports->map(function ($item, $key) {
                        $item->replies = Support::with('user')->where('reply_id', $item->reply_id)->orderBy('created_at', 'asc')->get();
                        $item->user = User::where('id', $item->user_id)->first(['id', 'name']);
                        $item->receiver = User::where('id', $item->receiver_id)->first(['id', 'name']);
                        return $item;
                    });
                }
            } else {
                if ($lender != '') {
                    if ($type == '') {
                        if (empty($status) && empty($from)) {
                            // $supports = Support::where('lender_id', $lender)->where('top_query', 1)
                            // ->skip($skip)->take($limit)->orderBy('updated_at', 'desc')->get();

                            // $count = Support::where('lender_id', $lender)->where('top_query', 1)->count();
                            // $supports = $supports->map(function ($item, $key) {
                            //     $item->replies = Support::with('user')->where('reply_id', $item->reply_id)->orderBy('created_at', 'asc')->get();
                            //     $item->user = User::where('id', $item->user_id)->first(['id', 'name']);
                            //     $item->receiver = User::where('id', $item->receiver_id)->first(['id', 'name']);
                            //     return $item;
                            // });
                            $status = 'all';
                            $supports = filterSupport($status, $lender, $skip, $limit, $_from, $_to);
                            $count = count($supports);
                            $supports = array_splice($supports, $skip, $limit);
                        } else if (!empty($status) && !empty($from)) {
                            if ($status == 'all') {
                                $status = 'all';
                                $supports = filterSupport($status, $lender, $skip, $limit, $_from, $_to);
                                $count = count($supports);
                                $supports = array_splice($supports, $skip, $limit);
                            } else if ($status == 'open') {
                                $status = 'open';
                                $supports = filterSupport($status, $lender, $skip, $limit, $_from, $_to);
                                $count = count($supports);
                                $supports = array_splice($supports, $skip, $limit);

                            }else if($status == 'closed'){
                                $status = 'closed';
                                $supports = filterSupport($status, $lender, $skip, $limit, $_from, $_to);
                                $count = count($supports);
                                $supports = array_splice($supports, $skip, $limit);
                            } else if ($status == 'answered') {
                                $status = 'answered';
                                $supports = filterSupport($status, $lender, $skip, $limit, $_from, $_to);
                                $count = count($supports);
                                $supports = array_splice($supports, $skip, $limit);

                            }
                            // else if($status == 'awaiting-response'){
                            //     $status = 'awaiting response';
                            //     $supports = filterSupport($status, $lender, $skip, $limit, $_from, $_to);
                            //     $count = count($supports);
                            // }
                        } else if (empty($status) && !empty($from)) {
                            $supports = Support::where('lender_id', $lender)->where('top_query', 1)->whereBetween('created_at', [$_from, $_to])->skip($skip)->take($limit)->orderBy('created_at', 'desc')->get();
                            $count = Support::where('lender_id', $lender)->where('top_query', 1)->whereBetween('created_at', [$_from, $_to])->count();
                            $supports = $supports->map(function ($item, $key) {
                                $item->replies = Support::with('user')->where('reply_id', $item->reply_id)->orderBy('created_at', 'asc')->get();
                                $item->user = User::where('id', $item->user_id)->first(['id', 'name']);
                                $item->receiver = User::where('id', $item->receiver_id)->first(['id', 'name']);
                                return $item;
                            });
                        } else if (!empty($status) && empty($from)) {
                            if ($status == 'all') {
                                $status = 'all';
                                $supports = filterSupport($status, $lender, $skip, $limit, $_from, $_to);
                                $count = count($supports);
                                $supports = array_splice($supports, $skip, $limit);
                            } else if ($status == 'open') {
                                $status = 'open';
                                $supports = filterSupport($status, $lender, $skip, $limit, $_from, $_to);
                                $count = count($supports);
                                $supports = array_splice($supports, $skip, $limit);
                            }else if($status == 'closed'){
                                $status = 'closed';
                                $supports = filterSupport($status, $lender, $skip, $limit, $_from, $_to);
                                $count = count($supports);
                                $supports = array_splice($supports, $skip, $limit);
                            } else if ($status == 'answered') {
                                $status = 'answered';
                                $supports = filterSupport($status, $lender, $skip, $limit, $_from, $_to);
                                $count = count($supports);
                                $supports = array_splice($supports, $skip, $limit);
                            }
                            // else if($status == 'awaiting-response'){
                            //     $status = 'awaiting response';
                            //     $supports = filterSupport($status, $lender, $skip, $limit);
                            //     $count = count($supports);
                            // }
                        }
                    }else{
                        // $supports = filterSupportByUser($type, $q, $status, $_from, $_to);
                        // if($supports->error == true){
                        //     return response()->json(['error' => $supports->message], 500);
                        // }else{
                        //     $count = count($supports->supports);
                        //     $supports = array_splice($supports->supports, $skip, $limit);
                        // }

                        if($type == 'ticket'){
                            $supports = Support::where('id', $q)->get();

                            if(count($supports)){
                                $supports = $supports->map(function ($item, $key) {
                                    $item->replies = Support::with('user')->where('reply_id', $item->reply_id)->orderBy('created_at', 'asc')->get();
                                    $item->user = User::where('id', $item->user_id)->first(['id', 'name']);
                                    $item->receiver = User::where('id', $item->receiver_id)->first(['id', 'name']);
                                    return $item;
                                });
                                $count = 1;

                            }else{
                                return response()->json(['error' => 'Ticket No does not exist'], 500);
                            }
                        }else if($type == 'phone' || $type == 'ippis'){
                            $user = User::where('phone', $q)->orWhere('ippis', $q)->first(['id']);
                            if(!$user){
                                return response()->json(['error' => 'User does not exist'], 500);
                            } else {
                                if ($status == 'all' || $status == '') {
                                    if ($_from == '') {
                                        $supports = Support::where('user_id', $user->id)->where('top_query', 1)->orderBy('created_at', 'asc')->get();
                                    } else {
                                        $supports = Support::where('user_id', $user->id)->where('top_query', 1)->whereBetween('created_at', [$_from, $_to])->orderBy('created_at', 'asc')->get();
                                    }
                                    foreach ($supports as $key => $value) {
                                        $data = (object)null;
                                        $data = Support::with('user')->where('reply_id', $value->reply_id)->orderBy('created_at', 'asc')->first();
                                        $data->replies = Support::with('user')->where('reply_id', $value->reply_id)->get();
                                        $data->user = User::where('id', $value->user_id)->first(['id', 'name']);
                                        $data->receiver = User::where('id', $value->receiver_id)->first(['id', 'name']);

                                        $lastIndex = count($data->replies) - 1;
                                        $lastReply = $data->replies[$lastIndex];

                                        if(strpos($lastReply->response, 'closed') === false){
                                            array_unshift($openTickets, $data);
                                        }
                                        if (strpos($lastReply->response, 'closed') !== false) {
                                            array_unshift($closedTickets, $data);
                                        }
                                    }
                                    $supports = array_merge($openTickets, $closedTickets);
                                    $count = count($supports);
                                    $supports = array_splice($supports, $skip, $limit);

                                }else if($status == 'closed'){
                                    if($_from == ''){
                                        $supports = Support::where('user_id', $user->id)->where('top_query', 1)
                                            ->orderBy('created_at', 'asc')->get();
                                    } else {
                                        $supports = Support::where('user_id', $user->id)->where('top_query', 1)
                                        ->whereBetween('created_at', [$_from, $_to])->orderBy('created_at', 'asc')->get();
                                    }
                                    foreach ($supports as $key => $value) {
                                        $data = (object)null;
                                        $data = Support::with('user')->where('reply_id', $value->reply_id)->orderBy('created_at', 'asc')->first();
                                        $data->replies = Support::with('user')->where('reply_id', $value->reply_id)->get();
                                        $data->user = User::where('id', $value->user_id)->first(['id', 'name']);
                                        $data->receiver = User::where('id', $value->receiver_id)->first(['id', 'name']);

                                        $lastIndex = count($data->replies) - 1;
                                        $lastReply = $data->replies[$lastIndex];
                                        if(strpos($lastReply->response, 'closed') !== false){
                                            array_unshift($closedTickets, $data);
                                        }
                                        $supports = $closedTickets;
                                    }
                                    $count = count($closedTickets);
                                    $supports = array_splice($closedTickets, $skip, $limit);
                                }else if($status == 'open'){
                                    $not = 'closed,closed';
                                    if ($_from == '') {
                                        $supports = Support::where('user_id', $user->id)->where('top_query', 1)
                                            ->orderBy('created_at', 'asc')->get();
                                    } else {
                                        $supports = Support::where('user_id', $user->id)->where('top_query', 1)
                                        ->whereBetween('created_at', [$_from, $_to])->orderBy('created_at', 'asc')->get();
                                    }
                                    foreach ($supports as $key => $value) {
                                        $data = (object)null;
                                        $data = Support::with('user')->where('reply_id', $value->reply_id)->orderBy('created_at', 'asc')->first();
                                        $data->replies = Support::with('user')->where('reply_id', $value->reply_id)->get();
                                        $data->user = User::where('id', $value->user_id)->first(['id', 'name']);
                                        $data->receiver = User::where('id', $value->receiver_id)->first(['id', 'name']);

                                        $lastIndex = count($data->replies) - 1;
                                        $lastReply = $data->replies[$lastIndex];
                                        if (strpos($lastReply->response, 'closed') === false) {
                                            array_unshift($openTickets, $data);
                                        }
                                    }
                                    $supports = $openTickets;
                                    $count = count($supports);
                                    $supports = array_splice($supports, $skip, $limit);

                                }else if($status == 'answered'){
                                    $answered = 'answered';
                                    if ($_from == '') {
                                        $supports = Support::where('user_id', $user->id)->where('response', 'like', '%' . $answered . '%')
                                            ->orderBy('created_at', 'asc')->get();
                                    } else {
                                        $supports = Support::where('user_id', $user->id)->where('response', 'like', '%' . $answered . '%')->whereBetween('created_at', [$_from, $_to])
                                            ->orderBy('created_at', 'asc')->get();
                                    }

                                    foreach ($supports as $key => $value) {
                                        $data = (object)null;
                                        $data = Support::with('user')->where('reply_id', $value->reply_id)->orderBy('created_at', 'asc')->first();
                                        $data->replies = Support::with('user')->where('reply_id', $value->reply_id)->get();
                                        $data->user = User::where('id', $value->user_id)->first(['id', 'name']);
                                        $data->receiver = User::where('id', $value->receiver_id)->first(['id', 'name']);

                                        $lastIndex = count($data->replies) - 1;
                                        $lastReply = $data->replies[$lastIndex];

                                        if (strpos($lastReply->response, 'answered') !== false) {
                                            $answeredTickets[] = $data;
                                        }
                                    }
                                    $supports = $answeredTickets;
                                    $count = count($supports);
                                    $supports = array_splice($supports, $skip, $limit);
                                }
                            }
                        }
                    }
                } else {
                    return response()->json(['error' => 'could not fetch tickets'], 500);
                }
            }


            $ticket = null;
            if ($ticketID != '') {
                $ticket = Support::where('id', $ticketID)->first();
                $ticket->replies = Support::with('user')->where('reply_id', $ticket->reply_id)->orderBy('created_at', 'asc')->get();
                $ticket->user = User::where('id', $ticket->user_id)->first(['id', 'name']);
                $ticket->receiver = User::where('id', $ticket->receiver_id)->first(['id', 'name']);
            }

            $num_of_pages = ceil($count / $limit);

            $result = (object)null;
            $result->page = $page;
            $result->data = $supports;
            $result->num_of_pages = $num_of_pages;
            $result->hasNext = ($page < $num_of_pages);
            $result->hasPrevious = ($page > 1);
            $result->total = $count;
            $result->ticket = $ticket;

            return response()->json(compact('result'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not fetch tickets, try again later'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        DB::beginTransaction();

        try {
            $input = $request->all();

            $user = User::where('id', $input['user_id'])->first();

            $to = null;
            if ($request->has('created_by') && $request->input('created_by') == '1') {
                $to = User::where('phone', $input['phone'])->first();
                if (!$to) {
                    return response()->json(['error' => 'could not send message, user not found!'], 500);
                }
            }

            $attachments = isset($input['attachment']) ? $input['attachment'] : [];

            if (isset($input['from']) && $input['from'] == 'mobile') {
                $attachments = json_decode($input['attachment']);
            }

            $fromStaff = ($request->has('created_by') && $request->input('created_by') == '1');

            $support = Support::create([
                'user_id' => $user->id,
                'subject' => $input['subject'],
                'message' => $input['message'],
                'attachment' => json_encode($attachments),
                'top_query' => 1,
                'response' => $input['response'],
                'status' => 0,
                'lender_id' => $user->lender_id,
                'receiver_id' => ($to == null) ? null : $to->id,
            ]);

            $support->reply_id = $support->id;
            $support->save();

            $notify = doNotify($user->id, 'supports', $support->id, 'new_ticket', null, null, $user->lender_id, null);

            DB::commit();

            try {
                $mail = (object)null;
                $mail->template = 'email.support';

                $mail->login = '';

                $mail->support_id = $support->id;
                $mail->support_url = env('MAIN_URL') . '/support?tid=' . $support->id;
                $mail->subject = 'FastCash Support - Ticket (#' . $support->id . ')';

                $attachments = [];
                foreach ($attachments as $input) {
                    $attach = (object)null;
                    $attach->name = $input['name'];

                    $attachments[] = $attach;
                }

                if ($fromStaff) {
                    $mail->from_name = 'FastCash Support';
                    $mail->from_email = '<EMAIL>';
                    $mail->to_name = $to->name;
                    $mail->to_email = $to->email;

                    if ($to->role == 'user' || $to->role == 'u-merchant') {
                        $mail->login = env('MAIN_URL');
                    } else {
                        $mail->login = '';
                    }

                    $mail->content = $support->message;
                    $mail->from_support = true;
                    $mail->uri = public_path() . '/attachment/';
                    $mail->attachments = $attachments;

                    $mail->hasCC = true;
                    $mail->cc_email = '<EMAIL>';

                    $send_to_user = (new MailHandler())->sendMailAttach($mail);
                } else {
                    $mail->from_name = $user->name;
                    $mail->from_email = $user->email;
                    $mail->to_name = 'FastCash Support';
                    $mail->to_email = '<EMAIL>';

                    $mail->content = $support->message;
                    $mail->uri = public_path() . '/attachment/';
                    $mail->attachments = $attachments;
                    $mail->from_support = false;

                    $send_to_support = (new MailHandler())->sendMailAttach($mail);

                    $mail_user = $mail;
                    $mail_user->from_name = 'FastCash Support';
                    $mail_user->from_email = '<EMAIL>';
                    $mail_user->to_name = $user->name;
                    $mail_user->to_email = $user->email;

                    if ($user->role == 'user' || $user->role == 'u-merchant') {
                        $mail_user->login = env('MAIN_URL');
                    } else {
                        $mail_user->login = '';
                    }

                    $mail_user->content = '';
                    $send_to_user = (new MailHandler())->sendMail($mail_user);
                }
            } catch (\Exception $e) {
                Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            }

            $support->replies = Support::with('user')->where('reply_id', $support->reply_id)->orderBy('created_at', 'asc')->get();
            $support->user = User::where('id', $support->user_id)->first(['id', 'name']);
            $support->receiver = User::where('id', $support->receiver_id)->first(['id', 'name']);

            return response()->json(compact('support'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not send message, try again later'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function reply(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required',
        ]);

        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        DB::beginTransaction();
        try {
            $fromSupport = $request->has('from_support') ? $request->input('from_support') : '';
            $input = $request->all();

            $user = User::find($input['user_id']);
            $support = Support::find($id);

            $attachments = isset($input['attachment']) ? $input['attachment'] : [];

            if (isset($input['from']) && $input['from'] == 'mobile') {
                $attachments = json_decode($input['attachment']);
            }

            $isFromUser = $user->id == $support->user_id ? true : false;

            if($isFromUser){
                $_support = Support::create([
                    'user_id' => $user->id,
                    'subject' => $support->subject,
                    'message' => $input['message'],
                    'attachment' => json_encode($attachments),
                    'top_query' => 0,
                    'read' => 1,
                    'response' => $input['response'],
                    'reply_id' => $support->id,
                    'lender_id' => $user->lender_id,
                    'receiver_id' => $support->receiver_id,
                ]);
            }else{
                $_support = Support::create([
                    'user_id' => $user->id,
                    'subject' => $support->subject,
                    'message' => $input['message'],
                    'attachment' => json_encode($attachments),
                    'top_query' => 0,
                    'response' => $input['response'],
                    'reply_id' => $support->id,
                    'lender_id' => $user->lender_id,
                    'receiver_id' => $support->receiver_id,
                ]);
            }

            $staff = $user->id == $support->user_id ? null : $user->id;
            $notify = doNotify($support->user_id, 'supports', $_support->id, 'reply_ticket', null, null, $user->lender_id, $staff);

            DB::commit();

            $support->user = User::where('id', $support->user_id)->first(['id', 'name']);
            $support->replies = Support::with('user')->where('reply_id', $support->reply_id)->orderBy('created_at', 'asc')->get();
            $support->receiver = User::where('id', $support->receiver_id)->first(['id', 'name']);

            if ($fromSupport == 'yes') {
                $owner = User::where('id', $support->receiver_id)->first();
            } else {
                $owner = User::where('id', $support->user_id)->first();
            }

            $attachments = [];
            foreach ($attachments as $input) {
                $attach = (object)null;
                $attach->name = $input['name'];

                $attachments[] = $attach;
            }

            $isFromSupport = ($fromSupport == 'yes');
            $this->sendMail($isFromSupport, $owner, $support->id, $input['message'], $attachments);

            $reply = Support::with('user')->where('id', $_support->id)->first();
            $user = loadUserData($user);

            return response()->json(['support' => $support, 'reply' => $reply, 'user' => $user], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'could not send message, try again later'], 500);
        }
    }

    /**
     * Send mail.
     *
     * @param $from_support
     * @param $user
     * @param $supportID
     * @param $message
     * @param $attachments
     *
     * @return void
     */
    private function sendMail($from_support, $user, $supportID, $message, $attachments)
    {
        try {
            $mail = (object)null;
            $mail->template = 'email.support';
            $mail->subject = 'FastCash Support - Ticket (#' . $supportID . ') has a reply';

            $mail->support_id = $supportID;
            $mail->support_url = env('MAIN_URL') . '/support?tid=' . $supportID;

            $mail->uri = public_path() . '/attachment/';
            $mail->attachments = $attachments;

            if ($from_support && $user) {
                $mail->from_name = 'FastCash Support';
                $mail->from_email = '<EMAIL>';
                $mail->to_name = str_replace(',', '', $user->name);
                $mail->to_email = $user->email;

                if ($user->role == 'user' || $user->role == 'u-merchant') {
                    $mail->login = env('MAIN_URL');
                } else {
                    $mail->login = '';
                }

                $mail->from_support = true;
                $mail->content = 'ticket-replied';
                $mail->response = $message;

                $mail->hasCC = true;
                $mail->cc_email = '<EMAIL>';

                $send_to_user = (new MailHandler())->sendMailAttach($mail);
            } else if (!$from_support && $user) {
                $mail->from_name = str_replace(',', '', $user->name);
                $mail->from_email = $user->email;
                $mail->to_name = 'FastCash Support';
                $mail->to_email = '<EMAIL>';

                $mail->login = '';

                $mail->from_support = false;
                $mail->content = $message;

                $mail->hasCC = false;
                $send_to_admin = (new MailHandler())->sendMailAttach($mail);

                $mail_user = $mail;
                $mail_user->subject = 'FastCash Support - Ticket (#' . $supportID . ')';
                $mail_user->from_name = 'FastCash Support';
                $mail_user->from_email = '<EMAIL>';
                $mail_user->to_name = $user->name;
                $mail_user->to_email = $user->email;

                if ($user->role == 'user' || $user->role == 'u-merchant') {
                    $mail_user->login = env('MAIN_URL');
                } else {
                    $mail_user->login = '';
                }

                $mail_user->content = '';
                $send_to_user = (new MailHandler())->sendMail($mail_user);
            }
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function close(Request $request, $id)
    {
        try {
            $input = $request->all();

            $user = User::find($input['user_id']);

            $support = Support::where('id', $id)->first();
            $support->status = 1;
            $support->save();

            $_support = Support::create([
                'user_id' => $user->id,
                'subject' => $support->subject,
                'message' => 'conversation was closed on ' . date('d-M-Y \a\t g:ia') . ' by ' . ucwords(strtolower($user->name)),
                'attachment' => null,
                'top_query' => 0,
                'status' => 1,
                'response' => 'closed,closed',
                'reply_id' => $support->id,
                'closed_by' => $user->id,
                'closed_at' => date('Y-m-d H:i:s'),
                'lender_id' => $user->lender_id,
                'receiver_id' => $support->receiver_id,
            ]);

            $staff = $user->id == $support->user_id ? null : $user->id;
            $notify = doNotify($support->user_id, 'supports', $_support->id, 'close_ticket', null, null, $user->lender_id, $staff);
            DB::commit();

            $owner = null;

            $sender = User::where('id', $support->user_id)->first();
            if ($sender && ($sender->role == 'user' || $sender->role == 'u-merchant')) {
                $owner = $sender;
            }

            $receiver = User::where('id', $support->receiver_id)->first();
            if ($receiver && ($receiver->role == 'user' || $receiver->role == 'u-merchant')) {
                $owner = $receiver;
            }

            try {
                if ($owner) {
                    $mail = (object)null;
                    $mail->from_name = 'FastCash Support';
                    $mail->from_email = '<EMAIL>';
                    $mail->to_name = str_replace(',', '', $owner->name);
                    $mail->to_email = $owner->email;
                    $mail->template = 'email.support';
                    $mail->subject = 'FastCash Support - Ticket (#' . $support->id . ') Closed';

                    if ($owner->role == 'user' || $user->role == 'u-merchant') {
                        $mail->login = env('MAIN_URL');
                    } else {
                        $mail->login = '';
                    }

                    $mail->from_support = false;
                    $mail->content = 'ticket-closed';

                    $mail->support_id = $support->id;
                    $mail->support_url = env('MAIN_URL') . '/support?tid=' . $support->id;

                    $mail->hasCC = true;
                    $mail->cc_email = '<EMAIL>';

                    $send = (new MailHandler())->sendMail($mail);
                }
            } catch (\Exception $e) {
                Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            }

            $support->replies = Support::with('user')->where('reply_id', $support->reply_id)->orderBy('created_at', 'asc')->get();
            $support->receiver = User::where('id', $support->receiver_id)->first(['id', 'name']);

            return response()->json(compact('support'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'could not close ticket'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
