import * as types from './types';
import { httpRequest, baseURL, walletAPI } from '../config/constants';

export const loadLenders = data => {
	return {
		type: types.LOAD_LENDERS,
		payload: data,
	};
};

export const addLender = data => {
	return {
		type: types.ADD_LENDER,
		payload: data,
	};
};

export const setLenderProfile = data => {
	return {
		type: types.SET_LENDER_PROFILE,
		payload: data,
	};
};

export const editLender = data => {
	return {
		type: types.EDIT_LENDER_PROFILE,
		payload: data,
	};
};

export const approveDeposit = data => {
    return (dispatch) => {
        return httpRequest(`${baseURL}${walletAPI}/approve/${data.id}`, 'POST', true, data);
    };
};

export const declineDeposit = data => {
    return (dispatch) => {
        return httpRequest(`${baseURL}${walletAPI}/${data.id}`, 'DELETE', true, data);
    };
};
