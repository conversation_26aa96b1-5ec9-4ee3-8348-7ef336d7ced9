import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, change, formValueSelector, untouch } from 'redux-form';
import Modal from 'react-modal';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import range from 'lodash.range';
import qs from 'querystring';

import loading from '../assets/img/loading.gif';
import { calculateNewLoan, setTenure, calculateLoan, setDisburseLoan, setMyMonthlyDeduction, setMyAmount, setMyMonthlyPrincipal, setMyMonthlyInterest } from '../actions/loan';
import OfferLetter from './OfferLetter';

const validate = values => {
    const errors = {};
    if (!values.amount) {
        errors.amount = 'Enter amount';
    }
    if (values.amount < 1000) {
        errors.amount = 'You should enter an amount greater than 1,000.00';
    }
    if (!values.tenure) {
        errors.type = 'Select tenure';
    }
    if (!values.account_number) {
        errors.type = 'Select account number';
    }
    if (!values.ointerest_rate) {
        errors.ointerest_rate = 'Enter interest rate';
    }
    if (!values.insurance) {
        errors.insurance = 'Enter insurance';
    }
    if (!values.processing_fee) {
        errors.processing_fee = 'Enter processing fee';
    }
    return errors;
};

const renderCheckbox = ({input, label, id, meta: {touched, error}}) => (
    <div className={`form-check ${(touched && error && 'has-error')}`}>
        <label htmlFor={id} className="form-check-label">
            <input {...input} id={id} type="checkbox" className="form-check-input" />{label}
        </label>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

const renderIGField = ({input, id, label, type, icon, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label className="lighter" htmlFor={id}>{label}</label>
        <div className="input-group mb-2 mr-sm-2 mb-sm-0">
            <input {...input} placeholder={label} type={type} className="form-control" />
            <div className="input-group-addon">{icon}</div>
        </div>
        {touched &&
            ((error && <span className="help-block">{error}</span>) ||
                (warning && <span className="help-block">{warning}</span>))}
    </div>
);

const renderField = ({input, id, label, type, disabled, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label className="lighter" htmlFor={id}>{label}</label>
        <div className="input-group mb-2 mr-sm-2 mb-sm-0">
            <input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
        </div>
        {touched &&
            ((error && <span className="help-block">{error}</span>) ||
                (warning && <span className="help-block">{warning}</span>))}
    </div>
);

const renderSelectField = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label className="lighter">{label}</label>
        <select {...input} className="form-control">
            {data.map(t => <option value={t} key={t}>{t}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

const renderSelectFieldA = ({input, label, data, meta: {touched, error}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <label className="lighter">{label}</label>
        <select {...input} className="form-control">
            {data.map((t, i) => <option value={t.id} key={i}>{t.value}</option>)}
        </select>
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class LoanForm extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
            tenure: this.props.tenure,
            modalIsOpen: false,
            loanData: null,
			errMessage: '',
			backUrl: '',
        };

        this.openModal = this.openModal.bind(this);
        this.closeModal = this.closeModal.bind(this);
        this.updateErrorMessage = this.updateErrorMessage.bind(this);
		this.loanCompleted = this.loanCompleted.bind(this);
    }
	
	componentWillMount() {
		Modal.setAppElement('body');
    }
    
    componentDidMount() {
        const { location } = this.props;
        const query = qs.parse(location.search.replace('?', ''));
        if(query && query._url){
            this.setState({ backUrl: query._url });
        }
    }
    
    openModal() {
        this.setState({ modalIsOpen: true });
    }

    closeModal = () => {
        this.setState({ modalIsOpen: false });
        document.body.className="pages";
    }

    loanCompleted = () => {
        this.closeModal();
        const { office, user } = this.props;
        setTimeout(() => {
            this.props.history.push(`/offices/view/${office.id}?search=${user.phone}&p=1&office=${office.name}`);
        }, 200);
    };
    
    updateLoanAmount = e => {
        const { tenure } = this.state;
        const { currentLoan, topup, lender, override } = this.props;
        const amount = e.target.value || 0;
        this.props.calculateNewLoan(amount, tenure, lender.interest_rate, lender, override);

        if(override && amount > 0){
            const principal = (amount / tenure).toFixed(2);
            this.props.dispatch(change('takeloan', 'omonthly_principal', principal));
        }

        if(topup === 1){
            const disburseAmount = amount - (parseFloat(currentLoan.monthly_principal) * (currentLoan.tenure - currentLoan.total_paid)) - parseFloat(currentLoan.monthly_interest);
            this.props.setDisburseLoan(disburseAmount < 0 ? 0 : disburseAmount);
        }
    };

    updateLoanTenure = e => {
        const tenure = e.target.value;
        this.setState({ tenure });
        const { netEarning, platform, lender, override } = this.props;
        this.props.setTenure(tenure);
        if(platform !== 'sme'){
            const percent = platform === 'ippis' ? 0.33 : 0.4;
            this.props.calculateLoan(netEarning, tenure, lender.interest_rate, percent);
        }
        this.props.calculateNewLoan(0, this.state.tenure, lender.interest_rate, lender, override);
        this.props.setDisburseLoan(0);
        this.props.setMyMonthlyDeduction(0);
        this.props.dispatch(change('takeloan', 'amount', ''));
        this.props.dispatch(untouch('takeloan', 'amount'));
        if(override) {
            this.props.dispatch(change('takeloan', 'omonthly_principal', ''));
            this.props.dispatch(untouch('takeloan', 'omonthly_principal'));
            this.props.dispatch(change('takeloan', 'omonthly_interest', ''));
            this.props.dispatch(untouch('takeloan', 'omonthly_interest'));
            this.props.dispatch(change('takeloan', 'ointerest_rate', ''));
            this.props.dispatch(untouch('takeloan', 'ointerest_rate'));
        }
        this.updateErrorMessage('');
    };

    updateMonthlyPrincipal = e => {
        const { override, o_interest } = this.props;
        if(override){
            const principal = e.target.value;
            this.props.setMyMonthlyPrincipal(principal);
            const mdeduction = parseFloat(principal) + parseFloat(o_interest);
            this.props.setMyMonthlyDeduction(mdeduction);
        }
    };

    updateMonthlyInterest = e => {
        const { override, o_principal } = this.props;
        if(override){
            const interest = e.target.value;
            this.props.setMyMonthlyInterest(interest);
            const mdeduction = parseFloat(o_principal) + parseFloat(interest);
            this.props.setMyMonthlyDeduction(mdeduction);
        }
    };

    onChangeOverride = e => {
        this.props.setMyMonthlyDeduction(0);
        this.props.setMyAmount(0);
        this.props.dispatch(change('takeloan', 'amount', ''));
        this.props.dispatch(untouch('takeloan', 'amount'));
        this.props.dispatch(change('takeloan', 'omonthly_principal', ''));
        this.props.dispatch(untouch('takeloan', 'omonthly_principal'));
        this.props.dispatch(change('takeloan', 'omonthly_interest', ''));
        this.props.dispatch(untouch('takeloan', 'omonthly_interest'));
        this.props.dispatch(change('takeloan', 'ointerest_rate', ''));
        this.props.dispatch(untouch('takeloan', 'ointerest_rate'));
    };

    updateErrorMessage = message => {
        this.setState({ errMessage: message });
        if(message !== ''){
            this.closeModal();
        }
    };

    updateFocus = e => {
        this.updateErrorMessage('');
    }

    takeLoan = data => {
        const { eligibleAmt, user, myMonthlyDeductions, netEarning, monthlyInterest, monthlyPrincipal, earnings, platform, topup, old_loan, myDisburseAmount, monthlyCommission, override, lender, loanID, section, remita_id } = this.props;
        
        if(parseFloat(data.amount) > eligibleAmt && !override){
            this.updateErrorMessage('Error, you cannot take a loan above your eligible amount');
            return;
        }

        if(topup === 1 && myDisburseAmount === 0){
            this.updateErrorMessage('Error, your topup amount should be greater than your outstanding amount');
            return;
        }

        this.updateErrorMessage('');
        const status = 0;
        
        const details = { ...data, user_id: user.id, monthly_deduction: myMonthlyDeductions, status, net_earning: netEarning, net_earnings: JSON.stringify(earnings), interest_rate: override? data.ointerest_rate:lender.interest_rate, monthly_interest: monthlyInterest, monthly_principal: monthlyPrincipal, platform, auth_code: earnings.length > 0 ? earnings[0].auth_code : 0, topup, old_loan, disburse_amount: myDisburseAmount, monthly_commission: monthlyCommission, admin_request: 1, loan_id: loanID, section, remita_id };
        // console.log(JSON.stringify(details));
        this.setState({ loanData: details });
        document.body.className="pages modal-open";
        this.openModal();
        return;
	};
    
    render() {
        const { submitting, pristine, reset, handleSubmit, user, accountNumbers, bankAccounts, override, lender } = this.props;
        const { loanData, modalIsOpen, errMessage, backUrl } = this.state;
		const _maxLoanTenure = parseInt(lender.max_loan_tenure, 10) + 1;
		const tenures = range(lender.min_loan_tenure, _maxLoanTenure);
        return (
            <div className="element-box">
                {errMessage && <div className="alert alert-danger" style={{margin: '0', marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${errMessage}`}}></div>}
                <form onSubmit={handleSubmit(this.takeLoan)} autoComplete="off">
                    <h5 className="element-box-header">Loan Request Form</h5>
                    <div className="row">
                        <div className="col-sm-6">
                            <Field
                                name="amount"
                                id="amount"
                                type="number"
                                component={renderIGField}
                                label="Desired Amount"
                                onChange={this.updateLoanAmount}
                                onFocus={this.updateFocus}
                                icon="NGN"
                            />
                        </div>
                        <div className="col-sm-6">
                            <Field
                                name="tenure"
                                component={renderSelectField}
                                label="Repayment Tenure"
                                data={tenures}
                                onChange={this.updateLoanTenure}
                            />
                        </div>
                    </div>
                    {override && (
                        <div className="row">
                            <div className="col-sm-12">
                                <Field
                                    name="omonthly_principal"
                                    id="omonthly_principal"
                                    type="number"
                                    component={renderIGField}
                                    label="Monthly Principal"
                                    onChange={this.updateMonthlyPrincipal}
                                    icon="NGN"
                                />
                            </div>
                            <div className="col-sm-6">
                                <Field
                                    name="omonthly_interest"
                                    id="omonthly_interest"
                                    type="number"
                                    component={renderIGField}
                                    label="Monthly Interest"
                                    onChange={this.updateMonthlyInterest}
                                    icon="NGN"
                                />
                            </div>
                            <div className="col-sm-6">
                                <Field
                                    name="ointerest_rate"
                                    id="ointerest_rate"
                                    type="number"
                                    component={renderIGField}
                                    label="Interest Rate"
                                    icon="%"
                                />
                            </div>
                            <div className="col-sm-6">
                                <Field
                                    name="insurance"
                                    id="insurance"
                                    type="number"
                                    component={renderIGField}
                                    label="Insurance"
                                    icon="%"
                                />
                            </div>
                            <div className="col-sm-6">
                                <Field
                                    name="processing_fee"
                                    id="processing_fee"
                                    type="number"
                                    component={renderIGField}
                                    label="Processing Fee"
                                    icon="%"
                                />
                            </div>
                        </div>
                    )}
					<div className="row">
						<div className="col-sm-6">
							{accountNumbers.length > 0 ? (
                                <Field
									name="account_number"
									component={renderSelectFieldA}
									data={accountNumbers}
									label="Account Number"
								/>
                            ) : (
                                <Field
									name="acnum"
									id="acnum"
									type="text"
									component={renderField}
									label="Account Number"
									disabled="disabled"
								/>
                            )}
						</div>
					</div>
					<div className="row">
						<div className="col-sm-12">
                            <div className="form-check">
                                <Field
                                    name="override"
                                    id="override"
                                    component={renderCheckbox}
                                    label={'Override Eligible Amount and Interest'}
                                    onChange={this.onChangeOverride}
                                />
                            </div>
						</div>
					</div>
                    <div className="form-buttons-w text-right compact">
                        {backUrl !== '' && <Link className="btn btn-grey" to={backUrl}>Go Back</Link>}
                        <button className="btn btn-grey" type="button" onClick={reset}>
                            <span>Clear</span>
                        </button>
                        <button className="mr-2 btn btn-primary" disabled={pristine || submitting} type="submit">
                            {submitting? <img src={loading} alt=""/> : 'Apply'}
                        </button>
                    </div>
                </form>
				{modalIsOpen && (
                    <Modal
						isOpen={modalIsOpen}
						onRequestClose={this.closeModal}
						contentLabel="Offer Letter"
						shouldCloseOnOverlayClick={false}
						overlayClassName="modal-dialog modal-ol"
						className="modal-content"
					>
						<OfferLetter
							loanData={loanData}
							closeModal={() => this.closeModal()}
							user={{...user, bankAccounts}}
							updateError={(msg) => this.updateErrorMessage(msg)}
							completed={() => this.loanCompleted()}
						/>
					</Modal>
                )}
                {modalIsOpen? (<div className="modal-backdrop fade show"/>) : ''}
            </div>
        );
    }
}

LoanForm = reduxForm({
    form: 'takeloan',
    validate,
})(LoanForm);

const selector = formValueSelector('takeloan');

const mapStateToProps = (state, ownProps) => {
	const amount = selector(state, 'amount');
    const account_number = ownProps.accountNumbers.length > 0 ? ownProps.accountNumbers[0].id : null;
    const override = selector(state, 'override');
    const o_principal = selector(state, 'omonthly_principal');
    const o_interest = selector(state, 'omonthly_interest');
    const o_interest_rate = selector(state, 'ointerest_rate');

    return {
        amount,
        override,
        o_principal,
        o_interest,
        o_interest_rate,
        myMonthlyDeductions: state.loan.myMonthlyDeductions,
		monthlyPrincipal: state.loan.monthlyPrincipal,
		monthlyInterest: state.loan.monthlyInterest,
		myDisburseAmount: state.loan.myDisburseAmount,
        initialValues: {
            tenure: ownProps.tenure,
			account_number,
			acnum: "None",
            insurance: ownProps.insurance,
            processing_fee: ownProps.processingFee,
		},
        earnings: state.loan.earnings,
        monthlyCommission: state.loan.monthlyCommission,
        office: ownProps.user.office,
        user: ownProps.user,
        lender: state.lender.profile,
        remita_id: state.loan.remita_id,
    }
};

export default withRouter(connect(mapStateToProps, { calculateNewLoan, setTenure, calculateLoan, setDisburseLoan, reset, change, untouch, setMyMonthlyDeduction, setMyAmount, setMyMonthlyPrincipal, setMyMonthlyInterest })(LoanForm));
