import React from 'react';

import Repayment from '../reports/Repayment';
import Loan from '../reports/Loan';
import Disbursement from '../reports/Disbursement';
import Wallet from '../reports/Wallet';
import CreditWallet from '../reports/CreditWallet';

const ReportView = ({ items, header, slug, skip, loading }) => {
	return (
		<table className="table table-striped table-lightfont">
			<thead>
				<tr>
					{header.map((item, i) => <th key={i}>{item}</th>)}
				</tr>
			</thead>
			<tbody>
				{!loading && slug === 'repayment' && <Repayment items={items} skip={skip}/>}
				{!loading && slug === 'loan' && <Loan items={items} skip={skip}/>}
				{!loading && slug === 'disbursed-loan' && <Disbursement items={items} skip={skip}/>}
				{!loading && slug === 'wallet' && <Wallet items={items} skip={skip}/>}
				{!loading && slug === 'credit-wallet' && <CreditWallet items={items} skip={skip}/>}
				{!loading && items.length === 0 && (
					<tr>
						<td colSpan={header.length}>
							<div className="text-center">No Report Found!</div>
						</td>
					</tr>
				)}
			</tbody>
		</table>
	);
};

export default ReportView;
