<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Setting;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use DB;
use Log;

class SettingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $lender = $request->has('lender') ? $request->input('lender') : '';

            if ($lender != '') {
                $settings = Setting::where('lender_id', 1)->get();
                return response()->json(['settings' => $settings], 200);
            }

            return response()->json(['error' => 'could not fetch settings'], 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'error, could not get settings'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'stop_loan_date' => 'required',
            'snooze_period' => 'required',
            'multifund_expire' => 'required',
        ]);
        if ($validator->fails()) {
            $error = validatorMessage($validator->errors());
            return response()->json(['error' => $error], 500);
        }

        DB::beginTransaction();
        try {
            $data = $request->all();
            foreach ($data as $key => $item) {
                Setting::where('name', $key)->where('lender_id', 1)->update(['value' => $item]);
            }

            DB::commit();

            $settings = Setting::where('lender_id', 1)->get();
            return response()->json($settings, 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not save settings, try again later'], 500);
        }
    }
}
