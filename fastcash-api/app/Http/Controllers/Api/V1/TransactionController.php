<?php

namespace App\Http\Controllers\Api\V1;

use App\Models\Lender;
use App\Models\Loan;
use App\Models\Office;
use App\Models\PaymentDate;
use App\Models\RemitaTransaction;
use App\Models\Schedule;
use App\Models\ScheduleHistory;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wallet;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\Remita;
use Log;
use DB;

class TransactionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        try {
            $lender = $request->has('lender') ? $request->input('lender') : '';
            $status = $request->has('status') ? $request->input('status') : '';

            if ($lender != '') {
                $limit = $request->has('pagesize') && $request->input('pagesize') != '' ? $request->input('pagesize') : 10;
                $page = $request->has('page') && $request->input('page') != '' ? $request->input('page') : 1;
                $skip = ($page - 1) * $limit;

                if ($status == '') {
                    $transactions = Transaction::where('lender_id', $lender)->where('approved', 1)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();
                } else {
                    $transactions = Transaction::where('lender_id', $lender)->where('status', $status)->where('approved', 1)->orderBy('created_at', 'desc')->skip($skip)->take($limit)->get();
                }

                $transactions = $transactions->map(function ($item, $key) use ($lender) {
                    $item->user = User::with('office', 'lender')->where('id', $item->user_id)->first(['id', 'name', 'platform', 'phone', 'ippis', 'customer_id']);
                    $item->loan = Loan::where('id', $item->loan_id)->first(['id', 'liquidate_approve']);
                    $item->office = Office::where('id', $item->office_id)->first();
                    return $item;
                });

                $t_count = Transaction::where('lender_id', $lender)->where('approved', 1)->count();

                $all_transactions = Transaction::where('lender_id', $lender)->get(['office_id']);
                $office_ids = [];
                foreach ($all_transactions as $loan) {
                    $office_ids[] = $loan->office_id;
                }
                $offices = Office::find($office_ids);

                $result = (object)null;
                $result->total = $t_count;
                $result->size = $limit;
                $result->page = $page;
                $result->data = $transactions;
                $result->offices = $offices;

                return response()->json(compact('result'), 200);
            }

            return response()->json(['error' => 'could not fetch transactions'], 400);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'could not fetch transactions'], 400);
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @param $id
     *
     * @return \Illuminate\Http\Response
     */
    public function loan($id)
    {
        try {
            $transactions = Transaction::withTrashed()->where('loan_id', $id)->get();
            $transactions = $transactions->map(function ($item, $key) {
                $item->loan = Loan::where('id', $item->loan_id)->first(['liquidate_approve', 'is_topup']);

                return $item;
            });

            return response()->json(compact('transactions'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            $transactions = [];
            return response()->json(compact('transactions'), 500);
        }
    }

    /**
     * Check repayments from remita.
     *
     * DEPRECATED
     *
     * @return array
     */
    public function checkPayments()
    {
        DB::beginTransaction();
        try {
            $transactions = [];

            $loans = Loan::where('status', 1)->where('liquidated', 0)->where('platform', 'remita')->get();
            foreach ($loans as $l) {
                $loan = Loan::with('user')->where('id', $l->id)->first();
                if ($loan) {
                    $approved = json_decode($loan->approved_remita);

                    $merchantId = env('MERCHANT_ID');
                    $apiKey = env('API_KEY');
                    $apiToken = env('API_TOKEN');

                    $requestId = time() * 1000;
                    $apiHash = hash('sha512', $apiKey . $requestId . $apiToken);
                    $authorization = "remitaConsumerKey=" . $apiKey . ", remitaConsumerToken=" . $apiHash;

                    $body = [
                        "authorisationCode" => $loan->auth_code,
                        "customerId" => $loan->user->customer_id,
                        "mandateReference" => $approved->data->mandateReference,
                    ];

                    $rn = (new Remita())->checkRepayments($merchantId, $apiKey, $requestId, $authorization, $body);

                    $status = $rn == null ? 'failed' : ($rn->responseCode == '00' ? 'success' : $rn->responseMsg);

                    logAPI('repayment', $loan->user->phone, $status);

                    if ($rn != null) {
                        if ($rn->responseCode != '00') {
                            Log::error('failed loan repayments:-> loan_id: ' . $loan->id . ' --- ' . json_encode($rn) . ' --- ' . json_encode($body));
                        } else {
                            $deductions = $rn->data->repayment;

                            if ($deductions != null) {
                                foreach ($deductions as $deduction) {
                                    //error_log('loan: id: '.$loan->id.' ---- '.json_encode($deduction));
                                    if ($deduction->paymentstatus == 'paid' || $deduction->paymentstatus == 'tpaid') {
                                        $transaction = Transaction::where('deduction_date', $deduction->deductiondate)->first();

                                        if (!$transaction) {
                                            $item = Transaction::create([
                                                'user_id' => $loan->user_id,
                                                'loan_id' => $loan->id,
                                                'office_id' => $loan->office_id,
                                                'lender_id' => $loan->lender_id,
                                                'source' => strtoupper($loan->platform),
                                                'amount' => $deduction->transactionamount,
                                                'status' => 'Pending',
                                                'transaction_flag' => 'deduction',
                                                'reference' => null,
                                                'channel' => 'remita-repayment',
                                                'approved' => 0,
                                                'repayment_date' => date('Y-m-d'),
                                                'principal' => $deduction->transactionamount - $loan->monthly_interest,
                                                'interest' => $loan->monthly_interest,
                                                'outst_amount' => calcOutstRemita($loan->id, $loan->total_deduction, 0)
                                            ]);

                                            $notify = doNotify($item->user_id, 'transactions', $item->id, 'remita_loan_repayment', 'success', null, $loan->lender_id, null);

                                            $transactions[] = $item->id;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            DB::commit();
            return ['done' => $transactions];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return null;
        }
    }

    /**
     * Display a listing of the resource
     *
     * DEPRECATED
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function repayment(Request $request)
    {
        DB::beginTransaction();
        try {
            $input = $request->all();
            $user = User::where('customer_id', $input['customer_id'])->first();
            if ($user) {
                $loan = Loan::find($user->loan_id);
                if ($loan) {
                    $transaction = Transaction::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'office_id' => $loan->office_id,
                        'lender_id' => $loan->lender_id,
                        'source' => strtoupper($loan->platform),
                        'amount' => $input['amount'],
                        'status' => 'Pending',
                        'reference' => json_encode($input),
                        'channel' => 'remita-repayment',
                        'transaction_flag' => 'deduction',
                        'approved' => 0,
                        'repayment_date' => date('Y-m-d'),
                        'principal' => $input['amount'] - $loan->monthly_interest,
                        'interest' => $loan->monthly_interest,
                        'outst_amount' => calcOutstRemita($loan->id, $loan->total_deduction, 0)
                    ]);

                    $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'remita_loan_repayment', 'success', null, $loan->lender_id, null);

                    if ($transaction) {
                        $data = (object)null;
                        $data->response_code = '00';
                        $data->response_descr = 'Request ack ok';
                        $data->ack_id = $loan->id;

                        //DB::commit();
                        return response()->json($data, 200);
                    }
                }
            }

            DB::rollBack();
            return response()->json(null, 500);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(null, 500);
        }
    }

    /**
     * Pay over repayment to wallet.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function wallet(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $input = $request->all();
            error_log('--------------------');

            $transaction = Transaction::where('id', $id)->where('approved', '<', 0)->first();
            $transaction->approved = 1;
            $transaction->approved_at = date('Y-m-d H:i:s');
            $transaction->approved_by = $input['staff_id'];
            $transaction->status = 'Completed';

            $user_lender = User::where('lender_id', $transaction->lender_id)->whereNull('ippis')->whereNotNull('username')->first();

            $staff = $input['staff_id'];

            // get amount from wallet
            $wallet_amount = Wallet::where('user_id', $transaction->user_id)->where('is_lender', 0)->where('approved', 1)->where('paid', 1)->sum('amount');

            // loan from transaction
            $loan = Loan::where('id', $transaction->loan_id)->first();

            // number of payments not made
            $pays = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)->count();

            // total amount paid
            $total_paid = Transaction::where('loan_id', $transaction->loan_id)->where(function ($query) {
                $query->where('transaction_flag', 'deduction')->orWhere('transaction_flag', 'liquidate');
            })->where('approved', 1)->sum('amount');

            //$is_liquidated = Loan::where('loan_id', $transaction->loan_id)->where('transaction_flag', 'liquidate')->where('approved', 1)->first();

            error_log('amount: ' . $transaction->amount);
            error_log('total deduction: ' . $loan->total_deduction);
            error_log('total paid: ' . $total_paid);

            $repay_deduction = null;
            $overpayment_amount = 0;

            $is_liquidated = Loan::where('id', $transaction->loan_id)->where('liquidate_approve', 1)->first();

            if ($loan->total_deduction > $total_paid && !$is_liquidated) {
                // check for outstanding deductions from current loan
                error_log('outstanding repayment found');

                $outstanding_amount = $loan->total_deduction - $total_paid;
                error_log('outstanding amount: ' . $outstanding_amount);

                if ($outstanding_amount > $transaction->amount) {
                    // credit user wallet to repay loan
                    $wallet1 = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'credit',
                        'amount' => $transaction->amount,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => 1,
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'paid' => 1,
                    ]);

                    // debit user account
                    $wallet = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'debit',
                        'amount' => $transaction->amount * -1,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => 1,
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'paid' => 1,
                    ]);

                    // credit lender
                    $wallet2 = Wallet::create([
                        'user_id' => $user_lender ? $user_lender->id : $input['staff_id'],
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'credit',
                        'amount' => $transaction->amount,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => 1,
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 1,
                        'paid' => 1,
                    ]);

                    $notify = doNotify($wallet->user_id, 'wallets', $wallet->id, 'repay_loan', 'success', null, $wallet->lender_id, $staff);

                    $transaction->outst_amount = $loan->total_deduction - ($total_paid + $transaction->amount);
                    $transaction->principal = $transaction->amount - $loan->monthly_interest;
                } else {
                    $transaction_amount = $transaction->amount;
                    $overpayment_amount = $transaction->amount - $outstanding_amount;

                    // credit user wallet to repay loan
                    $wallet1 = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'credit',
                        'amount' => $outstanding_amount,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => 1,
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'paid' => 1,
                    ]);

                    // debit user account
                    $wallet = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'debit',
                        'amount' => $outstanding_amount * -1,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => 1,
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'paid' => 1,
                    ]);

                    // credit lender
                    $wallet2 = Wallet::create([
                        'user_id' => $user_lender ? $user_lender->id : $input['staff_id'],
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'credit',
                        'amount' => $outstanding_amount,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => 1,
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 1,
                        'paid' => 1,
                    ]);

                    $notify = doNotify($wallet->user_id, 'wallets', $wallet->id, 'repay_loan', 'success', null, $wallet->lender_id, $staff);

                    if ($overpayment_amount > 0) {
                        // repayment
                        $repay_deduction = Transaction::create([
                            'user_id' => $loan->user_id,
                            'loan_id' => $loan->id,
                            'office_id' => $loan->office_id,
                            'lender_id' => $loan->lender_id,
                            'source' => strtoupper($loan->platform),
                            'amount' => $overpayment_amount,
                            'status' => 'Completed',
                            'reference' => null,
                            'channel' => 'credit-wallet-repayment',
                            'transaction_flag' => 'credit-wallet',
                            'approved' => 1,
                            'repayment_source' => $transaction->repayment_source,
                            'uploaded_by' => $staff,
                            'repayment_date' => $transaction->repayment_date,
                            'principal' => $loan->monthly_principal,
                            'interest' => $loan->monthly_interest,
                            'outst_amount' => 0.00,
                            'description' => 'Excess Repayment',
                            'approved_at' => date('Y-m-d H:i:s'),
                            'approved_by' => $staff,
                        ]);
                    }

                    $transaction->amount = $outstanding_amount;
                    $transaction->amount_paid = $transaction_amount;
                    $transaction->outst_amount = calcOutstRemita($loan->id, $loan->total_deduction, $outstanding_amount);
                    $transaction->principal = $outstanding_amount - $loan->monthly_interest;
                }

                $month = date('F');
                $pay = PaymentDate::where('paid', 0)->where('loan_id', $loan->id)->first();
                if ($pay) {
                    $month = Carbon::createFromFormat('Y-m-d H:i:s', $pay->payment_date)->format('F');
                }

                // update transaction
                $transaction->interest = $loan->monthly_interest;
                $transaction->description = $pays > 0 ? strtoupper($transaction->repayment_source) . ' Deduction for the Month of ' . $month : 'Excess Deduction';
                $transaction->save();

                if ($pay) {
                    $pay->paid = 1;
                    $pay->paid_at = date('Y-m-d H:i:s');
                    $pay->transaction_id = $transaction->id;
                    $pay->save();
                }

                if ($loan->liquidated == 0) {
                    error_log('liquidate now and continue');
                    $liquidated = liquidateLoan($loan->id);
                }
            } else {
                $overpayment_amount = $transaction->amount;

                // update transaction
                $transaction->outst_amount = 0;
                $transaction->transaction_flag = 'credit-wallet';
                $transaction->description = 'Excess Repayment';
                $transaction->save();
            }

            $_transaction = $repay_deduction ? $repay_deduction : $transaction;

            if ($overpayment_amount > 0) {
                // credit user wallet for over deduction
                $wallet1 = Wallet::create([
                    'user_id' => $_transaction->user_id,
                    'loan_id' => $_transaction->loan_id,
                    'category' => 'repay_deduction',
                    'type' => 'credit',
                    'amount' => $overpayment_amount,
                    'transaction_id' => $_transaction->id,
                    'approved' => 1,
                    'approved_by' => $input['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $_transaction->lender_id,
                    'is_lender' => 0,
                    'paid' => 1,
                ]);

                // credit lender
                $wallet2 = Wallet::create([
                    'user_id' => $user_lender ? $user_lender->id : $input['staff_id'],
                    'loan_id' => $_transaction->loan_id,
                    'category' => 'repay_deduction',
                    'type' => 'credit',
                    'amount' => $overpayment_amount,
                    'transaction_id' => $_transaction->id,
                    'approved' => 1,
                    'approved_by' => $input['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $_transaction->lender_id,
                    'is_lender' => 1,
                    'paid' => 1,
                ]);

                // debit lender
                $wallet2 = Wallet::create([
                    'user_id' => $user_lender ? $user_lender->id : $input['staff_id'],
                    'loan_id' => $_transaction->loan_id,
                    'category' => 'repay_deduction',
                    'type' => 'debit',
                    'amount' => $overpayment_amount * -1,
                    'transaction_id' => $_transaction->id,
                    'approved' => 1,
                    'approved_by' => $input['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $_transaction->lender_id,
                    'is_lender' => 1,
                    'paid' => 1,
                ]);

                $notify = doNotify($wallet1->user_id, 'wallets', $wallet1->id, 'repay_deduction', 'success', null, $_transaction->lender_id, $input['staff_id']);

                // update total amount
                $wallet_amount += $overpayment_amount;
            }

            // check if user has an active loan
            $user = User::where('id', $transaction->user_id)->first(['id', 'loan_id']);
            if ($user->loan_id && $wallet_amount > 0) {
                error_log('active loan found');
                $current_loan = Loan::where('id', $user->loan_id)->where('disbursed', 1)->first();
                if ($current_loan) {
                    error_log('loan found:' . $current_loan->id);

                    $date = Carbon::now()->endOfMonth()->format('Y-m-d');
                    error_log('date:' . $date);

                    // check for default or current debit
                    $not_paid = PaymentDate::where('loan_id', $current_loan->id)->where('paid', 0)->whereDate('payment_date', '<=', $date)->count();
                    error_log('not repaid: ' . $not_paid);

                    if ($not_paid > 0) {
                        $pay = PaymentDate::where('loan_id', $current_loan->id)->where('paid', 0)->whereDate('payment_date', '<=', $date)->first();
                        if ($pay) {
                            $payment_amount = $wallet_amount >= $current_loan->monthly_deduction ? $current_loan->monthly_deduction : $wallet_amount;

                            $month = Carbon::createFromFormat('Y-m-d H:i:s', $pay->payment_date)->format('F');

                            $repay = Transaction::create([
                                'user_id' => $current_loan->user_id,
                                'loan_id' => $current_loan->id,
                                'office_id' => $current_loan->office_id,
                                'lender_id' => $current_loan->lender_id,
                                'source' => strtoupper($current_loan->platform),
                                'amount' => $payment_amount,
                                'status' => 'Completed',
                                'reference' => null,
                                'channel' => 'wallet-repayments',
                                'transaction_flag' => 'deduction',
                                'approved' => 1,
                                'repayment_source' => $transaction->repayment_source,
                                'uploaded_by' => $staff,
                                'repayment_date' => date('Y-m-d H:i:s'),
                                'principal' => $payment_amount - $current_loan->monthly_interest,
                                'interest' => $current_loan->monthly_interest,
                                'outst_amount' => calcOutstRemita($current_loan->id, $current_loan->total_deduction, $payment_amount),
                                'description' => strtoupper($current_loan->platform) . ' Rpmt From Wallet for the Month of ' . $month,
                                'approved_at' => date('Y-m-d H:i:s')
                            ]);

                            // check for pending debit
                            $w_debit = Wallet::where('user_id', $current_loan->user_id)->where('loan_id', $current_loan->id)->where('type', 'debit')->where('category', 'repay_loan')->whereNull('transaction_id')->first();
                            if ($w_debit) {
                                $w_debit->transaction_id = $repay->id;
                                $w_debit->amount = $payment_amount * -1;
                                $w_debit->approved_by = $staff;
                                $w_debit->paid = 1;
                                $w_debit->save();
                            } else {
                                // debit user account
                                $w_debit = Wallet::create([
                                    'user_id' => $current_loan->user_id,
                                    'loan_id' => $current_loan->id,
                                    'category' => 'repay_loan',
                                    'type' => 'debit',
                                    'amount' => $payment_amount * -1,
                                    'transaction_id' => $repay->id,
                                    'approved' => 1,
                                    'approved_by' => 1,
                                    'approved_at' => date('Y-m-d H:i:s'),
                                    'lender_id' => $current_loan->lender_id,
                                    'is_lender' => 0,
                                    'payment_date' => $pay->payment_date,
                                    'paid' => 1,
                                ]);
                            }

                            // credit lender
                            $wallet2 = Wallet::create([
                                'user_id' => $user_lender ? $user_lender->id : $input['staff_id'],
                                'loan_id' => $current_loan->id,
                                'category' => 'repay_loan',
                                'type' => 'credit',
                                'amount' => $payment_amount,
                                'transaction_id' => $repay->id,
                                'approved' => 1,
                                'approved_by' => $staff,
                                'approved_at' => date('Y-m-d H:i:s'),
                                'lender_id' => $current_loan->lender_id,
                                'is_lender' => 1,
                                'paid' => 1,
                            ]);

                            $notify = doNotify($w_debit->user_id, 'wallets', $w_debit->id, 'repay_loan', 'success', null, $w_debit->lender_id, $staff);

                            $pay->paid = 1;
                            $pay->paid_at = date('Y-m-d H:i:s');
                            $pay->transaction_id = $repay->id;
                            $pay->save();

                            $liquidated = liquidateLoan($current_loan->id);

                            $notify = doNotify($repay->user_id, 'transactions', $repay->id, 'wallet_repayment', 'success', null, $current_loan->lender_id, $staff);
                        }
                    }
                }
            }

            DB::commit();

            $details = getFinancialOverview($transaction->lender_id, $transaction->lender_id);

            // all un approved transactions
            $unapproved_transactions = Transaction::where('approved', '<', 1)->whereNull('description')->count();

            return response()->json(['transaction' => $transaction, 'loan_details' => $details, 'unapproved_transactions' => $unapproved_transactions], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'failed to add to wallet'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            $input = $request->all();

            $loan = Loan::with('user')->where('id', $input['loan_id'])->first();

            if (!$loan) {
                return response()->json(['error' => 'loan not found'], 500);
            }

            // if ($loan->monthly_deduction > $input['amount'] || $loan->monthly_deduction < $input['amount']) {
            //     return response()->json(['error' => 'repayment amount is wrong, please enter the monthly deduction amount'], 500);
            // }

            $repayment_source = strtolower($input['repayment_source']);

            $pays = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)->get();

            if (($loan && $loan->liquidate_approve == 1) || count($pays) == 0) {
                $check = Transaction::where('repayment_reference', $input['repayment_reference'])->first();
                if ($check && $repayment_source != 'ippis') {
                    return response()->json(['error' => 'repayment reference already exist'], 500);
                }

                $transaction = Transaction::create([
                    'user_id' => $loan->user_id,
                    'loan_id' => $loan->id,
                    'office_id' => $loan->office_id,
                    'lender_id' => $loan->lender_id,
                    'source' => strtoupper($loan->platform),
                    'amount' => $input['amount'],
                    'status' => 'Pending',
                    'reference' => null,
                    'channel' => 'staff-repayment',
                    'transaction_flag' => 'deduction',
                    'approved' => -1,
                    'repayment_source' => $repayment_source,
                    'uploaded_by' => $input['staff_id'],
                    'repayment_date' => $input['repayment_date'],
                    'principal' => $input['amount'] - $loan->monthly_interest,
                    'interest' => $loan->monthly_interest,
                    'outst_amount' => calcOutstRemita($loan->id, $loan->total_deduction, 0)
                ]);

                info('loans has been fully repaid: ' . $loan->id . ' transaction: ' . $transaction->id);
            } else {
                if ($repayment_source == 'ippis') {
                    $transaction = Transaction::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'office_id' => $loan->office_id,
                        'lender_id' => $loan->lender_id,
                        'source' => strtoupper($loan->platform),
                        'amount' => $input['amount'],
                        'repayment_reference' => $input['repayment_reference'],
                        'repayment_date' => $input['repayment_date'],
                        'repayment_source' => 'ippis',
                        'uploaded_by' => $input['staff_id'],
                        'status' => 'Pending',
                        'transaction_flag' => 'deduction',
                        'channel' => 'staff-repayment',
                        'approved' => 0,
                        'reference' => null,
                        'principal' => $input['amount'] - $loan->monthly_interest,
                        'interest' => $loan->monthly_interest,
                        'outst_amount' => calcOutstRemita($loan->id, $loan->total_deduction, 0)
                    ]);
                } else {
                    $approved = json_decode($loan->approved_remita);
                    $mandate_ref = $approved->data->mandateReference;

                    $remita = RemitaTransaction::where('repayment_ref', $input['repayment_reference'])->where('mandate_reference', $mandate_ref)->first();

                    if ($remita == null) {
                        return response()->json(['error' => 'repayment reference not found!'], 500);
                    }

                    $check = Transaction::where('repayment_reference', $input['repayment_reference'])->first();
                    if ($check) {
                        $transaction = $check;
                    } else {
                        $transaction = Transaction::where('remita_transaction_id', $remita->id)->where('approved', 0)->first();
                    }

                    if (!$transaction) {
                        return response()->json(['error' => 'transaction not found!'], 500);
                    }

                    $ref = null;

                    $amount = $input['amount'];
                    $outAmount = calcOutstRemita($loan->id, $loan->total_deduction, $amount);

                    if (count($pays) == 1 && $outAmount <= 0) {
                        $merchantId = env('MERCHANT_ID');
                        $apiKey = env('API_KEY');
                        $apiToken = env('API_TOKEN');

                        $requestId = time() * 1000;
                        $apiHash = hash('sha512', $apiKey . $requestId . $apiToken);
                        $authorization = "remitaConsumerKey=" . $apiKey . ", remitaConsumerToken=" . $apiHash;

                        $body = [
                            "authorisationCode" => $loan->auth_code,
                            "customerId" => $loan->user->customer_id,
                            "mandateReference" => $mandate_ref,
                        ];

                        try {
                            $sl = (new Remita())->stopLoanCollection($merchantId, $apiKey, $requestId, $authorization, $body);

                            $status = $sl == null ? 'failed' : ($sl->responseCode == '00' ? 'success' : $sl->responseMsg);

                            logAPI('stop-loan', $loan->user->phone, $status);

                            if ($sl != null) {
                                info('stop loan:-> ' . json_encode($sl));
                                if ($sl->responseCode != '00') {
                                    Log::error('TransactionController.php Line 756 stop loan:-> ' . json_encode($sl));
                                } else {
                                    $ref = json_encode($sl);
                                }
                            }
                        } catch (\Exception $e) {
                            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
                        }
                    }

                    // update
                    $remita->bank_repayment_ref = $input['repayment_reference'];
                    $remita->save();

                    // calculate month
                    $month = date('F');
                    $pay = PaymentDate::where('paid', 0)->where('loan_id', $loan->id)->first();
                    if ($pay) {
                        $month = Carbon::createFromFormat('Y-m-d H:i:s', $pay->payment_date)->format('F');
                    }

                    $transaction->approved = 1;
                    $transaction->approved_by = $input['staff_id'];
                    $transaction->approved_at = date('Y-m-d H:i:s');
                    $transaction->description = strtoupper($repayment_source) . ' Deduction for the Month of ' . $month;
                    $transaction->status = 'Completed';
                    $transaction->outst_amount = $outAmount;
                    $transaction->repayment_date = $input['repayment_date'];
                    $transaction->reference = $ref;
                    $transaction->save();

                    $w_debit = Wallet::where('user_id', $loan->user_id)->where('loan_id', $loan->id)->where('type', 'debit')->where('category', 'repay_loan')->whereNull('transaction_id')->first();
                    if ($w_debit) {
                        $w_debit->transaction_id = $transaction->id;
                        $w_debit->amount = $input['amount'] * -1;
                        $w_debit->approved_by = $input['staff_id'];
                        $w_debit->paid = 1;
                        $w_debit->save();
                    } else {
                        // debit user account
                        $w_debit = Wallet::create([
                            'user_id' => $loan->user_id,
                            'loan_id' => $loan->id,
                            'category' => 'repay_loan',
                            'type' => 'debit',
                            'amount' => $input['amount'] * -1,
                            'transaction_id' => $transaction->id,
                            'approved' => 1,
                            'approved_by' => 1,
                            'approved_at' => date('Y-m-d H:i:s'),
                            'lender_id' => $loan->lender_id,
                            'is_lender' => 0,
                            'payment_date' => $pay->payment_date,
                            'paid' => 1,
                        ]);
                    }

                    // credit user wallet to repay loan
                    $wallet1 = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'credit',
                        'amount' => $input['amount'],
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $input['staff_id'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'paid' => 1,
                    ]);

                    // credit lender
                    $wallet2 = Wallet::create([
                        'user_id' => $input['staff_id'],
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'credit',
                        'amount' => $input['amount'],
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $input['staff_id'],
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 1,
                        'paid' => 1,
                    ]);

                    $notify = doNotify($w_debit->user_id, 'wallets', $w_debit->id, 'repay_loan', 'success', null, $w_debit->lender_id, $input['staff_id']);

                    $liquidated = liquidateLoan($loan->id);

                    if ($pay) {
                        $pay->paid = 1;
                        $pay->paid_at = date('Y-m-d H:i:s');
                        $pay->transaction_id = $transaction->id;
                        $pay->save();
                    }

                    $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'repayment_approved', 'success', null, $loan->lender_id, $input['staff_id']);
                }
            }

            $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'loan_repayment', 'success', 'from ' . $input['repayment_source'], $loan->lender_id, $input['staff_id']);

            DB::commit();

            return response()->json(compact('transaction'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            return response()->json(['error' => 'repayment failed'], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function approve(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $input = $request->all();

            $transaction = Transaction::where('id', $id)->first();
            $loan = Loan::where('id', $transaction->loan_id)->where('liquidate_approve', 0)->first();

            if (!$loan) {
                return response()->json(['error' => 'loan has been liquidated'], 500);
            }

            $user = User::where('id', $loan->user_id)->first();

            $total_amount = Transaction::where('loan_id', $transaction->loan_id)->where('transaction_flag', 'deduction')->sum('amount');
            $remains = $loan->total_deduction - $total_amount;

            $pays = PaymentDate::where('loan_id', $loan->id)->where('paid', 0)->get();
            if (count($pays) == 0 && $remains <= 0) {
                return response()->json(['error' => 'loans has been fully repaid'], 500);
            }

            $outAmount = calcOutstRemita($loan->id, $loan->total_deduction, $transaction->amount);

            // calculate month
            $month = date('F');
            $pay = PaymentDate::where('paid', 0)->where('loan_id', $loan->id)->first();
            if ($pay) {
                $month = Carbon::createFromFormat('Y-m-d H:i:s', $pay->payment_date)->format('F');
            }

            $staff = $input['staff_id'];

            if ($transaction->channel == 'remita-repayments') {
                $transaction->approved = 1;
                $transaction->approved_by = $input['staff_id'];
                $transaction->approved_at = date('Y-m-d H:i:s');
                $transaction->description = strtoupper('REMITA') . ' Deduction for the Month of ' . $month;
                $transaction->status = 'Completed';
                $transaction->principal = $transaction->amount - $loan->monthly_interest;
                $transaction->interest = $loan->monthly_interest;
                $transaction->outst_amount = $outAmount;
                $transaction->save();

                $w_debit = Wallet::where('user_id', $loan->user_id)->where('loan_id', $loan->id)->where('type', 'debit')->where('category', 'repay_loan')->whereNull('transaction_id')->first();
                if ($w_debit) {
                    $w_debit->transaction_id = $transaction->id;
                    $w_debit->amount = $transaction->amount * -1;
                    $w_debit->approved_by = $input['staff_id'];
                    $w_debit->paid = 1;
                    $w_debit->save();
                } else {
                    // debit user account
                    $w_debit = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'debit',
                        'amount' => $transaction->amount * -1,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $staff,
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'payment_date' => $pay->payment_date,
                        'paid' => 1,
                    ]);
                }

                // credit user wallet to repay loan
                $wallet1 = Wallet::create([
                    'user_id' => $loan->user_id,
                    'loan_id' => $loan->id,
                    'category' => 'repay_loan',
                    'type' => 'credit',
                    'amount' => $transaction->amount,
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $input['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $loan->lender_id,
                    'is_lender' => 0,
                    'paid' => 1,
                ]);

                // credit lender
                $wallet2 = Wallet::create([
                    'user_id' => $loan->user_id,
                    'loan_id' => $loan->id,
                    'category' => 'repay_loan',
                    'type' => 'credit',
                    'amount' => $transaction->amount,
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $input['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $loan->lender_id,
                    'is_lender' => 1,
                    'paid' => 1,
                ]);

                $notify = doNotify($w_debit->user_id, 'wallets', $w_debit->id, 'repay_loan', 'success', null, $w_debit->lender_id, $staff);
            } else if ($transaction->channel == 'schedule-repayment') {
                $schedule = Schedule::where('id', $transaction->schedule_id)->first();
                $schedule->outst_loan_amt = $outAmount;
                $schedule->save();

                $transaction->status = 'Completed';
                $transaction->approved = 1;
                $transaction->approved_by = $input['staff_id'];
                $transaction->approved_at = date('Y-m-d H:i:s');
                $transaction->description = $transaction->repayment_source == 'remita' ? strtoupper($loan->platform) . ' Deduction for the Month of ' . $month : 'Deduction for the Month of ' . $month;
                $transaction->principal = $transaction->amount - $loan->monthly_interest;
                $transaction->interest = $loan->monthly_interest;
                $transaction->outst_amount = $outAmount;
                $transaction->save();

                $w_debit = Wallet::where('user_id', $loan->user_id)->where('loan_id', $loan->id)->where('category', 'repay_loan')->where('type', 'debit')->whereNull('transaction_id')->first();
                if ($w_debit) {
                    $w_debit->transaction_id = $transaction->id;
                    $w_debit->amount = $schedule->monthly_dedux * -1;
                    $w_debit->approved_by = $input['staff_id'];
                    $w_debit->paid = 1;
                    $w_debit->save();
                } else {
                    // debit user account
                    $w_debit = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'debit',
                        'amount' => $schedule->monthly_dedux * -1,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => 1,
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'payment_date' => $pay->payment_date,
                        'paid' => 1,
                    ]);
                }

                // credit user wallet to repay loan
                $wallet1 = Wallet::create([
                    'user_id' => $loan->user_id,
                    'loan_id' => $loan->id,
                    'category' => 'repay_loan',
                    'type' => 'credit',
                    'amount' => $schedule->monthly_dedux,
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $input['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $loan->lender_id,
                    'is_lender' => 0,
                    'paid' => 1,
                ]);

                // credit lender
                $wallet2 = Wallet::create([
                    'user_id' => $input['staff_id'],
                    'loan_id' => $loan->id,
                    'category' => 'repay_loan',
                    'type' => 'credit',
                    'amount' => $schedule->monthly_dedux,
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $input['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $loan->lender_id,
                    'is_lender' => 1,
                    'paid' => 1,
                ]);

                $notify = doNotify($w_debit->user_id, 'wallets', $w_debit->id, 'repay_loan', 'success', null, $w_debit->lender_id, $staff);

                ScheduleHistory::create([
                    'schedule_id' => $schedule->id,
                    'wallet_id' => $wallet1->id,
                    'user_id' => $loan->user_id,
                    'loan_id' => $loan->id,
                    'lender_id' => $loan->lender_id,
                ]);
            } else if ($transaction->channel == 'staff-repayment') {
                $transaction->approved = 1;
                $transaction->approved_by = $input['staff_id'];
                $transaction->approved_at = date('Y-m-d H:i:s');
                $transaction->description = $transaction->repayment_source == 'remita' ? strtoupper($loan->platform) . ' Deduction for the Month of ' . $month : 'Deduction for the Month of ' . $month;
                $transaction->status = 'Completed';
                $transaction->principal = $transaction->amount - $loan->monthly_interest;
                $transaction->interest = $loan->monthly_interest;
                $transaction->outst_amount = $outAmount;
                $transaction->save();

                $w_debit = Wallet::where('user_id', $loan->user_id)->where('loan_id', $loan->id)->where('category', 'repay_loan')->where('type', 'debit')->whereNull('transaction_id')->first();
                if ($w_debit) {
                    $w_debit->transaction_id = $transaction->id;
                    $w_debit->amount = $transaction->amount * -1;
                    $w_debit->approved_by = $input['staff_id'];
                    $w_debit->paid = 1;
                    $w_debit->save();
                } else {
                    // debit user account
                    $w_debit = Wallet::create([
                        'user_id' => $loan->user_id,
                        'loan_id' => $loan->id,
                        'category' => 'repay_loan',
                        'type' => 'debit',
                        'amount' => $transaction->amount * -1,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => 1,
                        'approved_at' => date('Y-m-d H:i:s'),
                        'lender_id' => $loan->lender_id,
                        'is_lender' => 0,
                        'payment_date' => $pay->payment_date,
                        'paid' => 1,
                    ]);
                }

                // credit user wallet to repay loan
                $wallet1 = Wallet::create([
                    'user_id' => $loan->user_id,
                    'loan_id' => $loan->id,
                    'category' => 'repay_loan',
                    'type' => 'credit',
                    'amount' => $transaction->amount,
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $input['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $loan->lender_id,
                    'is_lender' => 0,
                    'paid' => 1,
                ]);

                // credit lender
                $wallet2 = Wallet::create([
                    'user_id' => $input['staff_id'],
                    'loan_id' => $loan->id,
                    'category' => 'repay_loan',
                    'type' => 'credit',
                    'amount' => $transaction->amount,
                    'transaction_id' => $transaction->id,
                    'approved' => 1,
                    'approved_by' => $input['staff_id'],
                    'approved_at' => date('Y-m-d H:i:s'),
                    'lender_id' => $loan->lender_id,
                    'is_lender' => 1,
                    'paid' => 1,
                ]);

                $notify = doNotify($w_debit->user_id, 'wallets', $w_debit->id, 'repay_loan', 'success', null, $w_debit->lender_id, $staff);
            }

            $bypass = $input['bypass_remita'];

            if ((count($pays) == 1 || count($pays) == 0 && $remains > 0) && $loan->platform == 'remita' && $bypass == 0) {
                $sl = (new Remita())->stopLoanCollection($input['merchantId'], $input['apiKey'], $input['requestId'], $input['authorization'], $input['body']);

                $status = $sl == null ? 'failed' : ($sl->responseCode == '00' ? 'success' : $sl->responseMsg);

                logAPI('stop-loan', $user->phone, $status);

                if ($sl != null) {
                    info('stop loan:-> ' . json_encode($sl));
                    if ($sl->responseCode != '00') {
                        DB::rollBack();
                        Log::error('TransactionController.php Line 759 stop loan error:-> ' . json_encode($sl));
                        return response()->json(['error' => 'Remita stop loan notification failed.'], 500);
                    }
                } else {
                    DB::rollBack();
                    return response()->json(['error' => 'Remita stop loan notification failed.'], 500);
                }
            }

            $liquidated = liquidateLoan($loan->id);

            if ($pay) {
                $pay->paid = 1;
                $pay->paid_at = date('Y-m-d H:i:s');
                $pay->transaction_id = $transaction->id;
                $pay->save();
            }

            $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'repayment_approved', 'success', null, $loan->lender_id, $input['staff_id']);

            DB::commit();

            $details = getFinancialOverview($loan->lender_id, $loan->lender_id);

            try {
                if ($loan->admin_request == 0) {
                    $send = repaymentEmail($loan, $transaction);
                }
            } catch (\Exception $e) {
                Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile());
            }

            // all un approved transactions
            $unapproved_transactions = Transaction::where('approved', '<', 1)->whereNull('description')->count();

            return response()->json(['transaction' => $transaction, 'loan_details' => $details, 'unapproved_transactions' => $unapproved_transactions], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile() . ' transaction id: ' . $id);
            return response()->json(['error' => 'payment approval failed'], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function pendingTransactions(Request $request)
    {
        try {
            $limit = $request->has('pagesize') && $request->input('pagesize') != '' ? $request->input('pagesize') : 10;
            $page = $request->has('page') && $request->input('page') != '' ? $request->input('page') : 1;
            $skip = ($page - 1) * $limit;

            $channel = $request->has('transaction_type') && $request->input('transaction_type') != '' ? $request->input('transaction_type') : '';

            $search = $request->has('search') && $request->input('search') != '' ? $request->input('search') : '';

            if ($search == '') {
                if ($channel == '') {
                    $transactions = DB::table('transactions')->join('users', 'users.id', '=', 'transactions.user_id')->where('transactions.approved', '<', 1)->whereNull('transactions.deleted_at')->whereNull('transactions.description')->skip($skip)->take($limit)->orderBy('users.name')->select('transactions.*')->get();

                    $count = Transaction::where('approved', '<', 1)->whereNull('description')->count();
                } else {
                    $transactions = DB::table('transactions')->join('users', 'users.id', '=', 'transactions.user_id')->where('transactions.approved', '<', 1)->whereNull('transactions.deleted_at')->where('channel', $channel)->whereNull('transactions.description')->skip($skip)->take($limit)->orderBy('users.name')->select('transactions.*')->get();

                    $count = Transaction::where('approved', '<', 1)->where('channel', $channel)->whereNull('description')->count();
                }
            } else {
                if ($channel == '') {
                    $transactions = DB::table('transactions')->join('users', 'users.id', '=', 'transactions.user_id')->where('transactions.approved', '<', 1)->whereNull('transactions.deleted_at')->whereNull('transactions.description')->where(function ($query) use ($search) {
                        $query->where('users.name', 'like', '%' . $search . '%')->orWhere('users.phone', 'like', '%' . $search . '%')->orWhere('users.ippis', 'like', '%' . $search . '%')->orWhere('users.customer_id', 'like', '%' . $search . '%');
                    })->skip($skip)->take($limit)->orderBy('users.name')->select('transactions.*')->get();

                    $count = DB::table('transactions')->join('users', 'users.id', '=', 'transactions.user_id')->where('transactions.approved', '<', 1)->whereNull('transactions.deleted_at')->whereNull('transactions.description')->where(function ($query) use ($search) {
                        $query->where('users.name', 'like', '%' . $search . '%')->orWhere('users.phone', 'like', '%' . $search . '%')->orWhere('users.ippis', 'like', '%' . $search . '%')->orWhere('users.customer_id', 'like', '%' . $search . '%');
                    })->count();
                } else {
                    $transactions = DB::table('transactions')->join('users', 'users.id', '=', 'transactions.user_id')->where('transactions.approved', '<', 1)->whereNull('transactions.deleted_at')->where('channel', $channel)->whereNull('transactions.description')->where(function ($query) use ($search) {
                        $query->where('users.name', 'like', '%' . $search . '%')->orWhere('users.phone', 'like', '%' . $search . '%')->orWhere('users.ippis', 'like', '%' . $search . '%')->orWhere('users.customer_id', 'like', '%' . $search . '%');
                    })->skip($skip)->take($limit)->orderBy('users.name')->select('transactions.*')->get();

                    $count = DB::table('transactions')->join('users', 'users.id', '=', 'transactions.user_id')->where('transactions.approved', '<', 1)->whereNull('transactions.deleted_at')->where('channel', $channel)->whereNull('transactions.description')->where(function ($query) use ($search) {
                        $query->where('users.name', 'like', '%' . $search . '%')->orWhere('users.phone', 'like', '%' . $search . '%')->orWhere('users.ippis', 'like', '%' . $search . '%')->orWhere('users.customer_id', 'like', '%' . $search . '%');
                    })->count();
                }
            }

            $transactions = $transactions->map(function ($item, $key) {
                $item->user = User::where('id', $item->user_id)->first(['id', 'ippis', 'customer_id', 'name', 'phone']);
                $item->loan = Loan::with('accountno')->where('id', $item->loan_id)->first();
                $item->lender = Lender::where('id', $item->lender_id)->first();
                if ($item->reference != null && $item->reference != '') {
                    $reference = json_decode($item->reference);
                    $item->payment_date = $reference->payment_date;
                }

                $search = "Deduction for the Month of";
                $tenure = Loan::where('id', $item->loan_id)->pluck('tenure');
                $approvedTrans = Transaction::where('approved', '>', 0)->where('loan_id', $item->loan_id)
                    ->where('transaction_flag', 'LIKE', '%' . $search . '%')->count();

                $remainingMonths = $tenure[0] - $approvedTrans;
                $item->pending_months = $remainingMonths;
                return $item;
            });

            $result = (object)null;
            $result->total = $count;
            $result->size = $limit;
            $result->page = $page;
            $result->data = $transactions;

            return response()->json(compact('result'), 200);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ': ' . $e->getMessage() . ': ' . $e->getFile());
            return response()->json(['error' => 'could not fetch transactions'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $input = $request->all();

            $transaction = Transaction::with('loan')->where('id', $id)->first();
            if ($transaction) {
                $lender_id = $transaction->lender_id;

                $paid = PaymentDate::where('transaction_id', $transaction->id)->first();
                if ($paid) {
                    $paid->paid = 0;
                    $paid->paid_at = null;
                    $paid->transaction_id = null;
                    $paid->save();
                }

                if ($transaction->schedule_id) {
                    $schedule = Schedule::where('id', $transaction->schedule_id)->delete();
                }

                if ($transaction->remita_transaction_id) {
                    $remita_transaction = RemitaTransaction::where('id', $transaction->remita_transaction_id)->delete();
                }

                $notify = doNotify($transaction->user_id, 'transactions', $transaction->id, 'repayment_declined', 'success', null, $lender_id, $input['staff_id']);

                $transaction->delete();

                DB::commit();

                $details = getFinancialOverview($lender_id, $lender_id);

                // all un approved transactions
                $unapproved_transactions = Transaction::where('approved', '<', 1)->whereNull('description')->count();

                return response()->json(['transaction' => $transaction, 'loan_details' => $details, 'unapproved_transactions' => $unapproved_transactions], 200);
            }

            return response()->json(['error' => 'transaction not found'], 500);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile() . ' tid:' . $id);
            return response()->json(['error' => 'could not delete transaction'], 500);
        }
    }

    /**
     * Liquidate loan.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function liquidate(Request $request, $id)
    {
        try {
            $loan = Loan::where('id', $id)->where('liquidated', 0)->first();

            if (!$loan) {
                return response()->json(['error' => 'loan has been liquidated'], 500);
            }

            $liquidated = liquidateLoan($loan->id);

            return response()->json(compact('liquidated'), 500);
        } catch (\Exception $e) {
            Log::error($e->getLine() . ' ' . $e->getMessage() . ' ' . $e->getFile() . ' tid:' . $id);
            return response()->json(['error' => 'could not liquidate loan'], 500);
        }
    }
}
