<?php

namespace Database\Seeders;

use App\Models\Wallet;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RepayLoanFix extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            DB::beginTransaction();

            $wallets = Wallet::where('category', 'repay_loan')->where('type', 'credit')->where('is_lender', 1)->get();

            foreach ($wallets as $item){
                $wallet = Wallet::where('loan_id', $item->loan_id)->where('transaction_id', $item->transaction_id)->where('category', 'repay_loan')->get();

                $transaction = Transaction::where('id', $item->transaction_id)->first();

                if(count($wallet) == 2) {
                    $wallet1 = Wallet::create([
                        'user_id' => $transaction->user_id,
                        'loan_id' => $transaction->loan_id,
                        'category' => 'repay_loan',
                        'type' => 'debit',
                        'amount' => $item->amount * -1,
                        'transaction_id' => $transaction->id,
                        'approved' => 1,
                        'approved_by' => $item->approved_by,
                        'approved_at' => $item->approved_at,
                        'created_at' => $item->created_at,
                        'lender_id' => $item->lender_id,
                        'is_lender' => 0,
                    ]);
                }
            }

            DB::commit();
        } catch (\Exception $e){
            DB::rollBack();
            Log::error($e->getMessage());
        }
    }
}
