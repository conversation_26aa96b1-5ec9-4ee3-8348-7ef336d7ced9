import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';
import qs from 'querystring';
import numeral from "numeral";

import { baseURL, httpRequest, loanAPI } from '../config/constants';
import loading from '../assets/img/loading.gif';
import ConsentWrapper from "../container/ConsentWrapper";
import { doNotify } from "../actions/general";

const validate = values => {
    const errors = {};
    if (!values.otp) {
        errors.otp = 'You have not entered your OTP';
    }
    return errors;
};

const renderField = ({input, id, label, type, disabled, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')}`}>
        <input {...input} placeholder={label} type={type} className="form-control" disabled={disabled} />
        {touched && error && <span className="help-block form-text with-errors form-control-feedback">{error}</span>}
    </div>
);

class ConsentLoan extends Component {
	state = {
		submitting: false,
		message: '',
		loan: null,
	};

	UNSAFE_componentWillMount() {
		const { location } = this.props;
		const query = qs.parse(location.search.replace('?', ''));
		if(query && query.p && query.p !== '') {
			this.fetchLoan(query.p);
		}
	}

	fetchLoan = async phone => {
		try {
			const rs = await httpRequest(`${baseURL}/loans/by/phone?phone_number=${phone}`, 'GET', true);
			this.setState({ loan: rs.loan });
		} catch (e) {
			const message = e.message || 'could not get loan';
			this.props.doNotify({ message, level: 'error' });
		}
	};
    
    giveConsent = async data => {
		const { location } = this.props;
		const query = qs.parse(location.search.replace('?', ''));

        if(!query.p || (query.p && query.p === '')) {
            window.scrollTo(0, 0);
            const message = 'Page not found check your SMS or contact support';
            throw new SubmissionError({
                _error: message,
            });
        }

        try {
			this.setState({ submitting: true });
			const url = `${baseURL}${loanAPI}/grant/consent`;
			await httpRequest(url, 'POST', false, {...data, phone_number:query.p});
			this.setState({ submitting: false, message: 'Consent granted. Your loan will be disbursed soon.' });
            window.scrollTo(0, 0);
			this.props.reset('consent-loan');
			this.props.history.push('/consent-loan');
        }
        catch (error) {
			this.setState({ submitting: false });
            window.scrollTo(0, 0);
            const info = error.message || 'Error, check your connection and try again';
            throw new SubmissionError({
                _error: info,
            });
        }
    };

    render() {
        const { handleSubmit, error } = this.props;
		const { submitting, message, loan } = this.state;
        return (
			loan && <ConsentWrapper>
			<div className="content-i" style={{tableLayout: 'fixed', display: 'table', width: '100%'}}>
				<div className="content-box">
					<div className="element-box consent-box" style={{margin: '0 auto'}}>
						{error && <div className="alert alert-danger" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
						{message !== '' && <div className="alert alert-success" style={{marginBottom: '15px'}} dangerouslySetInnerHTML={{__html: `<strong>Success!</strong> ${message}`}}></div>}
						{message === '' && <form onSubmit={handleSubmit(this.giveConsent)}>
							<div className="row">
								<div className="col-sm-12">
									<p style={{textAlign: 'center'}}>Do you accept the terms and conditions binding on your loan offer of {`₦${numeral(loan.amount).format('0,0.00')}`} for {loan.tenure} months? Note that repayment will commence on your next payment.</p>
									<p style={{textAlign: 'center'}}>Please enter your OTP to grant consent to this loan applicaition.</p>
								</div>
								<div className="col-sm-12">
									<Field
										name="otp"
										id="otp"
										type="text"
										component={renderField}
										label="Enter OTP"
									/>
								</div>
							</div>
							<div className="form-buttons-w text-center">
								<button className="btn btn-primary" disabled={submitting} type="submit">
									{submitting? <img src={loading} alt=""/> : 'I Accept'}
								</button>
							</div>
						</form>}
					</div>
				</div>
			</div>
			</ConsentWrapper>
        );
    }
}

ConsentLoan = reduxForm({
    form: 'consent-loan',
    validate
})(ConsentLoan);

const mapStateToProps = (state) => {
	return {
		initialValues: {
            otp: '',
		},
	}
};

export default connect(mapStateToProps, { doNotify, reset })(ConsentLoan);
