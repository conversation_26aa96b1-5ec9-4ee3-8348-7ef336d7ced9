import React from 'react';

import { currency, formatDate } from '../config/constants';

const Wallet = ({ items, skip }) => {
	return items.map((item, i) => {
		return (
			<tr key={i}>
				<td>{parseInt(item.sn, 10) + parseInt(skip, 10)}</td>
				<td>{item.name}</td>
				<td>{item.core_bank_id || '-'}</td>
				<td>{item.ippis || '-'}</td>
				<td>{item.phone}</td>
				<td>{item.employer || '-'}</td>
				<td>{item.mandate_ref}</td>
				<td>{item.loan_id}</td>
				<td>{item.lender}</td>
				<td>{currency(item.loan_amount)}</td>				               
				<td>{currency(item.disburse_amount > 0 ? item.disburse_amount : item.amount)}</td>
				<td>{item.is_topup === 1 ? 'Topup': 'New'}</td>
				<td>{item.tenure ? `${item.tenure}month${item.tenure > 1 ? 's':''}` : '-'}</td>
				<td>{item.acceptance_date}</td>
				<td>{currency(item.monthly_principal)}</td>
				<td>{currency(item.monthly_interest)}</td>
				<td>{item.disburse_date ? formatDate(item.disburse_date, 'YYYY-MM-DD HH:mm:ss') : '-'}</td>
				<td>{item.repayment_source}</td>				
                <td>{formatDate(item.repayment_date, 'YYYY-MM-DD HH:mm:ss')}</td>
				<td>{currency(item.amount)}</td>
				<td>{currency(item.repayment_principal)}</td>
				<td>{currency(item.repayment_interest)}</td>
				<td>{item.repayment_reference || '-'}</td>
				<td>{item.status}</td>
				<td>{formatDate(item.transaction_date, 'YYYY-MM-DD HH:mm:ss')}</td>                
				<td>{item.type}</td>
				<td>{item.description ? item.description : '-' }</td>
			</tr>
		);
	});
};

export default Wallet;
