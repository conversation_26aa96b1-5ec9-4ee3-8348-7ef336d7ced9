<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UpdateLoanTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dateTime('start_date')->nullable()->after('monthly_interest');
            $table->bigInteger('disbursed_by')->unsigned()->nullable()->change();
            $table->bigInteger('approved_by')->unsigned()->nullable()->change();
            $table->bigInteger('verified_by')->unsigned()->nullable()->change();
            $table->integer('accountno_id')->unsigned()->change();
            $table->bigInteger('office_id')->unsigned()->nullable()->change();
            $table->bigInteger('user_id')->unsigned()->change();

            $table->foreign('disbursed_by')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('approved_by')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('verified_by')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('accountno_id')->references('id')->on('accountnos')->onDelete('cascade');
            $table->foreign('office_id')->references('id')->on('offices')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropForeign(['disbursed_by', 'approved_by', 'verified_by', 'accountno_id', 'office_id', 'user_id']);
            $table->dropColumn(['start_date']);
        });
    }
}
