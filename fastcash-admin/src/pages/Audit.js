/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import qs from 'querystring';
import Pagination from 'antd/lib/pagination';
import Tooltip from 'antd/lib/tooltip';
import Modal from 'react-modal';

import { httpRequest, baseURL, loanAPI, currency, formatDate } from '../config/constants';
import { startBlock, stopBlock } from '../actions/ui-block';
import { toggleMenu } from '../actions/user';
import { doNotify } from '../actions/general';
import AuditTrail from '../components/AuditTrail';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 5;
const format = 'YYYY-MM-DD HH:mm:ss';

class Audit extends Component {
	state = {
		location: '',
		loans: [],
		loan: null,
		isOpen: false,

		currentPage: 1,
		totalPage: 0,
	};

	componentDidMount() {
		Modal.setAppElement('body');

		this.props.toggleMenu(false);

		const { location } = this.props;

		const query = qs.parse(location.search.replace('?', ''));
		const page = query && query.p ? parseInt(query.p, 10) : 1;

		this.setState({
			currentPage: page,
			location: location.pathname,
		});

		this.fetch(page);
	}

	fetch = async (pageNumber) => {
		try {
			this.props.startBlock();
			const url = `${baseURL}${loanAPI}/admin/audit?page=${pageNumber}&pagesize=${pageSize}`;
			const rs = await httpRequest(url, 'GET', true);
			const result = rs.result;
			this.setState({
				currentPage: pageNumber,
				totalPage: result.total,
				loans: result.data,
			});
			this.props.stopBlock();
			window.scrollTo(0, 0);
		} catch (e) {
			console.log(e);
			this.props.stopBlock();
			this.props.doNotify({ message: 'could not load loans', level: 'error' });
		}
	};

	onChange = (pageNumber) => {
		this.props.history.push(`/audit-admin?p=${pageNumber}`);
	};

	componentWillUpdate(nextProps, nextState) {
		const query = qs.parse(nextProps.location.search.replace('?', ''));
		const pageNumber = query && query.p ? parseInt(query.p, 10) : 1;
		if (
			nextState.currentPage !== pageNumber ||
			nextProps.location.pathname !== nextState.location
		) {
			this.setState({
				location: nextProps.location.pathname,
				currentPage: pageNumber,
			});

			this.fetch(pageNumber);
		}
	}

	componentWillUnmount() {
		this.props.toggleMenu(true);
	}

	openModal = (loan) => () => {
		document.body.className = 'pages modal-open';
		this.setState({ isOpen: true, loan });
	};

	closeModal = () => {
		this.setState({ isOpen: false, loan: null });
		document.body.className = 'pages';
	};
	
	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	setPassed = id => async () => {
		try {
			this.closeModal();
			this.props.startBlock();
			const url = `${baseURL}${loanAPI}/audit-passed/${id}`;
			await httpRequest(url, 'POST', true, {audited: 1});
			this.notify('', 'Loan audit passed!', 'success');
			this.props.stopBlock();
			this.fetch(this.state.currentPage);
		} catch (e) {
			console.log(e);
			this.props.stopBlock();
			const message = e.message || 'could not pass audit';
			this.notify('', message, 'error');
		}
	};

	regenWallet = (id, transactions) => async () => {
		try {
			this.closeModal();
			this.props.startBlock();
			const _transactions = transactions.map(item => item.id);
			const url = `${baseURL}${loanAPI}/regenerate-wallet/${id}`;
			await httpRequest(url, 'POST', true, {audited: 2, transactions: _transactions});
			this.notify('', 'Loan regenerated!', 'success');
			this.props.stopBlock();
			this.fetch(this.state.currentPage);
		} catch (e) {
			console.log(e);
			this.props.stopBlock();
			const message = e.message || 'could not pass audit';
			this.notify('', message, 'error');
		}
	};

	setAudited = id => async () => {
		try {
			this.closeModal();
			this.props.startBlock();
			const url = `${baseURL}${loanAPI}/audited/${id}`;
			await httpRequest(url, 'POST', true, {audited: 3});
			this.notify('', 'Loan audited!', 'success');
			this.props.stopBlock();
			this.fetch(this.state.currentPage);
		} catch (e) {
			console.log(e);
			this.props.stopBlock();
			const message = e.message || 'could not pass audit';
			this.notify('', message, 'error');
		}
	};

	render() {
		const { loans, loan, currentPage, totalPage, isOpen } = this.state;
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="element-wrapper">
						<h6 className="element-header">Loans</h6>
						<div className="element-box" style={{ padding: '1.0rem 0.4rem' }}>
							<div className="table-responsive" style={{ overflowX: 'scroll' }}>
								<table className="table table-striped table-lightfont">
									<thead>
										<tr>
											<th>Loan ID</th>
											<th>Name of Employee</th>
											<th>Loan Amount</th>
											<th>Loan Disbursed</th>
											<th>Monthly P/I</th>
											<th>S/E DATE</th>
											<th>Monthly Deduction</th>
											<th>Tenure</th>
											<th>Total Deduction</th>
											<th>Is Topup</th>
											<th>Loan Dates</th>
											<th>Audited</th>
											<th />
										</tr>
									</thead>
									<tbody>
										{loans.map((item, i) => (
											<tr key={i}>
												<td>{item.id}</td>
												<td>
													<small>{item.user.name}</small><br/>
													<small className="bold">{`${item.platform.toUpperCase()}`}</small>
												</td>
												<td><small>{currency(item.amount)}</small></td>
												<td><small>{currency(item.disburse_amount)}</small></td>
												<td nowrap="nowrap">
													<small>{`P: ${currency(item.monthly_principal)}`}</small><br/>
													<small>{`I: ${currency(item.monthly_interest)}`}</small>
												</td>
												<td nowrap="nowrap">
													<small>{`SD: ${item.verified ? formatDate(item.start_date, format) : '-'}`}</small><br/>
													<small>{`ED: ${item.verified ? formatDate(item.end_date, format) : '-'}`}</small>
												</td>
												<td><small>{currency(item.monthly_deduction)}</small></td>
												<td nowrap="nowrap"><small>{`${item.tenure} Month${item.tenure > 1 ? 's':''}`}</small></td>
												<td><small>{currency(item.total_deduction)}</small></td>
												<td>{item.is_topup === 0 ? 'No' : 'Yes'}</td>
												<td nowrap="nowrap">
													<small>{`REQ: ${formatDate(item.created_at, format)}`}</small><br/>
													<small>{`VER: ${item.verified ? formatDate(item.verified_at, format) : '-'}`}</small><br/>
													<small>{`APP: ${item.approved ? formatDate(item.approved_at, format) : '-'}`}</small><br/>
													<small>{`DIS: ${item.disbursed ? formatDate(item.disbursed_at, format) : '-'}`}</small><br/>
													<small>{`LIQ: ${item.liquidated ? formatDate(item.liquidated_at, format) : '-'}`}</small><br/>
													<small>{item.deleted_at ? <strong>{`DEC: ${formatDate(item.deleted_at, format)}`}</strong> : 'DEL: -'}</small>
												</td>
												<td><small>{item.audited_at ? formatDate(item.audited_at, format) : '-'}</small></td>
												<td>
													<Tooltip title="Check Audit Trail">
														<a
															className="cursor"
															onClick={this.openModal(item)}
														>
															<i className="os-icon os-icon-settings" />
														</a>
													</Tooltip>
												</td>
											</tr>
										))}
									</tbody>
								</table>
							</div>
							<div className="pagination pagination-center mt-4">
								<Pagination
									current={currentPage}
									pageSize={pageSize}
									total={totalPage}
									showTotal={(total) => `Total ${total} loans`}
									itemRender={itemRender}
									onChange={this.onChange}
								/>
							</div>
						</div>
					</div>
					<Modal
						isOpen={isOpen}
						onRequestClose={this.closeModal}
						contentLabel="Audit Trail"
						shouldCloseOnOverlayClick={false}
						overlayClassName="modal-dialog modal-lg"
						className="modal-content"
					>
						<AuditTrail
							closeModal={() => this.closeModal()}
							loan={loan}
							setPassed={this.setPassed}
							regenWallet={this.regenWallet}
							setAudited={this.setAudited}
						/>
					</Modal>
					{isOpen ? <div className="modal-backdrop fade show" /> : ''}
				</div>
			</div>
		);
	}
}

export default connect(null, { toggleMenu, startBlock, stopBlock, doNotify })(
	Audit
);
