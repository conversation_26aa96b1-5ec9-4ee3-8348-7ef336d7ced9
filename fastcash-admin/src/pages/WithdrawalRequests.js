/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import Pagination from 'antd/lib/pagination';
import qs from 'querystring';
import startCase from 'lodash.startcase';

import {
	httpRequest,
	baseURL,
	withdrawAPI,
	currency,
	formatDate,
	updateImmutable,
} from '../config/constants';
import { doNotify } from '../actions/general';
import { startBlock, stopBlock } from '../actions/ui-block';
import { setWithdrawals } from '../actions/user';
import DatePicker from 'react-datepicker';
import loading from '../assets/img/loading.gif';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Previous</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};
const pageSize = 12;

class WithdrawalRequests extends Component {
	state = {
		withdrawals: [],
		currentPage: 1,
		totalPage: 0,
		location: '',
		exporting: false,
		fromDate: null,
		toDate: null,
		_fromDate: '',
		_toDate: '',
		lender: '',
		status: '',
		allLenders: [],
	};

	componentDidMount() {
		const { location } = this.props;

		const query = qs.parse(location.search.replace('?', ''));

		const page = query && query.p ? parseInt(query.p, 10) : 1;

		const lender = query && query.lender ? query.lender : '';
		const status = query && query.status ? query.status : '';
		const _fromDate = query && query.from ? query.from : '';
		const _toDate = query && query.to ? query.to : '';

		this.setState({
			currentPage: page,
			location: location.pathname,
			status,
			lender,
			_fromDate,
			_toDate,
		});

		this.fetch(page, lender, status, _fromDate, _toDate);
	}

	fetch = async (pageNumber, lender = '', status = '', _fromDate = '', _toDate = '') => {
		try {
			this.props.startBlock();
			const rs = await httpRequest(`${baseURL}${withdrawAPI}?page=${pageNumber}&pagesize=${pageSize}&lender_id=${lender}&status=${status}&from=${_fromDate}&to=${_toDate}`, 'GET', true);
			const result = rs.result;
			this.setState({
				currentPage: pageNumber,
				totalPage: result.total,
				withdrawals: result.data,
				allLenders: result.lenders,
			});
			this.props.setWithdrawals(result.withdrawals);
			this.props.stopBlock();
			window.scrollTo(0, 0);
		} catch (e) {
			this.props.stopBlock();
			this.notify('', 'could not load withdrawal requests!', 'error');
		}
	};

	componentWillUpdate(nextProps, nextState) {
		const query = qs.parse(nextProps.location.search.replace('?', ''));
		const pageNumber = query && query.p ? parseInt(query.p, 10) : 1;
		const newLender = query && query.lender ? query.lender : '';
		const status = query && query.status ? query.status : '';
		const _fromDate = query && query.from ? query.from : '';
		const _toDate = query && query.to ? query.to : '';

		if (
			nextState.currentPage !== pageNumber ||
			nextProps.location.pathname !== nextState.location
		) {
			this.setState({
				location: nextProps.location.pathname,
				currentPage: pageNumber,
				lender: newLender,
				status: status,
				_fromDate: _fromDate,
				_toDate: _toDate,
				fromDate: _fromDate !== '' ? nextState.fromDate : null,
				toDate: _toDate !== '' ? nextState.toDate : null,
			});

			this.fetch(pageNumber, newLender, status, _fromDate, _toDate);
		}
	}

	setDateRange = () => {
		const { fromDate, toDate, _fromDate, _toDate } = this.state;
		let from, to;
		if (fromDate !== null) {
			from = fromDate ? fromDate.format('DD-MM-YYYY') : '';
			to = toDate ? toDate.format('DD-MM-YYYY') : '';
		} else {
			from = _fromDate;
			to = _toDate;
		}
		return { to, from };
	};

	approveRequest = async (id) => {
		const action = window.confirm('Do you want to approve this withdrawal request?');
		if (action) {
			try {
				this.props.startBlock();
				const { withdrawals } = this.state;
				const { user } = this.props;
				const data = { staff_id: user.id, lender_id: this.props.lender.id };
				const rs = await httpRequest(
					`${baseURL}${withdrawAPI}/approve/${id}`,
					'POST',
					true,
					data
				);
				const updated = updateImmutable(withdrawals, rs.withdraw);
				this.setState({ withdrawals: updated });
				this.notify('', 'Withdrawal Request Approved!', 'success');
				this.props.stopBlock();
			} catch (e) {
				console.log(e);
				this.props.stopBlock();
				const message = e.message || 'could not approve request';
				this.notify('', message, 'error');
			}
		}
	};

	declineRequest = async (id) => {
		const action = window.confirm('Do you want to decline withdrawal request?');
		if (action) {
			try {
				this.props.startBlock();
				const { withdrawals } = this.state;
				const { user, lender } = this.props;
				const data = { staff_id: user.id, lender_id: lender.id };
				const rs = await httpRequest(
					`${baseURL}${withdrawAPI}/decline/${id}`,
					'POST',
					true,
					data
				);
				const updated = updateImmutable(withdrawals, rs.withdraw);
				this.setState({ withdrawals: updated });
				this.notify('', 'Withdrawal Request Declined!', 'success');
				this.props.stopBlock();
			} catch (e) {
				console.log(e);
				this.props.stopBlock();
				const message = e.message || 'could not decline request';
				this.notify('', message, 'error');
			}
		}
	};

	onChange = (pageNumber) => {
		const { lender, status } = this.state;
		this.setState({ currentPage: pageNumber });

		let { from, to } = this.setDateRange();
		this.props.history.push(`/withdrawal-requests?p=${pageNumber}&lender=${lender}&status=${status}&from=${from}&to=${to}`);
		this.fetch(pageNumber, lender, status, from, to);

	};

	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	exportReport = async (e) => {
		try {
			e.preventDefault();
			this.setState({ exporting: true });
			const { status, lender, fromDate, toDate } = this.state;
			const from = fromDate ? fromDate.format('DD-MM-YYYY') : '';
			const to = toDate ? toDate.format('DD-MM-YYYY') : '';
			const rs = await httpRequest(`${baseURL}${withdrawAPI}/export/report?lender=${lender}&status=${status}&from=${from}&to=${to}`, 'GET', true);

			if (rs.file) {
				this.setState({ exporting: false });
				this.notify('', `Withdrawal report has been exported`, 'success');
				window.open(rs.file, '_blank');
			} else {
				this.notify('',rs.message || `could not export withdrawal report`,'success');
				this.setState({ exporting: false });
			}
		} catch (error) {
			this.setState({ exporting: false });
			const message = error.message || `could not export withdrawal report`;
			this.notify('', message, 'error');
		}
	};

	onSelectLender = (e) => {
		const lender = e.target.value;
		this.setState({ lender: lender });
		const { status } = this.state;
		const page = 1;
		this.props.history.push(
			`/withdrawal-requests?p=${page}&lender=${lender}&status=${status}&from=&to=`
		);
		this.fetch(page, lender, status);
	};

	onSelectStatus = (e) => {
		const status = e.target.value;
		const { lender, fromDate, toDate } = this.state;
		this.setState({ status: status });
		const from = fromDate ? fromDate.format('DD-MM-YYYY') : '';
		const to = toDate ? toDate.format('DD-MM-YYYY') : '';
		const page = 1;
		this.props.history.push(
			`/withdrawal-requests?p=${page}&lender=${lender}&status=${status}&from=${from}&to=${to}`
		);
		this.fetch(page, lender, status, from, to);
	};

	handleChangeDate = (date, type) => {
		this.setState({ [type]: date }, () => {
			if (type === 'fromDate') {
				this.setState({ toDate: null });
			}
		});

		if (type === 'toDate') {
			const { lender, status, fromDate } = this.state;
			const from = fromDate.format('DD-MM-YYYY');
			const to = date.format('DD-MM-YYYY');
			const page = 1;
			this.props.history.push(
				`/withdrawal-requests?p=${page}&lender=${lender}&status=${status}&from=${from}&to=${to}`
			);
			this.fetch(page, lender, status, from, to);
		}
	};

	render() {
		const {
			withdrawals,
			currentPage,
			exporting,
			totalPage,
			allLenders,
			lender,
			status,
			fromDate,
			toDate,
		} = this.state;
		return (
			<div className="content-i">
				<div className="control-header m-3">
					<div className="row align-items-center">
						<div className="col-12 mb-3">
							<div className="row">
								<div className="col-12 col-lg-6">
									<div className="flex">
										{allLenders.length > 0 && (
											<div className="col-12 col-lg-6 mr-2">
												<label className="d-block">Lenders: </label>
												<select
													className="form-control-sm"
													onChange={this.onSelectLender}
													value={lender}
												>
													<option value="">All</option>
													{allLenders.map((lender) => {
														return (
															<option key={lender.id} value={lender.id}>
																{lender.name}
															</option>
														);
													})}
												</select>
											</div>
										)}
										<div className="col-lg-6 col-12 ">
											<label className="d-block">Status: </label>
											<select
												className="form-control-sm"
												onChange={this.onSelectStatus}
												value={status}
											>
												<option value="">All</option>
												<option value="pending">Pending</option>
												<option value="approved">Approved</option>
												<option value="declined">Declined</option>
											</select>
										</div>
									</div>
								</div>
								<div className="col-12 col-lg-6">
									<div className="row">
										<div className="col-lg-6 col-12 date_input mr-2">
											<label className="d-block">From: </label>
											<DatePicker
												selected={fromDate}
												onChange={(e) => this.handleChangeDate(e, 'fromDate')}
												dateFormat="DD-MMM-YYYY"
												placeholderText="From Date"
												className="form-control white-bg"
												showMonthDropdown
												showYearDropdown
												dropdownMode="select"
												strictParsing
												peekNextMonth={false}
											/>
										</div>
										<div className="col-12 col-lg-6 date_input">
											<label className="d-block">To: </label>
											<DatePicker
												selected={toDate}
												onChange={(e) => this.handleChangeDate(e, 'toDate')}
												dateFormat="DD-MMM-YYYY"
												placeholderText="To Date"
												className="form-control white-bg"
												disabled={fromDate === null}
												minDate={fromDate}
												showMonthDropdown
												showYearDropdown
												dropdownMode="select"
												strictParsing
											/>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<div className="content-box">
					<div className="row">
						<div className="col-sm-12">
							<div className="element-wrapper">
								<div className="d-flex">
									<h6 className="element-header">Withdrawal Requests</h6>
									{exporting ? (
										<a className="btn btn-primary btn-sm btn-export-sm pointer">
											<img src={loading} alt="" />
										</a>
									) : (
										<a
											className="btn btn-primary btn-sm btn-export-sm pointer"
											href="#"
											onClick={this.exportReport}
										>
											<i className="os-icon os-icon-download-cloud" />
											<span>Export Withdrawal Request</span>
										</a>
									)}
								</div>
								<div className="element-box">
									<div className="table-responsive">
										<table className="table table-striped table-lightfont">
											<thead>
												<tr>
													<th>Name</th>
													<th>Lender</th>
													<th>Amount</th>
													<th>Bank Account</th>
													<th>Status</th>
													<th>Date</th>
													<th className="text-center">&nbsp;</th>
												</tr>
											</thead>
											<tbody>
												{withdrawals.map((item, i) => {
													return (
														<tr key={i}>
															<td>{item.user.name}</td>
															<td>{item.lender.name}</td>
															<td>{currency(item.amount)}</td>
															<td>
																<span>{`${startCase(
																	item.accountno.bank_name
																)} ${item.accountno.account_number}`}</span>
															</td>
															<td>
																<a
																	className={`badge badge-${
																		!item.deleted_at
																			? item.approved === 0
																				? 'warning'
																				: 'success text-white'
																			: 'danger text-white'
																	}`}
																>
																	{!item.deleted_at
																		? item.approved === 0
																			? 'Pending'
																			: 'Completed'
																		: 'Declined'}
																</a>
															</td>
															<td>
																{formatDate(
																	item.created_at,
																	'YYYY-MM-DD HH:mm:ss'
																)}
															</td>
															<td nowrap="nowrap">
																{item.approved === 0 && !item.deleted_at && (
																	<button
																		className="btn btn-sm btn-outline-primary cursor"
																		onClick={() => this.approveRequest(item.id)}
																	>
																		Approve
																	</button>
																)}
																{item.approved === 0 && !item.deleted_at && (
																	<button
																		className="btn btn-sm btn-outline-danger cursor ml-1"
																		onClick={() => this.declineRequest(item.id)}
																	>
																		<i className="os-icon os-icon-trash-2 mr-0" />
																	</button>
																)}
															</td>
														</tr>
													);
												})}
											</tbody>
										</table>
									</div>
									<div className="pagination pagination-center mt-4">
										<Pagination
											current={currentPage}
											pageSize={pageSize}
											total={totalPage}
											showTotal={(total) => `Total ${total} requests`}
											itemRender={itemRender}
											onChange={this.onChange}
										/>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state, ownProps) => {
	return {
		user: state.user.user,
		lender: state.lender.profile,
	};
};

export default connect(mapStateToProps, {
	doNotify,
	startBlock,
	stopBlock,
	setWithdrawals,
})(WithdrawalRequests);
