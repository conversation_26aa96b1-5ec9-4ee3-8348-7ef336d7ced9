/* eslint eqeqeq: 0 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { <PERSON>, withRouter } from 'react-router-dom';
import { Field, reduxForm, reset, SubmissionError } from 'redux-form';

import { baseURL, httpRequest, adminLoginAPI, authKey, settingKey } from '../config/constants';
import { changeUserData, signOut, setUnapprovedTransactions, setUnliquidatedLoans, setWithdrawals } from '../actions/user';
import { setLoanAnalytics } from '../actions/loan';
import loading from '../assets/img/loading.gif';
import logoBig from '../assets/img/fc-logo.png';
import { putSettings } from '../actions/settings';
import { doNotify, loadBanks, setSlips, loadRoles } from '../actions/general';
import { loadNotifications } from '../actions/notification';
import { loadMail } from '../actions/mail';
import { loadLenders } from '../actions/lender';
import { setUnreadSupport } from '../actions/support';
import { setLenderProfile } from '../actions/lender';

const validate = values => {
    const errors = {}
    if (!values.username) {
        errors.username = 'Enter your username';
    }
    if (!values.password) {
        errors.password = 'Enter your password';
    }
    return errors;
};

const renderField = ({input, id, icon, label, hideclass, type, meta: {touched, error, warning}}) => (
    <div className={`form-group ${(touched && error && 'has-error')} ${hideclass}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" />
        <div className={`pre-icon os-icon ${icon}`}></div>
        {touched &&
            ((error && <span className="help-block">{error}</span>) ||
                (warning && <span className="help-block">{warning}</span>))}
    </div>
);

class SignIn extends Component {
	constructor(props, context) {
		super(props, context);
		this.authAdmin = this.authAdmin.bind(this);
	}

    notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	authAdmin = async data => {
		try {
            const response = await httpRequest(`${baseURL}${adminLoginAPI}`, 'POST', false, data);
            const user = response.user;
            this.props.changeUserData(user, true);
            localStorage.setItem(authKey, response.token);
            this.props.reset('signinform');
            if (user.role == 'admin' || user.role == 'super') {
                this.props.setLenderProfile(response.lender);
                this.props.setLoanAnalytics(response.loan_details);
                this.props.loadMail(response.mails);
                this.props.setSlips(response.slips);
                this.props.loadBanks(response.banks);
                this.props.loadNotifications(user);
                this.props.loadLenders(response.lenders);
                this.props.loadRoles(response.roles);
                this.props.putSettings(response.settings);
                this.props.setUnreadSupport(response.unread_support);
                this.props.setUnapprovedTransactions(response.unapproved_transactions);
                this.props.setWithdrawals(response.withdrawals);
                this.props.setUnliquidatedLoans(response.unliquidated_loans);
                this.notify('', 'authentication successful!', 'success');
                this.props.history.push({ pathname: '/dashboard' });
            }
            else {
                localStorage.removeItem(authKey);
                localStorage.removeItem(settingKey);
                this.props.signOut();
                this.props.history.push({ pathname: '/' });
            }
        }
        catch (error) {
            const message = error.message || 'invalid sign in';
            this.notify('', message, 'error');
            throw new SubmissionError({
                _error: message,
            });
        }
	};

	componentDidMount() {
		document.body.className = 'auth-wrapper pages';
	}
    
    componentWillUnmount() {
        document.body.className = 'pages';
    }

    render() {
        const { handleSubmit, error, submitting, pristine } = this.props;
        return (
            <div className="all-wrapper menu-side with-pattern">
                <div className="auth-box-wh">
                    <div className="logo-w" style={{padding: '10%'}}>
                        <Link to="/"><img alt="logo" src={logoBig} style={{width: '50%'}}/></Link>
                    </div>
                    <h4 className="auth-header">FastCash Admin</h4>
                    {error && <div className="alert alert-danger" dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                    <form onSubmit={handleSubmit(this.authAdmin)} autoComplete="off">
                        <Field
                            name="username"
                            id="username"
                            type="text"
                            component={renderField}
                            label="Username"
                            icon="os-icon-user-male-circle"
                        />
                        <Field
                            name="password"
                            id="password"
                            type="password"
                            component={renderField}
                            label="Password"
                            icon="os-icon-fingerprint"
                        />
                        <div>
                            <Link className="btn btn-link" to="/forgot-password">Forgot Password? Click Here</Link>
                        </div>
                        <div className="buttons-w">
                            <button className="btn btn-primary" type="submit" disabled={pristine || submitting}>
                                {submitting? <img src={loading} alt=""/> : 'Log In'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    }
}

SignIn = reduxForm({
    form: 'signinform',
    validate
})(SignIn);

export default withRouter(connect(null, { reset, changeUserData, setLoanAnalytics, putSettings, signOut, setSlips, doNotify, loadNotifications, loadMail, loadBanks, loadLenders, loadRoles, setUnreadSupport, setUnapprovedTransactions, setUnliquidatedLoans, setLenderProfile, setWithdrawals })(SignIn));
