{"name": "fastcash", "version": "0.1.0", "private": true, "scripts": {"start": "react-scripts start", "clean": "sudo rm -rf build", "build": "yarn clean && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "antd": "^3.8.2", "bootstrap": "^4.5.2", "es6-promise": "^4.2.8", "font-awesome": "^4.7.0", "intro.js": "^3.3.1", "intro.js-react": "^0.3.0", "jquery": "^3.5.1", "jwt-decode": "^2.2.0", "lodash.range": "^3.2.0", "lodash.startcase": "^4.4.0", "lodash.truncate": "^4.4.2", "moment": "^2.27.0", "number-to-words": "^1.2.4", "numeral": "^2.0.6", "react": "^16.13.1", "react-block-ui": "^1.3.3", "react-dom": "^16.13.1", "react-facebook-pixel": "^1.0.3", "react-flutterwave-rave": "^1.0.4", "react-loadable": "^5.5.0", "react-modal": "^3.11.2", "react-notification-system": "^0.4.0", "react-redux": "^7.2.1", "react-router-dom": "^5.2.0", "react-router-redux": "^4.0.8", "react-scripts": "3.4.3", "redux": "^4.0.5", "redux-form": "^8.3.6", "redux-thunk": "^2.3.0", "simple-line-icons": "^2.5.5"}, "devDependencies": {"@types/lodash.range": "^3.2.6", "@types/lodash.startcase": "^4.4.6", "@types/lodash.truncate": "^4.4.6", "babel-loader": "^8.2.2", "redux-logger": "^3.0.6"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}