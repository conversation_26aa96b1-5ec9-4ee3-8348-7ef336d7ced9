<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class MultiLenderLoan extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->tinyInteger('is_multifund')->default(0)->after('is_topup');
            $table->dateTime('is_multifund_at')->nullable()->after('is_multifund');
            $table->bigInteger('is_multifund_by')->nullable()->after('is_multifund_at');
            $table->tinyInteger('cancel_multifund')->default(0)->after('is_multifund_by');
            $table->dateTime('cancel_multifund_at')->nullable()->after('cancel_multifund');
            $table->bigInteger('cancel_multifund_by')->nullable()->after('cancel_multifund_at');
            $table->tinyInteger('fully_funded')->default(0)->after('cancel_multifund_by');
            $table->dateTime('fully_funded_at')->nullable()->after('fully_funded');
            $table->tinyInteger('multifund_expired')->default(0)->after('is_multifund_by');
            $table->dateTime('multifund_expire_at')->nullable()->after('multifund_expired');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('loans', function (Blueprint $table) {
            $table->dropColumn(['is_multifund', 'is_multifund_at', 'is_multifund_by', 'cancel_multifund', 'cancel_multifund_at', 'cancel_multifund_by', 'fully_funded', 'fully_funded_at', 'multifund_expired', 'multifund_expire_at']);
        });
    }
}
