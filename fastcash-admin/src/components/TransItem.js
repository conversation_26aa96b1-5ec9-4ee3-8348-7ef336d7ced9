/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react';
import numeral from 'numeral';
import startCase from 'lodash.startcase';
import moment from 'moment';
import { Link } from 'react-router-dom';
import Tooltip from 'antd/lib/tooltip';

import { getTitle } from '../config/constants';

const TransItem = ({ description, user, amount, repayment, openModal, office, platform, loan }) => {
    const status = loan.deleted_by ? <Tooltip placement="top" title={getTitle('red', loan.decline_reason || '')}><div className="status-pill red"/></Tooltip> : (loan.approved === 1 ? (loan.disbursed === 1 ? <Tooltip placement="top" title={getTitle('green')}><div className="status-pill green"/></Tooltip>: <Tooltip placement="top" title={getTitle('yellow', 'not disbursed')}><div className="status-pill yellow"/></Tooltip>) : <Tooltip placement="top" title={getTitle('dark', 'not approved')}><div className="status-pill dark"/></Tooltip>);
    return (
        <tr>
            <td nowrap="nowrap">{moment(loan.created_at).format('DD-MMM-YYYY')}</td>
			<td nowrap="nowrap">{platform !== 'sme' ? startCase(platform) : '-'}</td>
            {description && <td>{user && `${loan.is_topup === 1 ? 'Topup-Loan' : 'New-Loan'} Request for ${loan.platform === 'sme' ? user.phone : (user.ippis || user.customer_id)}`}</td>}
            <td nowrap="nowrap">{user && user.name && startCase(user.name.toLowerCase())}</td>
            <td>{`₦${numeral(amount).format('0,0.00')}`}</td>
            <td>{`₦${numeral(repayment).format('0,0.00')}`}</td>
            <td className="text-center">{status}</td>
            <td>
                <a onClick={() => openModal(user, loan)} className="cursor"><i className="icon-eye"/></a>
				<Link title="View Office" to={`/offices/view/${office ? office.id : 0}?search=${user.phone}&p=1&office=${office ? office.name : ''}`} style={{marginLeft: 12}}><i className="os-icon os-icon-link-3"/></Link>
            </td>
        </tr>
    );
};

export default TransItem;