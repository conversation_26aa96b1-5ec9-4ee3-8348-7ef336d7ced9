/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import truncate from 'lodash.truncate';
import moment from 'moment';
import qs from 'querystring';
import startCase from 'lodash.startcase';
import { bindActionCreators } from 'redux';
import Modal from 'react-modal';
import Pagination from 'antd/lib/pagination';

import { loadSupport, toggleUpdated, updateTicket } from '../actions/support';
import supportImg from '../assets/img/support.png';
import Loading from '../components/Loading';
import ReplyTicket from '../components/ReplyTicket';
import { httpRequest, baseURL, supportAPI, rootURL } from '../config/constants';
import { doNotify } from '../actions/general';
import { SHOW_PROFILE } from '../actions/types';
import { startBlock, stopBlock } from '../actions/ui-block';
import CreateTicket from '../components/CreateTicket';
import DatePicker from 'react-datepicker';

const itemRender = (current, type, originalElement) => {
	if (type === 'prev') {
		return <a>Prev</a>;
	}
	if (type === 'next') {
		return <a>Next</a>;
	}
	return originalElement;
};

const pageSize = 5;

class Support extends Component {
    constructor(props, context) {
        super(props, context);
        this.state = {
			modalIsOpen: false,
			ticket: null,
			hasNext: false,
			hasPrevious: false,
			nextPage: 1,
			previousPage: 1,
			currentPage: 1,
			total: 0,
			search: '',
			searchField: '',
			fetching: false,
			toDate: null,
			fromDate: null,
			_fromDate: '',
        	_toDate: '',	
			status: '',
			hasSearched: false,
			searchType: '',
			totalPage: null
		};
		
		this.showProfile = this.showProfile.bind(this);
		this.openModal = this.openModal.bind(this);
        this.closeModal = this.closeModal.bind(this);
    }
	
	componentWillMount() {
		Modal.setAppElement('body');
	}
	
	componentDidMount() {
		const { location } = this.props;
		const path = qs.parse(location.search.replace('?', ''));
		const page = path && path.page ? path.page : 1;
		const status = path && path.status ? path.status : "";
		const _fromDate = path && path.from ? path.from : '';
		const _toDate = path && path.to ? path.to : '';
		const type = path && path.type ? path.type : '';
		const q = path && path.q ? path.q : '';

		this.setState({ search: location.search, status, currentPage: page, searchType: type, searchField: q });
		this.fetchSupport(page, status, _fromDate, _toDate, type, q);
	}

	fetchSupport = async (page, status = '', from = '', to = '', searchType = '', searchField = '') => {
		this.setState({ fetching: true, ticket: null });

		const { lender } = this.props;
		const results = 5;
		window.scrollTo(0, 0);

		try {
			const rs = await httpRequest(`${baseURL}${supportAPI}?results=${results}&page=${page}&lender=${lender.id}&status=${status}&from=${from}&to=${to}&type=${searchType}&q=${searchField}`, 'GET', true);
			const result = rs.result;
			this.props.loadSupport(result.data);
			this.setState({
				currentPage: parseInt(page, 10),
				fetching: false,
				hasNext: result.hasNext,
				hasPrevious: result.hasPrevious,
				nextPage: parseInt(result.page, 10) + 1,
				previousPage: parseInt(result.page, 10) - 1,
				total: result.total,
				totalPage: result.num_of_pages,
			});
			if(searchType === 'ticket'){
				this.setState({ hasSearched: true })
			}
		}
		catch (error) {
			const message = error.message || 'error, failed loading support tickets';
			this.props.doNotify({ message, level: 'error', title: '' });
			this.setState({ fetching: false });
		}
	}

	componentWillUpdate(nextProps, nextState) {
		const { location } = nextProps;
		const query = qs.parse(nextProps.location.search.replace('?', ''));

		const pageNumber = query && query.page ? query.page : 1;
		const status = query && query.status ? query.status : '';
		const fromDate = query && query.from ? query.from : '';
		const toDate = query && query.to ? query.to : '';
		const type = query && query.type ? query.type : '';
		const q = query && query.q ? query.q : '';

		if (location.search !== nextState.search && !nextState.fetching) {
			this.setState({ 
				search: location.search,
				currentPage: pageNumber,
				status: status,
				_fromDate: fromDate,
				_toDate: toDate,
				fromDate: fromDate !== '' ? nextState.fromDate : null,
                toDate: toDate !== '' ? nextState.toDate : null, 
				searchType: type, 
				searchField: q
			});

			this.fetchSupport(pageNumber, status, fromDate, toDate, type, q);
		}

		if (nextProps.updated) {
			const ticket = nextProps.tickets.find(t => t.id === nextState.ticket.id);
			if (ticket) {
				this.setState({ ticket });
			}
		}
	}

	componentDidUpdate(prevProps, prevState) {
		if (prevProps.updated) {
			this.props.toggleUpdated();
		}
	}
	
    setTicket = ticket => () => {
        this.setState({ ticket });
    }

    onChangeStatus = e => {
		const status = e.target.value;
		const page = 1;
		const { searchType, searchField } = this.state;		
		const {from, to} = this.setDateRange()

		if(status === 'all' && searchType === 'ticket'){
			this.setState({ searchType: '', searchField: '', hasSearched: false })
			this.props.history.push(`/support`);
			return;
		}
		
		this.setState({ status });
		
		this.props.history.push(`/support?&page=${page}&status=${status}&from=${from}&to=${to}&type=${searchType}&q=${searchField}`);
	}

	onChange = pageNumber => {
		const { status, searchField, searchType } = this.state;
		const {from, to} = this.setDateRange()
		this.props.history.push(`/support?&page=${pageNumber}&status=${status}&from=${from}&to=${to}&type=${searchType}&q=${searchField}`);
	};

	handleChangeDate = (date, type) => {
        this.setState({ [type]: date }, () => {
            if(type === 'fromDate'){
                this.setState({ 'toDate': null });
            }
        });

        if(type === 'toDate'){
            const { status, fromDate, searchType, searchField } = this.state;
            const from = fromDate.format("DD-MM-YYYY");
			const to = date.format("DD-MM-YYYY");
			const page = 1;
			this.props.history.push(`/support?&page=${page}&status=${status}&from=${from}&to=${to}&type=${searchType}&q=${searchField}`);			
        }
    }

	setDateRange = () => {
		const { fromDate, toDate, _fromDate, _toDate } = this.state;
		let from, to ;
		if(fromDate !== null){
			from = fromDate ? fromDate.format("DD-MM-YYYY") : "";
			to = toDate ? toDate.format("DD-MM-YYYY") : "";
		}else{
			from = _fromDate;
			to = _toDate;
		}
		return { to, from }
	}

	doSearch = () => {
		const { searchType, searchField, status } = this.state;
		const page = 1;

		if(searchType == null || searchType === ''){
			this.props.doNotify({ message: 'Specify a search type', level: 'error', title: '' });
			return;
		}

		const {from, to} = this.setDateRange()
		this.props.history.push(`/support?&page=${page}&status=${status}&from=${from}&to=${to}&type=${searchType}&q=${searchField}`);
	}

	onChangeSearch = (e) => {
		const searchType = e.target.value;
		this.setState({ searchType: searchType })
	}

	handleChange = (e) => this.setState({ searchField: e.target.value })

	onExit = () => {
		if (this.state.hasSearched){
			this.setState({ searchField: '', searchType: '', hasSearched: false })
			this.props.history.push(`/support`);			
		} else{
			this.setState({ searchField: '', searchType: '', hasSearched: false })
		}
	}
    
    openModal() {
        document.body.className="pages modal-open";
        this.setState({ modalIsOpen: true });
    }

    closeModal = () => {
        this.setState({ modalIsOpen: false, ticket: null, total: parseInt(this.state.total, 10) + 1 });
        document.body.className="pages";
    }
	
	badge = response => {
		const explode = response.split(',');
		const type = explode[1];

		if(type === 'new ticket' || type === 'awaiting customer reply') {
			return 'badge-danger-inverted';
		}
		else if(type === 'customer-reply') {
			return 'badge-info-inverted';
		}
		else if(type === 'closed') {
			return 'badge-default-inverted';
		}

		return '';
	}
	
	pill = response => {
		const explode = response.split(',');
		const type = explode[1];
		
		if(type === 'new ticket' || type === 'awaiting customer reply') {
			return 'red';
		}
		else if(type === 'customer-reply') {
			return 'blue';
		}
		else if(type === 'closed') {
			return 'dark';
		}

		return '';
	}
	
	closeTicket = ticketId => async () => {
		this.props.startBlock();
		try {
			const { user, lender } = this.props;
			const data = { user_id: user.id, lender_id: lender.id };
			const rs = await httpRequest(`${baseURL}${supportAPI}/close/${ticketId}`, 'POST', true, data);
			this.props.updateTicket(rs.support);
			this.props.stopBlock();
			this.props.doNotify({ message: 'ticket closed', level: 'success', title: '' });
		}
		catch (error) {
			const message = error.message || 'error, failed to close ticket';
			this.props.stopBlock();
			this.props.doNotify({ message, level: 'error', title: '' });
		}
	}

	showProfile = (id) => () => this.props.showProfile({ show_profile: true, user_id: id });

	render() {
        const { user, tickets } = this.props;
		const { ticket, currentPage, total, fetching, modalIsOpen, fromDate, toDate, searchField, searchType } = this.state;
		return (
			<div className="content-i">
				<div className="content-box">
					<div className="support-index">
						<div className="support-tickets">
							<div className="support-tickets-header">
								<div className="tickets-control">
									<h5>Tickets</h5>
									<div className="new-ticket">
                                        <button className="btn btn-primary" onClick={this.openModal}><i className="os-icon os-icon-plus mr-1" /> New Ticket</button>
									</div>
								</div>

								<div className="col-12 mb-3">
									<div className="d-flex w-100" style={{ border: '1px solid #aaa', borderRadius: '4px' }}>
										<select className="form-control" onChange={this.onChangeSearch} value={searchType} style={{ flex: '4', border: 'none' }}>
											<option value="">Choose method</option>
											<option value="ticket">Search by Ticket No</option>
											<option value="phone">Search by Phone </option>
											<option value="ippis">Search by Ippis</option>								
										</select>

										<div className="input-group" style={{ flex: '7', border: 'none' }}>
											<input type="text" className="form-control" value={searchField} onChange={this.handleChange} style={{ border: 'none' }} placeholder="Search here.."/>
											<div className="input-group-append">
												<button disabled={searchField.length < 1} onClick={this.doSearch} className="bg-info text-white cursor px-2" style={{ border: 'none' }}>
													<i className="os-icon os-icon-search"/>
												</button>
												<button className="bg-danger text-white cursor" onClick={this.onExit} style={{ border: 'none' }}>
													<i className="os-icon os-icon-x"/>
												</button>
											</div>
										</div>
									</div>
								</div>

								<div className="row px-3 mb-2">
									<div className="col">
										<select className="form-control" onChange={this.onChangeStatus}>
											<option value="">Choose Status</option>
											<option value="all">All</option>
											<option value="open">Open</option>
											<option value="closed">Closed</option>
											<option value="answered">Awaiting Response</option>
										</select>
									</div>								
								</div>
				
																							
								<div className="col-12 mb-3">
									<div className="d-flex justify-content-between">
										<div className="col-lg-6 col-12 date_input mr-2">
											<label className="d-block">From: </label>
											<DatePicker
												selected={fromDate}
												onChange={e => this.handleChangeDate(e, 'fromDate')}
												dateFormat="DD-MMM-YYYY"
												placeholderText="From Date"
												className="form-control white-bg"
												showMonthDropdown
												showYearDropdown
												dropdownMode="select"
												strictParsing
												peekNextMonth={false}
											/>
										</div>
										<div className="col-12 col-lg-6 date_input">
											<label className="d-block">To: </label>										
											<DatePicker
												selected={toDate}
												onChange={e => this.handleChangeDate(e, 'toDate')}
												dateFormat="DD-MMM-YYYY"
												placeholderText="To Date"
												className="form-control white-bg"
												disabled={fromDate === null}
												minDate={fromDate}
												showMonthDropdown
												showYearDropdown
												dropdownMode="select"
												strictParsing
											/>
										</div>
									</div>
									
								</div>
							</div>
							{fetching && <Loading />}
							{!fetching && tickets.map((item, i) => {
								const replies = item.replies;
								return (
									<div className={`support-ticket ${ticket && ticket.id === item.id ? 'active':''}`} onClick={this.setTicket(item)} key={i}>
										<div className="st-meta">
											<div className={`badge ${this.badge(replies[replies.length - 1].response)}`}>{replies[replies.length - 1].response.split(',').pop()}</div>
											<div className={`status-pill ${this.pill(replies[replies.length - 1].response)}`} />
										</div>
										<div className="st-body">
											<div className="ticket-content">
												<h6 className="ticket-title"><span>{`Ticket #${item.id}:`}</span> {item.subject}</h6>
												<div className="ticket-description">{truncate(item.message, { 'length': 58, })}</div>
											</div>
										</div>
										{item.receiver ? (
											<div className="st-foot">
												<span className="label">To:</span>
												<a className="value" onClick={this.showProfile(item.receiver.id)}><span>{startCase(item.receiver.name.toLowerCase())}</span></a>
												<span className="label">Created By:</span>
												<a className="value"><span>{startCase(item.user.name.toLowerCase())}</span></a>
												<span className="label">Created On:</span>
												<span className="value">{moment(item.created_at).format('D MMM, YYYY [at] h:ma')}</span>
											</div>
										) : (
											<div className="st-foot">
												<span className="label">Customer:</span>
												<a className="value" onClick={this.showProfile(item.user.id)}><span>{startCase(item.user.name.toLowerCase())}</span></a>
												<span className="label">Created On:</span>
												<span className="value">{moment(item.created_at).format('D MMM, YYYY [at] h:ma')}</span>
											</div>
										)}
									</div>
								);
							})}
							{!fetching && tickets.length === 0 && (
								<div className="support-ticket">
									<div className="st-body">
										<div className="ticket-content">
											<div className="ticket-description">No tickets created</div>
										</div>
									</div>
								</div>
							)}
							{!fetching && (
								<div className="controls-below-table mb-3">
									<div className="table-records-info">{`${total} Ticket${total > 1 ? 's':''}`}</div>																		
								</div>
								
							)}
							{!fetching && (
								<div className="controls-below-table">
									<div className="pagination pagination-center mt-4 w-100">
										<Pagination
											current={currentPage}
											pageSize={pageSize}
											total={total}
											itemRender={itemRender}
											onChange={this.onChange}
										/>
									</div>
								</div>
							)}
						</div>
						<div className="support-ticket-content-w">
							{ticket ? (
								<div className="support-ticket-content">
									<div className="support-ticket-content-header" style={{justifyContent: 'space-between'}}>
										<h3 className="ticket-header"><span>{`Ticket #${ticket.id}:`}</span>{ticket.subject}</h3>
										<div className="close-ticket">
											{ticket.status === 0 && (
												<button className="btn btn-primary" onClick={this.closeTicket(ticket.id)}><i className="os-icon os-icon-close mr-1" /> Close Ticket</button>
											)}
										</div>
									</div>
									<div className="ticket-thread">
										{ticket.replies.map((conv, i) => {
											const attachments = conv.attachment && conv.attachment !== '' ? JSON.parse(conv.attachment) : [];
											return (
												conv.status === 1 && conv.response === 'closed,closed' ? (
													<div className="ticket-reply" key={i}>
														<div className="ticket-closed">{conv.message}</div>
													</div>
												) : (
													<div className={`ticket-reply ${conv.user_id === user.id ? '':'highlight'}`} key={i}>
														<div className="ticket-reply-info">
															{conv.user.username ? (
																<a className="author with-avatar no-cursor">
																	<span>
																		{startCase(conv.user.name.toLowerCase())}
																	</span>
																</a>
															) : (
																<a className="author with-avatar">
																	<span onClick={this.showProfile(conv.user_id)}>
																		{startCase(conv.user.name.toLowerCase())}
																	</span>
																</a>
															)}
															{conv.receiver_id ? (
																<span className="info-data">
																	<span className="label">{conv.receiver_id === conv.user_id ? 'replied on':'submitted on'}</span>
																	<span className="value">{moment(conv.created_at).format('D MMM, YYYY [at] h:ma')}</span>
																</span>
															) : (
																<span className="info-data">
																	<span className="label">{conv.user_id !== user.id ? 'submitted on':'replied on'}</span>
																	<span className="value">{moment(conv.created_at).format('D MMM, YYYY [at] h:ma')}</span>
																</span>
															)}
														</div>
														<div className="ticket-reply-content">{conv.message}</div>
														{attachments.length > 0 && (
															<div className="ticket-attachments">
																{attachments.map((file, i) => {
																	return (
																		<a className="attachment" rel="noopener noreferrer" target="_blank" href={`${rootURL}/attachment/${file.name}`} key={i}>
																			<i className={`os-icon ${file.icon}`} />
																			<span>{truncate(file.name, { 'length': 20, })}</span>
																		</a>
																	);
																})}
															</div>
														)}
													</div>
												)
											);
										})}
									</div>
									{ticket.status === 0 && (
										<ReplyTicket title="Reply Ticket" id={ticket.id} user={user} />
									)}
								</div>
							) : (
								<div className="support-ticket-content" style={{textAlign: 'center'}}>
									<img src={supportImg} style={{width: '200px'}} alt=""/>
								</div>
							)}
						</div>
					</div>
				</div>
				{modalIsOpen && (
                    <Modal
						isOpen={modalIsOpen}
						onRequestClose={this.closeModal}
						contentLabel="Create Ticket"
						shouldCloseOnOverlayClick={false}
						overlayClassName="modal-dialog modal-md"
						className="modal-content"
					>
						<CreateTicket 
                            closeModal={() => this.closeModal()}
                            status="new"
                            title="Create Ticket"
                            user={user}
                        />
					</Modal>
                )}
                {modalIsOpen? (<div className="modal-backdrop fade show"/>) : ''}
			</div>
		);
	}
}

const mapStateToProps = (state) => {
	return {
		user: state.user.user,
		tickets: state.support.list,
		updated: state.support.updated,
		lender: state.lender.profile,
	}
};

const showProfile = (data) => {
	return { type: SHOW_PROFILE, data };
};

const mapDispatchToProps = (dispatch) => {
	return bindActionCreators({ doNotify, loadSupport, toggleUpdated, showProfile, updateTicket, startBlock, stopBlock }, dispatch);
};

export default connect(mapStateToProps, mapDispatchToProps)(Support);
