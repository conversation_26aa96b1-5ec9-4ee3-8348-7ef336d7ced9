<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddFkAccountnos extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('accountnos', function (Blueprint $table) {
            $table->bigInteger('user_id')->unsigned()->change();
            $table->integer('bank_code_id')->unsigned()->nullable()->after('bank_code');

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('bank_code_id')->references('id')->on('bank_codes')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('accountnos', function (Blueprint $table) {
            Schema::disableForeignKeyConstraints();

            $table->dropForeign(['bank_code_id', 'user_id']);
            $table->dropColumn(['bank_code_id']);

            Schema::enableForeignKeyConstraints();
        });
    }
}
