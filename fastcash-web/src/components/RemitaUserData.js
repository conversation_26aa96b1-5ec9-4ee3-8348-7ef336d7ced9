/* eslint-disable jsx-a11y/heading-has-content */
import React from 'react';
import startCase from 'lodash.startcase';
import { Link } from 'react-router-dom';

import profile_bg1 from '../assets/img/profile_bg1.jpg';

const RemitaUserData = ({ user, lender, location }) => {
	return (
		<div className="content-panel">
            <div className="user-profile compact">
                <div className="up-head-w" style={{backgroundImage: "url("+profile_bg1+")"}}>
                    <div className="up-main-info">
                        <h2 className="up-header">{startCase(user.name.toLowerCase())}</h2>
                    </div>
                    <svg className="decor" width="842px" height="219px" viewBox="0 0 842 219"
                            preserveAspectRatio="xMaxYMax meet" version="1.1"
                            xmlns="http://www.w3.org/2000/svg"
                            xmlnsXlink="http://www.w3.org/1999/xlink">
                        <g transform="translate(-381.000000, -362.000000)" fill="#FFFFFF">
                            <path className="decor-path" d="M1223,362 L1223,581 L381,581 C868.912802,575.666667 1149.57947,502.666667 1223,362 Z"></path>
                        </g>
                    </svg>
                </div>
                <div className="up-contents">
                    <div className="element-wrapper">
                        {location.pathname.split('/')[1] !== 'edit-profile' && (
                            <div className="text-center mt-2">
                                <Link to={`/edit-profile/${user.id}`} className="text-danger"><i className="fa fa-edit"/> Edit Profile</Link>
                            </div>
                        )}
                        <h6 className="element-header" style={{marginTop: '0', marginBottom: '0.4rem'}}/>
                        <div className="element-box-tp">
                            <div className="users-list-w">
                                <div className="user-w with-status status-green">
                                    <div className="user-name">
                                        <h6 className="user-title">{user.ippis || '--'}</h6>
                                        <div className="user-role">IPPIS NUMBER</div>
                                    </div>
                                    <div className="user-action">
                                        <div className="os-icon os-icon-tasks-checked"/>
                                    </div>
                                </div>
                                <div className="user-w with-status status-green">
                                    <div className="user-name">
                                        <h6 className="user-title">{user.customer_id}</h6>
                                        <div className="user-role">CUSTOMER ID</div>
                                    </div>
                                    <div className="user-action">
                                        <div className="os-icon os-icon-tasks-checked"/>
                                    </div>
                                </div>
                                {lender && (
                                    <div className="user-w with-status status-green">
                                        <div className="user-name">
                                            <h6 className="user-title">{lender.name}</h6>
                                            <div className="user-role">LENDING PLATFORM</div>
                                        </div>
                                        <div className="user-action">
                                            <div className="os-icon os-icon-tasks-checked"/>
                                        </div>
                                    </div>
                                )}
                                <div className="user-w with-status status-green">
                                    <div className="user-name">
                                        <h6 className="user-title">{user.employer || '--'}</h6>
                                        <div className="user-role">EMPLOYER</div>
                                    </div>
                                    <div className="user-action">
                                        <div className="os-icon os-icon-tasks-checked"/>
                                    </div>
                                </div>
                                <div className="user-w with-status status-green">
                                    <div className="user-name">
                                        <h6 className="user-title">{user.email || '--'}</h6>
                                        <div className="user-role">EMAIL</div>
                                    </div>
                                    <div className="user-action">
                                        <div className="os-icon os-icon-tasks-checked"/>
                                    </div>
                                </div>
                                <div className="user-w with-status status-green">
                                    <div className="user-name">
                                        <h6 className="user-title">{user.gender ? startCase(user.gender) : '--'}</h6>
                                        <div className="user-role">GENDER</div>
                                    </div>
                                    <div className="user-action">
                                        <div className="os-icon os-icon-tasks-checked"/>
                                    </div>
                                </div>
                                <div className="user-w with-status status-green">
                                    <div className="user-name">
                                        <h6 className="user-title">{user.phone}</h6>
                                        <div className="user-role">PHONE NUMBER</div>
                                    </div>
                                    <div className="user-action">
                                        <div className="os-icon os-icon-tasks-checked"/>
                                    </div>
                                </div>
                                {user && user.accountno && user.accountno.map((act, i) => {
                                    return (
                                        <div key={i} className="user-w with-status status-green">
                                            <div>
                                                <div className="user-name" style={{marginBottom: '12px'}}>
                                                    <h6 className="user-title">{`${act.status.toUpperCase()} ACCOUNT`}</h6>
                                                </div>
                                                <div className="user-name">
                                                    <h6 className="user-title">{act.bank_name}</h6>
                                                    <div className="user-role">BANK NAME</div>
                                                </div>
                                                <div className="user-name">
                                                    <h6 className="user-title">{act.account_number}</h6>
                                                    <div className="user-role">ACCOUNT NUMBER</div>
                                                </div>
                                            </div>
                                            <div className="user-action">
                                                <div className="os-icon os-icon-tasks-checked"/>
                                            </div>
                                        </div>
                                    )
                                })}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
	);
};

export default RemitaUserData;