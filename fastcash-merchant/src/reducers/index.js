import { combineReducers } from 'redux';
import { routerReducer } from 'react-router-redux';
import { reducer as formReducer } from 'redux-form';

import user from './user';
import loan from './loan';
import settings from './settings';
import { uiblock } from './ui-block';
import lender from './lender';
import general from './general';

const reducers = combineReducers({
	uiblock,
	form: formReducer,
	routing: routerReducer,
	user,
	loans: loan,
	settings,
	lender,
	general,
});

export default reducers;
