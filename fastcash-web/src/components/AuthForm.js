/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Field, reduxForm, reset, change, SubmissionError, formValueSelector } from 'redux-form';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import qs from 'querystring';

import { baseURL, httpRequest, verifyAPI, loginAPI, authKey, resendEmailAPI } from '../config/constants';
import { setCurrentUser, setPayslipData, setAccountNumber, setUserData, setRemitaData, setPhoneNumber } from '../actions/user';
import loading from '../assets/img/loading.gif';
import { putSettings } from '../actions/settings';
import { doNotify } from '../actions/general';
import { loadLenders, setLenderProfile } from '../actions/lender';
import { startBlock, stopBlock } from '../actions/ui-block';

const validate = values => {
    const errors = {}
    if (!values.phone) {
        errors.phone = '';
    }
    return errors;
};

const renderField = ({input, id, icon, label, hideclass, type, meta: {touched, error, warning}}) => (
    <div className={`form-group${(touched && error ? 'has-error':'')} ${hideclass}`}>
        <label htmlFor={id}>{label}</label>
        <input {...input} placeholder={label} type={type} className="form-control" />
        <div className={`pre-icon os-icon ${icon}`}/>
        {touched &&
            ((error && <span className="help-block">{error}</span>) ||
                (warning && <span className="help-block">{warning}</span>))}
    </div>
);

const renderSelectField = ({input, icon, label, hideclass, data, meta: {touched, error}}) => (
    <div className={`form-group${(touched && error ? ' has-error':'')} ${hideclass}`}>
        <label>{label}</label>
        <select {...input} className="form-control">
            <option value="">Select Bank</option>
            {data.map(t => <option value={t.id} key={t.id}>{t.name}</option>)}
        </select>
		<div className={`pre-icon os-icon ${icon}`}/>
        {touched && error && <span className="help-block">{error}</span>}
    </div>
);

class AuthForm extends Component {
    state = {
        phone: '',
        account_number: '',
        bank: '',
        show_bankaccount: false,
        show_password: false,
        label: '',
        verror: '',
        vinfo: '',
        el: '',
        email: '',
        checking: false,
        verify: false,
        user_id: '',
        user_email: '',
        vsuccess: '',
		querystring: '',
    }

	componentDidMount() {
		let querystring = '';
		const query = qs.parse(this.props.location.search.replace('?', ''));
		if (query.fcl) {
			querystring = `?fcl=${query.fcl}`;
		}
		this.setState({ querystring });
	}

	notify = (title, message, type) => {
		this.props.doNotify({
			message,
			level: type,
			title,
		});
	};

	resendEmail = async () => {
        try {
            const { user_email, user_id } = this.state;
            this.props.startBlock();
			const data = { email: user_email, user_id };
			await httpRequest(`${baseURL}${resendEmailAPI}`, 'POST', false, data);
			this.setState({
				vsuccess: 'Please check your email to verify and activate your account',
				verror: '',
				vinfo: '',
				verify: false,
			});
			this.props.reset('authform');
			this.props.stopBlock();
		} catch (error) {
			this.props.stopBlock();
			const message = error.message || 'could not re-send email';
			this.notify('', message, 'error');
		}
	};

	verifyUser = async () => {
        try {
            const { methodType } = this.props;
			
            const { phone, account_number, bank, querystring } = this.state;
			
            const data = { phone, account_number, bank, methodType };
			this.setState({
				verror: '',
				vinfo: '',
				checking: true,
				verify: false,
				vsuccess: '',
			});

			const url = `${baseURL}${verifyAPI}`;
			const rs = await httpRequest(url, 'POST', false, data);

			if (rs.el === 10) {
				// restart with account number
				this.props.change('method', 'account_number');
				this.setState({
					show_bankaccount: true,
					checking: false,
					el: rs.el,
					vinfo: '',
				});
			} else if (rs.el === 13) {
				// enter password to login
				this.setState({ label: 'Password', email: rs.email });
				this.setState({
					show_password: true,
					checking: false,
					el: rs.el,
				});
			} else if (rs.el === 12) {
				// setup with remita
				this.props.setPhoneNumber(rs.phone);
				this.props.setAccountNumber(rs.account_number);
				this.props.reset('authform');
				this.props.setLenderProfile(null);
				this.props.setPayslipData(null);
				this.props.setRemitaData(rs.remitaDatum);
				this.props.loadLenders(rs.lenders);
				this.setState({
					checking: false,
					el: rs.el,
				});
				this.props.history.push(`/setup${querystring}`);
			} else {
				const message = 'account number not found';
				this.setState({ verror: message, checking: false });
				this.notify('', message, 'error');
			}
		} catch (error) {
			const info =
				error.message || 'Error, check your connection and try again';
			this.setState({ verror: info, checking: false });
			this.notify('', info, 'error');
		}
	};

	authClient = async (data) => {
		try {
			if (!data.password) {
				return false;
			}
			
			this.setState({ verify: false, vsuccess: '', vinfo: '' });
			const details = { ...data, el: this.state.el, email: this.state.email };
			const url = `${baseURL}${loginAPI}`;
			const rs = await httpRequest(url, 'POST', false, details);

			if (rs.self === 15) {
				this.notify('', 'Authentication successful!', 'success');
				this.props.setCurrentUser(rs.user);
				this.props.putSettings(rs.settings);
				localStorage.setItem(authKey, rs.token);
				this.props.reset('authform');
				document.body.className = 'pages';
				this.props.history.push({ pathname: '/dashboard' });
			} else if (rs.self === 16) {
				const { user_email, user_id, error } = rs;
				this.setState({ verify: true, user_email, user_id, verror: error });
				this.props.setUserData(user_id, user_email);
			}
		} catch (error) {
			const message = error.message || 'invalid sign in';
			this.notify('', message, 'error');
			throw new SubmissionError({
				_error: message,
			});
		}
	};

	updatePhone = (e) => {
		this.props.change('method', 'phone');
		this.setState({
			phone: e.target.value,
			verror: '',
			vinfo: '',
			show_bankaccount: false,
			show_password: false,
			verify: false,
			vsuccess: '',
		});
	};

	updateAccountNumber = (e) => {
		this.setState({
			account_number: e.target.value,
			verror: '',
			vinfo: '',
			show_password: false,
			verify: false,
			vsuccess: '',
		});
	};

	updateBank = (e) => {
		this.setState({
			bank: e.target.value,
			verror: '',
			vinfo: '',
			show_password: false,
			verify: false,
			vsuccess: '',
		});
	};
    
    render() {
        const { handleSubmit, error, submitting, pristine, banks } = this.props;
        const { show_bankaccount, show_password, label, verror, vinfo, checking, verify, vsuccess } = this.state;
        return (
            <div className="login-box">
                <div className="auth-box-w intro-description">
                    <h4 className="auth-header">Authentication Form</h4>
                    {error && <div className="alert alert-danger" dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${error}`}}></div>}
                    {verror !== '' && <div className="alert alert-danger" dangerouslySetInnerHTML={{__html: `<strong>Error!</strong> ${verror}`}}></div>}
                    {vinfo !== '' && <div className="alert alert-info" dangerouslySetInnerHTML={{__html: vinfo}}></div>}
                    {vsuccess !== '' && <div className="alert alert-success" dangerouslySetInnerHTML={{__html: `<strong>Success!</strong> ${vsuccess}`}}></div>}
                    {verify && <div className="alert alert-danger" style={{marginTop: '5px',}}>Did not recieve email? <a className="cursor" role="button" tabIndex="0" onClick={this.resendEmail}>Resend Email</a> or <Link to="/change-email">Change Email</Link></div>}
                    <form onSubmit={handleSubmit(this.authClient)} autoComplete="off">
						<Field
                            name="phone"
                            id="phone"
                            type="number"
                            component={renderField}
                            label="Phone Number"
                            icon="os-icon-user-male-circle"
                            onChange={this.updatePhone.bind(this)}
                        />
						<Field
                            name="account_number"
                            id="account_number"
                            type="number"
                            component={renderField}
							hideclass={`fade10 ${show_bankaccount ? '':'hidden'}`}
                            label="Salary Account Number"
                            icon="os-icon-user-male-circle"
                            onChange={this.updateAccountNumber.bind(this)}
                        />
						<Field
							name="bank"
							data={banks}
							component={renderSelectField}
                            hideclass={`fade10 ${show_bankaccount ? '':'hidden'}`}
							label="Select Salary Bank"
							icon="os-icon-user-male-circle"
							onChange={this.updateBank.bind(this)}
						/>
                        <Field
                            name="password"
                            id="password"
                            type="password"
                            component={renderField}
                            hideclass={`fade10 ${show_password ? '':'hidden'}`}
                            label={label}
                            icon="os-icon-fingerprint"
                        />
                        <div>
                            <Link className="btn btn-link" to="/forgot-password">Forgot Password? Click Here</Link>
                        </div>
                        <div className={`buttons-w ${show_password? '':'hidden'}`}>
                            <button className="btn btn-primary" disabled={pristine || submitting} type="submit">
                                {submitting? <img src={loading} alt=""/> : 'Authenticate'}
                            </button>
                        </div>
                        <div className={`buttons-w ${show_password? 'hidden':''}`}>
                            <button type="button" className="btn btn-primary" onClick={this.verifyUser}>
                                {checking? <img src={loading} alt=""/> : 'Authenticate'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        );
    }
}

AuthForm = reduxForm({
    form: 'authform',
    validate
})(AuthForm);

const selector = formValueSelector('authform');

const mapStateToProps = (state, ownProps) => {
    const method = selector(state, 'method');

    return {
        initialValues: {
            method: 'phone',
			phone: '',
			account_number: '',
			bank: '',
        },
        methodType: method,
		banks: state.general.banks,
    }
};

export default withRouter(connect(mapStateToProps, { reset, setCurrentUser, setPayslipData, putSettings, setAccountNumber, setPhoneNumber, doNotify, loadLenders, setLenderProfile, change, startBlock, stopBlock, setUserData, setRemitaData })(AuthForm));
