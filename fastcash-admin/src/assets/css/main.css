/*!
 * Bootstrap v4.0.0-alpha.6 (https://getbootstrap.com)
 * Copyright 2011-2017 The Bootstrap Authors
 * Copyright 2011-2017 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */


/*! normalize.css v5.0.0 | MIT License | github.com/necolas/normalize.css */

html {
    font-family: sans-serif;
    line-height: 1.15;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%
}

body {
    margin: 0
}

article,
aside,
footer,
header,
nav,
section {
    display: block
}

h1 {
    font-size: 2em;
    margin: 0.67em 0
}

figcaption,
figure,
main {
    display: block
}

figure {
    margin: 1em 40px
}

hr {
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
    height: 0;
    overflow: visible
}

pre {
    font-family: monospace, monospace;
    font-size: 1em
}

a {
    background-color: transparent;
    -webkit-text-decoration-skip: objects
}

a:active,
a:hover {
    outline-width: 0
}

abbr[title] {
    border-bottom: none;
    text-decoration: underline;
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

b,
strong {
    font-weight: inherit
}

b,
strong {
    font-weight: bolder
}

code,
kbd,
samp {
    font-family: monospace, monospace;
    font-size: 1em
}

dfn {
    font-style: italic
}

mark {
    background-color: #ff0;
    color: #000
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -0.25em
}

sup {
    top: -0.5em
}

audio,
video {
    display: inline-block
}

audio:not([controls]) {
    display: none;
    height: 0
}

img {
    border-style: none
}

svg:not(:root) {
    overflow: hidden
}

button,
input,
optgroup,
select,
textarea {
    font-family: sans-serif;
    font-size: 100%;
    line-height: 1.15;
    margin: 0
}

button,
input {
    overflow: visible
}

button,
select {
    text-transform: none
}

button,
html [type="button"],
[type="reset"],
[type="submit"] {
    -webkit-appearance: button
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
    border-style: none;
    padding: 0
}

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
    outline: 1px dotted ButtonText
}

fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em
}

legend {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: inherit;
    display: table;
    max-width: 100%;
    padding: 0;
    white-space: normal
}

progress {
    display: inline-block;
    vertical-align: baseline
}

textarea {
    overflow: auto
}

[type="checkbox"],
[type="radio"] {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0
}

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
    height: auto
}

[type="search"] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

details,
menu {
    display: block
}

summary {
    display: list-item
}

canvas {
    display: inline-block
}

template {
    display: none
}

[hidden] {
    display: none
}

@media print {
    *,
    *::before,
    *::after,
    p::first-letter,
    div::first-letter,
    blockquote::first-letter,
    li::first-letter,
    p::first-line,
    div::first-line,
    blockquote::first-line,
    li::first-line {
        text-shadow: none !important;
        -webkit-box-shadow: none !important;
        box-shadow: none !important
    }
    a,
    a:visited {
        text-decoration: underline
    }
    abbr[title]::after {
        content: " (" attr(title) ")"
    }
    pre {
        white-space: pre-wrap !important
    }
    pre,
    blockquote {
        border: 1px solid #999;
        page-break-inside: avoid
    }
    thead {
        display: table-header-group
    }
    tr,
    img {
        page-break-inside: avoid
    }
    p,
    h2,
    h3 {
        orphans: 3;
        widows: 3
    }
    h2,
    h3 {
        page-break-after: avoid
    }
    .navbar {
        display: none
    }
    .badge {
        border: 1px solid #000
    }
    .table {
        border-collapse: collapse !important
    }
    .table td,
    .table th {
        background-color: #fff !important
    }
    .table-bordered th,
    .table-bordered td {
        border: 1px solid #ddd !important
    }
}

html {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

*,
*::before,
*::after {
    -webkit-box-sizing: inherit;
    box-sizing: inherit
}

@-ms-viewport {
    width: device-width
}

html {
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: transparent
}

body {
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #3E4B5B;
    background-color: #f8faff
}

[tabindex="-1"]:focus {
    outline: none !important
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin-top: 0;
    margin-bottom: .5rem
}

p {
    margin-top: 0;
    margin-bottom: 1rem
}

abbr[title],
abbr[data-original-title] {
    cursor: help
}

address {
    margin-bottom: 1rem;
    font-style: normal;
    line-height: inherit
}

ol,
ul,
dl {
    margin-top: 0;
    margin-bottom: 1rem
}

ol ol,
ul ul,
ol ul,
ul ol {
    margin-bottom: 0
}

dt {
    font-weight: 500
}

dd {
    margin-bottom: .5rem;
    margin-left: 0
}

blockquote {
    margin: 0 0 1rem
}

a {
    color: #3b75e3;
    text-decoration: none
}

a:focus,
a:hover {
    color: #1a50b7;
    text-decoration: underline
}

a:not([href]):not([tabindex]) {
    color: inherit;
    text-decoration: none
}

a:not([href]):not([tabindex]):focus,
a:not([href]):not([tabindex]):hover {
    color: inherit;
    text-decoration: none
}

a:not([href]):not([tabindex]):focus {
    outline: 0
}

pre {
    margin-top: 0;
    margin-bottom: 1rem;
    overflow: auto
}

figure {
    margin: 0 0 1rem
}

img {
    vertical-align: middle
}

[role="button"] {
    cursor: pointer
}

a,
area,
button,
[role="button"],
input,
label,
select,
summary,
textarea {
    -ms-touch-action: manipulation;
    touch-action: manipulation
}

table {
    border-collapse: collapse;
    background-color: transparent
}

caption {
    padding-top: 0.8rem 0.9rem;
    padding-bottom: 0.8rem 0.9rem;
    color: #aaa;
    text-align: left;
    caption-side: bottom
}

th {
    text-align: left
}

label {
    display: inline-block;
    margin-bottom: .5rem
}

button:focus {
    outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color
}

input,
button,
select,
textarea {
    line-height: inherit
}

input[type="radio"]:disabled,
input[type="checkbox"]:disabled {
    cursor: not-allowed
}

input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
    -webkit-appearance: listbox
}

textarea {
    resize: vertical
}

fieldset {
    min-width: 0;
    padding: 0;
    margin: 0;
    border: 0
}

legend {
    display: block;
    width: 100%;
    padding: 0;
    margin-bottom: .5rem;
    font-size: 1.5rem;
    line-height: inherit
}

input[type="search"] {
    -webkit-appearance: none
}

output {
    display: inline-block
}

[hidden] {
    display: none !important
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
    margin-bottom: .5rem;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500;
    line-height: 1.1;
    color: #334152
}

h1,
.h1 {
    font-size: 2.3rem
}

h2,
.h2 {
    font-size: 1.8rem
}

h3,
.h3 {
    font-size: 1.75rem
}

h4,
.h4 {
    font-size: 1.5rem
}

h5,
.h5 {
    font-size: 1.25rem
}

h6,
.h6 {
    font-size: 1rem
}

.lead {
    font-size: 1.25rem;
    font-weight: 300
}

.display-1 {
    font-size: 6rem;
    font-weight: 300;
    line-height: 1.1
}

.display-2 {
    font-size: 5.5rem;
    font-weight: 300;
    line-height: 1.1
}

.display-3 {
    font-size: 4.5rem;
    font-weight: 300;
    line-height: 1.1
}

.display-4 {
    font-size: 3.5rem;
    font-weight: 300;
    line-height: 1.1
}

hr {
    margin-top: 1rem;
    margin-bottom: 1rem;
    border: 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

small,
.small {
    font-size: 80%;
    font-weight: 400
}

mark,
.mark {
    padding: .2em;
    background-color: #fcf8e3
}

.list-unstyled {
    padding-left: 0;
    list-style: none
}

.list-inline {
    padding-left: 0;
    list-style: none
}

.list-inline-item {
    display: inline-block
}

.list-inline-item:not(:last-child) {
    margin-right: 5px
}

.initialism {
    font-size: 90%;
    text-transform: uppercase
}

.blockquote {
    padding: .5rem 1rem;
    margin-bottom: 1rem;
    font-size: 1.125rem;
    border-left: .25rem solid #eceeef
}

.blockquote-footer {
    display: block;
    font-size: 80%;
    color: #636c72
}

.blockquote-footer::before {
    content: "\2014 \00A0"
}

.blockquote-reverse {
    padding-right: 1rem;
    padding-left: 0;
    text-align: right;
    border-right: .25rem solid #eceeef;
    border-left: 0
}

.blockquote-reverse .blockquote-footer::before {
    content: ""
}

.blockquote-reverse .blockquote-footer::after {
    content: "\00A0 \2014"
}

.img-fluid {
    max-width: 100%;
    height: auto
}

.img-thumbnail {
    padding: .25rem;
    background-color: #f8faff;
    border: 1px solid #ddd;
    border-radius: .25rem;
    -webkit-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    max-width: 100%;
    height: auto
}

.figure {
    display: inline-block
}

.figure-img {
    margin-bottom: .5rem;
    line-height: 1
}

.figure-caption {
    font-size: 90%;
    color: #636c72
}

code,
kbd,
pre,
samp {
    font-family: Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace
}

code {
    padding: .2rem .4rem;
    font-size: 90%;
    color: #5782e4;
    background-color: #f2f7ff;
    border-radius: .25rem
}

a>code {
    padding: 0;
    color: inherit;
    background-color: inherit
}

kbd {
    padding: .2rem .4rem;
    font-size: 90%;
    color: #fff;
    background-color: #292b2c;
    border-radius: .2rem
}

kbd kbd {
    padding: 0;
    font-size: 100%;
    font-weight: 500
}

pre {
    display: block;
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 90%;
    color: #292b2c
}

pre code {
    padding: 0;
    font-size: inherit;
    color: inherit;
    background-color: transparent;
    border-radius: 0
}

.pre-scrollable {
    max-height: 340px;
    overflow-y: scroll
}

.container {
    position: relative;
    margin-left: auto;
    margin-right: auto;
    padding-right: 15px;
    padding-left: 15px
}

@media (min-width: 576px) {
    .container {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 768px) {
    .container {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 992px) {
    .container {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 1200px) {
    .container {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 1450px) {
    .container {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 576px) {
    .container {
        width: 540px;
        max-width: 100%
    }
}

@media (min-width: 768px) {
    .container {
        width: 720px;
        max-width: 100%
    }
}

@media (min-width: 992px) {
    .container {
        width: 960px;
        max-width: 100%
    }
}

@media (min-width: 1200px) {
    .container {
        width: 1140px;
        max-width: 100%
    }
}

@media (min-width: 1450px) {
    .container {
        width: 1380px;
        max-width: 100%
    }
}

.container-fluid {
    position: relative;
    margin-left: auto;
    margin-right: auto;
    padding-right: 15px;
    padding-left: 15px
}

@media (min-width: 576px) {
    .container-fluid {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 768px) {
    .container-fluid {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 992px) {
    .container-fluid {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 1200px) {
    .container-fluid {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 1450px) {
    .container-fluid {
        padding-right: 15px;
        padding-left: 15px
    }
}

.row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px
}

@media (min-width: 576px) {
    .row {
        margin-right: -15px;
        margin-left: -15px
    }
}

@media (min-width: 768px) {
    .row {
        margin-right: -15px;
        margin-left: -15px
    }
}

@media (min-width: 992px) {
    .row {
        margin-right: -15px;
        margin-left: -15px
    }
}

@media (min-width: 1200px) {
    .row {
        margin-right: -15px;
        margin-left: -15px
    }
}

@media (min-width: 1450px) {
    .row {
        margin-right: -15px;
        margin-left: -15px
    }
}

.no-gutters {
    margin-right: 0;
    margin-left: 0
}

.no-gutters>.col,
.no-gutters>[class*="col-"] {
    padding-right: 0;
    padding-left: 0
}

.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12,
.col,
.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm,
.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md,
.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg,
.col-xl-1,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl,
.col-xxl-1,
.col-xxl-2,
.col-xxl-3,
.col-xxl-4,
.col-xxl-5,
.col-xxl-6,
.col-xxl-7,
.col-xxl-8,
.col-xxl-9,
.col-xxl-10,
.col-xxl-11,
.col-xxl-12,
.col-xxl {
    position: relative;
    width: 100%;
    min-height: 1px;
    padding-right: 15px;
    padding-left: 15px
}

.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12,
.col,
.col-auto,
.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm,
.col-sm-auto,
.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md,
.col-md-auto,
.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg,
.col-lg-auto,
.col-xl-1,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl,
.col-xl-auto,
.col-xxl-1,
.col-xxl-2,
.col-xxl-3,
.col-xxl-4,
.col-xxl-5,
.col-xxl-6,
.col-xxl-7,
.col-xxl-8,
.col-xxl-9,
.col-xxl-10,
.col-xxl-11,
.col-xxl-12,
.col-xxl,
.col-xxl-auto,
.col-xxxl-1,
.col-xxxl-2,
.col-xxxl-3,
.col-xxxl-4,
.col-xxxl-5,
.col-xxxl-6,
.col-xxxl-7,
.col-xxxl-8,
.col-xxxl-9,
.col-xxxl-10,
.col-xxxl-11,
.col-xxxl-12,
.col-xxxl,
.col-xxxl-auto,
.col-xxxxl-1,
.col-xxxxl-2,
.col-xxxxl-3,
.col-xxxxl-4,
.col-xxxxl-5,
.col-xxxxl-6,
.col-xxxxl-7,
.col-xxxxl-8,
.col-xxxxl-9,
.col-xxxxl-10,
.col-xxxxl-11,
.col-xxxxl-12,
.col-xxxxl,
.col-xxxxl-auto {
	position: relative;
	width: 100%;
	min-height: 1px;
	padding-right: 10px;
	padding-left: 10px;
}

@media (min-width: 576px) {
    .col-1,
    .col-2,
    .col-3,
    .col-4,
    .col-5,
    .col-6,
    .col-7,
    .col-8,
    .col-9,
    .col-10,
    .col-11,
    .col-12,
    .col,
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12,
    .col-sm,
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12,
    .col-md,
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12,
    .col-lg,
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12,
    .col-xl,
    .col-xxl-1,
    .col-xxl-2,
    .col-xxl-3,
    .col-xxl-4,
    .col-xxl-5,
    .col-xxl-6,
    .col-xxl-7,
    .col-xxl-8,
    .col-xxl-9,
    .col-xxl-10,
    .col-xxl-11,
    .col-xxl-12,
    .col-xxl {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 768px) {
    .col-1,
    .col-2,
    .col-3,
    .col-4,
    .col-5,
    .col-6,
    .col-7,
    .col-8,
    .col-9,
    .col-10,
    .col-11,
    .col-12,
    .col,
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12,
    .col-sm,
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12,
    .col-md,
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12,
    .col-lg,
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12,
    .col-xl,
    .col-xxl-1,
    .col-xxl-2,
    .col-xxl-3,
    .col-xxl-4,
    .col-xxl-5,
    .col-xxl-6,
    .col-xxl-7,
    .col-xxl-8,
    .col-xxl-9,
    .col-xxl-10,
    .col-xxl-11,
    .col-xxl-12,
    .col-xxl {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 992px) {
    .col-1,
    .col-2,
    .col-3,
    .col-4,
    .col-5,
    .col-6,
    .col-7,
    .col-8,
    .col-9,
    .col-10,
    .col-11,
    .col-12,
    .col,
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12,
    .col-sm,
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12,
    .col-md,
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12,
    .col-lg,
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12,
    .col-xl,
    .col-xxl-1,
    .col-xxl-2,
    .col-xxl-3,
    .col-xxl-4,
    .col-xxl-5,
    .col-xxl-6,
    .col-xxl-7,
    .col-xxl-8,
    .col-xxl-9,
    .col-xxl-10,
    .col-xxl-11,
    .col-xxl-12,
    .col-xxl {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 1200px) {
    .col-1,
    .col-2,
    .col-3,
    .col-4,
    .col-5,
    .col-6,
    .col-7,
    .col-8,
    .col-9,
    .col-10,
    .col-11,
    .col-12,
    .col,
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12,
    .col-sm,
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12,
    .col-md,
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12,
    .col-lg,
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12,
    .col-xl,
    .col-xxl-1,
    .col-xxl-2,
    .col-xxl-3,
    .col-xxl-4,
    .col-xxl-5,
    .col-xxl-6,
    .col-xxl-7,
    .col-xxl-8,
    .col-xxl-9,
    .col-xxl-10,
    .col-xxl-11,
    .col-xxl-12,
    .col-xxl {
        padding-right: 15px;
        padding-left: 15px
    }
}

@media (min-width: 1450px) {
    .col-1,
    .col-2,
    .col-3,
    .col-4,
    .col-5,
    .col-6,
    .col-7,
    .col-8,
    .col-9,
    .col-10,
    .col-11,
    .col-12,
    .col,
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12,
    .col-sm,
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12,
    .col-md,
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12,
    .col-lg,
    .col-xl-1,
    .col-xl-2,
    .col-xl-3,
    .col-xl-4,
    .col-xl-5,
    .col-xl-6,
    .col-xl-7,
    .col-xl-8,
    .col-xl-9,
    .col-xl-10,
    .col-xl-11,
    .col-xl-12,
    .col-xl,
    .col-xxl-1,
    .col-xxl-2,
    .col-xxl-3,
    .col-xxl-4,
    .col-xxl-5,
    .col-xxl-6,
    .col-xxl-7,
    .col-xxl-8,
    .col-xxl-9,
    .col-xxl-10,
    .col-xxl-11,
    .col-xxl-12,
    .col-xxl {
        padding-right: 15px;
        padding-left: 15px
    }
}

.col {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%
}

.col-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto
}

.col-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 8.3333333333%;
    flex: 0 0 8.3333333333%;
    max-width: 8.3333333333%
}

.col-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 16.6666666667%;
    flex: 0 0 16.6666666667%;
    max-width: 16.6666666667%
}

.col-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%
}

.col-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.3333333333%;
    flex: 0 0 33.3333333333%;
    max-width: 33.3333333333%
}

.col-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 41.6666666667%;
    flex: 0 0 41.6666666667%;
    max-width: 41.6666666667%
}

.col-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%
}

.col-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 58.3333333333%;
    flex: 0 0 58.3333333333%;
    max-width: 58.3333333333%
}

.col-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.6666666667%;
    flex: 0 0 66.6666666667%;
    max-width: 66.6666666667%
}

.col-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%
}

.col-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 83.3333333333%;
    flex: 0 0 83.3333333333%;
    max-width: 83.3333333333%
}

.col-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 91.6666666667%;
    flex: 0 0 91.6666666667%;
    max-width: 91.6666666667%
}

.col-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%
}

.pull-0 {
    right: auto
}

.pull-1 {
    right: 8.3333333333%
}

.pull-2 {
    right: 16.6666666667%
}

.pull-3 {
    right: 25%
}

.pull-4 {
    right: 33.3333333333%
}

.pull-5 {
    right: 41.6666666667%
}

.pull-6 {
    right: 50%
}

.pull-7 {
    right: 58.3333333333%
}

.pull-8 {
    right: 66.6666666667%
}

.pull-9 {
    right: 75%
}

.pull-10 {
    right: 83.3333333333%
}

.pull-11 {
    right: 91.6666666667%
}

.pull-12 {
    right: 100%
}

.push-0 {
    left: auto
}

.push-1 {
    left: 8.3333333333%
}

.push-2 {
    left: 16.6666666667%
}

.push-3 {
    left: 25%
}

.push-4 {
    left: 33.3333333333%
}

.push-5 {
    left: 41.6666666667%
}

.push-6 {
    left: 50%
}

.push-7 {
    left: 58.3333333333%
}

.push-8 {
    left: 66.6666666667%
}

.push-9 {
    left: 75%
}

.push-10 {
    left: 83.3333333333%
}

.push-11 {
    left: 91.6666666667%
}

.push-12 {
    left: 100%
}

.offset-1 {
    margin-left: 8.3333333333%
}

.offset-2 {
    margin-left: 16.6666666667%
}

.offset-3 {
    margin-left: 25%
}

.offset-4 {
    margin-left: 33.3333333333%
}

.offset-5 {
    margin-left: 41.6666666667%
}

.offset-6 {
    margin-left: 50%
}

.offset-7 {
    margin-left: 58.3333333333%
}

.offset-8 {
    margin-left: 66.6666666667%
}

.offset-9 {
    margin-left: 75%
}

.offset-10 {
    margin-left: 83.3333333333%
}

.offset-11 {
    margin-left: 91.6666666667%
}

@media (min-width: 576px) {
    .col-sm {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }
    .col-sm-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }
    .col-sm-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.3333333333%;
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }
    .col-sm-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.6666666667%;
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }
    .col-sm-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }
    .col-sm-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3333333333%;
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }
    .col-sm-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.6666666667%;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }
    .col-sm-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }
    .col-sm-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.3333333333%;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }
    .col-sm-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.6666666667%;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }
    .col-sm-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }
    .col-sm-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.3333333333%;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }
    .col-sm-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.6666666667%;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }
    .col-sm-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }
    .pull-sm-0 {
        right: auto
    }
    .pull-sm-1 {
        right: 8.3333333333%
    }
    .pull-sm-2 {
        right: 16.6666666667%
    }
    .pull-sm-3 {
        right: 25%
    }
    .pull-sm-4 {
        right: 33.3333333333%
    }
    .pull-sm-5 {
        right: 41.6666666667%
    }
    .pull-sm-6 {
        right: 50%
    }
    .pull-sm-7 {
        right: 58.3333333333%
    }
    .pull-sm-8 {
        right: 66.6666666667%
    }
    .pull-sm-9 {
        right: 75%
    }
    .pull-sm-10 {
        right: 83.3333333333%
    }
    .pull-sm-11 {
        right: 91.6666666667%
    }
    .pull-sm-12 {
        right: 100%
    }
    .push-sm-0 {
        left: auto
    }
    .push-sm-1 {
        left: 8.3333333333%
    }
    .push-sm-2 {
        left: 16.6666666667%
    }
    .push-sm-3 {
        left: 25%
    }
    .push-sm-4 {
        left: 33.3333333333%
    }
    .push-sm-5 {
        left: 41.6666666667%
    }
    .push-sm-6 {
        left: 50%
    }
    .push-sm-7 {
        left: 58.3333333333%
    }
    .push-sm-8 {
        left: 66.6666666667%
    }
    .push-sm-9 {
        left: 75%
    }
    .push-sm-10 {
        left: 83.3333333333%
    }
    .push-sm-11 {
        left: 91.6666666667%
    }
    .push-sm-12 {
        left: 100%
    }
    .offset-sm-0 {
        margin-left: 0%
    }
    .offset-sm-1 {
        margin-left: 8.3333333333%
    }
    .offset-sm-2 {
        margin-left: 16.6666666667%
    }
    .offset-sm-3 {
        margin-left: 25%
    }
    .offset-sm-4 {
        margin-left: 33.3333333333%
    }
    .offset-sm-5 {
        margin-left: 41.6666666667%
    }
    .offset-sm-6 {
        margin-left: 50%
    }
    .offset-sm-7 {
        margin-left: 58.3333333333%
    }
    .offset-sm-8 {
        margin-left: 66.6666666667%
    }
    .offset-sm-9 {
        margin-left: 75%
    }
    .offset-sm-10 {
        margin-left: 83.3333333333%
    }
    .offset-sm-11 {
        margin-left: 91.6666666667%
    }
}

@media (min-width: 768px) {
    .col-md {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }
    .col-md-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }
    .col-md-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.3333333333%;
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }
    .col-md-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.6666666667%;
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }
    .col-md-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }
    .col-md-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3333333333%;
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }
    .col-md-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.6666666667%;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }
    .col-md-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }
    .col-md-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.3333333333%;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }
    .col-md-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.6666666667%;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }
    .col-md-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }
    .col-md-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.3333333333%;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }
    .col-md-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.6666666667%;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }
    .col-md-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }
    .pull-md-0 {
        right: auto
    }
    .pull-md-1 {
        right: 8.3333333333%
    }
    .pull-md-2 {
        right: 16.6666666667%
    }
    .pull-md-3 {
        right: 25%
    }
    .pull-md-4 {
        right: 33.3333333333%
    }
    .pull-md-5 {
        right: 41.6666666667%
    }
    .pull-md-6 {
        right: 50%
    }
    .pull-md-7 {
        right: 58.3333333333%
    }
    .pull-md-8 {
        right: 66.6666666667%
    }
    .pull-md-9 {
        right: 75%
    }
    .pull-md-10 {
        right: 83.3333333333%
    }
    .pull-md-11 {
        right: 91.6666666667%
    }
    .pull-md-12 {
        right: 100%
    }
    .push-md-0 {
        left: auto
    }
    .push-md-1 {
        left: 8.3333333333%
    }
    .push-md-2 {
        left: 16.6666666667%
    }
    .push-md-3 {
        left: 25%
    }
    .push-md-4 {
        left: 33.3333333333%
    }
    .push-md-5 {
        left: 41.6666666667%
    }
    .push-md-6 {
        left: 50%
    }
    .push-md-7 {
        left: 58.3333333333%
    }
    .push-md-8 {
        left: 66.6666666667%
    }
    .push-md-9 {
        left: 75%
    }
    .push-md-10 {
        left: 83.3333333333%
    }
    .push-md-11 {
        left: 91.6666666667%
    }
    .push-md-12 {
        left: 100%
    }
    .offset-md-0 {
        margin-left: 0%
    }
    .offset-md-1 {
        margin-left: 8.3333333333%
    }
    .offset-md-2 {
        margin-left: 16.6666666667%
    }
    .offset-md-3 {
        margin-left: 25%
    }
    .offset-md-4 {
        margin-left: 33.3333333333%
    }
    .offset-md-5 {
        margin-left: 41.6666666667%
    }
    .offset-md-6 {
        margin-left: 50%
    }
    .offset-md-7 {
        margin-left: 58.3333333333%
    }
    .offset-md-8 {
        margin-left: 66.6666666667%
    }
    .offset-md-9 {
        margin-left: 75%
    }
    .offset-md-10 {
        margin-left: 83.3333333333%
    }
    .offset-md-11 {
        margin-left: 91.6666666667%
    }
}

@media (min-width: 992px) {
    .col-lg {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }
    .col-lg-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }
    .col-lg-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.3333333333%;
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }
    .col-lg-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.6666666667%;
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }
    .col-lg-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }
    .col-lg-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3333333333%;
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }
    .col-lg-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.6666666667%;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }
    .col-lg-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }
    .col-lg-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.3333333333%;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }
    .col-lg-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.6666666667%;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }
    .col-lg-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }
    .col-lg-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.3333333333%;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }
    .col-lg-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.6666666667%;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }
    .col-lg-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }
    .pull-lg-0 {
        right: auto
    }
    .pull-lg-1 {
        right: 8.3333333333%
    }
    .pull-lg-2 {
        right: 16.6666666667%
    }
    .pull-lg-3 {
        right: 25%
    }
    .pull-lg-4 {
        right: 33.3333333333%
    }
    .pull-lg-5 {
        right: 41.6666666667%
    }
    .pull-lg-6 {
        right: 50%
    }
    .pull-lg-7 {
        right: 58.3333333333%
    }
    .pull-lg-8 {
        right: 66.6666666667%
    }
    .pull-lg-9 {
        right: 75%
    }
    .pull-lg-10 {
        right: 83.3333333333%
    }
    .pull-lg-11 {
        right: 91.6666666667%
    }
    .pull-lg-12 {
        right: 100%
    }
    .push-lg-0 {
        left: auto
    }
    .push-lg-1 {
        left: 8.3333333333%
    }
    .push-lg-2 {
        left: 16.6666666667%
    }
    .push-lg-3 {
        left: 25%
    }
    .push-lg-4 {
        left: 33.3333333333%
    }
    .push-lg-5 {
        left: 41.6666666667%
    }
    .push-lg-6 {
        left: 50%
    }
    .push-lg-7 {
        left: 58.3333333333%
    }
    .push-lg-8 {
        left: 66.6666666667%
    }
    .push-lg-9 {
        left: 75%
    }
    .push-lg-10 {
        left: 83.3333333333%
    }
    .push-lg-11 {
        left: 91.6666666667%
    }
    .push-lg-12 {
        left: 100%
    }
    .offset-lg-0 {
        margin-left: 0%
    }
    .offset-lg-1 {
        margin-left: 8.3333333333%
    }
    .offset-lg-2 {
        margin-left: 16.6666666667%
    }
    .offset-lg-3 {
        margin-left: 25%
    }
    .offset-lg-4 {
        margin-left: 33.3333333333%
    }
    .offset-lg-5 {
        margin-left: 41.6666666667%
    }
    .offset-lg-6 {
        margin-left: 50%
    }
    .offset-lg-7 {
        margin-left: 58.3333333333%
    }
    .offset-lg-8 {
        margin-left: 66.6666666667%
    }
    .offset-lg-9 {
        margin-left: 75%
    }
    .offset-lg-10 {
        margin-left: 83.3333333333%
    }
    .offset-lg-11 {
        margin-left: 91.6666666667%
    }
}

@media (min-width: 1200px) {
    .col-xl {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }
    .col-xl-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }
    .col-xl-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.3333333333%;
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }
    .col-xl-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.6666666667%;
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }
    .col-xl-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }
    .col-xl-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3333333333%;
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }
    .col-xl-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.6666666667%;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }
    .col-xl-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }
    .col-xl-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.3333333333%;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }
    .col-xl-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.6666666667%;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }
    .col-xl-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }
    .col-xl-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.3333333333%;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }
    .col-xl-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.6666666667%;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }
    .col-xl-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }
    .pull-xl-0 {
        right: auto
    }
    .pull-xl-1 {
        right: 8.3333333333%
    }
    .pull-xl-2 {
        right: 16.6666666667%
    }
    .pull-xl-3 {
        right: 25%
    }
    .pull-xl-4 {
        right: 33.3333333333%
    }
    .pull-xl-5 {
        right: 41.6666666667%
    }
    .pull-xl-6 {
        right: 50%
    }
    .pull-xl-7 {
        right: 58.3333333333%
    }
    .pull-xl-8 {
        right: 66.6666666667%
    }
    .pull-xl-9 {
        right: 75%
    }
    .pull-xl-10 {
        right: 83.3333333333%
    }
    .pull-xl-11 {
        right: 91.6666666667%
    }
    .pull-xl-12 {
        right: 100%
    }
    .push-xl-0 {
        left: auto
    }
    .push-xl-1 {
        left: 8.3333333333%
    }
    .push-xl-2 {
        left: 16.6666666667%
    }
    .push-xl-3 {
        left: 25%
    }
    .push-xl-4 {
        left: 33.3333333333%
    }
    .push-xl-5 {
        left: 41.6666666667%
    }
    .push-xl-6 {
        left: 50%
    }
    .push-xl-7 {
        left: 58.3333333333%
    }
    .push-xl-8 {
        left: 66.6666666667%
    }
    .push-xl-9 {
        left: 75%
    }
    .push-xl-10 {
        left: 83.3333333333%
    }
    .push-xl-11 {
        left: 91.6666666667%
    }
    .push-xl-12 {
        left: 100%
    }
    .offset-xl-0 {
        margin-left: 0%
    }
    .offset-xl-1 {
        margin-left: 8.3333333333%
    }
    .offset-xl-2 {
        margin-left: 16.6666666667%
    }
    .offset-xl-3 {
        margin-left: 25%
    }
    .offset-xl-4 {
        margin-left: 33.3333333333%
    }
    .offset-xl-5 {
        margin-left: 41.6666666667%
    }
    .offset-xl-6 {
        margin-left: 50%
    }
    .offset-xl-7 {
        margin-left: 58.3333333333%
    }
    .offset-xl-8 {
        margin-left: 66.6666666667%
    }
    .offset-xl-9 {
        margin-left: 75%
    }
    .offset-xl-10 {
        margin-left: 83.3333333333%
    }
    .offset-xl-11 {
        margin-left: 91.6666666667%
    }
}

@media (min-width: 1450px) {
    .col-xxl {
        -ms-flex-preferred-size: 0;
        flex-basis: 0;
        -webkit-box-flex: 1;
        -ms-flex-positive: 1;
        flex-grow: 1;
        max-width: 100%
    }
    .col-xxl-auto {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        width: auto
    }
    .col-xxl-1 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 8.3333333333%;
        flex: 0 0 8.3333333333%;
        max-width: 8.3333333333%
    }
    .col-xxl-2 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 16.6666666667%;
        flex: 0 0 16.6666666667%;
        max-width: 16.6666666667%
    }
    .col-xxl-3 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%
    }
    .col-xxl-4 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 33.3333333333%;
        flex: 0 0 33.3333333333%;
        max-width: 33.3333333333%
    }
    .col-xxl-5 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 41.6666666667%;
        flex: 0 0 41.6666666667%;
        max-width: 41.6666666667%
    }
    .col-xxl-6 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }
    .col-xxl-7 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 58.3333333333%;
        flex: 0 0 58.3333333333%;
        max-width: 58.3333333333%
    }
    .col-xxl-8 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 66.6666666667%;
        flex: 0 0 66.6666666667%;
        max-width: 66.6666666667%
    }
    .col-xxl-9 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 75%;
        flex: 0 0 75%;
        max-width: 75%
    }
    .col-xxl-10 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 83.3333333333%;
        flex: 0 0 83.3333333333%;
        max-width: 83.3333333333%
    }
    .col-xxl-11 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 91.6666666667%;
        flex: 0 0 91.6666666667%;
        max-width: 91.6666666667%
    }
    .col-xxl-12 {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%
    }
    .pull-xxl-0 {
        right: auto
    }
    .pull-xxl-1 {
        right: 8.3333333333%
    }
    .pull-xxl-2 {
        right: 16.6666666667%
    }
    .pull-xxl-3 {
        right: 25%
    }
    .pull-xxl-4 {
        right: 33.3333333333%
    }
    .pull-xxl-5 {
        right: 41.6666666667%
    }
    .pull-xxl-6 {
        right: 50%
    }
    .pull-xxl-7 {
        right: 58.3333333333%
    }
    .pull-xxl-8 {
        right: 66.6666666667%
    }
    .pull-xxl-9 {
        right: 75%
    }
    .pull-xxl-10 {
        right: 83.3333333333%
    }
    .pull-xxl-11 {
        right: 91.6666666667%
    }
    .pull-xxl-12 {
        right: 100%
    }
    .push-xxl-0 {
        left: auto
    }
    .push-xxl-1 {
        left: 8.3333333333%
    }
    .push-xxl-2 {
        left: 16.6666666667%
    }
    .push-xxl-3 {
        left: 25%
    }
    .push-xxl-4 {
        left: 33.3333333333%
    }
    .push-xxl-5 {
        left: 41.6666666667%
    }
    .push-xxl-6 {
        left: 50%
    }
    .push-xxl-7 {
        left: 58.3333333333%
    }
    .push-xxl-8 {
        left: 66.6666666667%
    }
    .push-xxl-9 {
        left: 75%
    }
    .push-xxl-10 {
        left: 83.3333333333%
    }
    .push-xxl-11 {
        left: 91.6666666667%
    }
    .push-xxl-12 {
        left: 100%
    }
    .offset-xxl-0 {
        margin-left: 0%
    }
    .offset-xxl-1 {
        margin-left: 8.3333333333%
    }
    .offset-xxl-2 {
        margin-left: 16.6666666667%
    }
    .offset-xxl-3 {
        margin-left: 25%
    }
    .offset-xxl-4 {
        margin-left: 33.3333333333%
    }
    .offset-xxl-5 {
        margin-left: 41.6666666667%
    }
    .offset-xxl-6 {
        margin-left: 50%
    }
    .offset-xxl-7 {
        margin-left: 58.3333333333%
    }
    .offset-xxl-8 {
        margin-left: 66.6666666667%
    }
    .offset-xxl-9 {
        margin-left: 75%
    }
    .offset-xxl-10 {
        margin-left: 83.3333333333%
    }
    .offset-xxl-11 {
        margin-left: 91.6666666667%
    }
}

.table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 1rem
}

.table th,
.table td {
    padding: 0.8rem 0.4rem;
    vertical-align: top;
    border-top: 1px solid rgba(83, 101, 140, 0.33)
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid rgba(83, 101, 140, 0.33)
}

.table tbody+tbody {
    border-top: 2px solid rgba(83, 101, 140, 0.33)
}

.table .table {
    background-color: #f8faff
}

.table-sm th,
.table-sm td {
    padding: .3rem
}

.table-bordered {
    border: 1px solid rgba(83, 101, 140, 0.33)
}

.table-bordered th,
.table-bordered td {
    border: 1px solid rgba(83, 101, 140, 0.33)
}

.table-bordered thead th,
.table-bordered thead td {
    border-bottom-width: 2px
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(94, 130, 152, 0.05)
}

.table-hover tbody tr:hover {
    background-color: rgba(18, 95, 142, 0.075)
}

.table-active,
.table-active>th,
.table-active>td {
    background-color: rgba(18, 95, 142, 0.075)
}

.table-hover .table-active:hover {
    background-color: rgba(15, 80, 119, 0.075)
}

.table-hover .table-active:hover>td,
.table-hover .table-active:hover>th {
    background-color: rgba(15, 80, 119, 0.075)
}

.table-success,
.table-success>th,
.table-success>td {
    background-color: #dff0d8
}

.table-hover .table-success:hover {
    background-color: #d0e9c6
}

.table-hover .table-success:hover>td,
.table-hover .table-success:hover>th {
    background-color: #d0e9c6
}

.table-info,
.table-info>th,
.table-info>td {
    background-color: #d9edf7
}

.table-hover .table-info:hover {
    background-color: #c4e3f3
}

.table-hover .table-info:hover>td,
.table-hover .table-info:hover>th {
    background-color: #c4e3f3
}

.table-warning,
.table-warning>th,
.table-warning>td {
    background-color: #fcf8e3
}

.table-hover .table-warning:hover {
    background-color: #faf2cc
}

.table-hover .table-warning:hover>td,
.table-hover .table-warning:hover>th {
    background-color: #faf2cc
}

.table-danger,
.table-danger>th,
.table-danger>td {
    background-color: #f2dede
}

.table-hover .table-danger:hover {
    background-color: #ebcccc
}

.table-hover .table-danger:hover>td,
.table-hover .table-danger:hover>th {
    background-color: #ebcccc
}

.thead-inverse th {
    color: #f8faff;
    background-color: #292b2c
}

.thead-default th {
    color: #464a4c;
    background-color: #eceeef
}

.table-inverse {
    color: #f8faff;
    background-color: #292b2c
}

.table-inverse th,
.table-inverse td,
.table-inverse thead th {
    border-color: #f8faff
}

.table-inverse.table-bordered {
    border: 0
}

.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -ms-overflow-style: -ms-autohiding-scrollbar
}

.table-responsive.table-bordered {
    border: 0
}

.form-control {
    display: block;
    width: 100%;
    padding: .5rem .7rem;
    font-size: .9rem;
    line-height: 1.25;
    color: #464a4c;
    background-color: #fff;
    background-image: none;
    background-clip: padding-box;
    border: 1px solid #cecece;
    border-radius: .25rem;
    -webkit-transition: border-color ease-in-out 0.15s, -webkit-box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, -webkit-box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s, -webkit-box-shadow ease-in-out 0.15s
}

.form-control::-ms-expand {
    background-color: transparent;
    border: 0
}

.form-control:focus {
    color: #464a4c;
    background-color: #fff;
    border-color: #023d7b;
    outline: none
}

.form-control::-webkit-input-placeholder {
    color: #636c72;
    opacity: 1
}

.form-control:-ms-input-placeholder {
    color: #636c72;
    opacity: 1
}

.form-control::placeholder {
    color: #636c72;
    opacity: 1
}

.form-control:disabled,
.form-control[readonly] {
    background-color: #eceeef;
    opacity: 1
}

.form-control:disabled {
    cursor: not-allowed
}

select.form-control:not([size]):not([multiple]) {
    height: calc(2.125rem + 2px)
}

select.form-control:focus::-ms-value {
    color: #464a4c;
    background-color: #fff
}

.form-control-file,
.form-control-range {
    display: block
}

.col-form-label {
    padding-top: calc(.5rem - 1px * 2);
    padding-bottom: calc(.5rem - 1px * 2);
    margin-bottom: 0
}

.col-form-label-lg {
    padding-top: calc(.75rem - 1px * 2);
    padding-bottom: calc(.75rem - 1px * 2);
    font-size: 1.25rem
}

.col-form-label-sm {
    padding-top: calc(.25rem - 1px * 2);
    padding-bottom: calc(.25rem - 1px * 2);
    font-size: .7rem
}

.col-form-legend {
    padding-top: .5rem;
    padding-bottom: .5rem;
    margin-bottom: 0;
    font-size: .9rem
}

.form-control-static {
    padding-top: .5rem;
    padding-bottom: .5rem;
    margin-bottom: 0;
    line-height: 1.25;
    border: solid transparent;
    border-width: 1px 0
}

.form-control-static.form-control-sm,
.input-group-sm>.form-control-static.form-control,
.input-group-sm>.form-control-static.input-group-addon,
.input-group-sm>.input-group-btn>.form-control-static.btn,
.wrapper-front .input-group-sm>.input-group-btn>.form-control-static.fc-button,
.all-wrapper .input-group-sm>.input-group-btn>.form-control-static.fc-button,
.form-control-static.form-control-lg,
.input-group-lg>.form-control-static.form-control,
.input-group-lg>.form-control-static.input-group-addon,
.input-group-lg>.input-group-btn>.form-control-static.btn,
.wrapper-front .input-group-lg>.input-group-btn>.form-control-static.fc-button,
.all-wrapper .input-group-lg>.input-group-btn>.form-control-static.fc-button {
    padding-right: 0;
    padding-left: 0
}

.form-control-sm,
.input-group-sm>.form-control,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.btn,
.wrapper-front .input-group-sm>.input-group-btn>.fc-button,
.all-wrapper .input-group-sm>.input-group-btn>.fc-button {
    padding: .25rem .7rem;
    font-size: .8rem;
    border-radius: .2rem
}

select.form-control-sm:not([size]):not([multiple]),
.input-group-sm>select.form-control:not([size]):not([multiple]),
.input-group-sm>select.input-group-addon:not([size]):not([multiple]),
.input-group-sm>.input-group-btn>select.btn:not([size]):not([multiple]),
.wrapper-front .input-group-sm>.input-group-btn>select.fc-button:not([size]):not([multiple]),
.all-wrapper .input-group-sm>.input-group-btn>select.fc-button:not([size]):not([multiple]) {
    height: 1.644rem
}

.form-control-lg,
.input-group-lg>.form-control,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.btn,
.wrapper-front .input-group-lg>.input-group-btn>.fc-button,
.all-wrapper .input-group-lg>.input-group-btn>.fc-button {
    padding: .75rem 1.5rem;
    font-size: 1.25rem;
    border-radius: .3rem
}

select.form-control-lg:not([size]):not([multiple]),
.input-group-lg>select.form-control:not([size]):not([multiple]),
.input-group-lg>select.input-group-addon:not([size]):not([multiple]),
.input-group-lg>.input-group-btn>select.btn:not([size]):not([multiple]),
.wrapper-front .input-group-lg>.input-group-btn>select.fc-button:not([size]):not([multiple]),
.all-wrapper .input-group-lg>.input-group-btn>select.fc-button:not([size]):not([multiple]) {
    height: 3.1666666667rem
}

.form-group {
    margin-bottom: 1rem
}

.form-text {
    display: block;
    margin-top: .25rem
}

.form-check {
    position: relative;
    display: block;
    margin-bottom: .5rem
}

.form-check.disabled .form-check-label {
    color: #aaa;
    cursor: not-allowed
}

.form-check-label {
    padding-left: 1.25rem;
    margin-bottom: 0;
    cursor: pointer
}

.form-check-input {
    position: absolute;
    margin-top: .25rem;
    margin-left: -1.25rem
}

.form-check-input:only-child {
    position: static
}

.form-check-inline {
    display: inline-block
}

.form-check-inline .form-check-label {
    vertical-align: middle
}

.form-check-inline+.form-check-inline {
    margin-left: .75rem
}

.form-control-feedback {
    margin-top: .25rem
}

.form-control-success,
.form-control-warning,
.form-control-danger {
    padding-right: 2.1rem;
    background-repeat: no-repeat;
    background-position: center right .53125rem;
    background-size: 1.0625rem 1.0625rem
}

.has-success .form-control-feedback,
.has-success .form-control-label,
.has-success .col-form-label,
.has-success .form-check-label,
.has-success .custom-control {
    color: #90be2e
}

.has-success .form-control {
    border-color: #90be2e
}

.has-success .input-group-addon {
    color: #90be2e;
    border-color: #90be2e;
    background-color: #e4f1c7
}

.has-success .form-control-success {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%2390be2e' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3E%3C/svg%3E")
}

.has-warning .form-control-feedback,
.has-warning .form-control-label,
.has-warning .col-form-label,
.has-warning .form-check-label,
.has-warning .custom-control {
    color: #f0ad4e
}

.has-warning .form-control {
    border-color: #f0ad4e
}

.has-warning .input-group-addon {
    color: #f0ad4e;
    border-color: #f0ad4e;
    background-color: #fff
}

.has-warning .form-control-warning {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23f0ad4e' d='M4.4 5.324h-.8v-2.46h.8zm0 1.42h-.8V5.89h.8zM3.76.63L.04 7.075c-.115.2.016.425.26.426h7.397c.242 0 .372-.226.258-.426C6.726 4.924 5.47 2.79 4.253.63c-.113-.174-.39-.174-.494 0z'/%3E%3C/svg%3E")
}

.has-danger .form-control-feedback,
.has-danger .form-control-label,
.has-danger .col-form-label,
.has-danger .form-check-label,
.has-danger .custom-control {
    color: #e65252
}

.has-danger .form-control {
    border-color: #e65252
}

.has-danger .input-group-addon {
    color: #e65252;
    border-color: #e65252;
    background-color: #fff
}

.has-danger .form-control-danger {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23e65252' viewBox='-2 -2 7 7'%3E%3Cpath stroke='%23d9534f' d='M0 0l3 3m0-3L0 3'/%3E%3Ccircle r='.5'/%3E%3Ccircle cx='3' r='.5'/%3E%3Ccircle cy='3' r='.5'/%3E%3Ccircle cx='3' cy='3' r='.5'/%3E%3C/svg%3E")
}

.form-inline {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.form-inline .form-check {
    width: 100%
}

@media (min-width: 576px) {
    .form-inline label {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        margin-bottom: 0
    }
    .form-inline .form-group {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-flex: 0;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        margin-bottom: 0
    }
    .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }
    .form-inline .form-control-static {
        display: inline-block
    }
    .form-inline .input-group {
        width: auto
    }
    .form-inline .form-control-label {
        margin-bottom: 0;
        vertical-align: middle
    }
    .form-inline .form-check {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        width: auto;
        margin-top: 0;
        margin-bottom: 0
    }
    .form-inline .form-check-label {
        padding-left: 0
    }
    .form-inline .form-check-input {
        position: relative;
        margin-top: 0;
        margin-right: .25rem;
        margin-left: 0
    }
    .form-inline .custom-control {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        padding-left: 0
    }
    .form-inline .custom-control-indicator {
        position: static;
        display: inline-block;
        margin-right: .25rem;
        vertical-align: text-bottom
    }
    .form-inline .has-feedback .form-control-feedback {
        top: 0
    }
}

.btn,
.wrapper-front .fc-button {
    display: inline-block;
    font-weight: 400;
    line-height: 1.25;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid transparent;
    padding: .5rem 1.2rem;
    font-size: 1rem;
    border-radius: .25rem;
    -webkit-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out
}

.btn:focus,
.wrapper-front .fc-button:focus,
.all-wrapper .fc-button:focus,
.btn:hover,
.wrapper-front .fc-button:hover,
.all-wrapper .fc-button:hover {
    text-decoration: none
}

.btn:focus,
.wrapper-front .fc-button:focus,
.all-wrapper .fc-button:focus,
.btn.focus,
.wrapper-front .focus.fc-button,
.all-wrapper .focus.fc-button {
    outline: 0;
    -webkit-box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.25);
    box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.25)
}

.btn.disabled,
.wrapper-front .disabled.fc-button,
.all-wrapper .disabled.fc-button,
.btn:disabled,
.wrapper-front .fc-button:disabled,
.all-wrapper .fc-button:disabled {
    cursor: not-allowed;
    opacity: .65
}

.btn:active,
.wrapper-front .fc-button:active,
.all-wrapper .fc-button:active,
.btn.active,
.wrapper-front .active.fc-button,
.all-wrapper .active.fc-button {
    background-image: none
}

a.btn.disabled,
.wrapper-front a.disabled.fc-button,
.all-wrapper a.disabled.fc-button,
fieldset[disabled] a.btn,
fieldset[disabled] .wrapper-front a.fc-button,
.wrapper-front fieldset[disabled] a.fc-button,
fieldset[disabled] .all-wrapper a.fc-button,
.all-wrapper fieldset[disabled] a.fc-button {
    pointer-events: none
}

.btn-primary,
.wrapper-front .fc-button.fc-state-active,
.all-wrapper .fc-button.fc-state-active {
    color: #fff;
    background-color: #047bf8;
    border-color: #047bf8
}

.btn-primary:hover,
.wrapper-front .fc-button.fc-state-active:hover,
.all-wrapper .fc-button.fc-state-active:hover {
    color: #fff;
    background-color: #0362c6;
    border-color: #035dbc
}

.btn-primary:focus,
.wrapper-front .fc-button.fc-state-active:focus,
.btn-primary.focus,
.wrapper-front .focus.fc-button.fc-state-active {
    -webkit-box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.5);
    box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.5)
}

.btn-primary.disabled,
.wrapper-front .disabled.fc-button.fc-state-active,
.btn-primary:disabled,
.wrapper-front .fc-button.fc-state-active:disabled {
    background-color: #047bf8;
    border-color: #047bf8
}

.btn-primary:active,
.wrapper-front .fc-button.fc-state-active:active,
.btn-primary.active,
.wrapper-front .active.fc-button.fc-state-active,
.show>.btn-primary.dropdown-toggle,
.wrapper-front .show>.dropdown-toggle.fc-button.fc-state-active {
    color: #fff;
    background-color: #0362c6;
    background-image: none;
    border-color: #035dbc
}

.btn-secondary {
    color: #fff;
    background-color: #383d4d;
    border-color: #383d4d
}

.btn-secondary:hover {
    color: #fff;
    background-color: #23262f;
    border-color: #1e212a
}

.btn-secondary:focus,
.btn-secondary.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(56, 61, 77, 0.5);
    box-shadow: 0 0 0 2px rgba(56, 61, 77, 0.5)
}

.btn-secondary.disabled,
.btn-secondary:disabled {
    background-color: #383d4d;
    border-color: #383d4d
}

.btn-secondary:active,
.btn-secondary.active,
.show>.btn-secondary.dropdown-toggle {
    color: #fff;
    background-color: #23262f;
    background-image: none;
    border-color: #1e212a
}

.btn-info {
    color: #fff;
    background-color: #5bc0de;
    border-color: #5bc0de
}

.btn-info:hover {
    color: #fff;
    background-color: #31b0d5;
    border-color: #2aabd2
}

.btn-info:focus,
.btn-info.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(91, 192, 222, 0.5);
    box-shadow: 0 0 0 2px rgba(91, 192, 222, 0.5)
}

.btn-info.disabled,
.btn-info:disabled {
    background-color: #5bc0de;
    border-color: #5bc0de
}

.btn-info:active,
.btn-info.active,
.show>.btn-info.dropdown-toggle {
    color: #fff;
    background-color: #31b0d5;
    background-image: none;
    border-color: #2aabd2
}

.btn-success {
    color: #fff;
    background-color: #90be2e;
    border-color: #90be2e
}

.btn-success:hover {
    color: #fff;
    background-color: #719524;
    border-color: #6b8d22
}

.btn-success:focus,
.btn-success.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(144, 190, 46, 0.5);
    box-shadow: 0 0 0 2px rgba(144, 190, 46, 0.5)
}

.btn-success.disabled,
.btn-success:disabled {
    background-color: #90be2e;
    border-color: #90be2e
}

.btn-success:active,
.btn-success.active,
.show>.btn-success.dropdown-toggle {
    color: #fff;
    background-color: #719524;
    background-image: none;
    border-color: #6b8d22
}

.btn-warning {
    color: #fff;
    background-color: #f0ad4e;
    border-color: #f0ad4e
}

.btn-warning:hover {
    color: #fff;
    background-color: #ec971f;
    border-color: #eb9316
}

.btn-warning:focus,
.btn-warning.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(240, 173, 78, 0.5);
    box-shadow: 0 0 0 2px rgba(240, 173, 78, 0.5)
}

.btn-warning.disabled,
.btn-warning:disabled {
    background-color: #f0ad4e;
    border-color: #f0ad4e
}

.btn-warning:active,
.btn-warning.active,
.show>.btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #ec971f;
    background-image: none;
    border-color: #eb9316
}

.btn-danger {
    color: #fff;
    background-color: #e65252;
    border-color: #e65252
}

.btn-danger:hover {
    color: #fff;
    background-color: #e02525;
    border-color: #db2020
}

.btn-danger:focus,
.btn-danger.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(230, 82, 82, 0.5);
    box-shadow: 0 0 0 2px rgba(230, 82, 82, 0.5)
}

.btn-danger.disabled,
.btn-danger:disabled {
    background-color: #e65252;
    border-color: #e65252
}

.btn-danger:active,
.btn-danger.active,
.show>.btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #e02525;
    background-image: none;
    border-color: #db2020
}

.btn-outline-primary {
    color: #047bf8;
    background-image: none;
    background-color: transparent;
    border-color: #047bf8
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: #047bf8;
    border-color: #047bf8
}

.btn-outline-primary:focus,
.btn-outline-primary.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.5);
    box-shadow: 0 0 0 2px rgba(4, 123, 248, 0.5)
}

.btn-outline-primary.disabled,
.btn-outline-primary:disabled {
    color: #047bf8;
    background-color: transparent
}

.btn-outline-primary:active,
.btn-outline-primary.active,
.show>.btn-outline-primary.dropdown-toggle {
    color: #fff;
    background-color: #047bf8;
    border-color: #047bf8
}

.btn-outline-secondary {
    color: #383d4d;
    background-image: none;
    background-color: transparent;
    border-color: #383d4d
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #383d4d;
    border-color: #383d4d
}

.btn-outline-secondary:focus,
.btn-outline-secondary.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(56, 61, 77, 0.5);
    box-shadow: 0 0 0 2px rgba(56, 61, 77, 0.5)
}

.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
    color: #383d4d;
    background-color: transparent
}

.btn-outline-secondary:active,
.btn-outline-secondary.active,
.show>.btn-outline-secondary.dropdown-toggle {
    color: #fff;
    background-color: #383d4d;
    border-color: #383d4d
}

.btn-outline-info {
    color: #5bc0de;
    background-image: none;
    background-color: transparent;
    border-color: #5bc0de
}

.btn-outline-info:hover {
    color: #fff;
    background-color: #5bc0de;
    border-color: #5bc0de
}

.btn-outline-info:focus,
.btn-outline-info.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(91, 192, 222, 0.5);
    box-shadow: 0 0 0 2px rgba(91, 192, 222, 0.5)
}

.btn-outline-info.disabled,
.btn-outline-info:disabled {
    color: #5bc0de;
    background-color: transparent
}

.btn-outline-info:active,
.btn-outline-info.active,
.show>.btn-outline-info.dropdown-toggle {
    color: #fff;
    background-color: #5bc0de;
    border-color: #5bc0de
}

.btn-outline-success {
    color: #90be2e;
    background-image: none;
    background-color: transparent;
    border-color: #90be2e
}

.btn-outline-success:hover {
    color: #fff;
    background-color: #90be2e;
    border-color: #90be2e
}

.btn-outline-success:focus,
.btn-outline-success.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(144, 190, 46, 0.5);
    box-shadow: 0 0 0 2px rgba(144, 190, 46, 0.5)
}

.btn-outline-success.disabled,
.btn-outline-success:disabled {
    color: #90be2e;
    background-color: transparent
}

.btn-outline-success:active,
.btn-outline-success.active,
.show>.btn-outline-success.dropdown-toggle {
    color: #fff;
    background-color: #90be2e;
    border-color: #90be2e
}

.btn-outline-warning {
    color: #f0ad4e;
    background-image: none;
    background-color: transparent;
    border-color: #f0ad4e
}

.btn-outline-warning:hover {
    color: #fff;
    background-color: #f0ad4e;
    border-color: #f0ad4e
}

.btn-outline-warning:focus,
.btn-outline-warning.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(240, 173, 78, 0.5);
    box-shadow: 0 0 0 2px rgba(240, 173, 78, 0.5)
}

.btn-outline-warning.disabled,
.btn-outline-warning:disabled {
    color: #f0ad4e;
    background-color: transparent
}

.btn-outline-warning:active,
.btn-outline-warning.active,
.show>.btn-outline-warning.dropdown-toggle {
    color: #fff;
    background-color: #f0ad4e;
    border-color: #f0ad4e
}

.btn-outline-danger {
    color: #e65252;
    background-image: none;
    background-color: transparent;
    border-color: #e65252
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #e65252;
    border-color: #e65252
}

.btn-outline-danger:focus,
.btn-outline-danger.focus {
    -webkit-box-shadow: 0 0 0 2px rgba(230, 82, 82, 0.5);
    box-shadow: 0 0 0 2px rgba(230, 82, 82, 0.5)
}

.btn-outline-danger.disabled,
.btn-outline-danger:disabled {
    color: #e65252;
    background-color: transparent
}

.btn-outline-danger:active,
.btn-outline-danger.active,
.show>.btn-outline-danger.dropdown-toggle {
    color: #fff;
    background-color: #e65252;
    border-color: #e65252
}

.btn-link {
    font-weight: 400;
    color: #3b75e3;
    border-radius: 0;
    outline: 0 none;
}

.btn-link,
.btn-link:active,
.btn-link.active,
.btn-link:disabled {
    background-color: transparent
}

.btn-link,
.btn-link:focus,
.btn-link:active {
    border-color: transparent
}

.btn-link:hover {
    border-color: transparent
}

.btn-link:focus,
.btn-link:hover {
    color: #1a50b7;
    text-decoration: underline;
    background-color: transparent
}

.btn-link:disabled {
    color: #636c72
}

.btn-link:disabled:focus,
.btn-link:disabled:hover {
    text-decoration: none
}

.btn-lg,
.btn-group-lg>.btn,
.wrapper-front .btn-group-lg>.fc-button {
    padding: .75rem 1.5rem;
    font-size: 1.25rem;
    border-radius: .3rem
}

.btn-sm,
.btn-group-sm>.btn,
.wrapper-front .btn-group-sm>.fc-button {
    padding: .2rem .5rem;
    font-size: .7rem;
    border-radius: .2rem
}

.btn-block {
    display: block;
    width: 100%
}

.btn-block+.btn-block {
    margin-top: .5rem
}

input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
    width: 100%
}

.dropup,
.dropdown {
    position: relative
}

.dropdown-toggle::after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: .3em;
    vertical-align: middle;
    content: "";
    border-top: .3em solid;
    border-right: .3em solid transparent;
    border-left: .3em solid transparent
}

.dropdown-toggle:focus {
    outline: 0
}

.dropup .dropdown-toggle::after {
    border-top: 0;
    border-bottom: .3em solid
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 10rem;
    padding: .5rem 0;
    margin: .125rem 0 0;
    font-size: .9rem;
    color: #3E4B5B;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: .25rem
}

.dropdown-divider {
    height: 1px;
    margin: .5rem 0;
    overflow: hidden;
    background-color: #eceeef
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 3px 1.5rem;
    clear: both;
    font-weight: 400;
    color: #292b2c;
    text-align: inherit;
    white-space: nowrap;
    background: none;
    border: 0
}

.dropdown-item:focus,
.dropdown-item:hover {
    color: #1d1e1f;
    text-decoration: none;
    background-color: #f7f7f9
}

.dropdown-item.active,
.dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #047bf8
}

.dropdown-item.disabled,
.dropdown-item:disabled {
    color: #636c72;
    cursor: not-allowed;
    background-color: transparent
}

.show>.dropdown-menu {
    display: block
}

.show>a {
    outline: 0
}

.dropdown-menu-right {
    right: 0;
    left: auto
}

.dropdown-menu-left {
    right: auto;
    left: 0
}

.dropdown-header {
    display: block;
    padding: .5rem 1.5rem;
    margin-bottom: 0;
    font-size: .7rem;
    color: #636c72;
    white-space: nowrap
}

.dropdown-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 990
}

.dropup .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-bottom: .125rem
}

.btn-group,
.btn-group-vertical {
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    vertical-align: middle
}

.btn-group>.btn,
.wrapper-front .btn-group>.fc-button,
.btn-group-vertical>.btn,
.wrapper-front .btn-group-vertical>.fc-button {
    position: relative;
    -webkit-box-flex: 0;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto
}

.btn-group>.btn:hover,
.wrapper-front .btn-group>.fc-button:hover,
.btn-group-vertical>.btn:hover,
.wrapper-front .btn-group-vertical>.fc-button:hover {
    z-index: 2
}

.btn-group>.btn:focus,
.wrapper-front .btn-group>.fc-button:focus,
.btn-group>.btn:active,
.wrapper-front .btn-group>.fc-button:active,
.btn-group>.btn.active,
.wrapper-front .btn-group>.active.fc-button,
.btn-group-vertical>.btn:focus,
.wrapper-front .btn-group-vertical>.fc-button:focus,
.btn-group-vertical>.btn:active,
.wrapper-front .btn-group-vertical>.fc-button:active,
.btn-group-vertical>.btn.active,
.wrapper-front .btn-group-vertical>.active.fc-button {
    z-index: 2
}

.btn-group .btn+.btn,
.btn-group .wrapper-front .fc-button+.btn,
.wrapper-front .btn-group .fc-button+.btn,
.btn-group .wrapper-front .btn+.fc-button,
.wrapper-front .btn-group .btn+.fc-button,
.btn-group .wrapper-front .fc-button+.fc-button,
.wrapper-front .btn-group .fc-button+.fc-button,
.btn-group .btn+.btn-group,
.btn-group .wrapper-front .fc-button+.btn-group,
.wrapper-front .btn-group .fc-button+.btn-group,
.btn-group .btn-group+.btn,
.btn-group .wrapper-front .btn-group+.fc-button,
.wrapper-front .btn-group .btn-group+.fc-button,
.btn-group .btn-group+.btn-group,
.btn-group-vertical .btn+.btn,
.btn-group-vertical .wrapper-front .fc-button+.btn,
.wrapper-front .btn-group-vertical .fc-button+.btn,
.btn-group-vertical .wrapper-front .btn+.fc-button,
.wrapper-front .btn-group-vertical .btn+.fc-button,
.btn-group-vertical .wrapper-front .fc-button+.fc-button,
.wrapper-front .btn-group-vertical .fc-button+.fc-button,
.btn-group-vertical .btn+.btn-group,
.btn-group-vertical .wrapper-front .fc-button+.btn-group,
.wrapper-front .btn-group-vertical .fc-button+.btn-group,
.btn-group-vertical .btn-group+.btn,
.btn-group-vertical .wrapper-front .btn-group+.fc-button,
.wrapper-front .btn-group-vertical .btn-group+.fc-button,
.btn-group-vertical .btn-group+.btn-group {
    margin-left: -1px
}

.btn-toolbar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.btn-toolbar .input-group {
    width: auto
}

.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle),
.wrapper-front .btn-group>.fc-button:not(:first-child):not(:last-child):not(.dropdown-toggle) {
    border-radius: 0
}

.btn-group>.btn:first-child,
.wrapper-front .btn-group>.fc-button:first-child {
    margin-left: 0
}

.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle),
.wrapper-front .btn-group>.fc-button:first-child:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.btn-group>.btn:last-child:not(:first-child),
.wrapper-front .btn-group>.fc-button:last-child:not(:first-child),
.btn-group>.dropdown-toggle:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.btn-group>.btn-group {
    float: left
}

.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn,
.wrapper-front .btn-group>.btn-group:not(:first-child):not(:last-child)>.fc-button {
    border-radius: 0
}

.btn-group>.btn-group:first-child:not(:last-child)>.btn:last-child,
.wrapper-front .btn-group>.btn-group:first-child:not(:last-child)>.fc-button:last-child,
.btn-group>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.btn-group>.btn-group:last-child:not(:first-child)>.btn:first-child,
.wrapper-front .btn-group>.btn-group:last-child:not(:first-child)>.fc-button:first-child {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
    outline: 0
}

.btn+.dropdown-toggle-split,
.wrapper-front .fc-button+.dropdown-toggle-split {
    padding-right: .9rem;
    padding-left: .9rem
}

.btn+.dropdown-toggle-split::after,
.wrapper-front .fc-button+.dropdown-toggle-split::after {
    margin-left: 0
}

.btn-sm+.dropdown-toggle-split,
.btn-group-sm>.btn+.dropdown-toggle-split,
.wrapper-front .btn-group-sm>.fc-button+.dropdown-toggle-split {
    padding-right: .375rem;
    padding-left: .375rem
}

.btn-lg+.dropdown-toggle-split,
.btn-group-lg>.btn+.dropdown-toggle-split,
.wrapper-front .btn-group-lg>.fc-button+.dropdown-toggle-split {
    padding-right: 1.125rem;
    padding-left: 1.125rem
}

.btn-group-vertical {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.btn-group-vertical .btn,
.btn-group-vertical .wrapper-front .fc-button,
.wrapper-front .btn-group-vertical .fc-button,
.btn-group-vertical .btn-group {
    width: 100%
}

.btn-group-vertical>.btn+.btn,
.wrapper-front .btn-group-vertical>.fc-button+.btn,
.wrapper-front .btn-group-vertical>.btn+.fc-button,
.wrapper-front .btn-group-vertical>.fc-button+.fc-button,
.btn-group-vertical>.btn+.btn-group,
.wrapper-front .btn-group-vertical>.fc-button+.btn-group,
.btn-group-vertical>.btn-group+.btn,
.wrapper-front .btn-group-vertical>.btn-group+.fc-button,
.btn-group-vertical>.btn-group+.btn-group {
    margin-top: -1px;
    margin-left: 0
}

.btn-group-vertical>.btn:not(:first-child):not(:last-child),
.wrapper-front .btn-group-vertical>.fc-button:not(:first-child):not(:last-child) {
    border-radius: 0
}

.btn-group-vertical>.btn:first-child:not(:last-child),
.wrapper-front .btn-group-vertical>.fc-button:first-child:not(:last-child) {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn:last-child:not(:first-child),
.wrapper-front .btn-group-vertical>.fc-button:last-child:not(:first-child) {
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn,
.wrapper-front .btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.fc-button {
    border-radius: 0
}

.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child,
.wrapper-front .btn-group-vertical>.btn-group:first-child:not(:last-child)>.fc-button:last-child,
.btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child,
.wrapper-front .btn-group-vertical>.btn-group:last-child:not(:first-child)>.fc-button:first-child {
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

[data-toggle="buttons"]>.btn input[type="radio"],
.wrapper-front [data-toggle="buttons"]>.fc-button input[type="radio"],
[data-toggle="buttons"]>.btn input[type="checkbox"],
.wrapper-front [data-toggle="buttons"]>.fc-button input[type="checkbox"],
[data-toggle="buttons"]>.btn-group>.btn input[type="radio"],
.wrapper-front [data-toggle="buttons"]>.btn-group>.fc-button input[type="radio"],
[data-toggle="buttons"]>.btn-group>.btn input[type="checkbox"],
.wrapper-front [data-toggle="buttons"]>.btn-group>.fc-button input[type="checkbox"] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none
}

.input-group {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%
}

.input-group .form-control {
    position: relative;
    z-index: 2;
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    width: 1%;
    margin-bottom: 0
}

.input-group .form-control:focus,
.input-group .form-control:active,
.input-group .form-control:hover {
    z-index: 3
}

.input-group-addon,
.input-group-btn,
.input-group .form-control {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child),
.input-group .form-control:not(:first-child):not(:last-child) {
    border-radius: 0
}

.input-group-addon,
.input-group-btn {
    white-space: nowrap;
    vertical-align: middle
}

.input-group-addon {
    padding: .5rem .7rem;
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.25;
    color: #464a4c;
    text-align: center;
    background-color: #eceeef;
    border: 1px solid #cecece;
    border-radius: .25rem
}

.input-group-addon.form-control-sm,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.input-group-addon.btn,
.wrapper-front .input-group-sm>.input-group-btn>.input-group-addon.fc-button {
    padding: .25rem .7rem;
    font-size: .7rem;
    border-radius: .2rem
}

.input-group-addon.form-control-lg,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.input-group-addon.btn,
.wrapper-front .input-group-lg>.input-group-btn>.input-group-addon.fc-button {
    padding: .75rem 1.5rem;
    font-size: 1.25rem;
    border-radius: .3rem
}

.input-group-addon input[type="radio"],
.input-group-addon input[type="checkbox"] {
    margin-top: 0
}

.input-group .form-control:not(:last-child),
.input-group-addon:not(:last-child),
.input-group-btn:not(:last-child)>.btn,
.wrapper-front .input-group-btn:not(:last-child)>.fc-button,
.input-group-btn:not(:last-child)>.btn-group>.btn,
.wrapper-front .input-group-btn:not(:last-child)>.btn-group>.fc-button,
.input-group-btn:not(:last-child)>.dropdown-toggle,
.input-group-btn:not(:first-child)>.btn:not(:last-child):not(.dropdown-toggle),
.wrapper-front .input-group-btn:not(:first-child)>.fc-button:not(:last-child):not(.dropdown-toggle),
.input-group-btn:not(:first-child)>.btn-group:not(:last-child)>.btn,
.wrapper-front .input-group-btn:not(:first-child)>.btn-group:not(:last-child)>.fc-button {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.input-group-addon:not(:last-child) {
    border-right: 0
}

.input-group .form-control:not(:first-child),
.input-group-addon:not(:first-child),
.input-group-btn:not(:first-child)>.btn,
.wrapper-front .input-group-btn:not(:first-child)>.fc-button,
.input-group-btn:not(:first-child)>.btn-group>.btn,
.wrapper-front .input-group-btn:not(:first-child)>.btn-group>.fc-button,
.input-group-btn:not(:first-child)>.dropdown-toggle,
.input-group-btn:not(:last-child)>.btn:not(:first-child),
.wrapper-front .input-group-btn:not(:last-child)>.fc-button:not(:first-child),
.input-group-btn:not(:last-child)>.btn-group:not(:first-child)>.btn,
.wrapper-front .input-group-btn:not(:last-child)>.btn-group:not(:first-child)>.fc-button {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.form-control+.input-group-addon:not(:first-child) {
    border-left: 0
}

.input-group-btn {
    position: relative;
    font-size: 0;
    white-space: nowrap
}

.input-group-btn>.btn,
.wrapper-front .input-group-btn>.fc-button {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.input-group-btn>.btn+.btn,
.wrapper-front .input-group-btn>.fc-button+.btn,
.wrapper-front .input-group-btn>.btn+.fc-button,
.wrapper-front .input-group-btn>.fc-button+.fc-button {
    margin-left: -1px
}

.input-group-btn>.btn:focus,
.wrapper-front .input-group-btn>.fc-button:focus,
.input-group-btn>.btn:active,
.wrapper-front .input-group-btn>.fc-button:active,
.input-group-btn>.btn:hover,
.wrapper-front .input-group-btn>.fc-button:hover {
    z-index: 3
}

.input-group-btn:not(:last-child)>.btn,
.wrapper-front .input-group-btn:not(:last-child)>.fc-button,
.input-group-btn:not(:last-child)>.btn-group {
    margin-right: -1px
}

.input-group-btn:not(:first-child)>.btn,
.wrapper-front .input-group-btn:not(:first-child)>.fc-button,
.input-group-btn:not(:first-child)>.btn-group {
    z-index: 2;
    margin-left: -1px
}

.input-group-btn:not(:first-child)>.btn:focus,
.wrapper-front .input-group-btn:not(:first-child)>.fc-button:focus,
.input-group-btn:not(:first-child)>.btn:active,
.wrapper-front .input-group-btn:not(:first-child)>.fc-button:active,
.input-group-btn:not(:first-child)>.btn:hover,
.wrapper-front .input-group-btn:not(:first-child)>.fc-button:hover,
.input-group-btn:not(:first-child)>.btn-group:focus,
.input-group-btn:not(:first-child)>.btn-group:active,
.input-group-btn:not(:first-child)>.btn-group:hover {
    z-index: 3
}

.custom-control {
    position: relative;
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    min-height: 1.5rem;
    padding-left: 1.5rem;
    margin-right: 1rem;
    cursor: pointer
}

.custom-control-input {
    position: absolute;
    z-index: -1;
    opacity: 0
}

.custom-control-input:checked~.custom-control-indicator {
    color: #fff;
    background-color: #047bf8
}

.custom-control-input:focus~.custom-control-indicator {
    -webkit-box-shadow: 0 0 0 1px #f8faff, 0 0 0 3px #047bf8;
    box-shadow: 0 0 0 1px #f8faff, 0 0 0 3px #047bf8
}

.custom-control-input:active~.custom-control-indicator {
    color: #fff;
    background-color: #b1d6fe
}

.custom-control-input:disabled~.custom-control-indicator {
    cursor: not-allowed;
    background-color: #eceeef
}

.custom-control-input:disabled~.custom-control-description {
    color: #636c72;
    cursor: not-allowed
}

.custom-control-indicator {
    position: absolute;
    top: .25rem;
    left: 0;
    display: block;
    width: 1rem;
    height: 1rem;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: #ddd;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 50% 50%
}

.custom-checkbox .custom-control-indicator {
    border-radius: .25rem
}

.custom-checkbox .custom-control-input:checked~.custom-control-indicator {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E")
}

.custom-checkbox .custom-control-input:indeterminate~.custom-control-indicator {
    background-color: #047bf8;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='%23fff' d='M0 2h4'/%3E%3C/svg%3E")
}

.custom-radio .custom-control-indicator {
    border-radius: 50%
}

.custom-radio .custom-control-input:checked~.custom-control-indicator {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fff'/%3E%3C/svg%3E")
}

.custom-controls-stacked {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.custom-controls-stacked .custom-control {
    margin-bottom: .25rem
}

.custom-controls-stacked .custom-control+.custom-control {
    margin-left: 0
}

.custom-select {
    display: inline-block;
    max-width: 100%;
    height: calc(2.25rem + 2px);
    padding: .375rem 1.75rem .375rem .75rem;
    line-height: 1.25;
    color: #464a4c;
    vertical-align: middle;
    background: #fff url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23333' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right .75rem center;
    background-size: 8px 10px;
    border: 1px solid #cecece;
    border-radius: .25rem;
    -moz-appearance: none;
    -webkit-appearance: none
}

.custom-select:focus {
    border-color: #7fbcfd;
    outline: none
}

.custom-select:focus::-ms-value {
    color: #464a4c;
    background-color: #fff
}

.custom-select:disabled {
    color: #636c72;
    cursor: not-allowed;
    background-color: #eceeef
}

.custom-select::-ms-expand {
    opacity: 0
}

.custom-select-sm {
    padding-top: .375rem;
    padding-bottom: .375rem;
    font-size: 75%
}

.custom-file {
    position: relative;
    display: inline-block;
    max-width: 100%;
    height: 2.5rem;
    margin-bottom: 0;
    cursor: pointer
}

.custom-file-input {
    min-width: 14rem;
    max-width: 100%;
    height: 2.5rem;
    margin: 0;
    filter: alpha(opacity=0);
    opacity: 0
}

.custom-file-control {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 5;
    height: 2.5rem;
    padding: .5rem 1rem;
    line-height: 1.5;
    color: #464a4c;
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: #fff;
    border: 1px solid #cecece;
    border-radius: .25rem
}

.custom-file-control:lang(en)::after {
    content: "Choose file..."
}

.custom-file-control::before {
    position: absolute;
    top: -1px;
    right: -1px;
    bottom: -1px;
    z-index: 6;
    display: block;
    height: 2.5rem;
    padding: .5rem 1rem;
    line-height: 1.5;
    color: #464a4c;
    background-color: #eceeef;
    border: 1px solid #cecece;
    border-radius: 0 .25rem .25rem 0
}

.custom-file-control:lang(en)::before {
    content: "Browse"
}

.nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}

.nav-link {
    display: block;
    padding: 0.5em 1em
}

.nav-link:focus,
.nav-link:hover {
    text-decoration: none
}

.nav-link.disabled {
    color: #636c72;
    cursor: not-allowed
}

.nav-tabs {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.nav-tabs .nav-item {
    margin-bottom: -1px
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-right-radius: .25rem;
    border-top-left-radius: .25rem
}

.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
    border-color: #eceeef #eceeef rgba(0, 0, 0, 0.1)
}

.nav-tabs .nav-link.disabled {
    color: #636c72;
    background-color: transparent;
    border-color: transparent
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
    color: #464a4c;
    background-color: transparent;
    border-color: #ddd #ddd transparent
}

.nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.nav-pills .nav-link {
    border-radius: 30px
}

.nav-pills .nav-link.active,
.nav-pills .nav-item.show .nav-link {
    color: #fff;
    cursor: default;
    background-color: #047bf8
}

.nav-fill .nav-item {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    text-align: center
}

.nav-justified .nav-item {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 100%;
    flex: 1 1 100%;
    text-align: center
}

.tab-content>.tab-pane {
    display: none
}

.tab-content>.active {
    display: block
}

.breadcrumb {
    padding: .75rem 1rem;
    margin-bottom: 1rem;
    list-style: none;
    background-color: #eceeef;
    border-radius: .25rem
}

.breadcrumb::after {
    display: block;
    content: "";
    clear: both
}

.breadcrumb-item {
    float: left
}

.breadcrumb-item+.breadcrumb-item::before {
    display: inline-block;
    padding-right: .5rem;
    padding-left: .5rem;
    color: #636c72;
    content: "/"
}

.breadcrumb-item+.breadcrumb-item:hover::before {
    text-decoration: underline
}

.breadcrumb-item+.breadcrumb-item:hover::before {
    text-decoration: none
}

.breadcrumb-item.active {
    color: #636c72
}

.pagination {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: .25rem
}

.page-item:first-child .page-link {
    margin-left: 0;
    border-bottom-left-radius: .25rem;
    border-top-left-radius: .25rem
}

.page-item:last-child .page-link {
    border-bottom-right-radius: .25rem;
    border-top-right-radius: .25rem
}

.page-item.active .page-link {
    z-index: 2;
    color: #fff;
    background-color: #047bf8;
    border-color: #047bf8
}

.page-item.disabled .page-link {
    color: #636c72;
    pointer-events: none;
    cursor: not-allowed;
    background-color: #fff;
    border-color: #ddd
}

.page-link {
    position: relative;
    display: block;
    padding: .5rem .75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #3b75e3;
    background-color: #fff;
    border: 1px solid #ddd
}

.page-link:focus,
.page-link:hover {
    color: #1a50b7;
    text-decoration: none;
    background-color: #eceeef;
    border-color: #ddd
}

.pagination-lg .page-link {
    padding: .75rem 1.5rem;
    font-size: 1.25rem
}

.pagination-lg .page-item:first-child .page-link {
    border-bottom-left-radius: .3rem;
    border-top-left-radius: .3rem
}

.pagination-lg .page-item:last-child .page-link {
    border-bottom-right-radius: .3rem;
    border-top-right-radius: .3rem
}

.pagination-sm .page-link {
    padding: .25rem .5rem;
    font-size: .7rem
}

.pagination-sm .page-item:first-child .page-link {
    border-bottom-left-radius: .2rem;
    border-top-left-radius: .2rem
}

.pagination-sm .page-item:last-child .page-link {
    border-bottom-right-radius: .2rem;
    border-top-right-radius: .2rem
}

.badge {
    display: inline-block;
    padding: .25em .4em;
    font-size: 75%;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25rem
}

.badge:empty {
    display: none
}

.btn .badge,
.wrapper-front .fc-button .badge {
    position: relative;
    top: -1px
}

a.badge:focus,
a.badge:hover {
    color: #fff;
    text-decoration: none;
    cursor: pointer
}

.badge-pill {
    padding-right: .6em;
    padding-left: .6em;
    border-radius: 10rem;
    color: #ffffff;
}

.badge-default {
    background-color: #636c72
}

.badge-default[href]:focus,
.badge-default[href]:hover {
    background-color: #4b5257
}

.badge-primary {
    background-color: #047bf8
}

.badge-primary[href]:focus,
.badge-primary[href]:hover {
    background-color: #0362c6
}

.badge-success {
    background-color: #90be2e
}

.badge-success[href]:focus,
.badge-success[href]:hover {
    background-color: #719524
}

.badge-info {
    background-color: #5bc0de
}

.badge-info[href]:focus,
.badge-info[href]:hover {
    background-color: #31b0d5
}

.badge-warning {
    background-color: #f0ad4e
}

.badge-warning[href]:focus,
.badge-warning[href]:hover {
    background-color: #ec971f
}

.badge-danger {
    background-color: #e65252
}

.badge-danger[href]:focus,
.badge-danger[href]:hover {
    background-color: #e02525
}
.badge-light {
	color: #292b2c;
	background-color: #f8f9fa;
}
.badge-light[href]:hover,
.badge-light[href]:focus {
	color: #292b2c;
	text-decoration: none;
	background-color: #dae0e5;
}
.badge-dark {
	color: #fff;
	background-color: #343a40;
}
.badge-dark[href]:hover,
.badge-dark[href]:focus {
	color: #fff;
	text-decoration: none;
	background-color: #1d2124;
}
.badge-default-inverted {
	background-color: #b4bbc3;
	border: 1px solid #101112;
	color: #040404;
	font-weight: 300;
}
.badge-default-inverted[href]:hover,
.badge-default-inverted[href]:focus {
	background-color: #98a2ac;
	border: 1px solid #000;
	color: #040404;
	font-weight: 300;
}
.badge-primary-inverted {
	background-color: #cce4ff;
	border: 1px solid #0362c6;
	color: #0356ad;
	font-weight: 300;
}
.badge-primary-inverted[href]:hover,
.badge-primary-inverted[href]:focus {
	background-color: #99caff;
	border: 1px solid #024994;
	color: #0356ad;
	font-weight: 300;
}
.badge-success-inverted {
	background-color: #c5f0c0;
	border: 1px solid #1b850f;
	color: #166e0c;
	font-weight: 300;
}
.badge-success-inverted[href]:hover,
.badge-success-inverted[href]:focus {
	background-color: #9fe697;
	border: 1px solid #12570a;
	color: #166e0c;
	font-weight: 300;
}
.badge-info-inverted {
	background-color: #ecf9fd;
	border: 1px solid #31b0d5;
	color: #28a1c5;
	font-weight: 300;
}
.badge-info-inverted[href]:hover,
.badge-info-inverted[href]:focus {
	background-color: #bfeaf7;
	border: 1px solid #2390b0;
	color: #28a1c5;
	font-weight: 300;
}
.badge-warning-inverted {
	background-color: #fff;
	border: 1px solid #f9d66f;
	color: #f8cf57;
	font-weight: 300;
}
.badge-warning-inverted[href]:hover,
.badge-warning-inverted[href]:focus {
	background-color: #fff9e8;
	border: 1px solid #f7c83e;
	color: #f8cf57;
	font-weight: 300;
}
.badge-danger-inverted {
	background-color: #fdebeb;
	border: 1px solid #e02525;
	color: #ce1e1e;
	font-weight: 300;
}
.badge-danger-inverted[href]:hover,
.badge-danger-inverted[href]:focus {
	background-color: #fabcbc;
	border: 1px solid #b71b1b;
	color: #ce1e1e;
	font-weight: 300;
}

.align-baseline {
    vertical-align: baseline !important
}

.align-top {
    vertical-align: top !important
}

.align-middle {
    vertical-align: middle !important
}

.align-bottom {
    vertical-align: bottom !important
}

.align-text-bottom {
    vertical-align: text-bottom !important
}

.align-text-top {
    vertical-align: text-top !important
}

.bg-faded {
    background-color: #e9efff
}

.bg-primary {
    background-color: #047bf8 !important
}

a.bg-primary:focus,
a.bg-primary:hover {
    background-color: #0362c6 !important
}

.bg-success {
    background-color: #90be2e !important
}

a.bg-success:focus,
a.bg-success:hover {
    background-color: #719524 !important
}

.bg-info {
    background-color: #5bc0de !important
}

a.bg-info:focus,
a.bg-info:hover {
    background-color: #31b0d5 !important
}

.bg-warning {
    background-color: #f0ad4e !important
}

a.bg-warning:focus,
a.bg-warning:hover {
    background-color: #ec971f !important
}

.bg-danger {
    background-color: #e65252 !important
}

a.bg-danger:focus,
a.bg-danger:hover {
    background-color: #e02525 !important
}

.bg-inverse {
    background-color: #292b2c !important
}

a.bg-inverse:focus,
a.bg-inverse:hover {
    background-color: #101112 !important
}

.border-0 {
    border: 0 !important
}

.border-top-0 {
    border-top: 0 !important
}

.border-right-0 {
    border-right: 0 !important
}

.border-bottom-0 {
    border-bottom: 0 !important
}

.border-left-0 {
    border-left: 0 !important
}

.rounded {
    border-radius: .25rem
}

.rounded-top {
    border-top-right-radius: .25rem;
    border-top-left-radius: .25rem
}

.rounded-right {
    border-bottom-right-radius: .25rem;
    border-top-right-radius: .25rem
}

.rounded-bottom {
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem
}

.rounded-left {
    border-bottom-left-radius: .25rem;
    border-top-left-radius: .25rem
}

.rounded-circle {
    border-radius: 50%
}

.rounded-0 {
    border-radius: 0
}

.clearfix::after {
    display: block;
    content: "";
    clear: both
}

.d-none {
    display: none !important
}

.d-inline {
    display: inline !important
}

.d-inline-block {
    display: inline-block !important
}

.d-block {
    display: block !important
}

.d-table {
    display: table !important
}

.d-table-cell {
    display: table-cell !important
}

.d-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important
}

.d-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important
}

@media (min-width: 576px) {
    .d-sm-none {
        display: none !important
    }
    .d-sm-inline {
        display: inline !important
    }
    .d-sm-inline-block {
        display: inline-block !important
    }
    .d-sm-block {
        display: block !important
    }
    .d-sm-table {
        display: table !important
    }
    .d-sm-table-cell {
        display: table-cell !important
    }
    .d-sm-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }
    .d-sm-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media (min-width: 768px) {
    .d-md-none {
        display: none !important
    }
    .d-md-inline {
        display: inline !important
    }
    .d-md-inline-block {
        display: inline-block !important
    }
    .d-md-block {
        display: block !important
    }
    .d-md-table {
        display: table !important
    }
    .d-md-table-cell {
        display: table-cell !important
    }
    .d-md-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }
    .d-md-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media (min-width: 992px) {
    .d-lg-none {
        display: none !important
    }
    .d-lg-inline {
        display: inline !important
    }
    .d-lg-inline-block {
        display: inline-block !important
    }
    .d-lg-block {
        display: block !important
    }
    .d-lg-table {
        display: table !important
    }
    .d-lg-table-cell {
        display: table-cell !important
    }
    .d-lg-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }
    .d-lg-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

@media (min-width: 1200px) {
    .d-xl-none {
        display: none !important
    }
    .d-xl-inline {
        display: inline !important
    }
    .d-xl-inline-block {
        display: inline-block !important
    }
    .d-xl-block {
        display: block !important
    }
    .d-xl-table {
        display: table !important
    }
    .d-xl-table-cell {
        display: table-cell !important
    }
    .d-xl-flex {
        display: -webkit-box !important;
        display: -ms-flexbox !important;
        display: flex !important
    }
    .d-xl-inline-flex {
        display: -webkit-inline-box !important;
        display: -ms-inline-flexbox !important;
        display: inline-flex !important
    }
}

.flex-first {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1
}

.flex-last {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
}

.flex-unordered {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0
}

.flex-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important
}

.flex-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important
}

.flex-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important
}

.flex-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important
}

.flex-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important
}

.flex-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important
}

.flex-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important
}

.justify-content-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important
}

.justify-content-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important
}

.justify-content-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important
}

.justify-content-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important
}

.justify-content-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important
}

.align-items-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important
}

.align-items-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important
}

.align-items-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important
}

.align-items-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important
}

.align-items-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important
}

.align-content-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important
}

.align-content-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important
}

.align-content-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important
}

.align-content-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important
}

.align-content-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important
}

.align-content-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important
}

.align-self-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important
}

.align-self-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important
}

.align-self-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important
}

.align-self-center {
    -ms-flex-item-align: center !important;
    align-self: center !important
}

.align-self-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important
}

.align-self-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important
}

@media (min-width: 576px) {
    .flex-sm-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1
    }
    .flex-sm-last {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }
    .flex-sm-unordered {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
    .flex-sm-row {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }
    .flex-sm-column {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }
    .flex-sm-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }
    .flex-sm-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }
    .flex-sm-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }
    .flex-sm-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }
    .flex-sm-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }
    .justify-content-sm-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }
    .justify-content-sm-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }
    .justify-content-sm-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important
    }
    .justify-content-sm-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }
    .justify-content-sm-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }
    .align-items-sm-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }
    .align-items-sm-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }
    .align-items-sm-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important
    }
    .align-items-sm-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }
    .align-items-sm-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }
    .align-content-sm-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }
    .align-content-sm-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }
    .align-content-sm-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }
    .align-content-sm-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }
    .align-content-sm-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }
    .align-content-sm-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }
    .align-self-sm-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important
    }
    .align-self-sm-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }
    .align-self-sm-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }
    .align-self-sm-center {
        -ms-flex-item-align: center !important;
        align-self: center !important
    }
    .align-self-sm-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }
    .align-self-sm-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important
    }
}

@media (min-width: 768px) {
    .flex-md-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1
    }
    .flex-md-last {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }
    .flex-md-unordered {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
    .flex-md-row {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }
    .flex-md-column {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }
    .flex-md-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }
    .flex-md-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }
    .flex-md-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }
    .flex-md-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }
    .flex-md-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }
    .justify-content-md-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }
    .justify-content-md-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }
    .justify-content-md-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important
    }
    .justify-content-md-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }
    .justify-content-md-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }
    .align-items-md-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }
    .align-items-md-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }
    .align-items-md-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important
    }
    .align-items-md-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }
    .align-items-md-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }
    .align-content-md-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }
    .align-content-md-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }
    .align-content-md-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }
    .align-content-md-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }
    .align-content-md-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }
    .align-content-md-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }
    .align-self-md-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important
    }
    .align-self-md-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }
    .align-self-md-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }
    .align-self-md-center {
        -ms-flex-item-align: center !important;
        align-self: center !important
    }
    .align-self-md-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }
    .align-self-md-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important
    }
}

@media (min-width: 992px) {
    .flex-lg-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1
    }
    .flex-lg-last {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }
    .flex-lg-unordered {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
    .flex-lg-row {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }
    .flex-lg-column {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }
    .flex-lg-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }
    .flex-lg-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }
    .flex-lg-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }
    .flex-lg-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }
    .flex-lg-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }
    .justify-content-lg-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }
    .justify-content-lg-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }
    .justify-content-lg-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important
    }
    .justify-content-lg-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }
    .justify-content-lg-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }
    .align-items-lg-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }
    .align-items-lg-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }
    .align-items-lg-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important
    }
    .align-items-lg-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }
    .align-items-lg-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }
    .align-content-lg-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }
    .align-content-lg-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }
    .align-content-lg-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }
    .align-content-lg-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }
    .align-content-lg-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }
    .align-content-lg-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }
    .align-self-lg-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important
    }
    .align-self-lg-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }
    .align-self-lg-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }
    .align-self-lg-center {
        -ms-flex-item-align: center !important;
        align-self: center !important
    }
    .align-self-lg-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }
    .align-self-lg-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important
    }
}

@media (min-width: 1200px) {
    .flex-xl-first {
        -webkit-box-ordinal-group: 0;
        -ms-flex-order: -1;
        order: -1
    }
    .flex-xl-last {
        -webkit-box-ordinal-group: 2;
        -ms-flex-order: 1;
        order: 1
    }
    .flex-xl-unordered {
        -webkit-box-ordinal-group: 1;
        -ms-flex-order: 0;
        order: 0
    }
    .flex-xl-row {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: row !important;
        flex-direction: row !important
    }
    .flex-xl-column {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important
    }
    .flex-xl-row-reverse {
        -webkit-box-orient: horizontal !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: row-reverse !important;
        flex-direction: row-reverse !important
    }
    .flex-xl-column-reverse {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: reverse !important;
        -ms-flex-direction: column-reverse !important;
        flex-direction: column-reverse !important
    }
    .flex-xl-wrap {
        -ms-flex-wrap: wrap !important;
        flex-wrap: wrap !important
    }
    .flex-xl-nowrap {
        -ms-flex-wrap: nowrap !important;
        flex-wrap: nowrap !important
    }
    .flex-xl-wrap-reverse {
        -ms-flex-wrap: wrap-reverse !important;
        flex-wrap: wrap-reverse !important
    }
    .justify-content-xl-start {
        -webkit-box-pack: start !important;
        -ms-flex-pack: start !important;
        justify-content: flex-start !important
    }
    .justify-content-xl-end {
        -webkit-box-pack: end !important;
        -ms-flex-pack: end !important;
        justify-content: flex-end !important
    }
    .justify-content-xl-center {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important
    }
    .justify-content-xl-between {
        -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
        justify-content: space-between !important
    }
    .justify-content-xl-around {
        -ms-flex-pack: distribute !important;
        justify-content: space-around !important
    }
    .align-items-xl-start {
        -webkit-box-align: start !important;
        -ms-flex-align: start !important;
        align-items: flex-start !important
    }
    .align-items-xl-end {
        -webkit-box-align: end !important;
        -ms-flex-align: end !important;
        align-items: flex-end !important
    }
    .align-items-xl-center {
        -webkit-box-align: center !important;
        -ms-flex-align: center !important;
        align-items: center !important
    }
    .align-items-xl-baseline {
        -webkit-box-align: baseline !important;
        -ms-flex-align: baseline !important;
        align-items: baseline !important
    }
    .align-items-xl-stretch {
        -webkit-box-align: stretch !important;
        -ms-flex-align: stretch !important;
        align-items: stretch !important
    }
    .align-content-xl-start {
        -ms-flex-line-pack: start !important;
        align-content: flex-start !important
    }
    .align-content-xl-end {
        -ms-flex-line-pack: end !important;
        align-content: flex-end !important
    }
    .align-content-xl-center {
        -ms-flex-line-pack: center !important;
        align-content: center !important
    }
    .align-content-xl-between {
        -ms-flex-line-pack: justify !important;
        align-content: space-between !important
    }
    .align-content-xl-around {
        -ms-flex-line-pack: distribute !important;
        align-content: space-around !important
    }
    .align-content-xl-stretch {
        -ms-flex-line-pack: stretch !important;
        align-content: stretch !important
    }
    .align-self-xl-auto {
        -ms-flex-item-align: auto !important;
        align-self: auto !important
    }
    .align-self-xl-start {
        -ms-flex-item-align: start !important;
        align-self: flex-start !important
    }
    .align-self-xl-end {
        -ms-flex-item-align: end !important;
        align-self: flex-end !important
    }
    .align-self-xl-center {
        -ms-flex-item-align: center !important;
        align-self: center !important
    }
    .align-self-xl-baseline {
        -ms-flex-item-align: baseline !important;
        align-self: baseline !important
    }
    .align-self-xl-stretch {
        -ms-flex-item-align: stretch !important;
        align-self: stretch !important
    }
}

.float-left {
    float: left !important
}

.float-right {
    float: right !important
}

.float-none {
    float: none !important
}

@media (min-width: 576px) {
    .float-sm-left {
        float: left !important
    }
    .float-sm-right {
        float: right !important
    }
    .float-sm-none {
        float: none !important
    }
}

@media (min-width: 768px) {
    .float-md-left {
        float: left !important
    }
    .float-md-right {
        float: right !important
    }
    .float-md-none {
        float: none !important
    }
}

@media (min-width: 992px) {
    .float-lg-left {
        float: left !important
    }
    .float-lg-right {
        float: right !important
    }
    .float-lg-none {
        float: none !important
    }
}

@media (min-width: 1200px) {
    .float-xl-left {
        float: left !important
    }
    .float-xl-right {
        float: right !important
    }
    .float-xl-none {
        float: none !important
    }
}

.fixed-top {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030
}

.fixed-bottom {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1030
}

.sticky-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1030
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto
}

.w-25 {
    width: 25% !important
}

.w-50 {
    width: 50% !important
}

.w-75 {
    width: 75% !important
}

.w-100 {
    width: 100% !important
}

.h-25 {
    height: 25% !important
}

.h-50 {
    height: 50% !important
}

.h-75 {
    height: 75% !important
}

.h-100 {
    height: 100% !important
}

.mw-100 {
    max-width: 100% !important
}

.mh-100 {
    max-height: 100% !important
}

.m-0 {
    margin: 0 0 !important
}

.mt-0 {
    margin-top: 0 !important
}

.mr-0 {
    margin-right: 0 !important
}

.mb-0 {
    margin-bottom: 0 !important
}

.ml-0 {
    margin-left: 0 !important
}

.mx-0 {
    margin-right: 0 !important;
    margin-left: 0 !important
}

.my-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important
}

.m-1 {
    margin: .25rem .25rem !important
}

.mt-1 {
    margin-top: .25rem !important
}

.mr-1 {
    margin-right: .25rem !important
}

.mb-1 {
    margin-bottom: .25rem !important
}

.ml-1 {
    margin-left: .25rem !important
}

.mx-1 {
    margin-right: .25rem !important;
    margin-left: .25rem !important
}

.my-1 {
    margin-top: .25rem !important;
    margin-bottom: .25rem !important
}

.m-2 {
    margin: .5rem .5rem !important
}

.mt-2 {
    margin-top: .5rem !important
}

.mr-2 {
    margin-right: .5rem !important
}

.mb-2 {
    margin-bottom: .5rem !important
}

.ml-2 {
    margin-left: .5rem !important
}

.mx-2 {
    margin-right: .5rem !important;
    margin-left: .5rem !important
}

.my-2 {
    margin-top: .5rem !important;
    margin-bottom: .5rem !important
}

.m-3 {
    margin: 1rem 1rem !important
}

.mt-3 {
    margin-top: 1rem !important
}

.mr-3 {
    margin-right: 1rem !important
}

.mb-3 {
    margin-bottom: 1rem !important
}

.ml-3 {
    margin-left: 1rem !important
}

.mx-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important
}

.my-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important
}

.m-4 {
    margin: 1.5rem 1.5rem !important
}

.mt-4 {
    margin-top: 1.5rem !important
}

.mr-4 {
    margin-right: 1.5rem !important
}

.mb-4 {
    margin-bottom: 1.5rem !important
}

.ml-4 {
    margin-left: 1.5rem !important
}

.mx-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important
}

.my-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important
}

.m-5 {
    margin: 3rem 3rem !important
}

.mt-5 {
    margin-top: 3rem !important
}

.mr-5 {
    margin-right: 3rem !important
}

.mb-5 {
    margin-bottom: 3rem !important
}

.ml-5 {
    margin-left: 3rem !important
}

.mx-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important
}

.my-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important
}

.p-0 {
    padding: 0 0 !important
}

.pt-0 {
    padding-top: 0 !important
}

.pr-0 {
    padding-right: 0 !important
}

.pb-0 {
    padding-bottom: 0 !important
}

.pl-0 {
    padding-left: 0 !important
}

.px-0 {
    padding-right: 0 !important;
    padding-left: 0 !important
}

.py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important
}

.p-1 {
    padding: .25rem .25rem !important
}

.pt-1 {
    padding-top: .25rem !important
}

.pr-1 {
    padding-right: .25rem !important
}

.pb-1 {
    padding-bottom: .25rem !important
}

.pl-1 {
    padding-left: .25rem !important
}

.px-1 {
    padding-right: .25rem !important;
    padding-left: .25rem !important
}

.py-1 {
    padding-top: .25rem !important;
    padding-bottom: .25rem !important
}

.p-2 {
    padding: .5rem .5rem !important
}

.pt-2 {
    padding-top: .5rem !important
}

.pr-2 {
    padding-right: .5rem !important
}

.pb-2 {
    padding-bottom: .5rem !important
}

.pl-2 {
    padding-left: .5rem !important
}

.px-2 {
    padding-right: .5rem !important;
    padding-left: .5rem !important
}

.py-2 {
    padding-top: .5rem !important;
    padding-bottom: .5rem !important
}

.p-3 {
    padding: 1rem 1rem !important
}

.pt-3 {
    padding-top: 1rem !important
}

.pr-3 {
    padding-right: 1rem !important
}

.pb-3 {
    padding-bottom: 1rem !important
}

.pl-3 {
    padding-left: 1rem !important
}

.px-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important
}

.py-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important
}

.p-4 {
    padding: 1.5rem 1.5rem !important
}

.pt-4 {
    padding-top: 1.5rem !important
}

.pr-4 {
    padding-right: 1.5rem !important
}

.pb-4 {
    padding-bottom: 1.5rem !important
}

.pl-4 {
    padding-left: 1.5rem !important
}

.px-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important
}

.py-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important
}

.p-5 {
    padding: 3rem 3rem !important
}

.pt-5 {
    padding-top: 3rem !important
}

.pr-5 {
    padding-right: 3rem !important
}

.pb-5 {
    padding-bottom: 3rem !important
}

.pl-5 {
    padding-left: 3rem !important
}

.px-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important
}

.py-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important
}

.m-auto {
    margin: auto !important
}

.mt-auto {
    margin-top: auto !important
}

.mr-auto {
    margin-right: auto !important
}

.mb-auto {
    margin-bottom: auto !important
}

.ml-auto {
    margin-left: auto !important
}

.mx-auto {
    margin-right: auto !important;
    margin-left: auto !important
}

.my-auto {
    margin-top: auto !important;
    margin-bottom: auto !important
}

@media (min-width: 576px) {
    .m-sm-0 {
        margin: 0 0 !important
    }
    .mt-sm-0 {
        margin-top: 0 !important
    }
    .mr-sm-0 {
        margin-right: 0 !important
    }
    .mb-sm-0 {
        margin-bottom: 0 !important
    }
    .ml-sm-0 {
        margin-left: 0 !important
    }
    .mx-sm-0 {
        margin-right: 0 !important;
        margin-left: 0 !important
    }
    .my-sm-0 {
        margin-top: 0 !important;
        margin-bottom: 0 !important
    }
    .m-sm-1 {
        margin: .25rem .25rem !important
    }
    .mt-sm-1 {
        margin-top: .25rem !important
    }
    .mr-sm-1 {
        margin-right: .25rem !important
    }
    .mb-sm-1 {
        margin-bottom: .25rem !important
    }
    .ml-sm-1 {
        margin-left: .25rem !important
    }
    .mx-sm-1 {
        margin-right: .25rem !important;
        margin-left: .25rem !important
    }
    .my-sm-1 {
        margin-top: .25rem !important;
        margin-bottom: .25rem !important
    }
    .m-sm-2 {
        margin: .5rem .5rem !important
    }
    .mt-sm-2 {
        margin-top: .5rem !important
    }
    .mr-sm-2 {
        margin-right: .5rem !important
    }
    .mb-sm-2 {
        margin-bottom: .5rem !important
    }
    .ml-sm-2 {
        margin-left: .5rem !important
    }
    .mx-sm-2 {
        margin-right: .5rem !important;
        margin-left: .5rem !important
    }
    .my-sm-2 {
        margin-top: .5rem !important;
        margin-bottom: .5rem !important
    }
    .m-sm-3 {
        margin: 1rem 1rem !important
    }
    .mt-sm-3 {
        margin-top: 1rem !important
    }
    .mr-sm-3 {
        margin-right: 1rem !important
    }
    .mb-sm-3 {
        margin-bottom: 1rem !important
    }
    .ml-sm-3 {
        margin-left: 1rem !important
    }
    .mx-sm-3 {
        margin-right: 1rem !important;
        margin-left: 1rem !important
    }
    .my-sm-3 {
        margin-top: 1rem !important;
        margin-bottom: 1rem !important
    }
    .m-sm-4 {
        margin: 1.5rem 1.5rem !important
    }
    .mt-sm-4 {
        margin-top: 1.5rem !important
    }
    .mr-sm-4 {
        margin-right: 1.5rem !important
    }
    .mb-sm-4 {
        margin-bottom: 1.5rem !important
    }
    .ml-sm-4 {
        margin-left: 1.5rem !important
    }
    .mx-sm-4 {
        margin-right: 1.5rem !important;
        margin-left: 1.5rem !important
    }
    .my-sm-4 {
        margin-top: 1.5rem !important;
        margin-bottom: 1.5rem !important
    }
    .m-sm-5 {
        margin: 3rem 3rem !important
    }
    .mt-sm-5 {
        margin-top: 3rem !important
    }
    .mr-sm-5 {
        margin-right: 3rem !important
    }
    .mb-sm-5 {
        margin-bottom: 3rem !important
    }
    .ml-sm-5 {
        margin-left: 3rem !important
    }
    .mx-sm-5 {
        margin-right: 3rem !important;
        margin-left: 3rem !important
    }
    .my-sm-5 {
        margin-top: 3rem !important;
        margin-bottom: 3rem !important
    }
    .p-sm-0 {
        padding: 0 0 !important
    }
    .pt-sm-0 {
        padding-top: 0 !important
    }
    .pr-sm-0 {
        padding-right: 0 !important
    }
    .pb-sm-0 {
        padding-bottom: 0 !important
    }
    .pl-sm-0 {
        padding-left: 0 !important
    }
    .px-sm-0 {
        padding-right: 0 !important;
        padding-left: 0 !important
    }
    .py-sm-0 {
        padding-top: 0 !important;
        padding-bottom: 0 !important
    }
    .p-sm-1 {
        padding: .25rem .25rem !important
    }
    .pt-sm-1 {
        padding-top: .25rem !important
    }
    .pr-sm-1 {
        padding-right: .25rem !important
    }
    .pb-sm-1 {
        padding-bottom: .25rem !important
    }
    .pl-sm-1 {
        padding-left: .25rem !important
    }
    .px-sm-1 {
        padding-right: .25rem !important;
        padding-left: .25rem !important
    }
    .py-sm-1 {
        padding-top: .25rem !important;
        padding-bottom: .25rem !important
    }
    .p-sm-2 {
        padding: .5rem .5rem !important
    }
    .pt-sm-2 {
        padding-top: .5rem !important
    }
    .pr-sm-2 {
        padding-right: .5rem !important
    }
    .pb-sm-2 {
        padding-bottom: .5rem !important
    }
    .pl-sm-2 {
        padding-left: .5rem !important
    }
    .px-sm-2 {
        padding-right: .5rem !important;
        padding-left: .5rem !important
    }
    .py-sm-2 {
        padding-top: .5rem !important;
        padding-bottom: .5rem !important
    }
    .p-sm-3 {
        padding: 1rem 1rem !important
    }
    .pt-sm-3 {
        padding-top: 1rem !important
    }
    .pr-sm-3 {
        padding-right: 1rem !important
    }
    .pb-sm-3 {
        padding-bottom: 1rem !important
    }
    .pl-sm-3 {
        padding-left: 1rem !important
    }
    .px-sm-3 {
        padding-right: 1rem !important;
        padding-left: 1rem !important
    }
    .py-sm-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important
    }
    .p-sm-4 {
        padding: 1.5rem 1.5rem !important
    }
    .pt-sm-4 {
        padding-top: 1.5rem !important
    }
    .pr-sm-4 {
        padding-right: 1.5rem !important
    }
    .pb-sm-4 {
        padding-bottom: 1.5rem !important
    }
    .pl-sm-4 {
        padding-left: 1.5rem !important
    }
    .px-sm-4 {
        padding-right: 1.5rem !important;
        padding-left: 1.5rem !important
    }
    .py-sm-4 {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important
    }
    .p-sm-5 {
        padding: 3rem 3rem !important
    }
    .pt-sm-5 {
        padding-top: 3rem !important
    }
    .pr-sm-5 {
        padding-right: 3rem !important
    }
    .pb-sm-5 {
        padding-bottom: 3rem !important
    }
    .pl-sm-5 {
        padding-left: 3rem !important
    }
    .px-sm-5 {
        padding-right: 3rem !important;
        padding-left: 3rem !important
    }
    .py-sm-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important
    }
    .m-sm-auto {
        margin: auto !important
    }
    .mt-sm-auto {
        margin-top: auto !important
    }
    .mr-sm-auto {
        margin-right: auto !important
    }
    .mb-sm-auto {
        margin-bottom: auto !important
    }
    .ml-sm-auto {
        margin-left: auto !important
    }
    .mx-sm-auto {
        margin-right: auto !important;
        margin-left: auto !important
    }
    .my-sm-auto {
        margin-top: auto !important;
        margin-bottom: auto !important
    }
}

@media (min-width: 768px) {
    .m-md-0 {
        margin: 0 0 !important
    }
    .mt-md-0 {
        margin-top: 0 !important
    }
    .mr-md-0 {
        margin-right: 0 !important
    }
    .mb-md-0 {
        margin-bottom: 0 !important
    }
    .ml-md-0 {
        margin-left: 0 !important
    }
    .mx-md-0 {
        margin-right: 0 !important;
        margin-left: 0 !important
    }
    .my-md-0 {
        margin-top: 0 !important;
        margin-bottom: 0 !important
    }
    .m-md-1 {
        margin: .25rem .25rem !important
    }
    .mt-md-1 {
        margin-top: .25rem !important
    }
    .mr-md-1 {
        margin-right: .25rem !important
    }
    .mb-md-1 {
        margin-bottom: .25rem !important
    }
    .ml-md-1 {
        margin-left: .25rem !important
    }
    .mx-md-1 {
        margin-right: .25rem !important;
        margin-left: .25rem !important
    }
    .my-md-1 {
        margin-top: .25rem !important;
        margin-bottom: .25rem !important
    }
    .m-md-2 {
        margin: .5rem .5rem !important
    }
    .mt-md-2 {
        margin-top: .5rem !important
    }
    .mr-md-2 {
        margin-right: .5rem !important
    }
    .mb-md-2 {
        margin-bottom: .5rem !important
    }
    .ml-md-2 {
        margin-left: .5rem !important
    }
    .mx-md-2 {
        margin-right: .5rem !important;
        margin-left: .5rem !important
    }
    .my-md-2 {
        margin-top: .5rem !important;
        margin-bottom: .5rem !important
    }
    .m-md-3 {
        margin: 1rem 1rem !important
    }
    .mt-md-3 {
        margin-top: 1rem !important
    }
    .mr-md-3 {
        margin-right: 1rem !important
    }
    .mb-md-3 {
        margin-bottom: 1rem !important
    }
    .ml-md-3 {
        margin-left: 1rem !important
    }
    .mx-md-3 {
        margin-right: 1rem !important;
        margin-left: 1rem !important
    }
    .my-md-3 {
        margin-top: 1rem !important;
        margin-bottom: 1rem !important
    }
    .m-md-4 {
        margin: 1.5rem 1.5rem !important
    }
    .mt-md-4 {
        margin-top: 1.5rem !important
    }
    .mr-md-4 {
        margin-right: 1.5rem !important
    }
    .mb-md-4 {
        margin-bottom: 1.5rem !important
    }
    .ml-md-4 {
        margin-left: 1.5rem !important
    }
    .mx-md-4 {
        margin-right: 1.5rem !important;
        margin-left: 1.5rem !important
    }
    .my-md-4 {
        margin-top: 1.5rem !important;
        margin-bottom: 1.5rem !important
    }
    .m-md-5 {
        margin: 3rem 3rem !important
    }
    .mt-md-5 {
        margin-top: 3rem !important
    }
    .mr-md-5 {
        margin-right: 3rem !important
    }
    .mb-md-5 {
        margin-bottom: 3rem !important
    }
    .ml-md-5 {
        margin-left: 3rem !important
    }
    .mx-md-5 {
        margin-right: 3rem !important;
        margin-left: 3rem !important
    }
    .my-md-5 {
        margin-top: 3rem !important;
        margin-bottom: 3rem !important
    }
    .p-md-0 {
        padding: 0 0 !important
    }
    .pt-md-0 {
        padding-top: 0 !important
    }
    .pr-md-0 {
        padding-right: 0 !important
    }
    .pb-md-0 {
        padding-bottom: 0 !important
    }
    .pl-md-0 {
        padding-left: 0 !important
    }
    .px-md-0 {
        padding-right: 0 !important;
        padding-left: 0 !important
    }
    .py-md-0 {
        padding-top: 0 !important;
        padding-bottom: 0 !important
    }
    .p-md-1 {
        padding: .25rem .25rem !important
    }
    .pt-md-1 {
        padding-top: .25rem !important
    }
    .pr-md-1 {
        padding-right: .25rem !important
    }
    .pb-md-1 {
        padding-bottom: .25rem !important
    }
    .pl-md-1 {
        padding-left: .25rem !important
    }
    .px-md-1 {
        padding-right: .25rem !important;
        padding-left: .25rem !important
    }
    .py-md-1 {
        padding-top: .25rem !important;
        padding-bottom: .25rem !important
    }
    .p-md-2 {
        padding: .5rem .5rem !important
    }
    .pt-md-2 {
        padding-top: .5rem !important
    }
    .pr-md-2 {
        padding-right: .5rem !important
    }
    .pb-md-2 {
        padding-bottom: .5rem !important
    }
    .pl-md-2 {
        padding-left: .5rem !important
    }
    .px-md-2 {
        padding-right: .5rem !important;
        padding-left: .5rem !important
    }
    .py-md-2 {
        padding-top: .5rem !important;
        padding-bottom: .5rem !important
    }
    .p-md-3 {
        padding: 1rem 1rem !important
    }
    .pt-md-3 {
        padding-top: 1rem !important
    }
    .pr-md-3 {
        padding-right: 1rem !important
    }
    .pb-md-3 {
        padding-bottom: 1rem !important
    }
    .pl-md-3 {
        padding-left: 1rem !important
    }
    .px-md-3 {
        padding-right: 1rem !important;
        padding-left: 1rem !important
    }
    .py-md-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important
    }
    .p-md-4 {
        padding: 1.5rem 1.5rem !important
    }
    .pt-md-4 {
        padding-top: 1.5rem !important
    }
    .pr-md-4 {
        padding-right: 1.5rem !important
    }
    .pb-md-4 {
        padding-bottom: 1.5rem !important
    }
    .pl-md-4 {
        padding-left: 1.5rem !important
    }
    .px-md-4 {
        padding-right: 1.5rem !important;
        padding-left: 1.5rem !important
    }
    .py-md-4 {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important
    }
    .p-md-5 {
        padding: 3rem 3rem !important
    }
    .pt-md-5 {
        padding-top: 3rem !important
    }
    .pr-md-5 {
        padding-right: 3rem !important
    }
    .pb-md-5 {
        padding-bottom: 3rem !important
    }
    .pl-md-5 {
        padding-left: 3rem !important
    }
    .px-md-5 {
        padding-right: 3rem !important;
        padding-left: 3rem !important
    }
    .py-md-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important
    }
    .m-md-auto {
        margin: auto !important
    }
    .mt-md-auto {
        margin-top: auto !important
    }
    .mr-md-auto {
        margin-right: auto !important
    }
    .mb-md-auto {
        margin-bottom: auto !important
    }
    .ml-md-auto {
        margin-left: auto !important
    }
    .mx-md-auto {
        margin-right: auto !important;
        margin-left: auto !important
    }
    .my-md-auto {
        margin-top: auto !important;
        margin-bottom: auto !important
    }
}

@media (min-width: 992px) {
    .m-lg-0 {
        margin: 0 0 !important
    }
    .mt-lg-0 {
        margin-top: 0 !important
    }
    .mr-lg-0 {
        margin-right: 0 !important
    }
    .mb-lg-0 {
        margin-bottom: 0 !important
    }
    .ml-lg-0 {
        margin-left: 0 !important
    }
    .mx-lg-0 {
        margin-right: 0 !important;
        margin-left: 0 !important
    }
    .my-lg-0 {
        margin-top: 0 !important;
        margin-bottom: 0 !important
    }
    .m-lg-1 {
        margin: .25rem .25rem !important
    }
    .mt-lg-1 {
        margin-top: .25rem !important
    }
    .mr-lg-1 {
        margin-right: .25rem !important
    }
    .mb-lg-1 {
        margin-bottom: .25rem !important
    }
    .ml-lg-1 {
        margin-left: .25rem !important
    }
    .mx-lg-1 {
        margin-right: .25rem !important;
        margin-left: .25rem !important
    }
    .my-lg-1 {
        margin-top: .25rem !important;
        margin-bottom: .25rem !important
    }
    .m-lg-2 {
        margin: .5rem .5rem !important
    }
    .mt-lg-2 {
        margin-top: .5rem !important
    }
    .mr-lg-2 {
        margin-right: .5rem !important
    }
    .mb-lg-2 {
        margin-bottom: .5rem !important
    }
    .ml-lg-2 {
        margin-left: .5rem !important
    }
    .mx-lg-2 {
        margin-right: .5rem !important;
        margin-left: .5rem !important
    }
    .my-lg-2 {
        margin-top: .5rem !important;
        margin-bottom: .5rem !important
    }
    .m-lg-3 {
        margin: 1rem 1rem !important
    }
    .mt-lg-3 {
        margin-top: 1rem !important
    }
    .mr-lg-3 {
        margin-right: 1rem !important
    }
    .mb-lg-3 {
        margin-bottom: 1rem !important
    }
    .ml-lg-3 {
        margin-left: 1rem !important
    }
    .mx-lg-3 {
        margin-right: 1rem !important;
        margin-left: 1rem !important
    }
    .my-lg-3 {
        margin-top: 1rem !important;
        margin-bottom: 1rem !important
    }
    .m-lg-4 {
        margin: 1.5rem 1.5rem !important
    }
    .mt-lg-4 {
        margin-top: 1.5rem !important
    }
    .mr-lg-4 {
        margin-right: 1.5rem !important
    }
    .mb-lg-4 {
        margin-bottom: 1.5rem !important
    }
    .ml-lg-4 {
        margin-left: 1.5rem !important
    }
    .mx-lg-4 {
        margin-right: 1.5rem !important;
        margin-left: 1.5rem !important
    }
    .my-lg-4 {
        margin-top: 1.5rem !important;
        margin-bottom: 1.5rem !important
    }
    .m-lg-5 {
        margin: 3rem 3rem !important
    }
    .mt-lg-5 {
        margin-top: 3rem !important
    }
    .mr-lg-5 {
        margin-right: 3rem !important
    }
    .mb-lg-5 {
        margin-bottom: 3rem !important
    }
    .ml-lg-5 {
        margin-left: 3rem !important
    }
    .mx-lg-5 {
        margin-right: 3rem !important;
        margin-left: 3rem !important
    }
    .my-lg-5 {
        margin-top: 3rem !important;
        margin-bottom: 3rem !important
    }
    .p-lg-0 {
        padding: 0 0 !important
    }
    .pt-lg-0 {
        padding-top: 0 !important
    }
    .pr-lg-0 {
        padding-right: 0 !important
    }
    .pb-lg-0 {
        padding-bottom: 0 !important
    }
    .pl-lg-0 {
        padding-left: 0 !important
    }
    .px-lg-0 {
        padding-right: 0 !important;
        padding-left: 0 !important
    }
    .py-lg-0 {
        padding-top: 0 !important;
        padding-bottom: 0 !important
    }
    .p-lg-1 {
        padding: .25rem .25rem !important
    }
    .pt-lg-1 {
        padding-top: .25rem !important
    }
    .pr-lg-1 {
        padding-right: .25rem !important
    }
    .pb-lg-1 {
        padding-bottom: .25rem !important
    }
    .pl-lg-1 {
        padding-left: .25rem !important
    }
    .px-lg-1 {
        padding-right: .25rem !important;
        padding-left: .25rem !important
    }
    .py-lg-1 {
        padding-top: .25rem !important;
        padding-bottom: .25rem !important
    }
    .p-lg-2 {
        padding: .5rem .5rem !important
    }
    .pt-lg-2 {
        padding-top: .5rem !important
    }
    .pr-lg-2 {
        padding-right: .5rem !important
    }
    .pb-lg-2 {
        padding-bottom: .5rem !important
    }
    .pl-lg-2 {
        padding-left: .5rem !important
    }
    .px-lg-2 {
        padding-right: .5rem !important;
        padding-left: .5rem !important
    }
    .py-lg-2 {
        padding-top: .5rem !important;
        padding-bottom: .5rem !important
    }
    .p-lg-3 {
        padding: 1rem 1rem !important
    }
    .pt-lg-3 {
        padding-top: 1rem !important
    }
    .pr-lg-3 {
        padding-right: 1rem !important
    }
    .pb-lg-3 {
        padding-bottom: 1rem !important
    }
    .pl-lg-3 {
        padding-left: 1rem !important
    }
    .px-lg-3 {
        padding-right: 1rem !important;
        padding-left: 1rem !important
    }
    .py-lg-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important
    }
    .p-lg-4 {
        padding: 1.5rem 1.5rem !important
    }
    .pt-lg-4 {
        padding-top: 1.5rem !important
    }
    .pr-lg-4 {
        padding-right: 1.5rem !important
    }
    .pb-lg-4 {
        padding-bottom: 1.5rem !important
    }
    .pl-lg-4 {
        padding-left: 1.5rem !important
    }
    .px-lg-4 {
        padding-right: 1.5rem !important;
        padding-left: 1.5rem !important
    }
    .py-lg-4 {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important
    }
    .p-lg-5 {
        padding: 3rem 3rem !important
    }
    .pt-lg-5 {
        padding-top: 3rem !important
    }
    .pr-lg-5 {
        padding-right: 3rem !important
    }
    .pb-lg-5 {
        padding-bottom: 3rem !important
    }
    .pl-lg-5 {
        padding-left: 3rem !important
    }
    .px-lg-5 {
        padding-right: 3rem !important;
        padding-left: 3rem !important
    }
    .py-lg-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important
    }
    .m-lg-auto {
        margin: auto !important
    }
    .mt-lg-auto {
        margin-top: auto !important
    }
    .mr-lg-auto {
        margin-right: auto !important
    }
    .mb-lg-auto {
        margin-bottom: auto !important
    }
    .ml-lg-auto {
        margin-left: auto !important
    }
    .mx-lg-auto {
        margin-right: auto !important;
        margin-left: auto !important
    }
    .my-lg-auto {
        margin-top: auto !important;
        margin-bottom: auto !important
    }
}

@media (min-width: 1200px) {
    .m-xl-0 {
        margin: 0 0 !important
    }
    .mt-xl-0 {
        margin-top: 0 !important
    }
    .mr-xl-0 {
        margin-right: 0 !important
    }
    .mb-xl-0 {
        margin-bottom: 0 !important
    }
    .ml-xl-0 {
        margin-left: 0 !important
    }
    .mx-xl-0 {
        margin-right: 0 !important;
        margin-left: 0 !important
    }
    .my-xl-0 {
        margin-top: 0 !important;
        margin-bottom: 0 !important
    }
    .m-xl-1 {
        margin: .25rem .25rem !important
    }
    .mt-xl-1 {
        margin-top: .25rem !important
    }
    .mr-xl-1 {
        margin-right: .25rem !important
    }
    .mb-xl-1 {
        margin-bottom: .25rem !important
    }
    .ml-xl-1 {
        margin-left: .25rem !important
    }
    .mx-xl-1 {
        margin-right: .25rem !important;
        margin-left: .25rem !important
    }
    .my-xl-1 {
        margin-top: .25rem !important;
        margin-bottom: .25rem !important
    }
    .m-xl-2 {
        margin: .5rem .5rem !important
    }
    .mt-xl-2 {
        margin-top: .5rem !important
    }
    .mr-xl-2 {
        margin-right: .5rem !important
    }
    .mb-xl-2 {
        margin-bottom: .5rem !important
    }
    .ml-xl-2 {
        margin-left: .5rem !important
    }
    .mx-xl-2 {
        margin-right: .5rem !important;
        margin-left: .5rem !important
    }
    .my-xl-2 {
        margin-top: .5rem !important;
        margin-bottom: .5rem !important
    }
    .m-xl-3 {
        margin: 1rem 1rem !important
    }
    .mt-xl-3 {
        margin-top: 1rem !important
    }
    .mr-xl-3 {
        margin-right: 1rem !important
    }
    .mb-xl-3 {
        margin-bottom: 1rem !important
    }
    .ml-xl-3 {
        margin-left: 1rem !important
    }
    .mx-xl-3 {
        margin-right: 1rem !important;
        margin-left: 1rem !important
    }
    .my-xl-3 {
        margin-top: 1rem !important;
        margin-bottom: 1rem !important
    }
    .m-xl-4 {
        margin: 1.5rem 1.5rem !important
    }
    .mt-xl-4 {
        margin-top: 1.5rem !important
    }
    .mr-xl-4 {
        margin-right: 1.5rem !important
    }
    .mb-xl-4 {
        margin-bottom: 1.5rem !important
    }
    .ml-xl-4 {
        margin-left: 1.5rem !important
    }
    .mx-xl-4 {
        margin-right: 1.5rem !important;
        margin-left: 1.5rem !important
    }
    .my-xl-4 {
        margin-top: 1.5rem !important;
        margin-bottom: 1.5rem !important
    }
    .m-xl-5 {
        margin: 3rem 3rem !important
    }
    .mt-xl-5 {
        margin-top: 3rem !important
    }
    .mr-xl-5 {
        margin-right: 3rem !important
    }
    .mb-xl-5 {
        margin-bottom: 3rem !important
    }
    .ml-xl-5 {
        margin-left: 3rem !important
    }
    .mx-xl-5 {
        margin-right: 3rem !important;
        margin-left: 3rem !important
    }
    .my-xl-5 {
        margin-top: 3rem !important;
        margin-bottom: 3rem !important
    }
    .p-xl-0 {
        padding: 0 0 !important
    }
    .pt-xl-0 {
        padding-top: 0 !important
    }
    .pr-xl-0 {
        padding-right: 0 !important
    }
    .pb-xl-0 {
        padding-bottom: 0 !important
    }
    .pl-xl-0 {
        padding-left: 0 !important
    }
    .px-xl-0 {
        padding-right: 0 !important;
        padding-left: 0 !important
    }
    .py-xl-0 {
        padding-top: 0 !important;
        padding-bottom: 0 !important
    }
    .p-xl-1 {
        padding: .25rem .25rem !important
    }
    .pt-xl-1 {
        padding-top: .25rem !important
    }
    .pr-xl-1 {
        padding-right: .25rem !important
    }
    .pb-xl-1 {
        padding-bottom: .25rem !important
    }
    .pl-xl-1 {
        padding-left: .25rem !important
    }
    .px-xl-1 {
        padding-right: .25rem !important;
        padding-left: .25rem !important
    }
    .py-xl-1 {
        padding-top: .25rem !important;
        padding-bottom: .25rem !important
    }
    .p-xl-2 {
        padding: .5rem .5rem !important
    }
    .pt-xl-2 {
        padding-top: .5rem !important
    }
    .pr-xl-2 {
        padding-right: .5rem !important
    }
    .pb-xl-2 {
        padding-bottom: .5rem !important
    }
    .pl-xl-2 {
        padding-left: .5rem !important
    }
    .px-xl-2 {
        padding-right: .5rem !important;
        padding-left: .5rem !important
    }
    .py-xl-2 {
        padding-top: .5rem !important;
        padding-bottom: .5rem !important
    }
    .p-xl-3 {
        padding: 1rem 1rem !important
    }
    .pt-xl-3 {
        padding-top: 1rem !important
    }
    .pr-xl-3 {
        padding-right: 1rem !important
    }
    .pb-xl-3 {
        padding-bottom: 1rem !important
    }
    .pl-xl-3 {
        padding-left: 1rem !important
    }
    .px-xl-3 {
        padding-right: 1rem !important;
        padding-left: 1rem !important
    }
    .py-xl-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important
    }
    .p-xl-4 {
        padding: 1.5rem 1.5rem !important
    }
    .pt-xl-4 {
        padding-top: 1.5rem !important
    }
    .pr-xl-4 {
        padding-right: 1.5rem !important
    }
    .pb-xl-4 {
        padding-bottom: 1.5rem !important
    }
    .pl-xl-4 {
        padding-left: 1.5rem !important
    }
    .px-xl-4 {
        padding-right: 1.5rem !important;
        padding-left: 1.5rem !important
    }
    .py-xl-4 {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important
    }
    .p-xl-5 {
        padding: 3rem 3rem !important
    }
    .pt-xl-5 {
        padding-top: 3rem !important
    }
    .pr-xl-5 {
        padding-right: 3rem !important
    }
    .pb-xl-5 {
        padding-bottom: 3rem !important
    }
    .pl-xl-5 {
        padding-left: 3rem !important
    }
    .px-xl-5 {
        padding-right: 3rem !important;
        padding-left: 3rem !important
    }
    .py-xl-5 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important
    }
    .m-xl-auto {
        margin: auto !important
    }
    .mt-xl-auto {
        margin-top: auto !important
    }
    .mr-xl-auto {
        margin-right: auto !important
    }
    .mb-xl-auto {
        margin-bottom: auto !important
    }
    .ml-xl-auto {
        margin-left: auto !important
    }
    .mx-xl-auto {
        margin-right: auto !important;
        margin-left: auto !important
    }
    .my-xl-auto {
        margin-top: auto !important;
        margin-bottom: auto !important
    }
}

.text-justify {
    text-align: justify !important
}

.text-nowrap {
    white-space: nowrap !important
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.text-left {
    text-align: left !important
}

.text-right {
    text-align: right !important
}

.text-center {
    text-align: center !important
}

@media (min-width: 576px) {
    .text-sm-left {
        text-align: left !important
    }
    .text-sm-right {
        text-align: right !important
    }
    .text-sm-center {
        text-align: center !important
    }
}

@media (min-width: 768px) {
    .text-md-left {
        text-align: left !important
    }
    .text-md-right {
        text-align: right !important
    }
    .text-md-center {
        text-align: center !important
    }
}

@media (min-width: 992px) {
    .text-lg-left {
        text-align: left !important
    }
    .text-lg-right {
        text-align: right !important
    }
    .text-lg-center {
        text-align: center !important
    }
}

@media (min-width: 1200px) {
    .text-xl-left {
        text-align: left !important
    }
    .text-xl-right {
        text-align: right !important
    }
    .text-xl-center {
        text-align: center !important
    }
}

.text-lowercase {
    text-transform: lowercase !important
}

.text-uppercase {
    text-transform: uppercase !important
}

.text-capitalize {
    text-transform: capitalize !important
}

.font-weight-normal {
    font-weight: 400
}

.font-weight-bold {
    font-weight: 500
}

.font-italic {
    font-style: italic
}

.text-white {
    color: #fff !important
}

.text-muted {
    color: #aaa !important
}

a.text-muted:focus,
a.text-muted:hover {
    color: #919090 !important
}

.text-primary {
    color: #047bf8 !important
}

a.text-primary:focus,
a.text-primary:hover {
    color: #0362c6 !important
}

.text-success {
    color: #90be2e !important
}

a.text-success:focus,
a.text-success:hover {
    color: #719524 !important
}

.text-info {
    color: #5bc0de !important
}

a.text-info:focus,
a.text-info:hover {
    color: #31b0d5 !important
}

.text-warning {
    color: #f0ad4e !important
}

a.text-warning:focus,
a.text-warning:hover {
    color: #ec971f !important
}

.text-danger {
    color: #e65252 !important
}

a.text-danger:focus,
a.text-danger:hover {
    color: #e02525 !important
}

.text-gray-dark {
    color: #292b2c !important
}

a.text-gray-dark:focus,
a.text-gray-dark:hover {
    color: #101112 !important
}

.text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0
}

.invisible {
    visibility: hidden !important
}

.hidden-xs-up {
    display: none !important
}

@media (max-width: 575px) {
    .hidden-xs-down {
        display: none !important
    }
}

@media (min-width: 576px) {
    .hidden-sm-up {
        display: none !important
    }
}

@media (max-width: 767px) {
    .hidden-sm-down {
        display: none !important
    }
}

@media (min-width: 768px) {
    .hidden-md-up {
        display: none !important
    }
}

@media (max-width: 991px) {
    .hidden-md-down {
        display: none !important
    }
}

@media (min-width: 992px) {
    .hidden-lg-up {
        display: none !important
    }
}

@media (max-width: 1199px) {
    .hidden-lg-down {
        display: none !important
    }
}

@media (min-width: 1200px) {
    .hidden-xl-up {
        display: none !important
    }
}

.hidden-xl-down {
    display: none !important
}

.visible-print-block {
    display: none !important
}

@media print {
    .visible-print-block {
        display: block !important
    }
}

.visible-print-inline {
    display: none !important
}

@media print {
    .visible-print-inline {
        display: inline !important
    }
}

.visible-print-inline-block {
    display: none !important
}

@media print {
    .visible-print-inline-block {
        display: inline-block !important
    }
}

@media print {
    .hidden-print {
        display: none !important
    }
}

.table th {
    font-weight: 500
}

.table.table-editable td:hover {
    background-color: #fff;
    -webkit-box-shadow: inset 0px 0px 0px 2px #047bf8;
    box-shadow: inset 0px 0px 0px 2px #047bf8
}

.table.table-lightborder td {
    border-top-color: rgba(83, 101, 140, 0.08)
}

.table.table-lightfont td {
    font-weight: 300
}

.table th,
.table td {
    vertical-align: middle
}

.table th img,
.table td img {
    max-width: 100%
}

.table thead th {
    border-bottom: 1px solid #999
}

.table tfoot th {
    border-top: 1px solid #999
}

.table tfoot th,
.table thead th {
    font-size: .7rem;
    text-transform: uppercase;
    border-top: none
}

.table tbody+tbody {
    border-top: 1px solid rgba(83, 101, 140, 0.33)
}

.table td.nowrap {
    white-space: nowrap
}

.table .row-actions {
    text-align: center
}

.table .row-actions .os-icon {
    font-size: 16px
}

.table .row-actions a {
    margin-right: 0.8rem;
    color: #3E4B5B
}

.table .row-actions a.danger {
    color: #9d1818
}

.table .row-actions a:last-child {
    margin-right: 0px
}

.table .cell-image-list {
    position: relative;
    display: inline-block;
    white-space: nowrap
}

.table .cell-image-list .cell-img {
    display: inline-block;
    width: 30px;
    height: 30px;
    background-size: cover;
    background-position: center center;
    border-radius: 2px;
    -webkit-box-shadow: 0px 0px 0px 2px #fff, 1px 1px 5px rgba(0, 0, 0, 0.8);
    box-shadow: 0px 0px 0px 2px #fff, 1px 1px 5px rgba(0, 0, 0, 0.8);
    vertical-align: middle;
    -webkit-transition: all 0.1s ease;
    transition: all 0.1s ease;
    -webkit-transform: scale(1);
    transform: scale(1);
    position: relative
}

.table .cell-image-list .cell-img:nth-child(1) {
    z-index: 5
}

.table .cell-image-list .cell-img:nth-child(2) {
    z-index: 4
}

.table .cell-image-list .cell-img:nth-child(3) {
    z-index: 3
}

.table .cell-image-list .cell-img:nth-child(4) {
    z-index: 2
}

.table .cell-image-list .cell-img:nth-child(5) {
    z-index: 1
}

.table .cell-image-list .cell-img+.cell-img {
    margin-left: -15px
}

.table .cell-image-list .cell-img+.cell-img:hover {
    -webkit-transform: translateX(5px);
    transform: translateX(5px)
}

.table .cell-image-list .cell-img-more {
    font-size: .7rem;
    display: inline-block;
    vertical-align: middle;
    margin-left: 10px;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    right: -70%;
    background-color: #fff;
    padding: 3px 5px;
    border-radius: 4px;
    z-index: 7;
    white-space: nowrap
}

.table-lg td {
    padding: 1.2rem 1.5rem
}

.table.table-v2 thead tr th,
.table.table-v2 tfoot tr th {
    text-align: center;
    border-top: 1px solid #999;
    border-bottom: 1px solid #999;
    background-color: rgba(0, 0, 0, 0.05)
}

.table.table-v2 thead tr th:first-child,
.table.table-v2 tfoot tr th:first-child {
    border-left: 1px solid #999
}

.table.table-v2 thead tr th:last-child,
.table.table-v2 tfoot tr th:last-child {
    border-right: 1px solid #999
}

.table.table-v2 tbody tr td {
    border-color: #d1d8e6
}

.controls-above-table {
    margin-bottom: 1rem
}

.controls-above-table .btn,
.controls-above-table .wrapper-front .fc-button,
.wrapper-front .controls-above-table .fc-button {
    margin-right: 0.5rem
}

.controls-above-table .form-control {
    margin-right: 1rem
}

.controls-above-table .form-control:last-child {
    margin-right: 0px
}

.controls-below-table {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    font-size: .9rem
}

.controls-below-table .table-records-info {
    color: rgba(0, 0, 0, 0.5)
}

.controls-below-table .table-records-pages ul {
    list-style: none
}

.controls-below-table .table-records-pages ul li {
    display: inline-block;
    margin: 0px 10px
}

.controls-below-table .table-records-pages ul li a.current {
    color: #3E4B5B
}

.wrapper-front table.dataTable {
    border-collapse: collapse !important
}

button,
input,
optgroup,
select,
textarea {
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 400
}

.form-control.rounded {
    border-radius: 30px
}

select.form-control.rounded {
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 40px;
    padding-left: 15px;
    background-position: right 5px top 50%;
    background-repeat: no-repeat;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAMCAYAAABSgIzaAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBNYWNpbnRvc2giIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NDZFNDEwNjlGNzFEMTFFMkJEQ0VDRTM1N0RCMzMyMkIiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NDZFNDEwNkFGNzFEMTFFMkJEQ0VDRTM1N0RCMzMyMkIiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo0NkU0MTA2N0Y3MUQxMUUyQkRDRUNFMzU3REIzMzIyQiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo0NkU0MTA2OEY3MUQxMUUyQkRDRUNFMzU3REIzMzIyQiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PuGsgwQAAAA5SURBVHjaYvz//z8DOYCJgUxAf42MQIzTk0D/M+KzkRGPoQSdykiKJrBGpOhgJFYTWNEIiEeAAAMAzNENEOH+do8AAAAASUVORK5CYII=)
}

.form-text {
    font-size: .9rem
}

.has-danger .form-control-feedback.text-muted {
    color: #e65252 !important
}

.form-control {
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif
}

.form-control.bright {
    border-color: #334652
}

.form-control[type="checkbox"] {
    width: auto;
    display: inline-block
}

.form-control::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.4)
}

.form-control:-ms-input-placeholder {
    color: rgba(0, 0, 0, 0.4)
}

.form-control::placeholder {
    color: rgba(0, 0, 0, 0.4)
}

.form-check-input {
    margin-right: 0.5rem
}

.form-buttons-w {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

.form-buttons-w .btn+.btn,
.form-buttons-w .wrapper-front .fc-button+.btn,
.wrapper-front .form-buttons-w .fc-button+.btn,
.form-buttons-w .wrapper-front .btn+.fc-button,
.wrapper-front .form-buttons-w .btn+.fc-button,
.form-buttons-w .wrapper-front .fc-button+.fc-button,
.wrapper-front .form-buttons-w .fc-button+.fc-button {
    margin-left: 10px
}

label.bigger {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    margin-top: 1rem
}

fieldset {
    margin-top: 2rem
}

legend {
    font-size: 1.1rem;
    display: block;
    margin-bottom: 1.5rem;
    position: relative;
    color: #047bf8
}

legend span {
    padding: 0px 0.5rem 0 0;
    background-color: #fff;
    display: inline-block;
    z-index: 2;
    position: relative
}

legend:before {
    content: "";
    position: absolute;
    left: 0px;
    right: 0px;
    height: 1px;
    top: 50%;
    background-color: rgba(0, 0, 0, 0.1);
    z-index: 1
}

.form-header {
    margin-bottom: 1rem;
    padding-top: 0.5rem;
    display: block
}

.form-desc {
    color: #999;
    margin-bottom: 1.5rem;
    font-weight: 300;
    font-size: .9rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: block
}

.nav.smaller {
    font-size: .8rem
}

.nav.smaller.nav-tabs .nav-link {
    padding: 0.7em 1.1em
}

.nav.smaller.nav-pills .nav-link {
    padding: 0.2em 1.1em
}

.nav.bigger {
    font-size: 1.25rem;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500
}

.nav.bigger.nav-tabs .nav-link.active:after,
.nav.bigger.nav-tabs .nav-item.show .nav-link:after {
    height: 6px;
    bottom: -3px;
    border-radius: 2px
}

.nav.bigger.nav-tabs .nav-link {
    padding-left: 0px;
    padding-right: 0px;
    margin-right: 2rem
}

.nav.upper {
    font-size: 1rem;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 2px
}

.nav.upper.nav-tabs .nav-link.active:after,
.nav.upper.nav-tabs .nav-item.show .nav-link:after {
    height: 3px;
    bottom: -2px;
    border-radius: 2px
}

.nav.upper.nav-tabs .nav-link {
    padding-left: 0px;
    padding-right: 0px;
    padding-bottom: 15px;
    margin-right: 2rem
}

.nav.upper.centered.nav-tabs .nav-link {
    padding-left: 0px;
    padding-right: 0px;
    padding-bottom: 15px;
    margin-left: 1rem;
    margin-right: 1rem
}

.nav.upper.centered.nav-tabs .nav-item {
    margin-right: 0px
}

.nav.centered {
    text-align: center;
    -ms-flex-pack: distribute;
    justify-content: space-around
}

.nav-link i {
    display: inline-block;
    color: #b0c4f3;
    font-size: 26px;
    margin-bottom: 5px
}

.nav-link span {
    display: block;
    font-size: .8rem
}

.nav-link.active i {
    color: #047bf8
}

.nav-tabs .nav-item {
    margin-bottom: 0px;
    margin-right: 1rem
}

.nav-tabs .nav-link {
    border: none;
    color: rgba(0, 0, 0, 0.3)
}

.nav-tabs .nav-link.disabled {
    color: #636c72;
    background-color: transparent;
    border-color: transparent
}

.nav-tabs .nav-link,
.nav-tabs .nav-item .nav-link {
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    position: relative
}

.nav-tabs .nav-link:after,
.nav-tabs .nav-item .nav-link:after {
    content: "";
    width: 0%;
    height: 3px;
    background-color: #047bf8;
    position: absolute;
    bottom: -2px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link:hover,
.nav-tabs .nav-item:hover .nav-link {
    color: #464a4c;
    background-color: transparent;
    border-color: #ddd #ddd transparent;
    position: relative
}

.nav-tabs .nav-link.active:after,
.nav-tabs .nav-item.show .nav-link:after,
.nav-tabs .nav-link:hover:after,
.nav-tabs .nav-item:hover .nav-link:after {
    width: 100%
}

.nav-pills .nav-link {
    border-radius: 30px;
    color: rgba(0, 0, 0, 0.4)
}

.nav-pills .nav-link.active,
.nav-pills .nav-item.show .nav-link {
    color: #fff;
    cursor: default;
    background-color: #047bf8
}

.toggled-buttons .btn-toggled {
    border: 2px solid transparent;
    border-radius: 4px;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 500;
    font-size: .8rem;
    padding: 4px 8px;
    color: rgba(0, 0, 0, 0.3);
    margin: 5px 0px
}

.toggled-buttons .btn-toggled.on,
.toggled-buttons .btn-toggled:hover {
    border-color: #047bf8;
    color: #047bf8
}

.toggled-buttons .btn-toggled+.btn-toggled {
    margin-left: 10px
}

.toggled-buttons.solid .btn-toggled {
    background-color: rgba(0, 0, 0, 0.07);
    color: rgba(0, 0, 0, 0.6);
    font-size: .9rem
}

.toggled-buttons.solid .btn-toggled.on,
.toggled-buttons.solid .btn-toggled:hover {
    background-color: #047bf8;
    color: #fff
}

.btn-sm,
.btn-group-sm>.btn,
.wrapper-front .btn-group-sm>.fc-button {
    padding: .2rem .5rem;
    font-size: .675rem;
    border-radius: .2rem;
    text-transform: uppercase
}

.btn-white,
.wrapper-front .fc-button {
    color: #333;
    background-color: #fff;
    border-color: rgba(0, 0, 0, 0.5)
}

.btn-white:hover,
.wrapper-front .fc-button:hover {
    color: #333;
    background-color: #e6e5e5;
    border-color: rgba(0, 0, 0, 0.5)
}

.btn-white:focus,
.wrapper-front .fc-button:focus,
.btn-white.focus,
.wrapper-front .focus.fc-button {
    -webkit-box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.5)
}

.btn-white.disabled,
.wrapper-front .disabled.fc-button,
.btn-white:disabled,
.wrapper-front .fc-button:disabled {
    background-color: #fff;
    border-color: rgba(0, 0, 0, 0.5)
}

.btn-white:active,
.wrapper-front .fc-button:active,
.btn-white.active,
.wrapper-front .active.fc-button,
.show>.btn-white.dropdown-toggle,
.wrapper-front .show>.dropdown-toggle.fc-button {
    color: #333;
    background-color: #e6e5e5;
    background-image: none;
    border-color: rgba(0, 0, 0, 0.5)
}

.btn,
.wrapper-front .fc-button {
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 400
}

.btn .os-icon,
.wrapper-front .fc-button .os-icon {
    font-size: 18px;
    margin-right: 1.2rem;
    display: inline-block;
    vertical-align: middle
}

.btn .os-icon+span,
.wrapper-front .fc-button .os-icon+span {
    display: inline-block;
    vertical-align: middle
}

.btn.btn-sm .os-icon,
.btn-group-sm>.btn .os-icon,
.wrapper-front .btn-group-sm>.fc-button .os-icon,
.wrapper-front .btn-sm.fc-button .os-icon {
    font-size: 14px;
    margin-right: .5rem
}

.btn.btn-rounded,
.wrapper-front .btn-rounded.fc-button {
    border-radius: 40px
}

.btn.btn-upper,
.wrapper-front .btn-upper.fc-button {
    text-transform: uppercase;
    letter-spacing: 2px;
    line-height: 1
}

.breadcrumb {
    list-style: none;
    margin: 0px;
    padding: 10px 30px 10px 30px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background-color: transparent
}

.breadcrumb li {
    margin-bottom: 0px;
    display: inline-block;
    text-transform: uppercase;
    font-size: .65rem;
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif
}

.breadcrumb li a {
    color: #3E4B5B
}

.breadcrumb li span {
    color: rgba(0, 0, 0, 0.4)
}

.breadcrumbs+.content-box {
    padding-top: 0px
}

.text-muted {
    font-weight: 300
}

@-webkit-keyframes showIntroShot1 {
    0% {
        opacity: 0;
        -webkit-transform: perspective(700px) translate3d(200px, -300px, -200px);
        transform: perspective(700px) translate3d(200px, -300px, -200px)
    }
    100% {
        opacity: 1;
        -webkit-transform: perspective(700px) translate3d(0px, 0px, 0px);
        transform: perspective(700px) translate3d(0px, 0px, 0px)
    }
}

@keyframes showIntroShot1 {
    0% {
        opacity: 0;
        -webkit-transform: perspective(700px) translate3d(200px, -300px, -200px);
        transform: perspective(700px) translate3d(200px, -300px, -200px)
    }
    100% {
        opacity: 1;
        -webkit-transform: perspective(700px) translate3d(0px, 0px, 0px);
        transform: perspective(700px) translate3d(0px, 0px, 0px)
    }
}

@-webkit-keyframes showIntroShot2 {
    0% {
        opacity: 0;
        -webkit-transform: perspective(700px) translate3d(250px, -250px, -200px);
        transform: perspective(700px) translate3d(250px, -250px, -200px)
    }
    100% {
        opacity: 1;
        -webkit-transform: perspective(700px) translate3d(0px, 0px, 0px);
        transform: perspective(700px) translate3d(0px, 0px, 0px)
    }
}

@keyframes showIntroShot2 {
    0% {
        opacity: 0;
        -webkit-transform: perspective(700px) translate3d(250px, -250px, -200px);
        transform: perspective(700px) translate3d(250px, -250px, -200px)
    }
    100% {
        opacity: 1;
        -webkit-transform: perspective(700px) translate3d(0px, 0px, 0px);
        transform: perspective(700px) translate3d(0px, 0px, 0px)
    }
}

@-webkit-keyframes showIntroShot3 {
    0% {
        opacity: 0;
        -webkit-transform: perspective(700px) translate3d(200px, -100px, -200px);
        transform: perspective(700px) translate3d(200px, -100px, -200px)
    }
    100% {
        opacity: 1;
        -webkit-transform: perspective(700px) translate3d(0px, 0px, 0px);
        transform: perspective(700px) translate3d(0px, 0px, 0px)
    }
}

@keyframes showIntroShot3 {
    0% {
        opacity: 0;
        -webkit-transform: perspective(700px) translate3d(200px, -100px, -200px);
        transform: perspective(700px) translate3d(200px, -100px, -200px)
    }
    100% {
        opacity: 1;
        -webkit-transform: perspective(700px) translate3d(0px, 0px, 0px);
        transform: perspective(700px) translate3d(0px, 0px, 0px)
    }
}

.shot1 {
    -webkit-animation: 0.6s ease-in-out 0.65s showIntroShot1;
    animation: 0.6s ease-in-out 0.65s showIntroShot1;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.shot2 {
    -webkit-animation: 0.7s ease-in-out 0.5s showIntroShot2;
    animation: 0.7s ease-in-out 0.5s showIntroShot2;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.shot3 {
    -webkit-animation: 0.5s ease-in-out 0.8s showIntroShot3;
    animation: 0.5s ease-in-out 0.8s showIntroShot3;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showIntroMedia {
    0% {
        -webkit-transform: perspective(1260px) rotateY(50deg) rotateX(-30deg) scale(0.8);
        transform: perspective(1260px) rotateY(50deg) rotateX(-30deg) scale(0.8)
    }
    100% {
        -webkit-transform: perspective(1260px) rotateY(-20.2deg) rotateX(10.6deg) scale(1);
        transform: perspective(1260px) rotateY(-20.2deg) rotateX(10.6deg) scale(1)
    }
}

@keyframes showIntroMedia {
    0% {
        -webkit-transform: perspective(1260px) rotateY(50deg) rotateX(-30deg) scale(0.8);
        transform: perspective(1260px) rotateY(50deg) rotateX(-30deg) scale(0.8)
    }
    100% {
        -webkit-transform: perspective(1260px) rotateY(-20.2deg) rotateX(10.6deg) scale(1);
        transform: perspective(1260px) rotateY(-20.2deg) rotateX(10.6deg) scale(1)
    }
}

.intro-media {
    -webkit-animation: 1.4s ease-in-out 0.2s showIntroMedia;
    animation: 1.4s ease-in-out 0.2s showIntroMedia;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showTopMenu {
    0% {
        -webkit-transform: translate3d(0, -100px, 0);
        transform: translate3d(0, -100px, 0)
    }
    100% {
        -webkit-transform: translate3d(0, 0px, 0);
        transform: translate3d(0, 0px, 0)
    }
}

@keyframes showTopMenu {
    0% {
        -webkit-transform: translate3d(0, -100px, 0);
        transform: translate3d(0, -100px, 0)
    }
    100% {
        -webkit-transform: translate3d(0, 0px, 0);
        transform: translate3d(0, 0px, 0)
    }
}

.menu-top-f {
    -webkit-animation: 0.4s ease-out 1.2s showTopMenu;
    animation: 0.4s ease-out 1.2s showTopMenu;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showIntroHeading {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -80px, 0);
        transform: translate3d(0, -80px, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0px, 0);
        transform: translate3d(0, 0px, 0)
    }
}

@keyframes showIntroHeading {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -80px, 0);
        transform: translate3d(0, -80px, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0px, 0);
        transform: translate3d(0, 0px, 0)
    }
}

.intro-heading {
    -webkit-animation: 1s ease 0.4s showIntroHeading;
    animation: 1s ease 0.4s showIntroHeading;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showIntroText {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 20px, 0);
        transform: translate3d(0, 20px, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0px, 0);
        transform: translate3d(0, 0px, 0)
    }
}

@keyframes showIntroText {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 20px, 0);
        transform: translate3d(0, 20px, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0px, 0);
        transform: translate3d(0, 0px, 0)
    }
}

.intro-text {
    -webkit-animation: 0.8s ease 0.8s showIntroText;
    animation: 0.8s ease 0.8s showIntroText;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showIntroDescription {
    0% {
        -webkit-transform: perspective(1260px) rotateY(25deg) rotateX(15deg);
        transform: perspective(1260px) rotateY(25deg) rotateX(15deg)
    }
    100% {
        -webkit-transform: perspective(1260px) rotateY(0deg) rotateX(0deg);
        transform: perspective(1260px) rotateY(0deg) rotateX(0deg)
    }
}

@keyframes showIntroDescription {
    0% {
        -webkit-transform: perspective(1260px) rotateY(25deg) rotateX(15deg);
        transform: perspective(1260px) rotateY(25deg) rotateX(15deg)
    }
    100% {
        -webkit-transform: perspective(1260px) rotateY(0deg) rotateX(0deg);
        transform: perspective(1260px) rotateY(0deg) rotateX(0deg)
    }
}

.intro-description {
    -webkit-animation: 2s ease 0.4s showIntroDescription;
    animation: 2s ease 0.4s showIntroDescription;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showIntroButton {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(40px, 0px, 0px);
        transform: translate3d(40px, 0px, 0px)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px);
        transform: translate3d(0px, 0px, 0px)
    }
}

@keyframes showIntroButton {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(40px, 0px, 0px);
        transform: translate3d(40px, 0px, 0px)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px);
        transform: translate3d(0px, 0px, 0px)
    }
}

.intro-buttons .btn-primary,
.intro-buttons .wrapper-front .fc-button.fc-state-active,
.wrapper-front .intro-buttons .fc-button.fc-state-active {
    -webkit-animation: 0.4s ease 1.4s showIntroButton;
    animation: 0.4s ease 1.4s showIntroButton;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.intro-buttons .btn-link {
    -webkit-animation: 0.4s ease 1.5s showIntroButton;
    animation: 0.4s ease 1.5s showIntroButton;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes fadeInFader2 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-460px, 0px, 0px) rotate(-45deg);
        transform: translate3d(-460px, 0px, 0px) rotate(-45deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(-360px, 0px, 0px) rotate(-45deg);
        transform: translate3d(-360px, 0px, 0px) rotate(-45deg)
    }
}

@keyframes fadeInFader2 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-460px, 0px, 0px) rotate(-45deg);
        transform: translate3d(-460px, 0px, 0px) rotate(-45deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(-360px, 0px, 0px) rotate(-45deg);
        transform: translate3d(-360px, 0px, 0px) rotate(-45deg)
    }
}

@-webkit-keyframes fadeInFader1 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-50px, -200px, 0px) rotate(-45deg);
        transform: translate3d(-50px, -200px, 0px) rotate(-45deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(-150px, -200px, 0px) rotate(-45deg);
        transform: translate3d(-150px, -200px, 0px) rotate(-45deg)
    }
}

@keyframes fadeInFader1 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-50px, -200px, 0px) rotate(-45deg);
        transform: translate3d(-50px, -200px, 0px) rotate(-45deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(-150px, -200px, 0px) rotate(-45deg);
        transform: translate3d(-150px, -200px, 0px) rotate(-45deg)
    }
}

.wrapper-front>.fade1 {
    -webkit-animation: 2s ease 0s fadeInFader1;
    animation: 2s ease 0s fadeInFader1;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.intro-w .fade2 {
    -webkit-animation: 2s ease 0s fadeInFader2;
    animation: 2s ease 0s fadeInFader2;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes fadeInPropertyItem {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0px, 150px, 0px) rotate(0deg);
        transform: translate3d(0px, 150px, 0px) rotate(0deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px) rotate(0deg);
        transform: translate3d(0px, 0px, 0px) rotate(0deg)
    }
}

@keyframes fadeInPropertyItem {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0px, 150px, 0px) rotate(0deg);
        transform: translate3d(0px, 150px, 0px) rotate(0deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px) rotate(0deg);
        transform: translate3d(0px, 0px, 0px) rotate(0deg)
    }
}

.property-items .property-item:nth-child(0) {
    -webkit-animation: 0.5s ease 0s fadeInPropertyItem;
    animation: 0.5s ease 0s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(1) {
    -webkit-animation: 0.5s ease .1s fadeInPropertyItem;
    animation: 0.5s ease .1s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(2) {
    -webkit-animation: 0.5s ease .2s fadeInPropertyItem;
    animation: 0.5s ease .2s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(3) {
    -webkit-animation: 0.5s ease .3s fadeInPropertyItem;
    animation: 0.5s ease .3s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(4) {
    -webkit-animation: 0.5s ease .4s fadeInPropertyItem;
    animation: 0.5s ease .4s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(5) {
    -webkit-animation: 0.5s ease .5s fadeInPropertyItem;
    animation: 0.5s ease .5s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(6) {
    -webkit-animation: 0.5s ease .6s fadeInPropertyItem;
    animation: 0.5s ease .6s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(7) {
    -webkit-animation: 0.5s ease .7s fadeInPropertyItem;
    animation: 0.5s ease .7s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(8) {
    -webkit-animation: 0.5s ease .8s fadeInPropertyItem;
    animation: 0.5s ease .8s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(9) {
    -webkit-animation: 0.5s ease .9s fadeInPropertyItem;
    animation: 0.5s ease .9s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(10) {
    -webkit-animation: 0.5s ease 1s fadeInPropertyItem;
    animation: 0.5s ease 1s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(11) {
    -webkit-animation: 0.5s ease 1.1s fadeInPropertyItem;
    animation: 0.5s ease 1.1s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(12) {
    -webkit-animation: 0.5s ease 1.2s fadeInPropertyItem;
    animation: 0.5s ease 1.2s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(13) {
    -webkit-animation: 0.5s ease 1.3s fadeInPropertyItem;
    animation: 0.5s ease 1.3s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.property-items .property-item:nth-child(14) {
    -webkit-animation: 0.5s ease 1.4s fadeInPropertyItem;
    animation: 0.5s ease 1.4s fadeInPropertyItem;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showPropertyTopBar {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0px, -30px, 0px) rotate(0deg);
        transform: translate3d(0px, -30px, 0px) rotate(0deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px) rotate(0deg);
        transform: translate3d(0px, 0px, 0px) rotate(0deg)
    }
}

@keyframes showPropertyTopBar {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0px, -30px, 0px) rotate(0deg);
        transform: translate3d(0px, -30px, 0px) rotate(0deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px) rotate(0deg);
        transform: translate3d(0px, 0px, 0px) rotate(0deg)
    }
}

.top-bar {
    -webkit-animation: 0.7s ease 0s showPropertyTopBar;
    animation: 0.7s ease 0s showPropertyTopBar;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

@-webkit-keyframes showPropertyFilter {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-50px, 0px, 0px) rotate(0deg);
        transform: translate3d(-50px, 0px, 0px) rotate(0deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px) rotate(0deg);
        transform: translate3d(0px, 0px, 0px) rotate(0deg)
    }
}

@keyframes showPropertyFilter {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-50px, 0px, 0px) rotate(0deg);
        transform: translate3d(-50px, 0px, 0px) rotate(0deg)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0px, 0px, 0px) rotate(0deg);
        transform: translate3d(0px, 0px, 0px) rotate(0deg)
    }
}

html {
    height: 100%
}

body {
    background-color: #f8faff
}

body.white {
    background-color: #fff
}

body.white .footer-w {
    background-image: none
}

body.white .footer-w .fade3 {
    display: none
}

b,
strong {
    font-weight: 500
}

.wrapper-front {
    position: relative;
    overflow: hidden
}

.os-container {
    max-width: 1600px;
    margin: 0px auto;
    padding-left: 40px;
    padding-right: 40px
}

.section-header-w {
    position: relative
}

.section-header {
    max-width: 700px;
    padding: 100px 0px 70px 0px
}

.section-header .section-sub-title {
    text-transform: uppercase;
    letter-spacing: 2px
}

.section-header .section-title {
    color: #047bf8;
    font-size: 2.99rem;
    margin: 30px 0px;
    margin-top: 10px;
    position: relative
}

.section-header .section-title:before,
.section-header .section-title:after {
    content: "";
    width: 15px;
    height: 15px;
    top: -29px;
    border-radius: 10px;
    position: absolute
}

.section-header .section-title:before {
    left: -52px;
    background-color: #98c9fd
}

.section-header .section-title:after {
    left: -40px;
    background-color: #047bf8
}

.section-header .section-desc {
    color: #868686;
    font-weight: 300;
    font-size: 1.2rem
}

.section-header.dark .section-sub-title {
    color: #f8c52a
}

.section-header.dark .section-title {
    color: #fff
}

.section-header.dark .section-title:before {
    background-color: #fdedbe
}

.section-header.dark .section-title:after {
    background-color: #f8c52a
}

.section-header.dark .section-desc {
    color: rgba(255, 255, 255, 0.6)
}

.section-header-w.centered {
    text-align: center
}

.section-header-w.centered .section-header {
    margin: 0px auto
}

.section-header-w.centered .section-header .section-title:before,
.section-header-w.centered .section-header .section-title:after {
    width: 26px;
    height: 26px;
    border-radius: 13px;
    top: -60px;
    left: 50%
}

.section-header-w.centered .section-header .section-title:before {
    -webkit-transform: translateX(-90%);
    transform: translateX(-90%);
    background-color: #98c9fd
}

.section-header-w.centered .section-header .section-title:after {
    -webkit-transform: translateX(-10%);
    transform: translateX(-10%);
    background-color: #047bf8
}

.fade1 {
    position: absolute;
    top: -100px;
    right: -300px;
    width: 700px;
    height: 600px;
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(227, 231, 248, 0)), to(#8BBAF5));
    background-image: linear-gradient(90deg, rgba(227, 231, 248, 0) 0%, #8BBAF5 100%);
    -webkit-transform: translate(-150px, -200px) rotate(-45deg);
    transform: translate(-150px, -200px) rotate(-45deg);
    z-index: -1
}

.fade1:before {
    content: "";
    position: absolute;
    right: 200px;
    top: -200px;
    width: 800px;
    height: 600px;
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(227, 231, 248, 0)), to(#E8F2FC));
    background-image: linear-gradient(90deg, rgba(227, 231, 248, 0) 0%, #E8F2FC 100%);
    z-index: -1
}

.fade2 {
    position: absolute;
    bottom: 0px;
    left: 0px;
    width: 800px;
    height: 600px;
    background-image: -webkit-gradient(linear, right top, left top, from(rgba(237, 240, 243, 0)), to(#8BBAF5));
    background-image: linear-gradient(-90deg, rgba(237, 240, 243, 0) 0%, #8BBAF5 100%);
    z-index: -1;
    -webkit-transform: translate(-360px, 0px) rotate(-45deg);
    transform: translate(-360px, 0px) rotate(-45deg)
}

.fade2:before {
    content: "";
    position: absolute;
    right: 200px;
    top: -200px;
    width: 800px;
    height: 600px;
    background-image: -webkit-gradient(linear, right top, left top, from(rgba(227, 231, 248, 0)), to(#E8F2FC));
    background-image: linear-gradient(-90deg, rgba(227, 231, 248, 0) 0%, #E8F2FC 100%);
    z-index: -1
}

.fade3 {
    position: absolute;
    right: -800px;
    top: 0px;
    width: 800px;
    height: 600px;
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(227, 231, 248, 0)), to(#8BBAF5));
    background-image: linear-gradient(90deg, rgba(227, 231, 248, 0) 0%, #8BBAF5 100%);
    -webkit-transform: translate(-360px, 0px) rotate(-45deg);
    transform: translate(-360px, 0px) rotate(-45deg);
    z-index: -1
}

.fade3:before {
    content: "";
    position: absolute;
    right: -40px;
    top: -200px;
    width: 1000px;
    height: 600px;
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(227, 231, 248, 0)), to(#E8F2FC));
    background-image: linear-gradient(90deg, rgba(227, 231, 248, 0) 0%, #E8F2FC 100%);
    z-index: -1
}

.fade4 {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 800px;
    height: 600px;
    background-image: -webkit-gradient(linear, right top, left top, from(rgba(237, 240, 243, 0)), to(rgba(54, 51, 175, 0.57)));
    background-image: linear-gradient(-90deg, rgba(237, 240, 243, 0) 0%, rgba(54, 51, 175, 0.57) 100%);
    z-index: 1;
    -webkit-transform: translate(-360px, 0px) rotate(-45deg);
    transform: translate(-360px, 0px) rotate(-45deg)
}

.fade4:before {
    content: "";
    position: absolute;
    right: 200px;
    top: -200px;
    width: 800px;
    height: 600px;
    background-image: -webkit-gradient(linear, right top, left top, from(rgba(227, 231, 248, 0)), to(rgba(236, 218, 255, 0.43)));
    background-image: linear-gradient(-90deg, rgba(227, 231, 248, 0) 0%, rgba(236, 218, 255, 0.43) 100%);
    z-index: -1
}

.menu-top-f {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.menu-top-f .logo-element {
    content: "";
    width: 26px;
    height: 26px;
    border-radius: 15px;
    position: relative;
    background-color: #98c9fd
}

.menu-top-f .logo-element:after {
    content: "";
    width: 26px;
    height: 26px;
    background-color: #047bf8;
    border-radius: 15px;
    right: -20px;
    position: absolute
}

.menu-top-f .menu-top-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.menu-top-f .logo-w a.logo {
    display: inline-block;
    vertical-align: middle
}

.menu-top-f .logo-w a.logo img {
    width: 50px;
    height: auto;
    display: inline-block
}

.menu-top-f ul.main-menu {
    list-style: none;
    margin: 0px;
    padding: 0px;
    text-transform: uppercase;
    color: #3E4B5B;
    font-weight: 500;
    letter-spacing: 3px;
    font-size: 1rem
}

.menu-top-f ul.main-menu li {
    display: inline-block
}

.menu-top-f ul.main-menu li a {
    display: inline-block;
    padding: 25px 25px;
    color: rgba(0, 0, 0, 0.25);
    position: relative;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-top-f ul.main-menu li a:after {
    content: "";
    background-color: #047bf8;
    position: absolute;
    bottom: 0px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 0px;
    height: 5px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.menu-top-f ul.main-menu li.active a,
.menu-top-f ul.main-menu li:hover a {
    color: #3E4B5B;
    text-decoration: none
}

.menu-top-f ul.main-menu li.active a:after,
.menu-top-f ul.main-menu li:hover a:after {
    width: 100%
}

.menu-top-f ul.small-menu {
    list-style: none;
    margin: 0px;
    padding: 0px;
    text-transform: uppercase;
    font-size: .8rem
}

.menu-top-f ul.small-menu li {
    display: inline-block;
    padding: 0px 10px
}

.menu-top-f ul.small-menu li a {
    color: #3E4B5B;
    display: inline-block;
    vertical-align: middle;
    letter-spacing: 2px
}

.menu-top-f ul.small-menu li a i {
    display: inline-block;
    vertical-align: middle;
    color: #BA8C0A;
    margin-right: 10px
}

.menu-top-f ul.small-menu li a span {
    display: inline-block;
    vertical-align: middle
}

.menu-top-f ul.small-menu li a.highlight {
    background-color: #047bf8;
    color: #fff;
    border-radius: 20px;
    padding: 3px 14px
}

.menu-top-f ul.small-menu li.separate {
    padding-left: 30px;
    margin-left: 15px;
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

.mobile-menu-w,
ul.mobile-menu-holder {
    display: none
}

.mobile-menu-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 20px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.mobile-menu-i .mobile-menu-trigger {
    color: #111;
    display: inline-block
}

.mobile-menu-holder {
    background-color: #fff;
    -webkit-box-shadow: 0px 0px 30px rgba(69, 101, 173, 0.1);
    box-shadow: 0px 0px 30px rgba(69, 101, 173, 0.1);
    position: relative;
    display: none
}

.mobile-menu-holder ul.mobile-menu {
    list-style: none;
    padding: 10px 1rem;
    margin-bottom: 0px
}

.mobile-menu-holder ul.mobile-menu>li {
    display: block;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    text-align: center
}

.mobile-menu-holder ul.mobile-menu>li:last-child {
    border-bottom: none
}

.mobile-menu-holder ul.mobile-menu>li.has-sub-menu>a:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e91c";
    font-size: 7px;
    color: rgba(0, 0, 0, 0.5);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    position: absolute;
    top: 50%;
    right: 10px
}

.mobile-menu-holder ul.mobile-menu>li.has-sub-menu.active .sub-menu {
    display: block
}

.mobile-menu-holder ul.mobile-menu>li>a {
    color: #3E4B5B;
    display: block;
    position: relative;
    padding: 15px
}

.mobile-menu-holder ul.mobile-menu>li>a:focus {
    text-decoration: none
}

.mobile-menu-holder ul.mobile-menu>li>a:hover {
    text-decoration: none
}

.mobile-menu-holder ul.mobile-menu>li .icon-w {
    color: #0073ff;
    font-size: 27px;
    display: block;
    padding: 1rem;
    width: 80px;
    text-align: center;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.mobile-menu-holder ul.mobile-menu>li span {
    padding: 1rem;
    display: block;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.mobile-menu-holder ul.mobile-menu>li .icon-w+span {
    padding-left: 0px
}

.mobile-menu-holder ul.sub-menu {
    padding: 1rem 0px;
    padding-left: 55px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: none
}

.mobile-menu-holder ul.sub-menu li {
    padding: 0.4rem 10px 0.4rem 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05)
}

.mobile-menu-holder ul.sub-menu li:last-child {
    border-bottom: none
}

.mobile-menu-holder ul.sub-menu li a {
    font-size: .9rem
}

.mobile-menu-holder.color-scheme-dark {
    background-image: -webkit-gradient(linear, left top, left bottom, from(#3D4D75), to(#31395B));
    background-image: linear-gradient(to bottom, #3D4D75 0%, #31395B 100%);
    background-repeat: repeat-x;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#1c4cc3), to(#1c2e7b));
    background-image: linear-gradient(to bottom, #1c4cc3 0%, #1c2e7b 100%);
    background-repeat: repeat-x;
    color: rgba(255, 255, 255, 0.9)
}

.mobile-menu-holder.color-scheme-dark .side-menu-magic {
    background-image: linear-gradient(-154deg, #6d16a3 8%, #5211e6 90%)
}

.mobile-menu-holder.color-scheme-dark ul.sub-menu li {
    border-bottom-color: rgba(255, 255, 255, 0.05)
}

.mobile-menu-holder.color-scheme-dark ul.sub-menu li a {
    color: #fff
}

.mobile-menu-holder.color-scheme-dark ul.mobile-menu .icon-w {
    color: #9db2ff
}

.mobile-menu-holder.color-scheme-dark ul.mobile-menu>li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05)
}

.mobile-menu-holder.color-scheme-dark ul.mobile-menu>li>a {
    color: #fff
}

.mobile-menu-holder.color-scheme-dark ul.mobile-menu>li>a:before {
    color: #fff
}

.mobile-menu-holder.color-scheme-dark .sub-menu-w {
    -webkit-box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2);
    box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2)
}

.relative {
    position: relative
}

.padded-v {
    padding: 1rem 10px
}

.padded-v-big {
    padding: 2rem 10px
}

.padded {
    padding: 1rem 2rem
}

.b-l {
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

.b-r {
    border-right: 1px solid rgba(0, 0, 0, 0.1)
}

.b-t {
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

.b-b {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.m-t {
    margin-top: 1rem
}

.m-b {
    margin-bottom: 1rem
}
.with-avatar {
	margin-right: 5px;
}
.with-avatar img {
	display: inline-block;
	vertical-align: middle;
	border-radius: 50px;
	width: 30px;
	height: auto;
	margin-right: 10px;
}
.with-avatar span {
	display: inline-block;
	vertical-align: middle;
}
a.with-avatar span {
	border-bottom: 1px solid #047bf8;
}

@media (min-width: 992px) {
    .padded-lg {
        padding: 1rem 2rem
    }
    .b-l-lg {
        border-left: 1px solid rgba(0, 0, 0, 0.1)
    }
    .b-r-lg {
        border-right: 1px solid rgba(0, 0, 0, 0.1)
    }
    .b-t-lg {
        border-top: 1px solid rgba(0, 0, 0, 0.1)
    }
    .b-b-lg {
        border-bottom: 1px solid rgba(0, 0, 0, 0.1)
    }
}


@font-face {
	font-family: 'osfont';
	src: url('data:application/x-font-ttf;charset=utf-8;base64,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')
		format('truetype');
	font-weight: normal;
	font-style: normal;
}
.os-icon {
	font-family: 'osfont' !important;
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.os-icon-activity:before {
	content: '\e985';
}
.os-icon-airplay:before {
	content: '\e988';
}
.os-icon-alert-circle:before {
	content: '\e989';
}
.os-icon-alert-octagon:before {
	content: '\e98a';
}
.os-icon-alert-triangle:before {
	content: '\e98b';
}
.os-icon-align-center:before {
	content: '\e98c';
}
.os-icon-align-justify:before {
	content: '\e98d';
}
.os-icon-align-left:before {
	content: '\e98e';
}
.os-icon-align-right:before {
	content: '\e98f';
}
.os-icon-anchor:before {
	content: '\e990';
}
.os-icon-aperture:before {
	content: '\e991';
}
.os-icon-arrow-down6:before {
	content: '\e992';
}
.os-icon-arrow-down-circle:before {
	content: '\e993';
}
.os-icon-arrow-down-left:before {
	content: '\e994';
}
.os-icon-arrow-down-right:before {
	content: '\e995';
}
.os-icon-arrow-left6:before {
	content: '\e996';
}
.os-icon-arrow-left-circle:before {
	content: '\e997';
}
.os-icon-arrow-right7:before {
	content: '\e998';
}
.os-icon-arrow-right-circle:before {
	content: '\e999';
}
.os-icon-arrow-up6:before {
	content: '\e99a';
}
.os-icon-arrow-up-circle:before {
	content: '\e99b';
}
.os-icon-arrow-up-left:before {
	content: '\e99c';
}
.os-icon-arrow-up-right:before {
	content: '\e99d';
}
.os-icon-at-sign:before {
	content: '\e99e';
}
.os-icon-award:before {
	content: '\e99f';
}
.os-icon-bar-chart:before {
	content: '\e9a0';
}
.os-icon-bar-chart-2:before {
	content: '\e9a1';
}
.os-icon-battery:before {
	content: '\e9a2';
}
.os-icon-battery-charging:before {
	content: '\e9a3';
}
.os-icon-bell:before {
	content: '\e9a4';
}
.os-icon-bell-off:before {
	content: '\e9a5';
}
.os-icon-bluetooth:before {
	content: '\e9a6';
}
.os-icon-bold:before {
	content: '\e9a7';
}
.os-icon-book:before {
	content: '\e9a8';
}
.os-icon-book-open:before {
	content: '\e9a9';
}
.os-icon-bookmark:before {
	content: '\e9aa';
}
.os-icon-box:before {
	content: '\e9ab';
}
.os-icon-briefcase:before {
	content: '\e9ac';
}
.os-icon-calendar:before {
	content: '\e9ad';
}
.os-icon-camera:before {
	content: '\e9ae';
}
.os-icon-camera-off:before {
	content: '\e9af';
}
.os-icon-cast:before {
	content: '\e9b0';
}
.os-icon-check:before {
	content: '\e9b1';
}
.os-icon-check-circle:before {
	content: '\e9b2';
}
.os-icon-check-square:before {
	content: '\e9b3';
}
.os-icon-chevron-down:before {
	content: '\e9b4';
}
.os-icon-chevron-left:before {
	content: '\e9b5';
}
.os-icon-chevron-right:before {
	content: '\e9b6';
}
.os-icon-chevron-up:before {
	content: '\e9b7';
}
.os-icon-chevrons-down:before {
	content: '\e9b8';
}
.os-icon-chevrons-left:before {
	content: '\e9b9';
}
.os-icon-chevrons-right:before {
	content: '\e9ba';
}
.os-icon-chevrons-up:before {
	content: '\e9bb';
}
.os-icon-chrome:before {
	content: '\e9bc';
}
.os-icon-circle:before {
	content: '\e9bd';
}
.os-icon-clipboard:before {
	content: '\e9be';
}
.os-icon-clock:before {
	content: '\e9bf';
}
.os-icon-cloud:before {
	content: '\e9c0';
}
.os-icon-cloud-drizzle:before {
	content: '\e9c1';
}
.os-icon-cloud-lightning:before {
	content: '\e9c2';
}
.os-icon-cloud-off:before {
	content: '\e9c3';
}
.os-icon-cloud-rain:before {
	content: '\e9c4';
}
.os-icon-cloud-snow:before {
	content: '\e9c5';
}
.os-icon-code:before {
	content: '\e9c6';
}
.os-icon-codepen:before {
	content: '\e9c7';
}
.os-icon-command:before {
	content: '\e9c8';
}
.os-icon-compass:before {
	content: '\e9c9';
}
.os-icon-copy:before {
	content: '\e9ca';
}
.os-icon-corner-down-left:before {
	content: '\e9cb';
}
.os-icon-corner-down-right:before {
	content: '\e9cc';
}
.os-icon-corner-left-down:before {
	content: '\e9cd';
}
.os-icon-corner-left-up:before {
	content: '\e9ce';
}
.os-icon-corner-right-down:before {
	content: '\e9cf';
}
.os-icon-corner-right-up:before {
	content: '\e9d0';
}
.os-icon-corner-up-left:before {
	content: '\e9d1';
}
.os-icon-corner-up-right:before {
	content: '\e9d2';
}
.os-icon-cpu:before {
	content: '\e9d3';
}
.os-icon-credit-card:before {
	content: '\e9d4';
}
.os-icon-crop:before {
	content: '\e9d5';
}
.os-icon-crosshair:before {
	content: '\e9d6';
}
.os-icon-database:before {
	content: '\e9d7';
}
.os-icon-delete:before {
	content: '\e9d8';
}
.os-icon-disc:before {
	content: '\e9d9';
}
.os-icon-dollar-sign:before {
	content: '\e9da';
}
.os-icon-download:before {
	content: '\e9db';
}
.os-icon-download-cloud:before {
	content: '\e9dc';
}
.os-icon-droplet:before {
	content: '\e9dd';
}
.os-icon-edit:before {
	content: '\e9de';
}
.os-icon-edit-2:before {
	content: '\e9df';
}
.os-icon-edit-32:before {
	content: '\e9e0';
}
.os-icon-external-link:before {
	content: '\e9e1';
}
.os-icon-eye:before {
	content: '\e9e2';
}
.os-icon-eye-off:before {
	content: '\e9e3';
}
.os-icon-facebook2:before {
	content: '\e9e4';
}
.os-icon-fast-forward:before {
	content: '\e9e5';
}
.os-icon-feather:before {
	content: '\e9e6';
}
.os-icon-file:before {
	content: '\e9e7';
}
.os-icon-file-minus:before {
	content: '\e9e8';
}
.os-icon-file-plus:before {
	content: '\e9e9';
}
.os-icon-file-text:before {
	content: '\e9ea';
}
.os-icon-film:before {
	content: '\e9eb';
}
.os-icon-filter:before {
	content: '\e9ec';
}
.os-icon-flag:before {
	content: '\e9ed';
}
.os-icon-folder:before {
	content: '\e9ee';
}
.os-icon-folder-minus:before {
	content: '\e9ef';
}
.os-icon-folder-plus:before {
	content: '\e9f0';
}
.os-icon-git-branch:before {
	content: '\e9f1';
}
.os-icon-git-commit:before {
	content: '\e9f2';
}
.os-icon-git-merge:before {
	content: '\e9f3';
}
.os-icon-git-pull-request:before {
	content: '\e9f4';
}
.os-icon-github:before {
	content: '\e9f5';
}
.os-icon-gitlab:before {
	content: '\e9f6';
}
.os-icon-globe:before {
	content: '\e9f7';
}
.os-icon-grid:before {
	content: '\e9f8';
}
.os-icon-hard-drive:before {
	content: '\e9f9';
}
.os-icon-hash:before {
	content: '\e9fa';
}
.os-icon-headphones:before {
	content: '\e9fb';
}
.os-icon-heart:before {
	content: '\e9fc';
}
.os-icon-help-circle:before {
	content: '\e9fd';
}
.os-icon-home:before {
	content: '\e9fe';
}
.os-icon-image:before {
	content: '\e9ff';
}
.os-icon-inbox:before {
	content: '\ea00';
}
.os-icon-info:before {
	content: '\ea01';
}
.os-icon-instagram:before {
	content: '\ea02';
}
.os-icon-italic:before {
	content: '\ea03';
}
.os-icon-layers:before {
	content: '\ea04';
}
.os-icon-layout:before {
	content: '\ea05';
}
.os-icon-life-buoy:before {
	content: '\ea06';
}
.os-icon-link:before {
	content: '\ea07';
}
.os-icon-link-2:before {
	content: '\ea08';
}
.os-icon-linkedin:before {
	content: '\ea09';
}
.os-icon-list:before {
	content: '\ea0a';
}
.os-icon-loader:before {
	content: '\ea0b';
}
.os-icon-lock:before {
	content: '\ea0c';
}
.os-icon-log-in:before {
	content: '\ea0d';
}
.os-icon-log-out:before {
	content: '\ea0e';
}
.os-icon-mail:before {
	content: '\ea0f';
}
.os-icon-map:before {
	content: '\ea10';
}
.os-icon-map-pin:before {
	content: '\ea11';
}
.os-icon-maximize:before {
	content: '\ea12';
}
.os-icon-maximize-2:before {
	content: '\ea13';
}
.os-icon-menu:before {
	content: '\ea14';
}
.os-icon-message-circle:before {
	content: '\ea15';
}
.os-icon-message-square:before {
	content: '\ea16';
}
.os-icon-mic:before {
	content: '\ea17';
}
.os-icon-mic-off:before {
	content: '\ea18';
}
.os-icon-minimize:before {
	content: '\ea19';
}
.os-icon-minimize-2:before {
	content: '\ea1a';
}
.os-icon-minus2:before {
	content: '\ea1b';
}
.os-icon-minus-circle:before {
	content: '\ea1c';
}
.os-icon-minus-square:before {
	content: '\ea1d';
}
.os-icon-monitor:before {
	content: '\ea1e';
}
.os-icon-moon:before {
	content: '\ea1f';
}
.os-icon-more-horizontal:before {
	content: '\ea20';
}
.os-icon-more-vertical:before {
	content: '\ea21';
}
.os-icon-move:before {
	content: '\ea22';
}
.os-icon-music:before {
	content: '\ea23';
}
.os-icon-navigation:before {
	content: '\ea24';
}
.os-icon-navigation-2:before {
	content: '\ea25';
}
.os-icon-octagon:before {
	content: '\ea26';
}
.os-icon-package:before {
	content: '\ea27';
}
.os-icon-paperclip:before {
	content: '\ea28';
}
.os-icon-pause:before {
	content: '\ea29';
}
.os-icon-pause-circle:before {
	content: '\ea2a';
}
.os-icon-percent:before {
	content: '\ea2b';
}
.os-icon-phone:before {
	content: '\ea2c';
}
.os-icon-phone-call:before {
	content: '\ea2d';
}
.os-icon-phone-forwarded:before {
	content: '\ea2e';
}
.os-icon-phone-incoming:before {
	content: '\ea2f';
}
.os-icon-phone-missed:before {
	content: '\ea30';
}
.os-icon-phone-off:before {
	content: '\ea31';
}
.os-icon-phone-outgoing:before {
	content: '\ea32';
}
.os-icon-pie-chart:before {
	content: '\ea33';
}
.os-icon-play:before {
	content: '\ea34';
}
.os-icon-play-circle:before {
	content: '\ea35';
}
.os-icon-plus:before {
	content: '\ea36';
}
.os-icon-plus-circle:before {
	content: '\ea37';
}
.os-icon-plus-square:before {
	content: '\ea38';
}
.os-icon-pocket:before {
	content: '\ea39';
}
.os-icon-power:before {
	content: '\ea3a';
}
.os-icon-printer:before {
	content: '\ea3b';
}
.os-icon-radio:before {
	content: '\ea3c';
}
.os-icon-refresh-ccw:before {
	content: '\ea3d';
}
.os-icon-refresh-cw:before {
	content: '\ea3e';
}
.os-icon-repeat:before {
	content: '\ea3f';
}
.os-icon-rewind:before {
	content: '\ea40';
}
.os-icon-rotate-ccw:before {
	content: '\ea41';
}
.os-icon-rotate-cw:before {
	content: '\ea42';
}
.os-icon-rss:before {
	content: '\ea43';
}
.os-icon-save:before {
	content: '\ea44';
}
.os-icon-scissors:before {
	content: '\ea45';
}
.os-icon-search2:before {
	content: '\ea46';
}
.os-icon-send:before {
	content: '\ea47';
}
.os-icon-server:before {
	content: '\ea48';
}
.os-icon-settings:before {
	content: '\ea49';
}
.os-icon-share:before {
	content: '\ea4a';
}
.os-icon-share-2:before {
	content: '\ea4b';
}
.os-icon-shield:before {
	content: '\ea4c';
}
.os-icon-shield-off:before {
	content: '\ea4d';
}
.os-icon-shopping-bag:before {
	content: '\ea4e';
}
.os-icon-shopping-cart:before {
	content: '\ea4f';
}
.os-icon-shuffle:before {
	content: '\ea50';
}
.os-icon-sidebar:before {
	content: '\ea51';
}
.os-icon-skip-back:before {
	content: '\ea52';
}
.os-icon-skip-forward:before {
	content: '\ea53';
}
.os-icon-slack:before {
	content: '\ea54';
}
.os-icon-slash:before {
	content: '\ea55';
}
.os-icon-sliders:before {
	content: '\ea56';
}
.os-icon-smartphone:before {
	content: '\ea57';
}
.os-icon-speaker:before {
	content: '\ea58';
}
.os-icon-square:before {
	content: '\ea59';
}
.os-icon-star:before {
	content: '\ea5a';
}
.os-icon-stop-circle:before {
	content: '\ea5b';
}
.os-icon-sun:before {
	content: '\ea5c';
}
.os-icon-sunrise:before {
	content: '\ea5d';
}
.os-icon-sunset:before {
	content: '\ea5e';
}
.os-icon-tablet:before {
	content: '\ea5f';
}
.os-icon-tag:before {
	content: '\ea60';
}
.os-icon-target:before {
	content: '\ea61';
}
.os-icon-terminal:before {
	content: '\ea62';
}
.os-icon-thermometer:before {
	content: '\ea63';
}
.os-icon-thumbs-down:before {
	content: '\ea64';
}
.os-icon-thumbs-up:before {
	content: '\ea65';
}
.os-icon-toggle-left:before {
	content: '\ea66';
}
.os-icon-toggle-right:before {
	content: '\ea67';
}
.os-icon-trash:before {
	content: '\ea68';
}
.os-icon-trash-2:before {
	content: '\ea69';
}
.os-icon-trending-down:before {
	content: '\ea6a';
}
.os-icon-trending-up:before {
	content: '\ea6b';
}
.os-icon-triangle:before {
	content: '\ea6c';
}
.os-icon-truck:before {
	content: '\ea6d';
}
.os-icon-tv:before {
	content: '\ea6e';
}
.os-icon-twitter2:before {
	content: '\ea6f';
}
.os-icon-type:before {
	content: '\ea70';
}
.os-icon-umbrella:before {
	content: '\ea71';
}
.os-icon-underline:before {
	content: '\ea72';
}
.os-icon-unlock:before {
	content: '\ea73';
}
.os-icon-upload:before {
	content: '\ea74';
}
.os-icon-upload-cloud:before {
	content: '\ea75';
}
.os-icon-user:before {
	content: '\ea76';
}
.os-icon-user-check:before {
	content: '\ea77';
}
.os-icon-user-minus:before {
	content: '\ea78';
}
.os-icon-user-plus:before {
	content: '\ea79';
}
.os-icon-user-x:before {
	content: '\ea7a';
}
.os-icon-users:before {
	content: '\ea7b';
}
.os-icon-video:before {
	content: '\ea7c';
}
.os-icon-video-off:before {
	content: '\ea7d';
}
.os-icon-voicemail:before {
	content: '\ea7e';
}
.os-icon-volume:before {
	content: '\ea7f';
}
.os-icon-volume-1:before {
	content: '\ea80';
}
.os-icon-volume-2:before {
	content: '\ea81';
}
.os-icon-volume-x:before {
	content: '\ea82';
}
.os-icon-watch:before {
	content: '\ea83';
}
.os-icon-wifi:before {
	content: '\ea84';
}
.os-icon-wifi-off:before {
	content: '\ea85';
}
.os-icon-wind:before {
	content: '\ea86';
}
.os-icon-x:before {
	content: '\ea87';
}
.os-icon-x-circle:before {
	content: '\ea88';
}
.os-icon-x-square:before {
	content: '\ea89';
}
.os-icon-zap:before {
	content: '\ea8a';
}
.os-icon-zap-off:before {
	content: '\ea8b';
}
.os-icon-zoom-in:before {
	content: '\ea8c';
}
.os-icon-zoom-out:before {
	content: '\ea8d';
}
.os-icon-star-full:before {
	content: '\e970';
}
.os-icon-arrow-right6:before {
	content: '\e986';
}
.os-icon-arrow-left7:before {
	content: '\e987';
}
.os-icon-arrow-2-right:before {
	content: '\e971';
}
.os-icon-minus:before {
	content: '\e96f';
}
.os-icon-arrow-right:before {
	content: '\e90e';
}
.os-icon-arrow-right2:before {
	content: '\e90f';
}
.os-icon-arrow-right3:before {
	content: '\e910';
}
.os-icon-arrow-right4:before {
	content: '\e911';
}
.os-icon-arrow-right5:before {
	content: '\e912';
}
.os-icon-arrow-left:before {
	content: '\e913';
}
.os-icon-arrow-left2:before {
	content: '\e914';
}
.os-icon-arrow-left3:before {
	content: '\e915';
}
.os-icon-arrow-left4:before {
	content: '\e916';
}
.os-icon-arrow-up:before {
	content: '\e917';
}
.os-icon-arrow-down:before {
	content: '\e918';
}
.os-icon-arrow-left5:before {
	content: '\e919';
}
.os-icon-arrow-down2:before {
	content: '\e91a';
}
.os-icon-arrow-down3:before {
	content: '\e91b';
}
.os-icon-arrow-down4:before {
	content: '\e91c';
}
.os-icon-arrow-up2:before {
	content: '\e91d';
}
.os-icon-arrow-up3:before {
	content: '\e91e';
}
.os-icon-arrow-down5:before {
	content: '\e91f';
}
.os-icon-arrow-up4:before {
	content: '\e920';
}
.os-icon-arrow-up5:before {
	content: '\e921';
}
.os-icon-search:before {
	content: '\e92c';
}
.os-icon-ui-34:before {
	content: '\e984';
}
.os-icon-ui-21:before {
	content: '\e983';
}
.os-icon-documents-15:before {
	content: '\e97f';
}
.os-icon-documents-17:before {
	content: '\e980';
}
.os-icon-documents-11:before {
	content: '\e981';
}
.os-icon-documents-13:before {
	content: '\e982';
}
.os-icon-ui-23:before {
	content: '\e97e';
}
.os-icon-home-11:before {
	content: '\e97a';
}
.os-icon-ui-09:before {
	content: '\e97b';
}
.os-icon-old-tv-2:before {
	content: '\e97c';
}
.os-icon-fire:before {
	content: '\e97d';
}
.os-icon-home-10:before {
	content: '\e976';
}
.os-icon-home-09:before {
	content: '\e977';
}
.os-icon-home-13:before {
	content: '\e978';
}
.os-icon-home-34:before {
	content: '\e979';
}
.os-icon-ui-90:before {
	content: '\e975';
}
.os-icon-ui-03:before {
	content: '\e974';
}
.os-icon-ui-83:before {
	content: '\e972';
}
.os-icon-ui-74:before {
	content: '\e973';
}
.os-icon-pencil-12:before {
	content: '\e96e';
}
.os-icon-ui-33:before {
	content: '\e96c';
}
.os-icon-ui-49:before {
	content: '\e96d';
}
.os-icon-grid-10:before {
	content: '\e96b';
}
.os-icon-common-03:before {
	content: '\e968';
}
.os-icon-ui-22:before {
	content: '\e969';
}
.os-icon-ui-46:before {
	content: '\e96a';
}
.os-icon-basic-1-138-quotes:before {
	content: '\e966';
	color: #474a56;
}
.os-icon-ui-07:before {
	content: '\e962';
}
.os-icon-social-09:before {
	content: '\e963';
}
.os-icon-finance-28:before {
	content: '\e964';
}
.os-icon-finance-29:before {
	content: '\e965';
}
.os-icon-checkmark:before {
	content: '\e961';
}
.os-icon-ui-93:before {
	content: '\e95d';
}
.os-icon-mail-14:before {
	content: '\e95e';
}
.os-icon-phone-15:before {
	content: '\e95f';
}
.os-icon-phone-18:before {
	content: '\e960';
}
.os-icon-ui-55:before {
	content: '\e95c';
}
.os-icon-mail-19:before {
	content: '\e95a';
}
.os-icon-mail-18:before {
	content: '\e95b';
}
.os-icon-grid-18:before {
	content: '\e950';
}
.os-icon-ui-02:before {
	content: '\e951';
}
.os-icon-ui-37:before {
	content: '\e952';
}
.os-icon-common-07:before {
	content: '\e953';
}
.os-icon-ui-54:before {
	content: '\e954';
}
.os-icon-ui-44:before {
	content: '\e955';
}
.os-icon-ui-15:before {
	content: '\e956';
}
.os-icon-documents-03:before {
	content: '\e957';
}
.os-icon-ui-92:before {
	content: '\e958';
}
.os-icon-phone-21:before {
	content: '\e959';
}
.os-icon-documents-07:before {
	content: '\e94c';
}
.os-icon-others-29:before {
	content: '\e94d';
}
.os-icon-ui-65:before {
	content: '\e94e';
}
.os-icon-ui-51:before {
	content: '\e94f';
}
.os-icon-mail-07:before {
	content: '\e94b';
}
.os-icon-mail-01:before {
	content: '\e949';
}
.os-icon-others-43:before {
	content: '\e94a';
}
.os-icon-mail-12:before {
	content: '\e967';
}
.os-icon-signs-11:before {
	content: '\e946';
}
.os-icon-coins-4:before {
	content: '\e947';
}
.os-icon-user-male-circle2:before {
	content: '\e948';
}
.os-icon-emoticon-smile:before {
	content: '\e943';
}
.os-icon-robot-2:before {
	content: '\e944';
}
.os-icon-robot-1:before {
	content: '\e945';
}
.os-icon-crown:before {
	content: '\e942';
}
.os-icon-cancel-circle:before {
	content: '\e93f';
}
.os-icon-cancel-square:before {
	content: '\e940';
}
.os-icon-close:before {
	content: '\e941';
}
.os-icon-grid-circles:before {
	content: '\e93c';
}
.os-icon-grid-squares-22:before {
	content: '\e93d';
}
.os-icon-grid-squares2:before {
	content: '\e93e';
}
.os-icon-tasks-checked:before {
	content: '\e93a';
}
.os-icon-hierarchy-structure-2:before {
	content: '\e93b';
}
.os-icon-agenda-1:before {
	content: '\e935';
}
.os-icon-cv-2:before {
	content: '\e936';
}
.os-icon-grid-squares-2:before {
	content: '\e937';
}
.os-icon-grid-squares:before {
	content: '\e938';
}
.os-icon-calendar-time:before {
	content: '\e939';
}
.os-icon-twitter:before {
	content: '\e933';
}
.os-icon-facebook:before {
	content: '\e934';
}
.os-icon-pie-chart-2:before {
	content: '\e92d';
}
.os-icon-pie-chart-1:before {
	content: '\e92e';
}
.os-icon-pie-chart-3:before {
	content: '\e92f';
}
.os-icon-donut-chart-1:before {
	content: '\e930';
}
.os-icon-bar-chart-up:before {
	content: '\e931';
}
.os-icon-bar-chart-stats-up:before {
	content: '\e932';
}
.os-icon-hamburger-menu-2:before {
	content: '\e92a';
}
.os-icon-hamburger-menu-1:before {
	content: '\e92b';
}
.os-icon-email-2-at:before {
	content: '\e928';
}
.os-icon-email-2-at2:before {
	content: '\e929';
}
.os-icon-fingerprint:before {
	content: '\e927';
}
.os-icon-basic-2-259-calendar:before {
	content: '\e926';
	color: #474a56;
}
.os-icon-arrow-2-up:before {
	content: '\e924';
}
.os-icon-arrow-2-down:before {
	content: '\e925';
}
.os-icon-bar-chart-down:before {
	content: '\e922';
}
.os-icon-graph-down:before {
	content: '\e923';
}
.os-icon-pencil-1:before {
	content: '\e90b';
}
.os-icon-edit-3:before {
	content: '\e90c';
}
.os-icon-edit-1:before {
	content: '\e90d';
}
.os-icon-database-remove:before {
	content: '\e908';
}
.os-icon-pencil-2:before {
	content: '\e909';
}
.os-icon-link-3:before {
	content: '\e90a';
}
.os-icon-email-forward:before {
	content: '\e907';
}
.os-icon-delivery-box-2:before {
	content: '\e900';
}
.os-icon-wallet-loaded:before {
	content: '\e901';
}
.os-icon-newspaper:before {
	content: '\e902';
}
.os-icon-window-content:before {
	content: '\e903';
}
.os-icon-donut-chart-2:before {
	content: '\e904';
}
.os-icon-text-input:before {
	content: '\e905';
}
.os-icon-user-male-circle:before {
	content: '\e906';
}

.intro-w {
    position: relative
}

.intro-w.layout-v1 {
    padding: 1% 0px
}

.intro-w.layout-v1 .intro-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 75vh
}

.intro-w.layout-v1 .intro-description {
    -webkit-box-flex: 0;
    -ms-flex: 0 1 600px;
    flex: 0 1 600px
}

.intro-w.layout-v1 .intro-description .intro-heading {
    font-size: 3.68rem;
    margin-bottom: 1.5rem;
    position: relative
}

.intro-w.layout-v1 .intro-description .intro-heading:before,
.intro-w.layout-v1 .intro-description .intro-heading:after {
    content: "";
    width: 20px;
    height: 20px;
    top: -55px;
    border-radius: 15px;
    position: absolute
}

.intro-w.layout-v1 .intro-description .intro-heading:before {
    left: 5;
    background-color: #b1d6fe
}

.intro-w.layout-v1 .intro-description .intro-heading:after {
    left: 20px;
    background-color: #047bf8
}

.intro-w.layout-v1 .intro-description .intro-heading span {
    color: #047bf8
}

.intro-w.layout-v1 .intro-description .intro-text {
    font-size: 1.3rem;
    /* color: #868686; */
    font-weight: 300
}

.intro-w.layout-v1 .intro-description .intro-buttons {
    padding-top: 30px
}

.intro-w.layout-v1 .intro-description .intro-buttons .btn,
.intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .fc-button,
.wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .fc-button {
    border-radius: 40px;
    font-size: 1.1rem;
    padding: 15px 30px
}

.intro-w.layout-v1 .intro-description .intro-buttons .btn i,
.intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .fc-button i,
.wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .fc-button i {
    font-size: 30px
}

.intro-w.layout-v1 .intro-description .intro-buttons .btn span.highlight,
.intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .fc-button span.highlight,
.wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .fc-button span.highlight {
    display: inline-block;
    vertical-align: middle;
    margin-left: 15px;
    padding-left: 15px;
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

.intro-w.layout-v1 .intro-description .intro-buttons .btn.btn-primary,
.intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .btn-primary.fc-button,
.wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .btn-primary.fc-button,
.intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .fc-button.fc-state-active,
.wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .fc-button.fc-state-active {
    border: transparent;
    margin-right: 50px
}

.intro-w.layout-v1 .intro-description .intro-buttons .btn.btn-link,
.intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .btn-link.fc-button,
.wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .btn-link.fc-button {
    border: transparent;
    border-bottom: 2px solid #047bf8;
    border-radius: 0px;
    padding: 5px 5px
}

.intro-w.layout-v1 .intro-media {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    padding-left: 120px;
    position: relative;
    -webkit-transform: perspective(1260px) rotateY(-20.2deg) rotateX(10.6deg) scale(1);
    transform: perspective(1260px) rotateY(-20.2deg) rotateX(10.6deg) scale(1)
}

.intro-w.layout-v1 .intro-media .shot {
    position: absolute
}

.intro-w.layout-v1 .intro-media .shot .shot-i {
    -webkit-box-shadow: 0 2px 50px 0 rgba(68, 125, 232, 0.5);
    box-shadow: 0 2px 50px 0 rgba(68, 125, 232, 0.5);
    background-size: cover
}

.intro-w.layout-v1 .intro-media .shot1 {
    z-index: 2;
    width: 95%
}

.intro-w.layout-v1 .intro-media .shot1 .shot-i {
    padding-bottom: 57%;
    -webkit-transform: translate(0, -70%);
    transform: translate(0, -70%)
}

.intro-w.layout-v1 .intro-media .shot2 {
    z-index: 1;
    width: 37%
}

.intro-w.layout-v1 .intro-media .shot2 .shot-i {
    padding-bottom: 105%;
    -webkit-transform: translate(-45%, -10%);
    transform: translate(-45%, -10%)
}

.intro-w.layout-v1 .intro-media .shot3 {
    z-index: 3;
    width: 75%
}

.intro-w.layout-v1 .intro-media .shot3 .shot-i {
    padding-bottom: 81%;
    -webkit-transform: translate(30%, -36%);
    transform: translate(30%, -36%)
}

.intro-w.layout-v1 .intro-media .shot4 {
    z-index: 4
}

.intro-w.layout-v1 .intro-media .shot4 .shot-i {
    padding-bottom: 81%;
    width: 500px;
    -webkit-transform: translate(10%, -16%);
    transform: translate(10%, -16%)
}

.counters-w {
    background-image: -webkit-gradient(linear, left top, right top, from(#047bf8), to(#0d1e73));
    background-image: linear-gradient(to right, #047bf8, #0d1e73);
    position: relative
}

.counters-w .decor {
    position: absolute;
    bottom: 100%;
    right: 0px;
    max-width: 100%;
    z-index: 3
}

.counters-w .decor .decor-path {
    fill: #0b3898
}

.counters-w .decor2 {
    position: absolute;
    top: 100%;
    left: 0px;
    max-width: 100%;
    z-index: 3;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.counters-w .decor2 .decor-path {
    fill: #0667dc
}

.counters-w .counters-i {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: distribute;
    justify-content: space-around;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 40px;
    color: #fff
}

.counters-w .counter {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.counters-w .counter .counter-value-w {
    text-align: center;
    margin-right: 25px;
    padding-right: 25px;
    border-right: 1px solid rgba(255, 255, 255, 0.1)
}

.counters-w .counter .counter-value {
    font-size: 5.06rem;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    line-height: 1
}

.counters-w .counter .counter-name {
    text-transform: uppercase;
    letter-spacing: 2px;
    font-weight: 500;
    padding-top: 0px
}

.counters-w .counter .counter-description {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.6);
    width: 200px
}

.features-table {
    border-left: 1px solid rgba(0, 0, 0, 0.1)
}

.features-table .feature-cell {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 60px;
    background-color: #fff;
    -webkit-box-shadow: 0px 5px 40px rgba(41, 111, 226, 0.3);
    box-shadow: 0px 5px 40px rgba(41, 111, 226, 0.3)
}

.features-table .feature-cell .feature-icon {
    color: #047bf8;
    font-size: 40px;
    margin-bottom: 40px
}

.features-table .feature-cell .feature-title {
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 20px
}

.features-table .feature-cell .feature-text {
    color: #868686;
    font-weight: 300
}

.testimonials-w {
    background-color: #0a1a3d;
    padding-bottom: 100px;
    padding-top: 100px;
    margin-top: 100px
}

.testimonials-w .testimonials-i {
    position: relative;
    z-index: 2
}

.testimonials-slider-w .slick-arrow {
    position: absolute;
    right: 40px;
    top: -50px;
    color: rgba(255, 255, 255, 0.3);
    background-color: transparent;
    border: none;
    font-size: 0px
}

.testimonials-slider-w .slick-arrow:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    display: block;
    font-size: 16px
}

.testimonials-slider-w .slick-arrow:hover {
    color: #fff
}

.testimonials-slider-w .slick-next {
    -webkit-transform: translateX(15px);
    transform: translateX(15px)
}

.testimonials-slider-w .slick-next:before {
    content: "\e910"
}

.testimonials-slider-w .slick-prev {
    -webkit-transform: translateX(-35px);
    transform: translateX(-35px)
}

.testimonials-slider-w .slick-prev:before {
    content: "\e919"
}

.testimonials-slider-w .slide-w {
    display: inline-block;
    width: 540px;
    padding: 20px;
    height: auto
}

.testimonials-slider-w .slide {
    background-color: #fff;
    border-radius: 4px;
    padding: 50px 60px;
    padding-bottom: 30px;
    -webkit-box-shadow: 0px 4px 30px rgba(0, 0, 0, 0.8);
    box-shadow: 0px 4px 30px rgba(0, 0, 0, 0.8);
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    position: relative
}

.testimonials-slider-w .slide:before {
    content: "\e966";
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 120px;
    color: rgba(4, 123, 248, 0.1);
    position: absolute;
    top: 10px;
    left: 20px
}

.testimonials-slider-w .testimonial-title {
    text-transform: uppercase;
    letter-spacing: 2px;
    color: #047bf8;
    margin-bottom: 10px;
    font-weight: 500
}

.testimonials-slider-w .testimonial-content {
    color: #868686;
    font-weight: 300
}

.testimonials-slider-w .testimonial-by {
    text-align: right;
    color: #868686;
    font-weight: 300;
    font-size: .9rem;
    margin-top: 20px
}

.testimonials-slider-w .testimonial-by strong {
    color: #3E4B5B;
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px
}

.testimonials-slider-w .testimonial-by span {
    display: inline-block;
    vertical-align: middle
}

.testimonials-slider-w .testimonial-by .avatar {
    display: inline-block;
    vertical-align: middle;
    margin-left: 20px
}

.testimonials-slider-w .testimonial-by .avatar img {
    width: 50px;
    height: auto;
    border-radius: 30px
}

.projects-slider-w {
    position: relative;
    margin-bottom: 60px
}

.projects-slider-w .slick-arrow {
    position: absolute;
    right: 40px;
    top: -50px;
    color: rgba(4, 123, 248, 0.3);
    background-color: transparent;
    border: none;
    font-size: 0px
}

.projects-slider-w .slick-arrow:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    display: block;
    font-size: 16px
}

.projects-slider-w .slick-arrow:hover {
    color: #fff
}

.projects-slider-w .slick-next {
    -webkit-transform: translateX(15px);
    transform: translateX(15px)
}

.projects-slider-w .slick-next:before {
    content: "\e910"
}

.projects-slider-w .slick-prev {
    -webkit-transform: translateX(-35px);
    transform: translateX(-35px)
}

.projects-slider-w .slick-prev:before {
    content: "\e919"
}

.project-slide-w {
    padding: 40px 20px;
    width: 450px;
    height: auto
}

.project-slide {
    background-color: #fff;
    -webkit-box-shadow: 0 2px 50px 0 rgba(4, 123, 248, 0.13);
    box-shadow: 0 2px 50px 0 rgba(4, 123, 248, 0.13);
    border-radius: 10px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
    will-change: transform
}

.project-slide .project-media-w {
    overflow: hidden;
    position: relative;
    padding: 10px
}

.project-slide .decor {
    position: absolute;
    bottom: -1px;
    right: 0px;
    max-width: 100%;
    z-index: 3;
    -webkit-filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.2));
    filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.2));
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease
}

.project-slide .decor .decor-path {
    fill: #fff
}

.project-slide .project-media {
    position: relative;
    overflow: hidden;
    border-radius: 8px 8px 0px 8px
}

.project-slide .project-media .project-media-i {
    border-radius: 8px 8px 0px 8px;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
    padding-bottom: 65%;
    background-size: cover;
    background-position: center center;
    will-change: transform
}

.project-slide .project-content {
    padding: 40px
}

.project-slide .project-title {
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: .8rem;
    font-weight: 500;
    margin-bottom: 15px
}

.project-slide .project-text {
    color: #868686;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-weight: 300;
    font-size: .9rem
}

.project-slide .project-icons-buton {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.project-slide .project-icons-buton .icons-w a {
    display: inline-block;
    vertical-align: middle;
    font-size: 22px;
    color: #047bf8;
    margin-right: 20px
}

.project-slide .project-icons-buton .btn-w {
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    padding-left: 20px;
    margin-left: 20px
}

.project-slide:hover {
    -webkit-box-shadow: 0 2px 40px 0 rgba(4, 123, 248, 0.3);
    box-shadow: 0 2px 40px 0 rgba(4, 123, 248, 0.3);
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0)
}

.project-slide:hover .project-media-i {
    -webkit-transform: scale(1.1);
    transform: scale(1.1)
}

.top-bar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch
}

.top-bar .logo-w {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 400px;
    flex: 0 0 400px;
    background-color: #4472fd;
    padding: 10px 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.top-bar .logo-w .filters-toggler {
    color: rgba(255, 255, 255, 0.7);
    font-size: 20px;
    line-height: 1;
    cursor: pointer
}

.top-bar .logo-w .filters-toggler .os-icon {
    display: inline-block;
    vertical-align: middle;
    line-height: 1
}

.top-bar .logo-w .filters-toggler:hover {
    color: #fff
}

.top-bar .logo-w .logo {
    display: inline-block;
    text-decoration: none
}

.top-bar .logo-w .logo-element {
    content: "";
    width: 26px;
    height: 26px;
    border-radius: 15px;
    position: relative;
    background-color: #98c9fd;
    display: inline-block;
    vertical-align: middle;
    margin-right: 40px;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.top-bar .logo-w .logo-element:after {
    content: "";
    width: 26px;
    height: 26px;
    background-color: #fff;
    border-radius: 15px;
    right: -20px;
    position: absolute;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.top-bar .logo-w .logo:hover .logo-element {
    -webkit-transform: translateX(5px);
    transform: translateX(5px)
}

.top-bar .logo-w .logo:hover .logo-element:after {
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px)
}

.top-bar .logo-w .logo:hover .logo-label:after {
    width: 100%;
    background-color: #fff
}

.top-bar .logo-w .logo-label {
    display: inline-block;
    vertical-align: middle;
    color: #fff;
    letter-spacing: 2px;
    text-transform: uppercase;
    font-weight: 500;
    font-size: 1.2rem;
    position: relative
}

.top-bar .logo-w .logo-label:after {
    height: 2px;
    position: absolute;
    width: 0%;
    left: 0px;
    bottom: -5px;
    background-color: #fff;
    content: "";
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease
}

.top-bar .filters {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    background-color: #24293d;
    padding: 0px 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.top-bar .filters .filters-header {
    padding-right: 20px
}

.top-bar .filters .filters-header h4 {
    color: #fff;
    letter-spacing: 2px;
    text-transform: uppercase;
    font-weight: 500;
    font-size: 1rem;
    margin: 0px
}

.top-bar .filters .filter-w {
    padding: 15px 20px;
    border-left: 1px solid rgba(255, 255, 255, 0.1)
}

.top-bar .filters .filter-w label {
    font-size: .8rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 0.4);
    display: inline-block;
    margin-right: 10px
}

.top-bar .filters .filter-w input.form-control {
    background-color: #040407;
    border-color: #040407;
    color: #fff;
    font-weight: 500;
    letter-spacing: 1px
}

.top-bar .filters .filter-w input.form-control.zip-width {
    width: 70px;
    padding-left: 5px
}

.top-bar .filters .filter-w input.form-control.date-range-picker {
    width: 250px;
    padding-left: 5px
}

.top-bar .filters .filter-w .input-group-addon {
    background-color: #040407;
    border-color: #040407;
    color: #4472fd;
    font-size: 20px
}

.top-bar .filters .buttons-w .btn,
.top-bar .filters .buttons-w .wrapper-front .fc-button,
.wrapper-front .top-bar .filters .buttons-w .fc-button {
    font-size: .9rem
}

.top-bar .filters .buttons-w .btn i.os-icon,
.top-bar .filters .buttons-w .wrapper-front .fc-button i.os-icon,
.wrapper-front .top-bar .filters .buttons-w .fc-button i.os-icon {
    margin: 0px
}

.top-bar .filters .buttons-w .btn i.os-icon+span,
.top-bar .filters .buttons-w .wrapper-front .fc-button i.os-icon+span,
.wrapper-front .top-bar .filters .buttons-w .fc-button i.os-icon+span {
    margin: 0px;
    margin-left: 10px
}

.top-bar .filters .buttons-w .btn span+i.os-icon,
.top-bar .filters .buttons-w .wrapper-front .fc-button span+i.os-icon,
.wrapper-front .top-bar .filters .buttons-w .fc-button span+i.os-icon {
    margin: 0px;
    margin-left: 10px
}

.support-index {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
}
.support-index .support-tickets {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 470px;
	flex: 0 0 470px;
	padding-right: 20px;
	margin-right: 20px;
	border-right: 1px solid rgba(0, 0, 0, 0.05);
}
.support-index .load-more-tickets {
	text-align: center;
	margin-top: 8px;
}
.support-index .load-more-tickets a {
	display: inline-block;
	padding: 10px;
	text-decoration: none;
}
.support-index .load-more-tickets a i {
	font-size: 20px;
	display: inline-block;
	vertical-align: middle;
}
.support-index .load-more-tickets a span {
	display: inline-block;
	vertical-align: middle;
	border-bottom: 1px solid #047bf8;
}
.support-index .load-more-tickets a i + span {
	margin-left: 10px;
}
.support-index .support-tickets-header h5 {
	margin-bottom: 0px;
}
.support-index .support-tickets-header .tickets-control {
	border-bottom: 1px solid rgba(0, 0, 0, 0.08);
	padding-bottom: 10px;
	margin-bottom: 10px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}
.support-index .support-tickets-header .tickets-control .element-search {
	position: relative;
	margin-left: auto;
}
.support-index .support-tickets-header .tickets-control .element-search:before {
	font-family: 'osfont' !important;
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	position: absolute;
	left: 10px;
	top: 48%;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	font-size: 16px;
	content: '\e92c';
	color: rgba(0, 0, 0, 0.2);
}
.support-index .support-tickets-header .tickets-control .element-search input {
	border: none;
	-webkit-box-shadow: none;
	box-shadow: none;
	background-color: rgba(0, 0, 0, 0.03);
	border-radius: 30px;
	padding: 4px 10px 4px 40px;
	display: block;
	width: 100%;
	font-size: 0.81rem;
	outline: none;
}
.support-index .support-tickets-header .tickets-control .element-search input::-webkit-input-placeholder {
	color: rgba(0, 0, 0, 0.3);
}
.support-index .support-tickets-header .tickets-control .element-search input:-ms-input-placeholder {
	color: rgba(0, 0, 0, 0.3);
}
.support-index .support-tickets-header .tickets-control .element-search input::-ms-input-placeholder {
	color: rgba(0, 0, 0, 0.3);
}
.support-index .support-tickets-header .tickets-control .element-search input::placeholder {
	color: rgba(0, 0, 0, 0.3);
}
.support-index .support-tickets-header .tickets-filter {
	margin-bottom: 15px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.08);
	padding-bottom: 10px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}
.support-index .support-tickets-header .tickets-filter .form-check {
	margin-bottom: 0px;
}
.support-index .support-tickets-header .tickets-filter .form-check label.form-check-label {
	vertical-align: middle;
	padding-left: 5px;
}
.support-index .support-tickets-header .tickets-filter .form-group {
	white-space: nowrap;
	margin-bottom: 0px;
}
.support-index .support-tickets-header .tickets-filter .form-group label {
	margin-bottom: 0px;
}
.support-index .support-tickets-header .tickets-filter select.form-control-sm,
.support-index .support-tickets-header .tickets-filter .input-group-sm > select.form-control,
.support-index .support-tickets-header .tickets-filter .input-group-sm > .input-group-prepend > select.input-group-text,
.support-index .support-tickets-header .tickets-filter .input-group-sm > .input-group-append > select.input-group-text,
.support-index .support-tickets-header .tickets-filter .input-group-sm > .input-group-prepend > select.btn,
.support-index
	.support-tickets-header
	.tickets-filter
	.all-wrapper
	.input-group-sm
	> .input-group-prepend
	> select.fc-button,
.all-wrapper
	.support-index
	.support-tickets-header
	.tickets-filter
	.input-group-sm
	> .input-group-prepend
	> select.fc-button,
.support-index .support-tickets-header .tickets-filter .input-group-sm > .input-group-append > select.btn,
.support-index
	.support-tickets-header
	.tickets-filter
	.all-wrapper
	.input-group-sm
	> .input-group-append
	> select.fc-button,
.all-wrapper
	.support-index
	.support-tickets-header
	.tickets-filter
	.input-group-sm
	> .input-group-append
	> select.fc-button {
	background-color: #fff;
}
.support-index .support-tickets-header .tickets-filter .stick-right {
	margin-left: auto;
}
.support-index .support-ticket {
	background-color: #fff;
	border-radius: 6px;
	-webkit-box-shadow: 0px 2px 4px rgba(126, 142, 177, 0.12);
	box-shadow: 0px 2px 4px rgba(126, 142, 177, 0.12);
	-webkit-transition: all 0.3s ease;
	transition: all 0.3s ease;
	position: relative;
}
.support-index .support-ticket + .support-ticket {
	margin-top: 20px;
}
.support-index .support-ticket:hover,
.support-index .support-ticket.active {
	-webkit-box-shadow: 0px 5px 12px rgba(126, 142, 177, 0.2), 0px 0px 0px 2px #047bf8;
	box-shadow: 0px 5px 12px rgba(126, 142, 177, 0.2), 0px 0px 0px 2px #047bf8;
	-webkit-transform: translateY(-3px);
	transform: translateY(-3px);
	cursor: pointer;
}
.support-index .support-ticket:hover .ticket-title,
.support-index .support-ticket.active .ticket-title {
	color: #047bf8;
}
.support-index .support-ticket.active {
	-webkit-transform: none;
	transform: none;
}
.support-index .st-meta {
	position: absolute;
	top: 5px;
	right: 5px;
	z-index: 99;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}
.support-index .st-meta > div {
	margin-left: 10px;
}
.support-index .st-meta > i {
	margin-left: 10px;
	color: #eaa81d;
	font-size: 16px;
}
.support-index .st-meta .badge {
	font-size: 0.72rem;
	padding: 2px 5px;
}
.support-index .st-body {
	-webkit-box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.05);
	box-shadow: 0px 5px 15px rgba(0, 0, 0, 0.05);
	padding: 20px;
	border-radius: 6px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}
.support-index .st-body .avatar {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 50px;
	flex: 0 0 50px;
	padding-right: 15px;
}
.support-index .st-body .avatar img {
	width: 50px;
	height: auto;
	border-radius: 50px;
}
.support-index .st-body .ticket-content .ticket-title {
	margin-bottom: 5px;
}
.support-index .st-body .ticket-content .ticket-description {
	color: #636c72;
	font-size: 0.81rem;
	font-weight: 300;
	height: 1.215rem;
	overflow: hidden;
}
.support-index .st-foot {
	padding: 10px 20px;
	font-size: 0.81rem;
}
.support-index .st-foot .label {
	color: #636c72;
	font-weight: 300;
	display: inline-block;
	vertical-align: middle;
}
.support-index .st-foot .value {
	vertical-align: middle;
	display: inline-block;
	color: #047bf8;
}
.support-index .st-foot a.value span {
	border-bottom: 1px solid #047bf8;
}
.support-index .st-foot a.value.with-avatar img {
	width: 25px;
}
.support-index .st-foot .label + .value {
	margin-left: 10px;
}
.support-index .st-foot .value + .label {
	margin-left: 20px;
}
.support-index .support-ticket-content-w {
	-webkit-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
	background-color: #fff;
	padding: 10px;
	-webkit-box-shadow: 0px 2px 4px rgba(126, 142, 177, 0.12);
	box-shadow: 0px 2px 4px rgba(126, 142, 177, 0.12);
	border-radius: 6px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	position: relative;
}
.support-index .support-ticket-content-w .support-ticket-content {
	-webkit-box-flex: 1;
	-ms-flex: 1;
	flex: 1;
	padding: 10px 30px;
	padding-right: 60px;
	position: relative;
}
.support-index .support-ticket-content-w .support-ticket-content .support-ticket-content-header {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	margin-bottom: 30px;
	padding: 10px 0px;
}
.support-index .support-ticket-content-w .support-ticket-content .support-ticket-content-header .show-ticket-info {
	margin-left: auto;
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	padding-left: 20px;
	padding-top: 5px;
}
.support-index .support-ticket-content-w .support-ticket-content .support-ticket-content-header h3,
.support-index .support-ticket-content-w .support-ticket-content .support-ticket-content-header h4 {
	margin-bottom: 0px;
}
.support-index .support-ticket-content-w .support-ticket-content .show-ticket-info,
.support-index .support-ticket-content-w .support-ticket-content .back-to-index {
	display: none;
	text-decoration: none;
	white-space: nowrap;
}
.support-index .support-ticket-content-w .support-ticket-content .show-ticket-info span,
.support-index .support-ticket-content-w .support-ticket-content .back-to-index span {
	display: inline-block;
	vertical-align: middle;
	text-transform: uppercase;
	color: #636c72;
	letter-spacing: 1px;
	font-size: 0.72rem;
	font-weight: 500;
}
.support-index .support-ticket-content-w .support-ticket-content .show-ticket-info i,
.support-index .support-ticket-content-w .support-ticket-content .back-to-index i {
	display: inline-block;
	vertical-align: middle;
	font-size: 19px;
}
.support-index .support-ticket-content-w .support-ticket-content .show-ticket-info span + i,
.support-index .support-ticket-content-w .support-ticket-content .back-to-index span + i {
	margin-left: 7px;
}
.support-index .support-ticket-content-w .support-ticket-content .show-ticket-info:hover span,
.support-index .support-ticket-content-w .support-ticket-content .back-to-index:hover span {
	color: #047bf8;
}
.support-index .support-ticket-content-w .support-ticket-content .back-to-index i {
	font-size: 12px;
}
.support-index .support-ticket-content-w .support-ticket-content .back-to-index span {
	margin-left: 10px;
}
.support-index .support-ticket-content-w .ticket-header {
	margin-bottom: 40px;
}
.support-index .support-ticket-content-w .ticket-reply {
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
	padding-bottom: 20px;
}
.support-index .support-ticket-content-w .ticket-reply:last-child {
	border-bottom: none;
}
.support-index .support-ticket-content-w .ticket-reply + .ticket-reply {
	margin-top: 20px;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info {
	padding: 10px 0px;
	margin-bottom: 5px;
	position: relative;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .badge {
	vertical-align: middle;
	display: inline-block;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .actions {
	position: absolute;
	top: 50%;
	right: 0px;
	-webkit-transform: translateY(-50%);
	transform: translateY(-50%);
	font-size: 18px;
	color: #047bf8;
	text-decoration: none;
	cursor: pointer;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .actions > i {
	vertical-align: middle;
	display: inline-block;
	-webkit-transition: all 0.3s ease;
	transition: all 0.3s ease;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .actions .actions-list {
	position: absolute;
	background-color: #0f2338;
	color: #fff;
	font-size: 0.9rem;
	padding: 12px 12px;
	border-radius: 6px;
	visibility: hidden;
	opacity: 0;
	-webkit-transform: translateY(10px);
	transform: translateY(10px);
	-webkit-transition: all 0.2s ease;
	transition: all 0.2s ease;
	top: 0px;
	right: 0px;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .actions .actions-list a {
	display: block;
	padding: 5px 10px;
	border-bottom: 1px solid rgba(255, 255, 255, 0.05);
	color: #fff;
	text-decoration: none;
	white-space: nowrap;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .actions .actions-list a:last-child {
	border-bottom: none;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .actions .actions-list a i {
	font-size: 17px;
	display: inline-block;
	vertical-align: middle;
	margin-right: 10px;
	color: #fff;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .actions .actions-list a span {
	color: rgba(255, 255, 255, 0.7);
	display: inline-block;
	vertical-align: middle;
	-webkit-transition: all 0.2s ease;
	transition: all 0.2s ease;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .actions .actions-list a:hover span {
	color: #fff;
	-webkit-transform: translateX(-3px);
	transform: translateX(-3px);
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .actions .actions-list a.danger i {
	color: #ff5b5b;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .actions .actions-list a.danger span {
	color: #ff5b5b;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .actions:hover > i {
	-webkit-transform: rotate(180deg);
	transform: rotate(180deg);
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .actions:hover .actions-list {
	visibility: visible;
	-webkit-transform: translateY(0px);
	transform: translateY(0px);
	opacity: 1;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .author {
	display: inline-block;
	vertical-align: middle;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .info-data {
	display: inline-block;
	vertical-align: middle;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .info-data .label {
	color: #636c72;
	display: inline-block;
	vertical-align: middle;
	margin-right: 5px;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .info-data .value {
	display: inline-block;
	vertical-align: middle;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info > span + span {
	margin-left: 10px;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-content {
	padding: 20px 25px;
	margin-left: 15px;
	font-size: 0.99rem;
	background-color: #f3f3f7;
	border-radius: 6px;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-reply-content p:last-child {
	margin-bottom: 0px;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-attachments {
	text-align: right;
	margin-top: 10px;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-attachments .attachment {
	display: inline-block;
	padding: 5px 10px;
	padding-right: 20px;
	margin-bottom: 5px;
	background-color: #f6f7f8;
	color: #047bf8;
	border-radius: 6px;
	font-size: 0.72rem;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-attachments .attachment i {
	display: inline-block;
	vertical-align: middle;
	font-size: 18px;
	margin-right: 10px;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-attachments .attachment span {
	display: inline-block;
	vertical-align: middle;
}
.support-index .support-ticket-content-w .ticket-reply .ticket-attachments .attachment + .attachment {
	margin-left: 20px;
}
.support-index .support-ticket-content-w .ticket-reply.highlight .ticket-reply-info {
	border-bottom: none;
}
.support-index .support-ticket-content-w .ticket-reply.highlight .ticket-reply-content {
	background-color: #fff6d7;
}
.support-index .support-ticket-content-w .support-ticket-info {
	background-color: #f6f7f9;
	background: -webkit-gradient(linear, left top, left bottom, from(#eff1f7), to(#f9fafc));
	background: linear-gradient(to bottom, #eff1f7, #f9fafc);
	padding: 30px 20px;
	border-radius: 6px;
	-webkit-box-flex: 0;
	-ms-flex: 0 0 250px;
	flex: 0 0 250px;
	position: relative;
}
.support-index .support-ticket-content-w .support-ticket-info .close-ticket-info {
	position: absolute;
	top: 10px;
	right: 15px;
	color: #047bf8;
	text-decoration: none;
	font-size: 20px;
}
.support-index .support-ticket-content-w .support-ticket-info .info-header {
	color: #047bf8;
	text-align: center;
	margin-bottom: 15px;
}
.support-index .support-ticket-content-w .support-ticket-info .info-section {
	padding: 0px;
	margin-bottom: 40px;
	padding-bottom: 20px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.support-index .support-ticket-content-w .support-ticket-info .info-section .label {
	color: #636c72;
	display: block;
	margin-bottom: 5px;
}
.support-index .support-ticket-content-w .support-ticket-info .info-section .value {
	display: block;
}
.support-index .support-ticket-content-w .support-ticket-info .info-section .value + .label {
	margin-top: 10px;
}
.support-index .support-ticket-content-w .support-ticket-info .info-section:last-child {
	margin-bottom: 0px;
	border-bottom: none;
	padding-bottom: 0px;
}
.support-index .support-ticket-content-w .support-ticket-info ul.users-list {
	list-style: none;
	padding: 0px;
	margin: 0px;
	margin-bottom: 20px;
}
.support-index .support-ticket-content-w .support-ticket-info ul.users-list li {
	margin-bottom: 10px;
}
.support-index .support-ticket-content-w .support-ticket-info ul.users-list.as-tiles {
	display: inline-block;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
}
.support-index .support-ticket-content-w .support-ticket-info ul.users-list.as-tiles li {
	text-align: center;
	-webkit-box-flex: 0;
	-ms-flex: 0 0 47%;
	flex: 0 0 47%;
}
.support-index .support-ticket-content-w .support-ticket-info ul.users-list.as-tiles li a {
	display: block;
	background-color: #fff;
	-webkit-box-shadow: 0px 2px 4px rgba(126, 142, 177, 0.12);
	box-shadow: 0px 2px 4px rgba(126, 142, 177, 0.12);
	padding: 10px;
	border-radius: 6px;
	margin: 0px;
	text-decoration: none;
	-webkit-transition: all 0.2s ease;
	transition: all 0.2s ease;
}
.support-index .support-ticket-content-w .support-ticket-info ul.users-list.as-tiles li a span {
	display: block;
	border-bottom: none;
	color: #3e4b5b;
	font-size: 0.72rem;
}
.support-index .support-ticket-content-w .support-ticket-info ul.users-list.as-tiles li a .avatar {
	margin: 0px;
	width: 50px;
	height: 50px;
	margin-bottom: 5px;
	background-size: cover;
	display: inline-block;
	border-radius: 50px;
}
.support-index .support-ticket-content-w .support-ticket-info ul.users-list.as-tiles li a i {
	margin-right: 0px;
	margin-bottom: 10px;
}
.support-index .support-ticket-content-w .support-ticket-info ul.users-list.as-tiles li a:hover {
	-webkit-box-shadow: 0px 5px 12px rgba(126, 142, 177, 0.2);
	box-shadow: 0px 5px 12px rgba(126, 142, 177, 0.2);
	-webkit-transform: translateY(-3px);
	transform: translateY(-3px);
}
.support-index .support-ticket-content-w .support-ticket-info ul.users-list.as-tiles li a:hover span {
	color: #047bf8;
}
.support-index .support-ticket-content-w .support-ticket-info .customer {
	text-align: center;
	padding-bottom: 20px;
	margin-bottom: 30px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.support-index .support-ticket-content-w .support-ticket-info .customer .avatar {
	margin-bottom: 10px;
}
.support-index .support-ticket-content-w .support-ticket-info .customer .avatar img {
	border-radius: 50px;
	width: 100px;
	height: auto;
}
.support-index .support-ticket-content-w .support-ticket-info .customer .customer-name {
	margin-bottom: 5px;
}
.support-index .support-ticket-content-w .support-ticket-info .customer .customer-tickets {
	color: #636c72;
}
.support-index .support-ticket-content-w.folded-info .support-ticket-content {
	padding-right: 30px;
}
.support-index .support-ticket-content-w.folded-info .support-ticket-info {
	display: none;
}
.support-index .support-ticket-content-w.folded-info .show-ticket-info {
	display: inline-block;
}
.color-scheme-dark .support-index .st-meta .badge {
	border: rgba(0, 0, 0, 0.5) !important;
}
.color-scheme-dark .support-index .st-body .ticket-content .ticket-description {
	color: #ccd9e8;
}
.color-scheme-dark .support-index .st-foot {
	background-color: rgba(0, 0, 0, 0.05);
}
.color-scheme-dark .support-index .st-foot .label {
	color: rgba(218, 226, 243, 0.4);
}
.color-scheme-dark .support-index .support-tickets-header .tickets-control,
.color-scheme-dark .support-index .support-tickets-header .tickets-filter {
	border-bottom-color: rgba(255, 255, 255, 0.05);
}
.color-scheme-dark .support-index .support-ticket {
	background-color: #323c58;
	-webkit-box-shadow: 0px 5px 15px rgba(22, 22, 35, 0.1);
	box-shadow: 0px 5px 15px rgba(22, 22, 35, 0.1);
}
.color-scheme-dark .support-index .support-ticket.active,
.color-scheme-dark .support-index .support-ticket:hover {
	background-color: #047bf8;
}
.color-scheme-dark .support-index .support-ticket.active .ticket-title,
.color-scheme-dark .support-index .support-ticket:hover .ticket-title {
	color: #fff;
}
.color-scheme-dark .support-index .support-ticket.active .st-foot a.value,
.color-scheme-dark .support-index .support-ticket.active .st-foot .value,
.color-scheme-dark .support-index .support-ticket:hover .st-foot a.value,
.color-scheme-dark .support-index .support-ticket:hover .st-foot .value {
	color: #fff;
}
.color-scheme-dark .support-index .support-ticket.active .label,
.color-scheme-dark .support-index .support-ticket:hover .label {
	color: rgba(255, 255, 255, 0.6);
}
.color-scheme-dark .support-index .support-tickets-header .tickets-filter label {
	color: rgba(218, 226, 243, 0.4);
}
.color-scheme-dark .support-index .support-tickets-header .tickets-filter select {
	background-color: rgba(0, 0, 0, 0.2);
	border-color: rgba(0, 0, 0, 0.4);
	color: #ccd9e8;
}
.color-scheme-dark .support-index .support-tickets-header .tickets-control .element-search input {
	background-color: rgba(0, 0, 0, 0.1);
}
.color-scheme-dark .support-index .support-tickets-header .tickets-control .element-search:before {
	color: rgba(218, 226, 243, 0.4);
}
.color-scheme-dark
	.support-index
	.support-tickets-header
	.tickets-control
	.element-search
	input::-webkit-input-placeholder {
	color: rgba(218, 226, 243, 0.4);
}
.color-scheme-dark .support-index .support-tickets-header .tickets-control .element-search input:-ms-input-placeholder {
	color: rgba(218, 226, 243, 0.4);
}
.color-scheme-dark
	.support-index
	.support-tickets-header
	.tickets-control
	.element-search
	input::-ms-input-placeholder {
	color: rgba(218, 226, 243, 0.4);
}
.color-scheme-dark .support-index .support-tickets-header .tickets-control .element-search input::placeholder {
	color: rgba(218, 226, 243, 0.4);
}
.color-scheme-dark .support-index .support-ticket-content-w {
	background-color: #323c58;
	-webkit-box-shadow: 0px 5px 15px rgba(22, 22, 35, 0.1);
	box-shadow: 0px 5px 15px rgba(22, 22, 35, 0.1);
}
.color-scheme-dark .support-index .support-ticket-content-w .ticket-reply {
	border-bottom-color: rgba(255, 255, 255, 0.05);
}
.color-scheme-dark .support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .info-data .label {
	color: rgba(218, 226, 243, 0.4);
}
.color-scheme-dark .support-index .support-ticket-content-w .ticket-reply .ticket-attachments .attachment {
	background-color: #293148;
}
.color-scheme-dark .support-index .support-ticket-content-w .support-ticket-info {
	background-image: none;
	background-color: #293148;
}
.color-scheme-dark .support-index .support-ticket-content-w .support-ticket-info ul.users-list.as-tiles li a {
	background-color: #3b4768;
	-webkit-box-shadow: 0px 5px 15px rgba(22, 22, 35, 0.1);
	box-shadow: 0px 5px 15px rgba(22, 22, 35, 0.1);
}
.color-scheme-dark .support-index .support-ticket-content-w .support-ticket-info ul.users-list.as-tiles li a span {
	color: #ccd9e8;
}
.color-scheme-dark .support-index .support-ticket-content-w .support-ticket-info .info-section {
	border-bottom-color: rgba(255, 255, 255, 0.05);
}
.color-scheme-dark .support-index .support-ticket-content-w .support-ticket-info .info-section .label {
	color: rgba(218, 226, 243, 0.4);
}
.color-scheme-dark .support-index .support-ticket-content-w .support-ticket-info .customer {
	border-bottom-color: rgba(255, 255, 255, 0.05);
}
.color-scheme-dark .support-index .support-ticket-content-w .support-ticket-info .customer .customer-tickets {
	color: rgba(218, 226, 243, 0.4);
}
.color-scheme-dark .support-index .support-ticket-content-w .ticket-reply .ticket-reply-content {
    background-color: #3b4768;
}

.call-to-action {
    text-align: center;
    max-width: 800px;
    margin: 0px auto;
    padding: 50px 20px
}

.call-to-action .cta-header {
    font-size: 2.76rem
}

.call-to-action .cta-desc {
    font-weight: 300;
    color: #868686;
    font-size: 1.3rem
}

.call-to-action .cta-btn {
    margin-top: 40px
}

.call-to-action .cta-btn .btn-lg,
.call-to-action .cta-btn .btn-group-lg>.btn,
.call-to-action .cta-btn .wrapper-front .btn-group-lg>.fc-button,
.wrapper-front .call-to-action .cta-btn .btn-group-lg>.fc-button {
    font-size: 1.6rem;
    font-weight: 500;
    padding: 18px 40px;
    text-transform: uppercase;
    letter-spacing: 3px;
    padding-left: 50px
}

.call-to-action .cta-btn span {
    display: inline-block;
    vertical-align: middle
}

.call-to-action .cta-btn i {
    margin-right: 0px;
    font-size: 30px;
    display: inline-block;
    vertical-align: middle
}

.call-to-action .cta-btn span+i {
    margin-left: 10px;
    opacity: 0.4
}

.call-to-action .cta-btn i+i {
    margin-left: -18px
}

.footer-w {
    color: #868686;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#f8faff), to(#fff));
    background-image: linear-gradient(to bottom, #f8faff, #fff);
    font-weight: 300;
    margin-top: 10px;
    position: relative;
    z-index: 2;
    overflow: hidden
}

.footer-w .logo-element {
    content: "";
    width: 26px;
    height: 26px;
    border-radius: 15px;
    position: relative;
    margin-bottom: 30px;
    background-color: #98c9fd
}

.footer-w .logo-element:after {
    content: "";
    width: 26px;
    height: 26px;
    background-color: #047bf8;
    border-radius: 15px;
    right: -20px;
    position: absolute
}

.footer-w .footer-i {
    padding-top: 50px;
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

.footer-w .footer-i .heading-big {
    margin-bottom: 10px;
    text-transform: uppercase;
    font-size: 1.75rem;
    letter-spacing: 3px
}

.footer-w .footer-i .heading-small {
    color: #047bf8;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: .8rem;
    margin-bottom: 20px
}

.footer-w .footer-i ul {
    list-style-type: square;
    color: #047bf8
}

.footer-w .footer-i ul li {
    color: #868686
}

.footer-w .footer-i ul.social-links {
    margin: 0px;
    padding: 0px
}

.footer-w .footer-i ul.social-links li {
    display: inline-block;
    margin-right: 15px
}

.footer-w .footer-i ul.social-links li a {
    display: inline-block;
    vertical-align: middle
}

.footer-w .footer-i ul.social-links li a:hover {
    text-decoration: none
}

.footer-w .footer-i ul.social-links li a i {
    font-size: 50px
}

.footer-w .footer-i ul.social-links li a .os-icon-facebook {
    color: #0d509a
}

.footer-w .footer-i ul.social-links li a .os-icon-twitter {
    color: #2fafff
}

.footer-w .deep-footer {
    text-align: center;
    padding: 20px;
    font-size: .8rem;
    margin-top: 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.1)
}

.br-theme-osadmin .br-widget {
    height: 28px;
    white-space: nowrap
}

.br-theme-osadmin .br-widget a {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-decoration: none;
    margin-right: 2px
}

.br-theme-osadmin .br-widget a:after {
    content: '\e970';
    color: #d2d2d2
}

.br-theme-osadmin .br-widget a.br-active:after {
    color: #EDB867
}

.br-theme-osadmin .br-widget a.br-selected:after {
    color: #EDB867
}

.br-theme-osadmin .br-widget .br-current-rating {
    display: none
}

.br-theme-osadmin .br-readonly a {
    cursor: default
}

@media print {
    .br-theme-osadmin .br-widget a:after {
        content: '\f006';
        color: black
    }
    .br-theme-osadmin .br-widget a.br-active:after,
    .br-theme-osadmin .br-widget a.br-selected:after {
        content: '\e970';
        color: black
    }
}

.irs-line-mid,
.irs-line-left,
.irs-line-right,
.irs-bar,
.irs-bar-edge,
.irs-slider {
    background-color: #dddddd
}

.irs {
    height: 40px
}

.irs-with-grid {
    height: 60px
}

.irs-line {
    height: 5px;
    top: 25px;
    border-radius: 2px
}

.irs-line-left {
    height: 12px
}

.irs-line-mid {
    height: 12px
}

.irs-line-right {
    height: 12px
}

.irs-bar {
    height: 5px;
    top: 25px;
    background-color: #98c9fd
}

.irs-bar-edge {
    top: 25px;
    height: 12px;
    width: 9px
}

.irs-shadow {
    height: 3px;
    top: 34px;
    background: #000;
    opacity: 0.25
}

.lt-ie9 .irs-shadow {
    filter: alpha(opacity=25)
}

.irs-slider {
    width: 11px;
    height: 11px;
    top: 22px;
    background-color: #047bf8;
    -webkit-box-shadow: 0px 0px 0px 2px #fff;
    box-shadow: 0px 0px 0px 2px #fff;
    border-radius: 20px;
    cursor: pointer
}

.irs-slider:hover {
    background-color: #024994
}

.irs-min,
.irs-max {
    color: #999;
    font-size: 10px;
    line-height: 1.333;
    text-shadow: none;
    top: 0;
    padding: 1px 3px;
    background: #e1e4e9;
    border-radius: 4px
}

.irs-from,
.irs-to,
.irs-single {
    color: #fff;
    font-size: 10px;
    line-height: 1.333;
    text-shadow: none;
    padding: 1px 5px;
    background: #ed5565;
    border-radius: 4px
}

.irs-from:after,
.irs-to:after,
.irs-single:after {
    position: absolute;
    display: block;
    content: "";
    bottom: -6px;
    left: 50%;
    width: 0;
    height: 0;
    margin-left: -3px;
    overflow: hidden;
    border: 3px solid transparent;
    border-top-color: #ed5565
}

.irs-grid-pol {
    background: #e1e4e9
}

.irs-grid-text {
    color: #999
}

.dropzone {
    border: 2px dashed #047bf8
}

.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
    border-color: #cecece
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #e2ebff;
    border: 1px solid #4771d2
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: #474a50;
    margin-right: 4px
}

.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: #eff2ff
}

.form-control {
    font-weight: 300
}

.select2 {
    font-weight: 300
}

body .daterangepicker {
    -webkit-box-shadow: 3px 3px 40px rgba(0, 0, 0, 0.2);
    box-shadow: 3px 3px 40px rgba(0, 0, 0, 0.2);
    font-family: "Proxima Nova W01", "Rubik", -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif
}

body .daterangepicker td.in-range {
    background-color: #bdd5ff
}

body .daterangepicker td.active,
body .daterangepicker td.active:hover {
    background-color: #047bf8
}

body .daterangepicker th {
    font-weight: 500
}

body .daterangepicker:before {
    border-bottom-color: #3E4B5B
}

body .daterangepicker .calendar td {
    font-weight: 300;
    font-size: .9rem
}

body .daterangepicker .calendar th.month {
    color: #047bf8
}

body .daterangepicker thead tr:first-child th {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding-bottom: 5px
}

body .daterangepicker thead tr:first-child+tr th {
    padding-top: 10px
}

body .daterangepicker .daterangepicker_input i {
    left: 4px;
    top: 3px;
    font-size: 18px
}

body .daterangepicker .fa.fa-calendar.glyphicon.glyphicon-calendar:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e926"
}

body .daterangepicker .fa.fa-chevron-left.glyphicon.glyphicon-chevron-left:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e919";
    font-size: 10px
}

body .daterangepicker .fa.fa-chevron-right.glyphicon.glyphicon-chevron-right:before {
    font-family: 'osfont' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\e910";
    font-size: 10px
}

.dataTables_length select {
    display: inline-block;
    width: 50px;
    margin: 0px 5px;
    vertical-align: middle
}

.dataTables_filter input {
    display: inline-block;
    width: 130px;
    margin: 0px 5px;
    vertical-align: middle
}

.dataTables_wrapper .row:first-child {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    margin-top: 1rem;
    padding-bottom: 0.5rem
}

.dataTables_wrapper .row:last-child {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
    margin-bottom: 1rem;
    padding-top: 0.5rem
}

.fc-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.fc-header td {
    padding: 10px 0px
}

.fc-header h2 {
    text-transform: uppercase;
    font-size: 18px
}

.fc-content {
    color: #fff
}

.fc-event {
    background-color: #3584ff;
    -webkit-box-shadow: 5px 5px 10px 0px #bdd4ff;
    box-shadow: 5px 5px 10px 0px #bdd4ff;
    border: none;
    padding: 6px;
    padding-left: 9px;
    color: #fff;
    border-radius: 4px
}

.fc-day-number {
    color: #6B6862
}

.fc-day-header {
    font-weight: 300;
    color: #6B6862;
    text-transform: uppercase;
    font-size: 12px
}

.fc-other-month {
    background-color: #eee
}

.wrapper-front .fc-button {
    padding: 5px 10px;
    height: auto;
    margin: 0px 5px;
    background-image: none;
    -webkit-box-shadow: none;
    box-shadow: none
}

.wrapper-front .fc-button.fc-state-active {
    outline: none;
    text-shadow: none
}

table.dataTable {
    clear: both;
    margin-top: 6px !important;
    margin-bottom: 6px !important;
    max-width: none !important;
    border-collapse: separate !important
}

table.dataTable td,
table.dataTable th {
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

table.dataTable td.dataTables_empty,
table.dataTable th.dataTables_empty {
    text-align: center
}

table.dataTable.nowrap th,
table.dataTable.nowrap td {
    white-space: nowrap
}

div.dataTables_wrapper div.dataTables_length label {
    font-weight: normal;
    text-align: left;
    white-space: nowrap
}

div.dataTables_wrapper div.dataTables_length select {
    width: 75px;
    display: inline-block
}

div.dataTables_wrapper div.dataTables_filter {
    text-align: right
}

div.dataTables_wrapper div.dataTables_filter label {
    font-weight: normal;
    white-space: nowrap;
    text-align: left
}

div.dataTables_wrapper div.dataTables_filter input {
    margin-left: 0.5em;
    display: inline-block;
    width: auto
}

div.dataTables_wrapper div.dataTables_info {
    padding-top: 0.85em;
    white-space: nowrap
}

div.dataTables_wrapper div.dataTables_paginate {
    margin: 0;
    white-space: nowrap;
    text-align: right
}

div.dataTables_wrapper div.dataTables_paginate ul.pagination {
    margin: 2px 0;
    white-space: nowrap;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

div.dataTables_wrapper div.dataTables_processing {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    margin-left: -100px;
    margin-top: -26px;
    text-align: center;
    padding: 1em 0
}

table.dataTable thead>tr>th.sorting_asc,
table.dataTable thead>tr>th.sorting_desc,
table.dataTable thead>tr>th.sorting,
table.dataTable thead>tr>td.sorting_asc,
table.dataTable thead>tr>td.sorting_desc,
table.dataTable thead>tr>td.sorting {
    padding-right: 30px
}

table.dataTable thead>tr>th:active,
table.dataTable thead>tr>td:active {
    outline: none
}

table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_desc,
table.dataTable thead .sorting_asc_disabled,
table.dataTable thead .sorting_desc_disabled {
    cursor: pointer;
    position: relative
}

table.dataTable thead .sorting:before,
table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:before,
table.dataTable thead .sorting_desc_disabled:after {
    position: absolute;
    bottom: 0.9em;
    display: block;
    opacity: 0.3
}

table.dataTable thead .sorting:before,
table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_desc:before,
table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_desc_disabled:before {
    right: 1em;
    content: "\2191"
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:after {
    right: 0.5em;
    content: "\2193"
}

table.dataTable thead .sorting_asc:before,
table.dataTable thead .sorting_desc:after {
    opacity: 1
}

table.dataTable thead .sorting_asc_disabled:before,
table.dataTable thead .sorting_desc_disabled:after {
    opacity: 0
}

div.dataTables_scrollHead table.dataTable {
    margin-bottom: 0 !important
}

div.dataTables_scrollBody table {
    border-top: none;
    margin-top: 0 !important;
    margin-bottom: 0 !important
}

div.dataTables_scrollBody table thead .sorting:after,
div.dataTables_scrollBody table thead .sorting_asc:after,
div.dataTables_scrollBody table thead .sorting_desc:after {
    display: none
}

div.dataTables_scrollBody table tbody tr:first-child th,
div.dataTables_scrollBody table tbody tr:first-child td {
    border-top: none
}

div.dataTables_scrollFoot table {
    margin-top: 0 !important;
    border-top: none
}

@media screen and (max-width: 767px) {
    div.dataTables_wrapper div.dataTables_length,
    div.dataTables_wrapper div.dataTables_filter,
    div.dataTables_wrapper div.dataTables_info,
    div.dataTables_wrapper div.dataTables_paginate {
        text-align: center
    }
}

table.dataTable.table-condensed>thead>tr>th {
    padding-right: 20px
}

table.dataTable.table-condensed .sorting:after,
table.dataTable.table-condensed .sorting_asc:after,
table.dataTable.table-condensed .sorting_desc:after {
    top: 6px;
    right: 6px
}

table.table-bordered.dataTable th,
table.table-bordered.dataTable td {
    border-left-width: 0
}

table.table-bordered.dataTable th:last-child,
table.table-bordered.dataTable th:last-child,
table.table-bordered.dataTable td:last-child,
table.table-bordered.dataTable td:last-child {
    border-right-width: 0
}

table.table-bordered.dataTable tbody th,
table.table-bordered.dataTable tbody td {
    border-bottom-width: 0
}

div.dataTables_scrollHead table.table-bordered {
    border-bottom-width: 0
}

div.table-responsive>div.dataTables_wrapper>div.row {
    margin: 0
}

div.table-responsive>div.dataTables_wrapper>div.row>div[class^="col-"]:first-child {
    padding-left: 0
}

div.table-responsive>div.dataTables_wrapper>div.row>div[class^="col-"]:last-child {
    padding-right: 0
}

@media (max-width: 1650px) {
    .section-header {
        padding-left: 40px;
        padding-right: 40px
    }
}

@media (max-width: 1550px) {
	.support-index .support-ticket-content-w .ticket-reply .ticket-reply-info .badge {
		position: absolute;
		top: -10px;
		right: 0px;
	}
}

@media (max-width: 1400px) {
	.support-index .support-ticket-content-w .ticket-reply .ticket-reply-content {
		margin-left: 0px;
	}
	.support-index .support-ticket-content-w .support-ticket-content {
		padding-right: 30px;
		padding-left: 20px;
	}
	.support-index .support-ticket-content-w.folded-info .support-ticket-content {
		padding-right: 20px;
	}
	.support-index .support-tickets {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 400px;
		flex: 0 0 400px;
	}
	.support-index .st-foot {
		font-size: 0.72rem;
	}
	.support-index .st-foot .value + .label {
		margin-left: 10px;
	}
	.support-index .support-tickets {
		margin-right: 15px;
		padding-right: 15px;
	}
}

@media (max-width: 1350px) {
    .intro-w.layout-v1 .intro-description .intro-heading {
        font-size: 2.99rem
    }
    .section-header .section-title {
        font-size: 2.53rem
    }
    .features-table .feature-cell {
        padding: 50px 40px
    }
    .call-to-action .cta-header {
        font-size: 2.53rem
    }
    .menu-top-f ul.main-menu li a {
        padding: 20px 15px
    }
    .top-bar .logo-w {
        -webkit-box-flex: 0;
        -ms-flex: 0 0 350px;
        flex: 0 0 350px
    }
}

@media (max-width: 1250px) {
	.support-index .support-ticket-content-w .support-ticket-info {
		position: absolute;
		top: 10px;
		right: 10px;
		width: 250px;
		-webkit-box-shadow: 0px 20px 60px rgba(4, 123, 248, 0.3);
		box-shadow: 0px 20px 60px rgba(4, 123, 248, 0.3);
		background-color: #fff;
		background-image: none;
		display: none;
	}
	.support-index .support-ticket-content-w .support-ticket-content .show-ticket-info {
		display: block;
	}
	.support-index .support-ticket-content-w.force-show-folded-info .support-ticket-info {
		display: block;
	}
	.support-index .support-ticket-content-w .support-ticket-content {
		padding-right: 20px;
		padding-left: 20px;
	}
	.support-index .support-ticket-content-w.folded-info .support-ticket-content {
		padding: 10px 20px;
	}
}

@media (max-width: 1150px) {
    .intro-w.layout-v1 .intro-description .intro-text {
        font-size: 1.1rem
    }
    .intro-w.layout-v1 .intro-description .intro-heading {
        font-size: 2.53rem
    }
    .section-header .section-title {
        font-size: 2.3rem
    }
    .intro-w.layout-v1 .intro-description {
        -ms-flex-preferred-size: 500px;
        flex-basis: 500px
    }
    .menu-top-f ul.small-menu li.separate {
        padding-left: 15px;
        margin-left: 0px
    }
}

@media (max-width: 1024px) {
    .os-container {
        padding: 0px 20px
    }
    .features-table .b-l,
    .features-table .b-r,
    .features-table .b-b,
    .features-table .b-t {
        border-width: 0px
    }
    .menu-top-f .menu-top-i {
        display: none
    }
    .mobile-menu-w {
        display: block
    }
}

@media (min-width: 768px) and (max-width: 1024px) {
    .counters-w .counter .counter-value {
        font-size: 2.99rem
    }
    .counters-w .counter .counter-name {
        font-size: .9rem
    }
    .counters-w .counter .counter-description {
        font-size: 1rem;
        width: 160px
    }
}

@media (max-width: 768px) {
    .intro-w.layout-v1 .intro-i {
        -webkit-box-orient: vertical;
        -webkit-box-direction: reverse;
        -ms-flex-direction: column-reverse;
        flex-direction: column-reverse;
        height: auto
    }
    .intro-w.layout-v1 .intro-description {
        width: auto;
        padding: 50px;
        -ms-flex-preferred-size: auto;
        flex-basis: auto
    }
    .intro-w.layout-v1 .intro-media {
        width: 90%;
        padding-bottom: 45%;
        padding-top: 35%
    }
    .counters-w .counters-i {
        display: block
    }
    .counters-w .counter {
        margin-bottom: 20px;
        padding-bottom: 20px;
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
        justify-content: space-between;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1)
    }
    .counters-w .counter:last-child {
        margin-bottom: 10px;
        padding-bottom: 0px;
        border-bottom: none
    }
    .counters-w .counter .counter-value-w {
        width: 140px
    }
    .counters-w .counter .counter-description {
        width: auto;
        -webkit-box-flex: 1;
        -ms-flex: 1 1 auto;
        flex: 1 1 auto;
        font-size: 1.2rem
    }
	.support-index .support-tickets {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 100%;
		flex: 0 0 100%;
		margin-right: 0px;
		padding-right: 0px;
		border-right: 0px;
	}
	.support-index .support-ticket-content-w {
		display: none;
	}
	.support-index .support-ticket-content-w .support-ticket-content {
		padding: 10px 5px;
	}
	.support-index .support-ticket-content-w.folded-info .support-ticket-content {
		padding: 10px 5px;
	}
	.support-index.show-ticket-content {
		position: relative;
	}
	.support-index.show-ticket-content .support-ticket-content-w {
		display: block;
		-webkit-box-flex: 1;
		-ms-flex: 1;
		flex: 1;
	}
	.support-index.show-ticket-content .support-tickets {
		display: none;
	}
	.support-index.show-ticket-content .support-ticket-content-w .support-ticket-content .back-to-index {
		display: block;
		position: absolute;
		top: 0px;
		left: 10px;
	}
	.support-index.show-ticket-content .support-ticket-content-w .support-ticket-content .show-ticket-info {
		position: absolute;
		top: 0px;
		right: 10px;
	}
	.support-index .support-ticket-content-w .support-ticket-content .support-ticket-content-header {
		-webkit-box-orient: vertical;
		-webkit-box-direction: normal;
		-ms-flex-direction: column;
		flex-direction: column;
		padding-top: 0px;
		padding-top: 20px;
	}
	.support-index .support-ticket-content-w .support-ticket-content .support-ticket-content-header .show-ticket-info {
		padding-top: 0px;
	}
	.support-index .support-ticket-content-w .support-ticket-content .support-ticket-content-header .ticket-header {
		padding-top: 20px;
		border-top: 1px solid rgba(0, 0, 0, 0.07);
	}
}

@media (max-width: 767px) {
    .intro-w.layout-v1 .intro-description .intro-heading {
        font-size: 1.84rem
    }
    .intro-w.layout-v1 .intro-media {
        padding-left: 20px
    }
    .intro-w.layout-v1 .intro-description {
        padding: 40px 20px;
        text-align: center
    }
    .intro-w.layout-v1 .intro-description .intro-heading:before {
        left: 47.5%;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%)
    }
    .intro-w.layout-v1 .intro-description .intro-heading:after {
        left: 52.5%;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%)
    }
    .intro-w.layout-v1 .intro-media {
        padding-top: 40%;
        padding-bottom: 50%
    }
    .intro-w.layout-v1 .intro-description .intro-buttons {
        text-align: center
    }
    .intro-w.layout-v1 .intro-description .intro-buttons .btn.btn-primary,
    .intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .btn-primary.fc-button,
    .wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .btn-primary.fc-button,
    .intro-w.layout-v1 .intro-description .intro-buttons .wrapper-front .fc-button.fc-state-active,
    .wrapper-front .intro-w.layout-v1 .intro-description .intro-buttons .fc-button.fc-state-active {
        margin-bottom: 20px;
        margin-right: 0px
    }
    .counters-w {
        margin-bottom: 40px
    }
    .section-header .section-title {
        font-size: 1.84rem
    }
    .counters-w .counter {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        text-align: center
    }
    .counters-w .counter .counter-value-w {
        margin-right: 0px;
        padding-right: 0px;
        border-right: none;
        margin-bottom: 10px
    }
    .counters-w .counters-i {
        padding: 40px 10px
    }
    .footer-w .b-r {
        border-width: 0px
    }
    .testimonials-slider-w .slick-arrow,
    .projects-slider-w .slick-arrow {
        top: -20px
    }
    .section-header {
        padding-top: 50px;
        padding-bottom: 50px
    }
    .section-header .section-desc {
        font-size: 1rem
    }
    .project-slide .project-content {
        padding: 20px
    }
    .testimonials-slider-w .slide {
        padding: 50px 40px;
        padding-bottom: 25px;
        text-align: center
    }
    .testimonials-w {
        padding: 50px 0px;
        margin-top: 70px
    }
    .testimonials-slider-w .testimonial-by {
        text-align: center;
        margin-top: 20px
    }
    .testimonials-slider-w .testimonial-by .avatar {
        margin-top: 10px
    }
}